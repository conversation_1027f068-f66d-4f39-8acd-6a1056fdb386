'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON>, useParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  ArrowLeft,
  Edit,
  Building2,
  Phone,
  Mail,
  MapPin,
  Star,
  Package,
  Calendar,
  DollarSign,
  CreditCard,
  Globe,
  User,
  Trash2
} from 'lucide-react'

interface Supplier {
  _id: string
  name: string
  code: string
  contactPerson: {
    name: string
    title?: string
    email?: string
    phone?: string
    mobile?: string
  }
  company: {
    address: {
      street?: string
      city?: string
      state?: string
      country?: string
      zipCode?: string
    }
    phone?: string
    website?: string
  }
  paymentTerms: string
  creditLimit: number
  currency: string
  categories: Array<{
    _id: string
    name: string
    color: string
  }>
  rating: number
  isActive: boolean
  lastOrderDate?: string
  totalOrders: number
  totalOrderValue: number
  createdAt: string
}

export default function SupplierDetailPage() {
  const { user, isAuthenticated } = useAuth()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const params = useParams()
  const supplierId = params.id as string

  const [supplier, setSupplier] = useState<Supplier | null>(null)
  const [loading, setLoading] = useState(true)

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const t = {
    en: {
      supplierDetails: 'Supplier Details',
      back: 'Back to Suppliers',
      edit: 'Edit Supplier',
      delete: 'Delete Supplier',
      companyInfo: 'Company Information',
      contactInfo: 'Contact Information',
      businessInfo: 'Business Information',
      performanceMetrics: 'Performance Metrics',
      supplierCode: 'Supplier Code',
      paymentTerms: 'Payment Terms',
      creditLimit: 'Credit Limit',
      categories: 'Categories',
      rating: 'Rating',
      status: 'Status',
      totalOrders: 'Total Orders',
      totalValue: 'Total Value',
      lastOrder: 'Last Order',
      joinedDate: 'Joined Date',
      contactPerson: 'Contact Person',
      title: 'Title',
      email: 'Email',
      phone: 'Phone',
      mobile: 'Mobile',
      address: 'Address',
      website: 'Website',
      active: 'Active',
      inactive: 'Inactive',
      notFound: 'Supplier not found',
      loadingError: 'Error loading supplier details'
    },
    mm: {
      supplierDetails: 'ပေးသွင်းသူ အသေးစိတ်များ',
      back: 'ပေးသွင်းသူများ သို့ ပြန်သွားရန်',
      edit: 'ပေးသွင်းသူ ပြင်ရန်',
      delete: 'ပေးသွင်းသူ ဖျက်ရန်',
      companyInfo: 'ကုမ္ပဏီ အချက်အလက်များ',
      contactInfo: 'ဆက်သွယ်ရေး အချက်အလက်များ',
      businessInfo: 'လုပ်ငန်း အချက်အလက်များ',
      performanceMetrics: 'စွမ်းဆောင်ရည် တိုင်းတာမှုများ',
      supplierCode: 'ပေးသွင်းသူ ကုဒ်',
      paymentTerms: 'ငွေပေးချေမှု စည်းကမ်းများ',
      creditLimit: 'ခရက်ဒစ် ကန့်သတ်ချက်',
      categories: 'အမျိုးအစားများ',
      rating: 'အဆင့်သတ်မှတ်ချက်',
      status: 'အခြေအနေ',
      totalOrders: 'စုစုပေါင်း အမှာစာများ',
      totalValue: 'စုစုပေါင်း တန်ဖိုး',
      lastOrder: 'နောက်ဆုံး အမှာစာ',
      joinedDate: 'ပါဝင်သည့် ရက်စွဲ',
      contactPerson: 'ဆက်သွယ်ရမည့် ပုဂ္ဂိုလ်',
      title: 'ရာထူး',
      email: 'အီးမေးလ်',
      phone: 'ဖုန်းနံပါတ်',
      mobile: 'မိုဘိုင်း',
      address: 'လိပ်စာ',
      website: 'ဝက်ဘ်ဆိုက်',
      active: 'အသုံးပြုနေသော',
      inactive: 'အသုံးမပြုတော့သော',
      notFound: 'ပေးသွင်းသူ မတွေ့ရှိပါ',
      loadingError: 'ပေးသွင်းသူ အသေးစိတ်များ ရယူရာတွင် အမှားရှိသည်'
    }
  }

  const text = t[language]

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }

    if (supplierId) {
      fetchSupplierDetails()
    }
  }, [isAuthenticated, supplierId])

  const fetchSupplierDetails = async () => {
    try {
      setLoading(true)
      const response = await apiClient.getSupplier(supplierId)

      if (response.success && response.data) {
        setSupplier((response as any).data)
      } else {
        console.error('Failed to load supplier:', response.error)
      }
    } catch (error) {
      console.error('Error loading supplier:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleDelete = async () => {
    if (!supplier) return

    const confirmMessage = language === 'mm'
      ? `${supplier.name} ကို ဖျက်မည်လား? ဤလုပ်ဆောင်ချက်ကို ပြန်ပြင်၍မရပါ။`
      : `Delete ${supplier.name}? This action cannot be undone.`

    if (window.confirm(confirmMessage)) {
      try {
        const response = await apiClient.deleteSupplier(supplierId)

        if (response.success) {
          alert(language === 'mm' ? 'ပေးသွင်းသူ ဖျက်ပြီးပါပြီ' : 'Supplier deleted successfully!')
          router.push('/suppliers')
        } else {
          alert('Failed to delete supplier: ' + (response.error || 'Unknown error'))
        }
      } catch (error) {
        console.error('Error deleting supplier:', error)
        alert('Error deleting supplier. Please try again.')
      }
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString(language === 'mm' ? 'my-MM' : 'en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const getPaymentTermsText = (terms: string) => {
    const termsMap: Record<string, string> = {
      'cash': language === 'mm' ? 'ငွေသား' : 'Cash',
      'net_15': language === 'mm' ? '၁၅ ရက်' : 'Net 15 Days',
      'net_30': language === 'mm' ? '၃၀ ရက်' : 'Net 30 Days',
      'net_45': language === 'mm' ? '၄၅ ရက်' : 'Net 45 Days',
      'net_60': language === 'mm' ? '၆၀ ရက်' : 'Net 60 Days',
      'net_90': language === 'mm' ? '၉၀ ရက်' : 'Net 90 Days',
      'custom': language === 'mm' ? 'စိတ်ကြိုက်' : 'Custom Terms'
    }
    return termsMap[terms] || terms
  }

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ))
  }

  if (!isAuthenticated) {
    return null
  }

  if (loading) {
    return (
      <MainLayout language={language}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Building2 className="h-12 w-12 mx-auto mb-4 animate-spin text-blue-600" />
            <p className="text-gray-600 dark:text-gray-400">
              {language === 'mm' ? 'ပေးသွင်းသူ အချက်အလက်များ ရယူနေသည်...' : 'Loading supplier details...'}
            </p>
          </div>
        </div>
      </MainLayout>
    )
  }

  if (!supplier) {
    return (
      <MainLayout language={language}>
        <div className="text-center py-12">
          <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
            {text.notFound}
          </h3>
          <p className="text-gray-500 mb-4">
            {text.loadingError}
          </p>
          <Button onClick={() => router.push('/suppliers')}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            {text.back}
          </Button>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/suppliers')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {text.back}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {text.supplierDetails}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {supplier.name} - {supplier.code}
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={() => router.push(`/suppliers/${supplierId}/edit`)}
            >
              <Edit className="h-4 w-4 mr-2" />
              {text.edit}
            </Button>
            <Button
              variant="destructive"
              onClick={handleDelete}
            >
              <Trash2 className="h-4 w-4 mr-2" />
              {text.delete}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Company Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5 text-blue-600" />
                {text.companyInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">{text.supplierCode}</label>
                <p className="text-sm">{supplier.code}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">{text.status}</label>
                <div className="mt-1">
                  <Badge
                    variant="outline"
                    className={supplier.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                  >
                    {supplier.isActive ? text.active : text.inactive}
                  </Badge>
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">{text.categories}</label>
                <div className="flex flex-wrap gap-1 mt-1">
                  {supplier.categories.map((category) => (
                    <Badge key={category._id} variant="outline" className="text-xs">
                      {category.name}
                    </Badge>
                  ))}
                </div>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">{text.joinedDate}</label>
                <p className="text-sm">{formatDate(supplier.createdAt)}</p>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-green-600" />
                {text.contactInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">{text.contactPerson}</label>
                <p className="text-sm font-medium">{supplier.contactPerson.name}</p>
                {supplier.contactPerson.title && (
                  <p className="text-xs text-gray-500">{supplier.contactPerson.title}</p>
                )}
              </div>
              {supplier.contactPerson.email && (
                <div className="flex items-center gap-2">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{supplier.contactPerson.email}</span>
                </div>
              )}
              {supplier.contactPerson.phone && (
                <div className="flex items-center gap-2">
                  <Phone className="h-4 w-4 text-gray-400" />
                  <span className="text-sm">{supplier.contactPerson.phone}</span>
                </div>
              )}
              {supplier.company.website && (
                <div className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-gray-400" />
                  <a 
                    href={supplier.company.website} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="text-sm text-blue-600 hover:underline"
                  >
                    {supplier.company.website}
                  </a>
                </div>
              )}
              {supplier.company.address && (
                <div className="flex items-start gap-2">
                  <MapPin className="h-4 w-4 text-gray-400 mt-0.5" />
                  <div className="text-sm">
                    {supplier.company.address.street && <div>{supplier.company.address.street}</div>}
                    {supplier.company.address.city && <div>{supplier.company.address.city}</div>}
                    {supplier.company.address.state && <div>{supplier.company.address.state}</div>}
                    {supplier.company.address.country && <div>{supplier.company.address.country}</div>}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Business Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-purple-600" />
                {text.businessInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-500">{text.paymentTerms}</label>
                <p className="text-sm">{getPaymentTermsText(supplier.paymentTerms)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">{text.creditLimit}</label>
                <p className="text-sm font-medium">{formatCurrency(supplier.creditLimit)}</p>
              </div>
              <div>
                <label className="text-sm font-medium text-gray-500">{text.rating}</label>
                <div className="flex items-center gap-2 mt-1">
                  <div className="flex gap-1">
                    {getRatingStars(supplier.rating)}
                  </div>
                  <span className="text-sm text-gray-600">{supplier.rating}/5</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Metrics */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5 text-orange-600" />
              {text.performanceMetrics}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{supplier.totalOrders}</div>
                <div className="text-sm text-gray-600">{text.totalOrders}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{formatCurrency(supplier.totalOrderValue)}</div>
                <div className="text-sm text-gray-600">{text.totalValue}</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-purple-600">
                  {supplier.lastOrderDate ? formatDate(supplier.lastOrderDate) : '-'}
                </div>
                <div className="text-sm text-gray-600">{text.lastOrder}</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
