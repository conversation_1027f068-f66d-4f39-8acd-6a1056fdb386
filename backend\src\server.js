const express = require('express');
const cors = require('cors');
const helmet = require('helmet');
const morgan = require('morgan');
const compression = require('compression');
const http = require('http');
require('dotenv').config();

const connectDB = require('./config/database');
const { errorHandler, notFound } = require('./utils/errorHandler');
const WebSocketServer = require('./websocket/websocketServer');

// Import routes
const authRoutes = require('./routes/auth');
const userRoutes = require('./routes/users');
const productRoutes = require('./routes/products');
const categoryRoutes = require('./routes/categories');
const saleRoutes = require('./routes/sales');
const receiptRoutes = require('./routes/receipts');
const inventoryRoutes = require('./routes/inventory');
const stockAdjustmentRoutes = require('./routes/stockAdjustments');
const purchaseOrderRoutes = require('./routes/purchaseOrders');
const purchaseOrderReceivingRoutes = require('./routes/purchaseOrderReceiving');
const supplierRoutes = require('./routes/suppliers');
const reportRoutes = require('./routes/reports');
const settingRoutes = require('./routes/settings');
const customerRoutes = require('./routes/customers');
const forecastingRoutes = require('./routes/forecasting');
const posRoutes = require('./routes/pos');
const exchangeRateRoutes = require('./routes/exchangeRates');
const dashboardRoutes = require('./routes/dashboard');
const invoiceRoutes = require('./routes/invoiceRoutes');
const hardwareRoutes = require('./routes/hardware');
const paymentRoutes = require('./routes/payments');
const aiRoutes = require('./routes/ai');

const app = express();

// Connect to real MongoDB database
console.log('🚀 Connecting to real MongoDB database...');

// Connect to MongoDB
connectDB();

// Security middleware
app.use(helmet());

// CORS configuration
app.use(cors({
    origin: [
        'http://localhost:3000',
        'http://localhost:3001',
        'http://localhost:3002',
        process.env.CORS_ORIGIN
    ].filter(Boolean),
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// Compression middleware
app.use(compression());

// Logging middleware
if (process.env.NODE_ENV === 'development') {
    app.use(morgan('dev'));
} else {
    app.use(morgan('combined'));
}

// Body parsing middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Static files
app.use('/uploads', express.static('uploads'));

// Health check endpoint
app.get('/health', (req, res) => {
    res.status(200).json({
        status: 'OK',
        message: 'BitsTech POS API is running',
        timestamp: new Date().toISOString(),
        version: process.env.APP_VERSION || '1.0.0'
    });
});

// API routes
const apiPrefix = process.env.API_PREFIX || '/api';

app.use(`${apiPrefix}/auth`, authRoutes);
app.use(`${apiPrefix}/users`, userRoutes);
app.use(`${apiPrefix}/products`, productRoutes);
app.use(`${apiPrefix}/categories`, categoryRoutes);
app.use(`${apiPrefix}/sales`, saleRoutes);
app.use(`${apiPrefix}/receipts`, receiptRoutes);
app.use(`${apiPrefix}/inventory`, inventoryRoutes);
app.use(`${apiPrefix}/stock-adjustments`, stockAdjustmentRoutes);
app.use(`${apiPrefix}/purchase-orders`, purchaseOrderRoutes);
app.use(`${apiPrefix}/purchase-orders`, purchaseOrderReceivingRoutes);
app.use(`${apiPrefix}/suppliers`, supplierRoutes);
app.use(`${apiPrefix}/reports`, reportRoutes);
app.use(`${apiPrefix}/settings`, settingRoutes);
app.use(`${apiPrefix}/customers`, customerRoutes);
app.use(`${apiPrefix}/forecasting`, forecastingRoutes);
app.use(`${apiPrefix}/pos`, posRoutes);
app.use(`${apiPrefix}/exchange-rates`, exchangeRateRoutes);
app.use(`${apiPrefix}/dashboard`, dashboardRoutes);
app.use(`${apiPrefix}/invoice`, invoiceRoutes);
app.use(`${apiPrefix}/hardware`, hardwareRoutes);
app.use(`${apiPrefix}/payments`, paymentRoutes);
app.use(`${apiPrefix}/ai`, aiRoutes);

// Welcome route
app.get('/', (req, res) => {
    res.json({
        message: 'Welcome to BitsTech POS API',
        version: '1.0.0',
        documentation: '/api/docs',
        health: '/health'
    });
});

// Error handling middleware
app.use(notFound);
app.use(errorHandler);

// Start server
const PORT = process.env.PORT || 5001;

// Create HTTP server
const server = http.createServer(app);

// Initialize WebSocket server
const wsServer = new WebSocketServer(server);

// Make WebSocket server available globally for broadcasting
global.wsServer = wsServer;

server.listen(PORT, () => {
    console.log(`
🚀 BitsTech POS Server is running!
📍 Environment: ${process.env.NODE_ENV || 'development'}
🌐 Server: http://localhost:${PORT}
🔗 API: http://localhost:${PORT}${apiPrefix}
📊 Health: http://localhost:${PORT}/health
🔌 WebSocket: ws://localhost:${PORT}/ws
    `);

    // Log available routes for debugging
    console.log('📋 Available API Routes:');
    console.log(`   POST ${apiPrefix}/auth/login`);
    console.log(`   POST ${apiPrefix}/auth/register`);
    console.log(`   GET  ${apiPrefix}/auth/me`);
    console.log(`   GET  ${apiPrefix}/products`);
    console.log(`   GET  ${apiPrefix}/categories`);
    console.log(`   GET  ${apiPrefix}/dashboard/stats`);
    console.log(`   GET  ${apiPrefix}/sales`);
    console.log(`   POST ${apiPrefix}/pos/checkout`);
    console.log('🔄 Server ready to accept connections...');
});

// Graceful shutdown
process.on('SIGTERM', () => {
    console.log('SIGTERM received. Shutting down gracefully...');
    server.close(() => {
        console.log('Process terminated');
    });
});

process.on('SIGINT', () => {
    console.log('SIGINT received. Shutting down gracefully...');
    server.close(() => {
        console.log('Process terminated');
    });
});

module.exports = app;
