'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import {
  Package,
  Search,
  Truck,
  CheckCircle,
  AlertTriangle,
  Clock,
  Scan,
  FileText,
  Eye,
  ArrowLeft,
  RefreshCw,
  Filter,
  Calendar,
  User,
  MapPin,
  Phone
} from 'lucide-react'

interface PendingPO {
  _id: string
  orderNumber: string
  supplier: {
    name: string
    code: string
    contactPerson: {
      name: string
      phone?: string
    }
    address?: string
  }
  orderDate: string
  expectedDeliveryDate: string
  status: 'pending' | 'approved' | 'ordered' | 'partially_received'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  totalItems: number
  totalAmount: number
  currency: string
  completionPercentage: number
  pendingItems: number
  receivedItems: number
  items: Array<{
    _id: string
    productName: string
    sku: string
    quantity: number
    receivedQuantity: number
    unitCost: number
    totalCost: number
  }>
}

export default function ReceivePurchaseOrdersPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [pendingPOs, setPendingPOs] = useState<PendingPO[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchPendingPOs()
    }
  }, [isAuthenticated, searchQuery, priorityFilter])

  const fetchPendingPOs = async () => {
    try {
      setLoading(true)

      // Mock data for pending POs that need receiving
      const mockPendingPOs: PendingPO[] = [
        {
          _id: '2',
          orderNumber: 'PO-240102-002',
          supplier: {
            name: 'Logitech Myanmar',
            code: 'SUP-LOG-002',
            contactPerson: {
              name: 'Mary Johnson',
              phone: '+95-1-345678'
            },
            address: '123 Business Street, Yangon'
          },
          orderDate: '2024-01-02T14:30:00Z',
          expectedDeliveryDate: '2024-01-10T14:30:00Z',
          status: 'partially_received',
          priority: 'high',
          totalItems: 50,
          totalAmount: 7200000,
          currency: 'MMK',
          completionPercentage: 60,
          pendingItems: 20,
          receivedItems: 30,
          items: [
            {
              _id: '3',
              productName: 'Logitech MX Master 3S',
              sku: 'MOU001',
              quantity: 30,
              receivedQuantity: 18,
              unitCost: 120000,
              totalCost: 3600000
            },
            {
              _id: '4',
              productName: 'Logitech MX Keys',
              sku: 'KEY001',
              quantity: 20,
              receivedQuantity: 12,
              unitCost: 180000,
              totalCost: 3600000
            }
          ]
        },
        {
          _id: '3',
          orderNumber: 'PO-240103-003',
          supplier: {
            name: 'Samsung Electronics Myanmar',
            code: 'SUP-SAM-003',
            contactPerson: {
              name: 'David Lee',
              phone: '+95-1-456789'
            },
            address: '456 Technology Avenue, Yangon'
          },
          orderDate: '2024-01-03T09:15:00Z',
          expectedDeliveryDate: '2024-01-20T09:15:00Z',
          status: 'ordered',
          priority: 'urgent',
          totalItems: 23,
          totalAmount: 8400000,
          currency: 'MMK',
          completionPercentage: 0,
          pendingItems: 23,
          receivedItems: 0,
          items: [
            {
              _id: '5',
              productName: 'Samsung 27" Monitor',
              sku: 'MON001',
              quantity: 8,
              receivedQuantity: 0,
              unitCost: 450000,
              totalCost: 3600000
            },
            {
              _id: '6',
              productName: 'Samsung 1TB SSD',
              sku: 'SSD001',
              quantity: 15,
              receivedQuantity: 0,
              unitCost: 320000,
              totalCost: 4800000
            }
          ]
        },
        {
          _id: '4',
          orderNumber: 'PO-240104-004',
          supplier: {
            name: 'Intel Myanmar Distribution',
            code: 'SUP-INT-004',
            contactPerson: {
              name: 'Sarah Wilson',
              phone: '+95-1-567890'
            },
            address: '789 Hardware Plaza, Yangon'
          },
          orderDate: '2024-01-04T11:00:00Z',
          expectedDeliveryDate: '2024-01-18T11:00:00Z',
          status: 'approved',
          priority: 'normal',
          totalItems: 12,
          totalAmount: 5100000,
          currency: 'MMK',
          completionPercentage: 0,
          pendingItems: 12,
          receivedItems: 0,
          items: [
            {
              _id: '7',
              productName: 'Intel Core i7-13700K',
              sku: 'CPU001',
              quantity: 6,
              receivedQuantity: 0,
              unitCost: 425000,
              totalCost: 2550000
            },
            {
              _id: '8',
              productName: 'Intel Core i5-13600K',
              sku: 'CPU002',
              quantity: 6,
              receivedQuantity: 0,
              unitCost: 425000,
              totalCost: 2550000
            }
          ]
        }
      ]

      // Apply filters
      let filteredPOs = mockPendingPOs

      if (searchQuery) {
        filteredPOs = filteredPOs.filter(po =>
          po.orderNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
          po.supplier.name.toLowerCase().includes(searchQuery.toLowerCase())
        )
      }

      if (priorityFilter) {
        filteredPOs = filteredPOs.filter(po => po.priority === priorityFilter)
      }

      setPendingPOs(filteredPOs)
    } catch (error) {
      console.error('Error fetching pending POs:', error)
      setPendingPOs([])
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />
      case 'approved': return <CheckCircle className="h-4 w-4 text-blue-600" />
      case 'ordered': return <Truck className="h-4 w-4 text-purple-600" />
      case 'partially_received': return <Package className="h-4 w-4 text-orange-600" />
      default: return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'approved':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Approved</Badge>
      case 'ordered':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800">Ordered</Badge>
      case 'partially_received':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800">Partial</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'low':
        return <Badge variant="outline" className="bg-gray-100 text-gray-600">Low</Badge>
      case 'normal':
        return <Badge variant="outline" className="bg-blue-100 text-blue-600">Normal</Badge>
      case 'high':
        return <Badge variant="outline" className="bg-orange-100 text-orange-600">High</Badge>
      case 'urgent':
        return <Badge variant="outline" className="bg-red-100 text-red-600">Urgent</Badge>
      default:
        return <Badge variant="outline">Normal</Badge>
    }
  }

  const formatPrice = (price: number, currency: string) => {
    return `${price.toLocaleString()} ${currency}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/purchase-orders')}
            className="hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Purchase Orders'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 to-purple-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Package className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'ကုန်ပစ္စည်း လက်ခံခြင်း' : 'Receive Purchase Orders'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm'
                      ? 'ဝယ်ယူမှု အမှာစာများ လက်ခံပြီး စာရင်းဝင်ခြင်း'
                      : 'Receive and process incoming purchase orders'
                    }
                  </p>
                </div>
              </div>
              <Button
                onClick={fetchPendingPOs}
                className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {language === 'mm' ? 'ပြန်လည်ရယူ' : 'Refresh'}
              </Button>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စောင့်ဆိုင်းနေသော PO များ' : 'Pending POs'}
                  </p>
                  <p className="text-2xl font-bold">{pendingPOs.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-xl">
                  <Package className="h-6 w-6 text-orange-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စောင့်ဆိုင်းနေသော ပစ္စည်းများ' : 'Pending Items'}
                  </p>
                  <p className="text-2xl font-bold">
                    {pendingPOs.reduce((sum, po) => sum + po.pendingItems, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'လက်ခံပြီး ပစ္စည်းများ' : 'Received Items'}
                  </p>
                  <p className="text-2xl font-bold">
                    {pendingPOs.reduce((sum, po) => sum + po.receivedItems, 0)}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-red-100 dark:bg-red-900/20 rounded-xl">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                </div>
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'အရေးပေါ် PO များ' : 'Urgent POs'}
                  </p>
                  <p className="text-2xl font-bold">
                    {pendingPOs.filter(po => po.priority === 'urgent').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={language === 'mm' ? 'PO နံပါတ် သို့မဟုတ် ပေးသွင်းသူ ရှာရန်...' : 'Search by PO number or supplier...'}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-4">
                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800"
                  aria-label={language === 'mm' ? 'ဦးစားပေးမှု စစ်ထုတ်ရန်' : 'Filter by priority'}
                >
                  <option value="">{language === 'mm' ? 'ဦးစားပေးမှု အားလုံး' : 'All Priorities'}</option>
                  <option value="urgent">{language === 'mm' ? 'အရေးပေါ်' : 'Urgent'}</option>
                  <option value="high">{language === 'mm' ? 'မြင့်' : 'High'}</option>
                  <option value="normal">{language === 'mm' ? 'ပုံမှန်' : 'Normal'}</option>
                  <option value="low">{language === 'mm' ? 'နိမ့်' : 'Low'}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pending POs List */}
        <div className="space-y-4">
          {pendingPOs.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Package className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {language === 'mm' ? 'လက်ခံရန် PO မရှိပါ' : 'No POs to Receive'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {language === 'mm'
                    ? 'လက်ရှိ လက်ခံရန် ဝယ်ယူမှု အမှာစာ မရှိပါ'
                    : 'There are no purchase orders pending for receiving'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            pendingPOs.map((po) => (
              <Card key={po._id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-4">
                        <div className="flex items-center gap-2">
                          {getStatusIcon(po.status)}
                          <h3 className="text-lg font-semibold">{po.orderNumber}</h3>
                        </div>
                        <div className="flex gap-2">
                          {getStatusBadge(po.status)}
                          {getPriorityBadge(po.priority)}
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-4">
                        <div className="flex items-center gap-2">
                          <User className="h-4 w-4 text-gray-500" />
                          <div>
                            <p className="text-sm font-medium">{po.supplier.name}</p>
                            <p className="text-xs text-gray-600">{po.supplier.contactPerson.name}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Phone className="h-4 w-4 text-gray-500" />
                          <div>
                            <p className="text-sm">{po.supplier.contactPerson.phone}</p>
                            <p className="text-xs text-gray-600">{language === 'mm' ? 'ဆက်သွယ်ရန်' : 'Contact'}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Calendar className="h-4 w-4 text-gray-500" />
                          <div>
                            <p className="text-sm">{formatDate(po.expectedDeliveryDate)}</p>
                            <p className="text-xs text-gray-600">{language === 'mm' ? 'မျှော်လင့်ထားသော ရက်' : 'Expected Date'}</p>
                          </div>
                        </div>

                        <div className="flex items-center gap-2">
                          <Package className="h-4 w-4 text-gray-500" />
                          <div>
                            <p className="text-sm font-medium">{po.pendingItems} / {po.totalItems}</p>
                            <p className="text-xs text-gray-600">{language === 'mm' ? 'စောင့်ဆိုင်း / စုစုပေါင်း' : 'Pending / Total'}</p>
                          </div>
                        </div>
                      </div>

                      {/* Progress Bar */}
                      <div className="mb-4">
                        <div className="flex justify-between text-sm mb-1">
                          <span>{language === 'mm' ? 'တိုးတက်မှု' : 'Progress'}</span>
                          <span>{po.completionPercentage}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${po.completionPercentage}%` }}
                          ></div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-lg font-bold">{formatPrice(po.totalAmount, po.currency)}</p>
                          <p className="text-sm text-gray-600">{language === 'mm' ? 'စုစုပေါင်း ပမာဏ' : 'Total Amount'}</p>
                        </div>
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      <Button
                        onClick={() => router.push(`/purchase-orders/receive/${po._id}`)}
                        className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                      >
                        <Scan className="h-4 w-4 mr-2" />
                        {language === 'mm' ? 'လက်ခံရန်' : 'Receive'}
                      </Button>

                      <Button
                        variant="outline"
                        onClick={() => router.push(`/purchase-orders/${po._id}`)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        {language === 'mm' ? 'ကြည့်ရန်' : 'View'}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </MainLayout>
  )
}
