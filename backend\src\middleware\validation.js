// Product validation
const validateProduct = (req, res, next) => {
  const { name, sku, category, price, cost } = req.body;

  // Required fields
  if (!name || !name.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Product name is required'
    });
  }

  if (!sku || !sku.trim()) {
    return res.status(400).json({
      success: false,
      message: 'SKU is required'
    });
  }

  if (!category || !category.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Category is required'
    });
  }

  // Price validation
  if (price !== undefined && (isNaN(price) || price < 0)) {
    return res.status(400).json({
      success: false,
      message: 'Price must be a valid positive number'
    });
  }

  if (cost !== undefined && (isNaN(cost) || cost < 0)) {
    return res.status(400).json({
      success: false,
      message: 'Cost must be a valid positive number'
    });
  }

  next();
};

// Category validation
const validateCategory = (req, res, next) => {
  const { name } = req.body;

  if (!name || !name.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Category name is required'
    });
  }

  next();
};

// User validation
const validateUser = (req, res, next) => {
  const { name, email, password, role } = req.body;

  // Required fields
  if (!name || !name.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Name is required'
    });
  }

  if (!email || !email.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Email is required'
    });
  }

  // Email validation
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(email)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid email format'
    });
  }

  // Password validation (only for new users)
  if (req.method === 'POST' && (!password || password.length < 6)) {
    return res.status(400).json({
      success: false,
      message: 'Password must be at least 6 characters long'
    });
  }

  // Role validation
  const validRoles = ['admin', 'manager', 'cashier'];
  if (role && !validRoles.includes(role)) {
    return res.status(400).json({
      success: false,
      message: 'Invalid role'
    });
  }

  next();
};

// Stock Adjustment validation
const validateStockAdjustment = (req, res, next) => {
  const { type, items, reason } = req.body;

  // Required fields
  if (!type || !['increase', 'decrease'].includes(type)) {
    return res.status(400).json({
      success: false,
      message: 'Type must be either "increase" or "decrease"'
    });
  }

  if (!items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'At least one item is required'
    });
  }

  if (!reason || !reason.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Reason is required'
    });
  }

  // Validate items
  for (const item of items) {
    if (!item.product) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required for all items'
      });
    }

    if (!item.quantity || item.quantity <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Quantity must be greater than 0'
      });
    }
  }

  next();
};

// Purchase Order validation
const validatePurchaseOrder = (req, res, next) => {
  const { supplier, expectedDeliveryDate, items } = req.body;

  // Required fields
  if (!supplier || !supplier.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Supplier is required'
    });
  }

  if (!expectedDeliveryDate) {
    return res.status(400).json({
      success: false,
      message: 'Expected delivery date is required'
    });
  }

  if (!items || !Array.isArray(items) || items.length === 0) {
    return res.status(400).json({
      success: false,
      message: 'At least one item is required'
    });
  }

  // Validate items
  for (const item of items) {
    if (!item.product) {
      return res.status(400).json({
        success: false,
        message: 'Product ID is required for all items'
      });
    }

    if (!item.quantity || item.quantity <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Quantity must be greater than 0'
      });
    }

    if (!item.unitCost || item.unitCost <= 0) {
      return res.status(400).json({
        success: false,
        message: 'Unit cost must be greater than 0'
      });
    }
  }

  next();
};

// Supplier validation
const validateSupplier = (req, res, next) => {
  const { name, contactPerson, company } = req.body;

  // Required fields
  if (!name || !name.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Supplier name is required'
    });
  }

  if (!contactPerson || !contactPerson.name || !contactPerson.name.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Contact person name is required'
    });
  }

  if (!company || !company.name || !company.name.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Company name is required'
    });
  }

  if (!company.address || !company.address.trim()) {
    return res.status(400).json({
      success: false,
      message: 'Company address is required'
    });
  }

  // Email validation if provided
  if (contactPerson.email && contactPerson.email.trim()) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(contactPerson.email)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid email format'
      });
    }
  }

  // Phone validation if provided
  if (contactPerson.phone && contactPerson.phone.trim()) {
    const phoneRegex = /^[\+]?[0-9\-\(\)\s]+$/;
    if (!phoneRegex.test(contactPerson.phone)) {
      return res.status(400).json({
        success: false,
        message: 'Invalid phone number format'
      });
    }
  }

  next();
};

module.exports = {
  validateProduct,
  validateCategory,
  validateUser,
  validateStockAdjustment,
  validatePurchaseOrder,
  validateSupplier
};
