const mongoose = require('mongoose');
const Product = require('../models/Product');
const StockMovement = require('../models/StockMovement');
const User = require('../models/User');

const generateInventoryTransactions = async () => {
    try {
        console.log('🌱 Generating inventory transactions...');
        
        const products = await Product.find({ isActive: true });
        const users = await User.find({ isActive: true });
        
        if (products.length === 0 || users.length === 0) {
            throw new Error('No products or users found. Please seed products and users first.');
        }
        
        const transactionTypes = ['in', 'out', 'adjustment', 'return', 'damage', 'transfer'];
        const transactions = [];
        
        // Generate transactions for the last 60 days
        const today = new Date();
        const sixtyDaysAgo = new Date(today.getTime() - (60 * 24 * 60 * 60 * 1000));
        
        for (const product of products) {
            // Initial stock entry
            const initialStock = Math.floor(Math.random() * 100) + 50;
            transactions.push({
                product: product._id,
                type: 'in',
                quantity: initialStock,
                previousQuantity: 0,
                newQuantity: initialStock,
                unitCost: product.cost || product.price * 0.7,
                totalCost: (product.cost || product.price * 0.7) * initialStock,
                reason: 'Initial stock',
                createdBy: users[0]._id,
                createdAt: sixtyDaysAgo,
                updatedAt: sixtyDaysAgo
            });
            
            // Generate random transactions for each product
            const transactionCount = Math.floor(Math.random() * 10) + 5;
            let currentStock = initialStock;
            
            for (let i = 0; i < transactionCount; i++) {
                const transactionDate = new Date(sixtyDaysAgo.getTime() + Math.random() * (today.getTime() - sixtyDaysAgo.getTime()));
                const type = transactionTypes[Math.floor(Math.random() * transactionTypes.length)];
                
                let quantity, newQuantity, reason;
                
                switch (type) {
                    case 'in':
                        quantity = Math.floor(Math.random() * 50) + 10;
                        newQuantity = currentStock + quantity;
                        reason = 'Stock replenishment';
                        break;
                    case 'out':
                        quantity = Math.min(Math.floor(Math.random() * 10) + 1, currentStock);
                        newQuantity = currentStock - quantity;
                        reason = 'Product sale';
                        break;
                    case 'adjustment':
                        const adjustment = Math.floor(Math.random() * 20) - 10; // -10 to +10
                        quantity = Math.abs(adjustment);
                        newQuantity = currentStock + adjustment;
                        reason = adjustment > 0 ? 'Stock correction (increase)' : 'Stock correction (decrease)';
                        break;
                    case 'return':
                        quantity = Math.floor(Math.random() * 5) + 1;
                        newQuantity = currentStock + quantity;
                        reason = 'Customer return';
                        break;
                    case 'damage':
                        quantity = Math.min(Math.floor(Math.random() * 5) + 1, currentStock);
                        newQuantity = currentStock - quantity;
                        reason = 'Damaged goods';
                        break;
                    case 'transfer':
                        quantity = Math.min(Math.floor(Math.random() * 10) + 1, currentStock);
                        newQuantity = currentStock - quantity;
                        reason = 'Transfer to branch';
                        break;
                }
                
                // Ensure stock doesn't go negative
                if (newQuantity < 0) {
                    newQuantity = 0;
                    quantity = currentStock;
                }

                const unitCost = product.cost || product.price * 0.7;
                const totalCost = quantity * unitCost;

                transactions.push({
                    product: product._id,
                    type: type,
                    quantity: quantity,
                    previousQuantity: currentStock,
                    newQuantity: newQuantity,
                    unitCost: unitCost,
                    totalCost: totalCost,
                    reason: reason,
                    createdBy: users[Math.floor(Math.random() * users.length)]._id,
                    createdAt: transactionDate,
                    updatedAt: transactionDate
                });
                
                currentStock = newQuantity;
            }
            
            // Update product stock
            await Product.findByIdAndUpdate(product._id, {
                stock: currentStock,
                lowStockThreshold: Math.floor(Math.random() * 20) + 5
            });
        }
        
        return transactions;
        
    } catch (error) {
        console.error('❌ Error generating inventory transactions:', error);
        throw error;
    }
};

const seedInventory = async () => {
    try {
        console.log('🌱 Seeding inventory transactions...');
        
        // Clear existing inventory transactions
        await StockMovement.deleteMany({});
        console.log('🗑️ Cleared existing inventory transactions');

        // Generate and create transactions
        const transactionData = await generateInventoryTransactions();
        const transactions = await StockMovement.insertMany(transactionData);
        console.log(`✅ Created ${transactions.length} inventory transactions`);

        // Display inventory statistics
        const totalTransactions = await StockMovement.countDocuments();
        
        const transactionsByType = await StockMovement.aggregate([
            { $group: { _id: '$type', count: { $sum: 1 }, totalCost: { $sum: '$totalCost' } } },
            { $sort: { count: -1 } }
        ]);
        
        const lowStockProducts = await Product.find({
            $expr: { $lte: ['$stock', '$lowStockThreshold'] },
            isActive: true
        });
        
        const totalInventoryValue = await Product.aggregate([
            {
                $project: {
                    value: { $multiply: ['$stock', { $ifNull: ['$cost', { $multiply: ['$price', 0.7] }] }] }
                }
            },
            { $group: { _id: null, total: { $sum: '$value' } } }
        ]);
        
        console.log('\n📊 Inventory Statistics:');
        console.log(`- Total Transactions: ${totalTransactions}`);
        console.log(`- Low Stock Products: ${lowStockProducts.length}`);
        console.log(`- Total Inventory Value: ${totalInventoryValue[0]?.total || 0} MMK`);
        
        console.log('\n📦 Transactions by Type:');
        transactionsByType.forEach(type => {
            console.log(`- ${type._id}: ${type.count} transactions (${type.totalCost} MMK)`);
        });
        
        if (lowStockProducts.length > 0) {
            console.log('\n⚠️ Low Stock Products:');
            lowStockProducts.slice(0, 5).forEach(product => {
                console.log(`- ${product.name}: ${product.stock} units (threshold: ${product.lowStockThreshold})`);
            });
            if (lowStockProducts.length > 5) {
                console.log(`... and ${lowStockProducts.length - 5} more`);
            }
        }
        
        console.log('🎉 Inventory seeding completed successfully!');
        
    } catch (error) {
        console.error('❌ Error seeding inventory:', error);
        throw error;
    }
};

module.exports = { seedInventory };

// Run if called directly
if (require.main === module) {
    const connectDB = async () => {
        try {
            const mongoURI = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos';
            await mongoose.connect(mongoURI);
            console.log('✅ MongoDB connected for inventory seeding');
        } catch (error) {
            console.error('❌ MongoDB connection failed:', error.message);
            process.exit(1);
        }
    };

    const runSeed = async () => {
        try {
            await connectDB();
            await seedInventory();
            console.log('\n🎯 Inventory seeding completed successfully!');
            process.exit(0);
        } catch (error) {
            console.error('❌ Inventory seeding failed:', error);
            process.exit(1);
        }
    };

    runSeed();
}
