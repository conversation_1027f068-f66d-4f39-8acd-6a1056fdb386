'use client'

import { useState, useEffect, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { Input } from '@/components/ui/input'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useSettings } from '@/contexts/settings-context'
import {
  Search,
  X,
  Settings,
  User,
  Bell,
  Shield,
  Database,
  Palette,
  Globe,
  DollarSign,
  FileText,
  Building,
  Printer,
  Smartphone
} from 'lucide-react'

interface SearchResult {
  id: string
  title: string
  titleLocal: string
  description: string
  descriptionLocal: string
  category: string
  categoryLocal: string
  path: string
  icon: any
  keywords: string[]
}

interface SettingsSearchProps {
  isOpen: boolean
  onClose: () => void
}

export function SettingsSearch({ isOpen, onClose }: SettingsSearchProps) {
  const { language } = useSettings()
  const router = useRouter()
  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [selectedIndex, setSelectedIndex] = useState(0)
  const inputRef = useRef<HTMLInputElement>(null)

  const searchData: SearchResult[] = [
    {
      id: 'general',
      title: 'General Settings',
      titleLocal: 'အထွေထွေ ဆက်တင်များ',
      description: 'Basic system configuration and preferences',
      descriptionLocal: 'အခြေခံ စနစ် ပြင်ဆင်မှု နှင့် ရွေးချယ်မှုများ',
      category: 'System',
      categoryLocal: 'စနစ်',
      path: '/settings/general',
      icon: Settings,
      keywords: ['general', 'basic', 'system', 'config', 'preferences', 'အထွေထွေ', 'အခြေခံ', 'စနစ်']
    },
    {
      id: 'users',
      title: 'User Management',
      titleLocal: 'အသုံးပြုသူ စီမံခန့်ခွဲမှု',
      description: 'Manage users, roles, and permissions',
      descriptionLocal: 'အသုံးပြုသူများ၊ အခန်းကဏ္ဍများ နှင့် ခွင့်ပြုချက်များ စီမံခန့်ခွဲပါ',
      category: 'Users',
      categoryLocal: 'အသုံးပြုသူများ',
      path: '/settings/users',
      icon: User,
      keywords: ['users', 'roles', 'permissions', 'accounts', 'အသုံးပြုသူ', 'အခန်းကဏ္ဍ', 'ခွင့်ပြုချက်']
    },
    {
      id: 'notifications',
      title: 'Notifications',
      titleLocal: 'အကြောင်းကြားချက်များ',
      description: 'Configure alerts and notification settings',
      descriptionLocal: 'သတိပေးချက်များ နှင့် အကြောင်းကြားချက် ဆက်တင်များ ပြင်ဆင်ပါ',
      category: 'Communication',
      categoryLocal: 'ဆက်သွယ်ရေး',
      path: '/settings/notifications',
      icon: Bell,
      keywords: ['notifications', 'alerts', 'email', 'sms', 'push', 'အကြောင်းကြား', 'သတိပေး']
    },
    {
      id: 'security',
      title: 'Security & Privacy',
      titleLocal: 'လုံခြုံရေး နှင့် ကိုယ်ရေးကိုယ်တာ',
      description: 'Security settings and privacy controls',
      descriptionLocal: 'လုံခြုံရေး ဆက်တင်များ နှင့် ကိုယ်ရေးကိုယ်တာ ထိန်းချုပ်မှုများ',
      category: 'Security',
      categoryLocal: 'လုံခြုံရေး',
      path: '/settings/security',
      icon: Shield,
      keywords: ['security', 'privacy', 'password', 'backup', 'လုံခြုံရေး', 'ကိုယ်ရေးကိုယ်တာ', 'စကားဝှက်']
    },
    {
      id: 'database',
      title: 'Database Management',
      titleLocal: 'ဒေတာဘေ့စ် စီမံခန့်ခွဲမှု',
      description: 'Database backup, restore, and maintenance',
      descriptionLocal: 'ဒေတာဘေ့စ် အရန်သိမ်းမှု၊ ပြန်လည်ရယူမှု နှင့် ပြုပြင်ထိန်းသိမ်းမှု',
      category: 'Data',
      categoryLocal: 'ဒေတာ',
      path: '/settings/database',
      icon: Database,
      keywords: ['database', 'backup', 'restore', 'maintenance', 'ဒေတာဘေ့စ်', 'အရန်သိမ်း', 'ပြန်လည်ရယူ']
    },
    {
      id: 'appearance',
      title: 'Appearance & Themes',
      titleLocal: 'အသွင်အပြင် နှင့် အပြင်အဆင်များ',
      description: 'Customize colors, themes, and visual settings',
      descriptionLocal: 'အရောင်များ၊ အပြင်အဆင်များ နှင့် မြင်ကွင်း ဆက်တင်များ စိတ်ကြိုက်ပြင်ဆင်ပါ',
      category: 'Interface',
      categoryLocal: 'မျက်နှာပြင်',
      path: '/settings/appearance',
      icon: Palette,
      keywords: ['appearance', 'themes', 'colors', 'dark', 'light', 'အသွင်အပြင်', 'အရောင်', 'မှောင်', 'လင်း']
    },
    {
      id: 'language',
      title: 'Language & Region',
      titleLocal: 'ဘာသာစကား နှင့် ဒေသ',
      description: 'Language preferences and regional settings',
      descriptionLocal: 'ဘာသာစကား ရွေးချယ်မှုများ နှင့် ဒေသဆိုင်ရာ ဆက်တင်များ',
      category: 'Localization',
      categoryLocal: 'ဒေသအလိုက်',
      path: '/settings/language',
      icon: Globe,
      keywords: ['language', 'region', 'locale', 'myanmar', 'english', 'thai', 'ဘာသာစကား', 'ဒေသ']
    },
    {
      id: 'currency',
      title: 'Currency & Exchange',
      titleLocal: 'ငွေကြေး နှင့် လဲလှယ်နှုန်း',
      description: 'Currency settings and exchange rates',
      descriptionLocal: 'ငွေကြေး ဆက်တင်များ နှင့် လဲလှယ်နှုန်းများ',
      category: 'Finance',
      categoryLocal: 'ငွေကြေး',
      path: '/settings/currency',
      icon: DollarSign,
      keywords: ['currency', 'exchange', 'rates', 'mmk', 'usd', 'thb', 'ငွေကြေး', 'လဲလှယ်နှုန်း']
    },
    {
      id: 'receipts',
      title: 'Receipt Templates',
      titleLocal: 'ဘောက်ချာ ပုံစံများ',
      description: 'Customize receipt and invoice templates',
      descriptionLocal: 'ဘောက်ချာ နှင့် ငွေတောင်းခံလွှာ ပုံစံများ စိတ်ကြိုက်ပြင်ဆင်ပါ',
      category: 'Documents',
      categoryLocal: 'စာရွက်စာတမ်းများ',
      path: '/settings/receipts',
      icon: FileText,
      keywords: ['receipts', 'templates', 'invoice', 'print', 'ဘောက်ချာ', 'ပုံစံ', 'ငွေတောင်းခံလွှာ']
    },
    {
      id: 'company',
      title: 'Company Information',
      titleLocal: 'ကုမ္ပဏီ အချက်အလက်များ',
      description: 'Business details and company settings',
      descriptionLocal: 'လုပ်ငန်း အသေးစိတ်များ နှင့် ကုမ္ပဏီ ဆက်တင်များ',
      category: 'Business',
      categoryLocal: 'လုပ်ငန်း',
      path: '/settings/company',
      icon: Building,
      keywords: ['company', 'business', 'information', 'details', 'ကုမ္ပဏီ', 'လုပ်ငန်း', 'အချက်အလက်']
    },
    {
      id: 'printers',
      title: 'Printer Settings',
      titleLocal: 'ပရင်တာ ဆက်တင်များ',
      description: 'Configure printers and printing options',
      descriptionLocal: 'ပရင်တာများ နှင့် ပုံနှိပ်ခြင်း ရွေးချယ်မှုများ ပြင်ဆင်ပါ',
      category: 'Hardware',
      categoryLocal: 'ဟာ့ဒ်ဝဲ',
      path: '/settings/printers',
      icon: Printer,
      keywords: ['printers', 'printing', 'thermal', 'receipt', 'ပရင်တာ', 'ပုံနှိပ်', 'ဘောက်ချာ']
    },
    {
      id: 'mobile',
      title: 'Mobile Settings',
      titleLocal: 'မိုဘိုင်း ဆက်တင်များ',
      description: 'Mobile app configuration and sync settings',
      descriptionLocal: 'မိုဘိုင်း အက်ပ် ပြင်ဆင်မှု နှင့် ထပ်တူပြုမှု ဆက်တင်များ',
      category: 'Mobile',
      categoryLocal: 'မိုဘိုင်း',
      path: '/settings/mobile',
      icon: Smartphone,
      keywords: ['mobile', 'app', 'sync', 'offline', 'မိုဘိုင်း', 'အက်ပ်', 'ထပ်တူပြု']
    }
  ]

  useEffect(() => {
    if (isOpen && inputRef.current && typeof window !== 'undefined') {
      // Small delay to ensure modal is fully rendered
      setTimeout(() => {
        inputRef.current?.focus()
      }, 100)
    }
  }, [isOpen])

  useEffect(() => {
    if (!query.trim()) {
      setResults([])
      setSelectedIndex(0)
      return
    }

    const filtered = searchData.filter(item => {
      const searchTerms = query.toLowerCase().split(' ')
      return searchTerms.every(term =>
        item.title.toLowerCase().includes(term) ||
        item.titleLocal.toLowerCase().includes(term) ||
        item.description.toLowerCase().includes(term) ||
        item.descriptionLocal.toLowerCase().includes(term) ||
        item.category.toLowerCase().includes(term) ||
        item.categoryLocal.toLowerCase().includes(term) ||
        item.keywords.some(keyword => keyword.toLowerCase().includes(term))
      )
    })

    setResults(filtered)
    setSelectedIndex(0)
  }, [query])

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'ArrowDown') {
      e.preventDefault()
      setSelectedIndex(prev => Math.min(prev + 1, results.length - 1))
    } else if (e.key === 'ArrowUp') {
      e.preventDefault()
      setSelectedIndex(prev => Math.max(prev - 1, 0))
    } else if (e.key === 'Enter') {
      e.preventDefault()
      if (results[selectedIndex]) {
        handleResultClick(results[selectedIndex])
      }
    } else if (e.key === 'Escape') {
      onClose()
    }
  }

  const handleResultClick = (result: SearchResult) => {
    router.push(result.path)
    onClose()
    setQuery('')
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-start justify-center pt-20">
      <div className="bg-white dark:bg-gray-800 rounded-2xl shadow-2xl w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden">
        {/* Search Input */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
            <Input
              ref={inputRef}
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={language === 'mm' ? 'ဆက်တင်များ ရှာဖွေရန်...' : 'Search settings...'}
              className="pl-10 pr-10 py-3 text-lg border-0 focus:ring-0 bg-transparent"
            />
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 h-auto p-1"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </div>

        {/* Search Results */}
        <div className="max-h-96 overflow-y-auto">
          {query.trim() && results.length === 0 ? (
            <div className="p-8 text-center text-gray-500">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>{language === 'mm' ? 'ရလဒ် မတွေ့ပါ' : 'No results found'}</p>
            </div>
          ) : results.length > 0 ? (
            <div className="p-2">
              {results.map((result, index) => {
                const Icon = result.icon
                return (
                  <div
                    key={result.id}
                    onClick={() => handleResultClick(result)}
                    className={`p-4 rounded-lg cursor-pointer transition-colors ${
                      index === selectedIndex
                        ? 'bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-700'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-700/50'
                    }`}
                  >
                    <div className="flex items-start gap-3">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                        <Icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium">
                            {language === 'mm' ? result.titleLocal : result.title}
                          </h3>
                          <Badge variant="outline" className="text-xs">
                            {language === 'mm' ? result.categoryLocal : result.category}
                          </Badge>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {language === 'mm' ? result.descriptionLocal : result.description}
                        </p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          ) : (
            <div className="p-8 text-center text-gray-500">
              <Settings className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p className="mb-2">
                {language === 'mm' ? 'ဆက်တင်များ ရှာဖွေပါ' : 'Search for settings'}
              </p>
              <p className="text-sm">
                {language === 'mm' 
                  ? 'အသုံးပြုသူများ၊ လုံခြုံရေး၊ အသွင်အပြင် စသည်တို့ကို ရှာဖွေပါ'
                  : 'Try searching for users, security, appearance, etc.'
                }
              </p>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
          <div className="flex items-center justify-between text-xs text-gray-500">
            <div className="flex items-center gap-4">
              <span>↑↓ {language === 'mm' ? 'ရွေးချယ်ရန်' : 'Navigate'}</span>
              <span>↵ {language === 'mm' ? 'ဖွင့်ရန်' : 'Open'}</span>
              <span>Esc {language === 'mm' ? 'ပိတ်ရန်' : 'Close'}</span>
            </div>
            <span>{results.length} {language === 'mm' ? 'ရလဒ်' : 'results'}</span>
          </div>
        </div>
      </div>
    </div>
  )
}
