// Settings Validation and Conflict Detection
// This module provides comprehensive validation for all settings

interface ValidationRule {
  field: string
  type: 'required' | 'email' | 'phone' | 'url' | 'number' | 'range' | 'pattern' | 'custom'
  message: string
  messageLocal: string
  params?: any
  validator?: (value: any, allSettings?: any) => boolean
}

interface ValidationResult {
  isValid: boolean
  errors: ValidationError[]
  warnings: ValidationWarning[]
  conflicts: SettingsConflict[]
}

interface ValidationError {
  field: string
  message: string
  messageLocal: string
  type: 'error'
}

interface ValidationWarning {
  field: string
  message: string
  messageLocal: string
  type: 'warning'
}

interface SettingsConflict {
  fields: string[]
  message: string
  messageLocal: string
  type: 'conflict'
  severity: 'low' | 'medium' | 'high'
}

class SettingsValidator {
  private rules: Map<string, ValidationRule[]> = new Map()
  private conflictRules: Array<(settings: any) => SettingsConflict[]> = []

  constructor() {
    this.initializeValidationRules()
    this.initializeConflictRules()
  }

  private initializeValidationRules() {
    // Company Information Rules
    this.addRule('company', {
      field: 'storeName',
      type: 'required',
      message: 'Store name is required',
      messageLocal: 'ဆိုင်အမည် လိုအပ်ပါသည်'
    })

    this.addRule('company', {
      field: 'storeEmail',
      type: 'email',
      message: 'Valid email address is required',
      messageLocal: 'မှန်ကန်သော အီးမေးလ် လိပ်စာ လိုအပ်ပါသည်'
    })

    this.addRule('company', {
      field: 'storePhone',
      type: 'phone',
      message: 'Valid phone number is required',
      messageLocal: 'မှန်ကန်သော ဖုန်းနံပါတ် လိုအပ်ပါသည်'
    })

    // Tax & Currency Rules
    this.addRule('tax', {
      field: 'defaultTaxRate',
      type: 'range',
      message: 'Tax rate must be between 0 and 100',
      messageLocal: 'အခွန်နှုန်းထား ၀ မှ ၁၀၀ ကြားတွင် ရှိရမည်',
      params: { min: 0, max: 100 }
    })

    this.addRule('currency', {
      field: 'exchangeRates.USD.rate',
      type: 'number',
      message: 'USD exchange rate must be a positive number',
      messageLocal: 'USD လဲလှယ်နှုန်း အပေါင်းကိန်း ဖြစ်ရမည်'
    })

    this.addRule('currency', {
      field: 'exchangeRates.THB.rate',
      type: 'number',
      message: 'THB exchange rate must be a positive number',
      messageLocal: 'THB လဲလှယ်နှုန်း အပေါင်းကိန်း ဖြစ်ရမည်'
    })

    // User Management Rules
    this.addRule('users', {
      field: 'email',
      type: 'email',
      message: 'Valid email address is required',
      messageLocal: 'မှန်ကန်သော အီးမေးလ် လိပ်စာ လိုအပ်ပါသည်'
    })

    this.addRule('users', {
      field: 'password',
      type: 'custom',
      message: 'Password must be at least 8 characters with uppercase, lowercase, and numbers',
      messageLocal: 'စကားဝှက်သည် အနည်းဆုံး ၈ လုံး ရှိရမည်၊ အကြီးအသေး နှင့် ဂဏန်းများ ပါဝင်ရမည်',
      validator: (value: string) => {
        if (!value || value.length < 8) return false
        return /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/.test(value)
      }
    })

    // Security Rules
    this.addRule('security', {
      field: 'sessionTimeout',
      type: 'range',
      message: 'Session timeout must be between 15 and 480 minutes',
      messageLocal: 'Session timeout သည် ၁၅ မှ ၄၈၀ မိနစ် ကြားတွင် ရှိရမည်',
      params: { min: 15, max: 480 }
    })

    this.addRule('security', {
      field: 'maxLoginAttempts',
      type: 'range',
      message: 'Max login attempts must be between 3 and 10',
      messageLocal: 'အများဆုံး လော့ဂ်အင် ကြိုးစားမှု ၃ မှ ၁၀ ကြားတွင် ရှိရမည်',
      params: { min: 3, max: 10 }
    })

    // Notification Rules
    this.addRule('notifications', {
      field: 'emailSettings.smtpHost',
      type: 'required',
      message: 'SMTP host is required for email notifications',
      messageLocal: 'အီးမေးလ် အကြောင်းကြားချက်များအတွက် SMTP host လိုအပ်ပါသည်'
    })

    this.addRule('notifications', {
      field: 'emailSettings.smtpPort',
      type: 'range',
      message: 'SMTP port must be between 1 and 65535',
      messageLocal: 'SMTP port သည် ၁ မှ ၆၅၅၃၅ ကြားတွင် ရှိရမည်',
      params: { min: 1, max: 65535 }
    })
  }

  private initializeConflictRules() {
    // Currency conflicts
    this.conflictRules.push((settings) => {
      const conflicts: SettingsConflict[] = []
      
      if (settings.currency?.primaryCurrency === 'USD' && 
          settings.currency?.exchangeRates?.USD?.rate !== 1) {
        conflicts.push({
          fields: ['currency.primaryCurrency', 'currency.exchangeRates.USD.rate'],
          message: 'Primary currency USD should have exchange rate of 1',
          messageLocal: 'အဓိက ငွေကြေး USD သည် လဲလှယ်နှုန်း ၁ ရှိရမည်',
          type: 'conflict',
          severity: 'medium'
        })
      }

      return conflicts
    })

    // Security conflicts
    this.conflictRules.push((settings) => {
      const conflicts: SettingsConflict[] = []
      
      if (settings.security?.twoFactorAuth === false && 
          settings.security?.sessionTimeout > 120) {
        conflicts.push({
          fields: ['security.twoFactorAuth', 'security.sessionTimeout'],
          message: 'Long session timeout without 2FA may pose security risks',
          messageLocal: '2FA မရှိဘဲ ရှည်လျားသော session timeout သည် လုံခြုံရေး အန္တရာယ် ဖြစ်နိုင်သည်',
          type: 'conflict',
          severity: 'high'
        })
      }

      return conflicts
    })

    // Notification conflicts
    this.conflictRules.push((settings) => {
      const conflicts: SettingsConflict[] = []
      
      if (settings.notifications?.emailNotifications === true && 
          !settings.notifications?.emailSettings?.smtpHost) {
        conflicts.push({
          fields: ['notifications.emailNotifications', 'notifications.emailSettings.smtpHost'],
          message: 'Email notifications enabled but SMTP not configured',
          messageLocal: 'အီးမေးလ် အကြောင်းကြားချက် ဖွင့်ထားသော်လည်း SMTP မပြင်ဆင်ထားပါ',
          type: 'conflict',
          severity: 'high'
        })
      }

      return conflicts
    })

    // Backup conflicts
    this.conflictRules.push((settings) => {
      const conflicts: SettingsConflict[] = []
      
      if (settings.database?.autoBackup === true && 
          settings.database?.retentionDays < 7) {
        conflicts.push({
          fields: ['database.autoBackup', 'database.retentionDays'],
          message: 'Auto backup enabled but retention period is too short',
          messageLocal: 'အလိုအလျောက် အရန်သိမ်းမှု ဖွင့်ထားသော်လည်း သိမ်းဆည်းကာလ တိုလွန်းပါသည်',
          type: 'conflict',
          severity: 'medium'
        })
      }

      return conflicts
    })
  }

  private addRule(category: string, rule: ValidationRule) {
    if (!this.rules.has(category)) {
      this.rules.set(category, [])
    }
    this.rules.get(category)!.push(rule)
  }

  private validateField(value: any, rule: ValidationRule, allSettings?: any): ValidationError | null {
    switch (rule.type) {
      case 'required':
        if (!value || (typeof value === 'string' && value.trim() === '')) {
          return {
            field: rule.field,
            message: rule.message,
            messageLocal: rule.messageLocal,
            type: 'error'
          }
        }
        break

      case 'email':
        if (value && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(value)) {
          return {
            field: rule.field,
            message: rule.message,
            messageLocal: rule.messageLocal,
            type: 'error'
          }
        }
        break

      case 'phone':
        if (value && !/^[\+]?[0-9\s\-\(\)]{10,}$/.test(value)) {
          return {
            field: rule.field,
            message: rule.message,
            messageLocal: rule.messageLocal,
            type: 'error'
          }
        }
        break

      case 'url':
        if (value && !/^https?:\/\/.+/.test(value)) {
          return {
            field: rule.field,
            message: rule.message,
            messageLocal: rule.messageLocal,
            type: 'error'
          }
        }
        break

      case 'number':
        if (value !== undefined && value !== null && (isNaN(value) || value < 0)) {
          return {
            field: rule.field,
            message: rule.message,
            messageLocal: rule.messageLocal,
            type: 'error'
          }
        }
        break

      case 'range':
        if (value !== undefined && value !== null) {
          const num = Number(value)
          if (isNaN(num) || num < rule.params.min || num > rule.params.max) {
            return {
              field: rule.field,
              message: rule.message,
              messageLocal: rule.messageLocal,
              type: 'error'
            }
          }
        }
        break

      case 'custom':
        if (rule.validator && !rule.validator(value, allSettings)) {
          return {
            field: rule.field,
            message: rule.message,
            messageLocal: rule.messageLocal,
            type: 'error'
          }
        }
        break
    }

    return null
  }

  private getNestedValue(obj: any, path: string): any {
    return path.split('.').reduce((current, key) => current?.[key], obj)
  }

  public validateSettings(category: string, settings: any): ValidationResult {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []
    const conflicts: SettingsConflict[] = []

    // Validate individual fields
    const categoryRules = this.rules.get(category) || []
    for (const rule of categoryRules) {
      const value = this.getNestedValue(settings, rule.field)
      const error = this.validateField(value, rule, settings)
      if (error) {
        errors.push(error)
      }
    }

    // Check for conflicts
    for (const conflictRule of this.conflictRules) {
      const foundConflicts = conflictRule(settings)
      conflicts.push(...foundConflicts)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      conflicts
    }
  }

  public validateAllSettings(allSettings: any): ValidationResult {
    const errors: ValidationError[] = []
    const warnings: ValidationWarning[] = []
    const conflicts: SettingsConflict[] = []

    // Validate each category
    for (const [category, categorySettings] of Object.entries(allSettings)) {
      const result = this.validateSettings(category, categorySettings)
      errors.push(...result.errors)
      warnings.push(...result.warnings)
      conflicts.push(...result.conflicts)
    }

    // Check cross-category conflicts
    for (const conflictRule of this.conflictRules) {
      const foundConflicts = conflictRule(allSettings)
      conflicts.push(...foundConflicts)
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings,
      conflicts
    }
  }

  public addCustomRule(category: string, rule: ValidationRule) {
    this.addRule(category, rule)
  }

  public addConflictRule(rule: (settings: any) => SettingsConflict[]) {
    this.conflictRules.push(rule)
  }
}

// Create singleton instance
let settingsValidator: SettingsValidator | null = null

export function getSettingsValidator(): SettingsValidator {
  if (!settingsValidator) {
    settingsValidator = new SettingsValidator()
  }
  return settingsValidator
}

// Utility functions
export function validateSettings(category: string, settings: any): ValidationResult {
  const validator = getSettingsValidator()
  return validator.validateSettings(category, settings)
}

export function validateAllSettings(allSettings: any): ValidationResult {
  const validator = getSettingsValidator()
  return validator.validateAllSettings(allSettings)
}

// Export types
export type { 
  ValidationRule, 
  ValidationResult, 
  ValidationError, 
  ValidationWarning, 
  SettingsConflict 
}
