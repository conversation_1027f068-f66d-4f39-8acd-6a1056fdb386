'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuTrigger 
} from '@/components/ui/dropdown-menu'
import { Globe, Check } from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import apiClient from '@/lib/api'

interface LanguageSwitcherProps {
  className?: string
  variant?: 'default' | 'ghost' | 'outline'
  size?: 'default' | 'sm' | 'lg'
}

export function LanguageSwitcher({ 
  className = '', 
  variant = 'ghost',
  size = 'default'
}: LanguageSwitcherProps) {
  const { user, updateUser } = useAuth()
  const [isChanging, setIsChanging] = useState(false)
  
  const currentLanguage = user?.preferences?.language || 'en'
  
  const languages = [
    { code: 'en', name: 'English', flag: '🇺🇸' },
    { code: 'mm', name: 'မြန်မာ', flag: '🇲🇲' }
  ]
  
  const handleLanguageChange = async (languageCode: string) => {
    if (languageCode === currentLanguage || isChanging) return
    
    try {
      setIsChanging(true)
      
      // Update user preferences
      if (user) {
        const updatedPreferences = {
          ...user.preferences,
          language: languageCode
        }
        
        // Update locally first for immediate UI response
        updateUser({
          ...user,
          preferences: updatedPreferences
        })
        
        // Update on server
        await apiClient.updateProfile({
          preferences: updatedPreferences
        })
        
        console.log(`🌐 Language changed to: ${languageCode}`)
        
        // Dispatch global language change event
        window.dispatchEvent(new CustomEvent('language-changed', {
          detail: { language: languageCode }
        }))
      }
    } catch (error) {
      console.error('Failed to update language:', error)
      // Revert on error
      if (user) {
        updateUser({
          ...user,
          preferences: {
            ...user.preferences,
            language: currentLanguage
          }
        })
      }
    } finally {
      setIsChanging(false)
    }
  }
  
  const getCurrentLanguage = () => {
    return languages.find(lang => lang.code === currentLanguage) || languages[0]
  }
  
  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant={variant} 
          size={size}
          className={`${className} ${isChanging ? 'opacity-50' : ''}`}
          disabled={isChanging}
        >
          <Globe className="h-4 w-4 mr-2" />
          <span className="hidden sm:inline">
            {getCurrentLanguage().name}
          </span>
          <span className="sm:hidden">
            {getCurrentLanguage().flag}
          </span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-48">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageChange(language.code)}
            className="flex items-center justify-between cursor-pointer"
          >
            <div className="flex items-center gap-2">
              <span className="text-lg">{language.flag}</span>
              <span>{language.name}</span>
            </div>
            {currentLanguage === language.code && (
              <Check className="h-4 w-4 text-green-600" />
            )}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  )
}
