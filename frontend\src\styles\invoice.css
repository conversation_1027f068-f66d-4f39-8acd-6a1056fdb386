/* Invoice Page Styles */

/* A4 Print Styles */
@media print {
  * {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
  }

  body {
    font-family: Arial, sans-serif;
    font-size: 12px;
    line-height: 1.4;
    background: white !important;
    color: black !important;
  }

  @page {
    size: A4;
    margin: 10mm;
  }

  .invoice-container {
    width: 100% !important;
    max-width: none !important;
    margin: 0 !important;
    padding: 0 !important;
    background: white !important;
    box-shadow: none !important;
    border-radius: 0 !important;
    min-height: auto !important;
  }

  .print\:hidden {
    display: none !important;
  }

  .print\:shadow-none {
    box-shadow: none !important;
  }

  .print\:rounded-none {
    border-radius: 0 !important;
  }

  /* Ensure proper spacing for print */
  .invoice-header {
    margin-bottom: 20px;
  }

  .invoice-items {
    margin: 20px 0;
  }

  .invoice-summary {
    margin-top: 20px;
  }

  .invoice-footer {
    margin-top: 30px;
    page-break-inside: avoid;
  }

  /* Table styles for print */
  table {
    width: 100%;
    border-collapse: collapse;
  }

  th, td {
    padding: 8px;
    border: 1px solid #ddd;
    text-align: left;
  }

  th {
    background-color: #f5f5f5 !important;
    font-weight: bold;
  }

  /* Signature section */
  .signature-section {
    margin-top: 40px;
    page-break-inside: avoid;
  }

  .signature-line {
    border-bottom: 1px solid #000;
    height: 40px;
    margin-bottom: 5px;
  }
}

/* Screen Styles */
@media screen {
  .invoice-container {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
    border-radius: 12px;
    overflow: hidden;
  }

  .template-card {
    transition: all 0.3s ease;
  }

  .template-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  }

  .template-gradient {
    background: linear-gradient(135deg, var(--gradient-from), var(--gradient-to));
  }

  .invoice-animation {
    animation: fadeInUp 0.6s ease-out;
  }

  @keyframes fadeInUp {
    from {
      opacity: 0;
      transform: translateY(30px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .template-selection {
    animation: slideInLeft 0.5s ease-out;
  }

  @keyframes slideInLeft {
    from {
      opacity: 0;
      transform: translateX(-30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .invoice-preview {
    animation: slideInRight 0.5s ease-out;
  }

  @keyframes slideInRight {
    from {
      opacity: 0;
      transform: translateX(30px);
    }
    to {
      opacity: 1;
      transform: translateX(0);
    }
  }
}

/* Template Color Variables */
.template-red {
  --accent-color: #dc2626;
  --accent-light: #fef2f2;
  --accent-border: #fecaca;
}

.template-blue {
  --accent-color: #2563eb;
  --accent-light: #eff6ff;
  --accent-border: #bfdbfe;
}

.template-purple {
  --accent-color: #9333ea;
  --accent-light: #faf5ff;
  --accent-border: #d8b4fe;
}

.template-green {
  --accent-color: #16a34a;
  --accent-light: #f0fdf4;
  --accent-border: #bbf7d0;
}

.template-gold {
  --accent-color: #ca8a04;
  --accent-light: #fefce8;
  --accent-border: #fde047;
}

.template-black {
  --accent-color: #374151;
  --accent-light: #f9fafb;
  --accent-border: #d1d5db;
}

/* Responsive Design */
@media (max-width: 768px) {
  .invoice-container {
    margin: 10px;
    padding: 20px;
  }

  .template-grid {
    grid-template-columns: 1fr;
    gap: 15px;
  }

  .invoice-header {
    flex-direction: column;
    text-align: center;
  }

  .invoice-summary {
    width: 100%;
  }

  .signature-grid {
    grid-template-columns: 1fr;
    gap: 30px;
  }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  .invoice-container {
    background: white !important;
    color: black !important;
  }
}

/* Loading Animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Button Hover Effects */
.action-button {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.action-button:hover::before {
  left: 100%;
}

/* Template Selection Indicator */
.template-selected {
  position: relative;
}

.template-selected::after {
  content: '✓';
  position: absolute;
  top: 10px;
  right: 10px;
  background: var(--accent-color);
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .invoice-container {
    border: 2px solid black;
  }

  .template-card {
    border: 2px solid black;
  }

  button {
    border: 2px solid black;
  }
}
