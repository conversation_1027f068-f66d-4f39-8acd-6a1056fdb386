// Hardware Integration Routes
const express = require('express');
const router = express.Router();
const { protect, authorize, auditLog } = require('../middleware/auth');
const printerService = require('../services/printerService');
const barcodeService = require('../services/barcodeService');

// @desc    Get available printers
// @route   GET /api/hardware/printers
// @access  Private
router.get('/printers', protect, async (req, res) => {
    try {
        const result = await printerService.getAvailablePrinters();
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Print receipt
// @route   POST /api/hardware/printers/print
// @access  Private
router.post('/printers/print', protect, auditLog('print_receipt'), async (req, res) => {
    try {
        const { receiptData, printerName } = req.body;
        
        if (!receiptData) {
            return res.status(400).json({
                success: false,
                error: 'Receipt data is required'
            });
        }

        const result = await printerService.printReceipt(receiptData, printerName);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get printer status
// @route   GET /api/hardware/printers/:name/status
// @access  Private
router.get('/printers/:name/status', protect, async (req, res) => {
    try {
        const { name } = req.params;
        const result = await printerService.getPrinterStatus(name);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Set default printer
// @route   PUT /api/hardware/printers/default
// @access  Private (Manager/Admin only)
router.put('/printers/default', protect, authorize('admin', 'manager'), async (req, res) => {
    try {
        const { printerName } = req.body;
        
        if (!printerName) {
            return res.status(400).json({
                success: false,
                error: 'Printer name is required'
            });
        }

        const result = await printerService.setDefaultPrinter(printerName);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get available barcode scanners
// @route   GET /api/hardware/scanners
// @access  Private
router.get('/scanners', protect, async (req, res) => {
    try {
        const result = await barcodeService.getAvailableScanners();
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Start barcode scanning
// @route   POST /api/hardware/scanners/start
// @access  Private
router.post('/scanners/start', protect, async (req, res) => {
    try {
        const { scannerName } = req.body;
        const result = await barcodeService.startScanning(scannerName);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Stop barcode scanning
// @route   POST /api/hardware/scanners/stop
// @access  Private
router.post('/scanners/stop', protect, async (req, res) => {
    try {
        const { scannerName } = req.body;
        const result = await barcodeService.stopScanning(scannerName);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Manual barcode scan
// @route   POST /api/hardware/scanners/manual
// @access  Private
router.post('/scanners/manual', protect, auditLog('manual_barcode_scan'), async (req, res) => {
    try {
        const { barcode } = req.body;
        
        if (!barcode) {
            return res.status(400).json({
                success: false,
                error: 'Barcode is required'
            });
        }

        const result = await barcodeService.manualScan(barcode);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get scanner status
// @route   GET /api/hardware/scanners/:name/status
// @access  Private
router.get('/scanners/:name/status', protect, async (req, res) => {
    try {
        const { name } = req.params;
        const result = await barcodeService.getScannerStatus(name);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Test scanner
// @route   POST /api/hardware/scanners/:name/test
// @access  Private (Manager/Admin only)
router.post('/scanners/:name/test', protect, authorize('admin', 'manager'), async (req, res) => {
    try {
        const { name } = req.params;
        const result = await barcodeService.testScanner(name);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Configure scanner settings
// @route   PUT /api/hardware/scanners/:name/config
// @access  Private (Manager/Admin only)
router.put('/scanners/:name/config', protect, authorize('admin', 'manager'), async (req, res) => {
    try {
        const { name } = req.params;
        const { settings } = req.body;
        
        if (!settings) {
            return res.status(400).json({
                success: false,
                error: 'Settings are required'
            });
        }

        const result = await barcodeService.configureScannerSettings(name, settings);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Initialize hardware services
// @route   POST /api/hardware/initialize
// @access  Private (Admin only)
router.post('/initialize', protect, authorize('admin'), async (req, res) => {
    try {
        const printerResult = await printerService.initialize();
        const scannerResult = await barcodeService.initialize();
        
        res.json({
            success: true,
            message: 'Hardware services initialized',
            printer: printerResult,
            scanner: scannerResult
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get hardware status overview
// @route   GET /api/hardware/status
// @access  Private
router.get('/status', protect, async (req, res) => {
    try {
        const printerStatus = await printerService.getAvailablePrinters();
        const scannerStatus = await barcodeService.getAvailableScanners();
        
        res.json({
            success: true,
            hardware: {
                printers: printerStatus,
                scanners: scannerStatus,
                lastChecked: new Date().toISOString()
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
