'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  X,
  Save,
  User,
  Mail,
  Phone,
  MapPin,
  Shield,
  DollarSign,
  Calendar,
  Users,
  AlertCircle
} from 'lucide-react'

interface UserFormData {
  name: string
  email: string
  phone: string
  role: 'admin' | 'manager' | 'cashier' | ''
  status: 'active' | 'inactive' | 'suspended' | ''
  department: string
  salary: string
  address: string
  emergencyContact: string
  employeeId: string
  dateOfBirth: string
  hireDate: string
  notes: string
  permissions: string[]
}

interface UserFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (userData: UserFormData) => void
  language: 'en' | 'mm'
  editUser?: any
  isEditing?: boolean
}

export function UserFormModal({
  isOpen,
  onClose,
  onSave,
  language,
  editUser,
  isEditing = false
}: UserFormModalProps) {
  const [formData, setFormData] = useState<UserFormData>({
    name: editUser?.name || '',
    email: editUser?.email || '',
    phone: editUser?.phone || '',
    role: editUser?.role || '',
    status: editUser?.status || 'active',
    department: editUser?.department || '',
    salary: editUser?.salary?.toString() || '',
    address: editUser?.address || '',
    emergencyContact: editUser?.emergencyContact || '',
    employeeId: editUser?.employeeId || '',
    dateOfBirth: editUser?.dateOfBirth ? editUser.dateOfBirth.split('T')[0] : '',
    hireDate: editUser?.hireDate ? editUser.hireDate.split('T')[0] : new Date().toISOString().split('T')[0],
    notes: editUser?.notes || '',
    permissions: editUser?.permissions || []
  })

  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [saving, setSaving] = useState(false)

  if (!isOpen) return null

  const updateFormData = (field: keyof UserFormData, value: string | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const togglePermission = (permission: string) => {
    const currentPermissions = formData.permissions
    const updatedPermissions = currentPermissions.includes(permission)
      ? currentPermissions.filter(p => p !== permission)
      : [...currentPermissions, permission]

    updateFormData('permissions', updatedPermissions)
  }

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!formData.name.trim()) {
      newErrors.name = language === 'mm' ? 'အမည် လိုအပ်သည်' : 'Name is required'
    }

    if (!formData.email.trim()) {
      newErrors.email = language === 'mm' ? 'အီးမေးလ် လိုအပ်သည်' : 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = language === 'mm' ? 'မှန်ကန်သော အီးမေးလ် ဖော်မတ် မဟုတ်ပါ' : 'Invalid email format'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = language === 'mm' ? 'ဖုန်းနံပါတ် လိုအပ်သည်' : 'Phone number is required'
    }

    if (!formData.role) {
      newErrors.role = language === 'mm' ? 'အခန်းကဏ္ဍ ရွေးချယ်ရန် လိုအပ်သည်' : 'Role is required'
    }

    if (!formData.department.trim()) {
      newErrors.department = language === 'mm' ? 'ဌာနခွဲ လိုအပ်သည်' : 'Department is required'
    }

    if (formData.salary && isNaN(Number(formData.salary))) {
      newErrors.salary = language === 'mm' ? 'လစာ ကိန်းဂဏန်း ဖြစ်ရမည်' : 'Salary must be a number'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!validateForm()) return

    try {
      setSaving(true)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call

      onSave(formData)
      onClose()
    } catch (error) {
      console.error('Error saving user:', error)
    } finally {
      setSaving(false)
    }
  }

  const availablePermissions = [
    { id: 'pos', label: language === 'mm' ? 'POS စနစ်' : 'POS Terminal' },
    { id: 'inventory', label: language === 'mm' ? 'စတော့ စီမံခန့်ခွဲမှု' : 'Inventory Management' },
    { id: 'sales', label: language === 'mm' ? 'ရောင်းအား စီမံခန့်ခွဲမှု' : 'Sales Management' },
    { id: 'reports', label: language === 'mm' ? 'အစီရင်ခံစာများ' : 'Reports' },
    { id: 'users', label: language === 'mm' ? 'အသုံးပြုသူ စီမံခန့်ခွဲမှု' : 'User Management' },
    { id: 'settings', label: language === 'mm' ? 'စနစ် ဆက်တင်များ' : 'System Settings' }
  ]

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                {isEditing
                  ? (language === 'mm' ? 'အသုံးပြုသူ ပြင်ဆင်ရန်' : 'Edit User')
                  : (language === 'mm' ? 'အသုံးပြုသူ အသစ် ထည့်ရန်' : 'Add New User')
                }
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'အသုံးပြုသူ အချက်အလက်များ နှင့် အခွင့်အရေးများ ဖြည့်စွက်ပါ'
                  : 'Fill in user information and permissions'
                }
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အခြေခံ အချက်အလက်များ' : 'Basic Information'}
              </h3>

              <div>
                <Label htmlFor="name">
                  {language === 'mm' ? 'အမည်' : 'Full Name'} *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  placeholder={language === 'mm' ? 'ဥပမာ: မမမြင့်' : 'e.g. Sarah Johnson'}
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.name}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="email">
                  {language === 'mm' ? 'အီးမေးလ်' : 'Email Address'} *
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData('email', e.target.value)}
                  placeholder={language === 'mm' ? 'အီးမေးလ် ရိုက်ထည့်ပါ' : 'Enter email address'}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.email}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="phone">
                  {language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone Number'} *
                </Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => updateFormData('phone', e.target.value)}
                  placeholder={language === 'mm' ? 'ဥပမာ: 09-987-654-321' : 'e.g. +95-9-987-654-321'}
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.phone}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="employeeId">
                  {language === 'mm' ? 'ဝန်ထမ်း နံပါတ်' : 'Employee ID'}
                </Label>
                <Input
                  id="employeeId"
                  value={formData.employeeId}
                  onChange={(e) => updateFormData('employeeId', e.target.value)}
                  placeholder={language === 'mm' ? 'ဝန်ထမ်း နံပါတ် ရိုက်ထည့်ပါ' : 'Enter employee ID'}
                />
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-600" />
                {language === 'mm' ? 'အခန်းကဏ္ဍ နှင့် အခြေအနေ' : 'Role & Status'}
              </h3>

              <div>
                <Label htmlFor="role">
                  {language === 'mm' ? 'အခန်းကဏ္ဍ' : 'Role'} *
                </Label>
                <Select value={formData.role} onValueChange={(value) => updateFormData('role', value)}>
                  <SelectTrigger className={errors.role ? 'border-red-500' : ''}>
                    <SelectValue placeholder={language === 'mm' ? 'အခန်းကဏ္ဍ ရွေးချယ်ပါ' : 'Select role'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="admin">{language === 'mm' ? 'စီမံခန့်ခွဲသူ' : 'Administrator'}</SelectItem>
                    <SelectItem value="manager">{language === 'mm' ? 'မန်နေဂျာ' : 'Manager'}</SelectItem>
                    <SelectItem value="cashier">{language === 'mm' ? 'ငွေကောက်သူ' : 'Cashier'}</SelectItem>
                  </SelectContent>
                </Select>
                {errors.role && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.role}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="status">
                  {language === 'mm' ? 'အခြေအနေ' : 'Status'}
                </Label>
                <Select value={formData.status} onValueChange={(value) => updateFormData('status', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="active">{language === 'mm' ? 'လက်ရှိ အသုံးပြုနေ' : 'Active'}</SelectItem>
                    <SelectItem value="inactive">{language === 'mm' ? 'အသုံးမပြုတော့' : 'Inactive'}</SelectItem>
                    <SelectItem value="suspended">{language === 'mm' ? 'ရပ်ဆိုင်းထား' : 'Suspended'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="department">
                  {language === 'mm' ? 'ဌာနခွဲ' : 'Department'} *
                </Label>
                <Input
                  id="department"
                  value={formData.department}
                  onChange={(e) => updateFormData('department', e.target.value)}
                  placeholder={language === 'mm' ? 'ဌာနခွဲ ရိုက်ထည့်ပါ' : 'Enter department'}
                  className={errors.department ? 'border-red-500' : ''}
                />
                {errors.department && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.department}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="salary">
                  {language === 'mm' ? 'လစာ (MMK)' : 'Salary (MMK)'}
                </Label>
                <Input
                  id="salary"
                  type="number"
                  value={formData.salary}
                  onChange={(e) => updateFormData('salary', e.target.value)}
                  placeholder={language === 'mm' ? 'လစာ ရိုက်ထည့်ပါ' : 'Enter salary amount'}
                  className={errors.salary ? 'border-red-500' : ''}
                />
                {errors.salary && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.salary}
                  </p>
                )}
              </div>
            </div>
          </div>

          {/* Additional Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <MapPin className="h-5 w-5 text-purple-600" />
              {language === 'mm' ? 'နောက်ထပ် အချက်အလက်များ' : 'Additional Information'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="dateOfBirth">
                  {language === 'mm' ? 'မွေးနေ့' : 'Date of Birth'}
                </Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => updateFormData('dateOfBirth', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="hireDate">
                  {language === 'mm' ? 'အလုပ်ဝင်သည့်ရက်' : 'Hire Date'}
                </Label>
                <Input
                  id="hireDate"
                  type="date"
                  value={formData.hireDate}
                  onChange={(e) => updateFormData('hireDate', e.target.value)}
                />
              </div>
            </div>

            <div>
              <Label htmlFor="address">
                {language === 'mm' ? 'လိပ်စာ' : 'Address'}
              </Label>
              <Textarea
                id="address"
                value={formData.address}
                onChange={(e) => updateFormData('address', e.target.value)}
                placeholder={language === 'mm' ? 'ဥပမာ: အမှတ် ၄၅၆၊ ဗိုလ်ချုပ်လမ်း၊ ဗဟန်းမြို့နယ်၊ ရန်ကုန်မြို့' : 'e.g. 456 Business Street, Central District, Yangon'}
                rows={2}
              />
            </div>

            <div>
              <Label htmlFor="emergencyContact">
                {language === 'mm' ? 'အရေးပေါ် ဆက်သွယ်ရန်' : 'Emergency Contact'}
              </Label>
              <Input
                id="emergencyContact"
                value={formData.emergencyContact}
                onChange={(e) => updateFormData('emergencyContact', e.target.value)}
                placeholder={language === 'mm' ? 'အရေးပေါ် ဆက်သွယ်ရန် ဖုန်းနံပါတ်' : 'Emergency contact number'}
              />
            </div>

            <div>
              <Label htmlFor="notes">
                {language === 'mm' ? 'မှတ်ချက်များ' : 'Notes'}
              </Label>
              <Textarea
                id="notes"
                value={formData.notes}
                onChange={(e) => updateFormData('notes', e.target.value)}
                placeholder={language === 'mm' ? 'မှတ်ချက်များ ရိုက်ထည့်ပါ' : 'Enter any additional notes'}
                rows={3}
              />
            </div>
          </div>

          {/* Permissions */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Shield className="h-5 w-5 text-orange-600" />
              {language === 'mm' ? 'အခွင့်အရေးများ' : 'Permissions'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {availablePermissions.map((permission) => (
                <div
                  key={permission.id}
                  className="flex items-center justify-between p-3 border border-gray-200 rounded-lg hover:bg-gray-50"
                >
                  <span className="font-medium">{permission.label}</span>
                  <input
                    type="checkbox"
                    checked={formData.permissions.includes(permission.id)}
                    onChange={() => togglePermission(permission.id)}
                    className="rounded"
                    aria-label={permission.label}
                  />
                </div>
              ))}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <Button variant="outline" onClick={onClose}>
              {language === 'mm' ? 'မလုပ်တော့' : 'Cancel'}
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {saving ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {language === 'mm' ? 'သိမ်းရန်' : 'Save User'}
                </div>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
