'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { useAuth } from '@/contexts/auth-context'
import { useSettings } from '@/contexts/settings-context'
import { Home, Save, RotateCcw, Languages, Sparkles, ArrowLeft } from 'lucide-react'
import { toast } from 'sonner'

interface HomepageContent {
  // Main Content
  title: string
  subtitle: string
  description: string
  getStarted: string
  tryDemo: string
  login: string
  
  // Features Section
  features: string
  feature1: string
  feature1Desc: string
  feature2: string
  feature2Desc: string
  feature3: string
  feature3Desc: string
  feature4: string
  feature4Desc: string
  feature5: string
  feature5Desc: string
  feature6: string
  feature6Desc: string
  
  // Footer
  footer: string
}

export default function HomepageContentPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const router = useRouter()
  
  const [englishContent, setEnglishContent] = useState<HomepageContent>({
    title: "BitsTech POS",
    subtitle: "Complete Point of Sale System for Myanmar Businesses",
    description: "Revolutionary POS solution with real-time inventory, multi-payment support (KBZ Pay, Wave Money, AYA Pay), and powerful analytics. Built specifically for Myanmar entrepreneurs.",
    getStarted: "Get Started",
    tryDemo: "Try Demo",
    login: "Login",
    features: "Complete POS Features for Your Business",
    feature1: "🏪 Advanced POS Terminal",
    feature1Desc: "Lightning-fast checkout with barcode scanning, receipt printing, and support for cash, KBZ Pay, Wave Money, AYA Pay payments",
    feature2: "📦 Smart Inventory Management",
    feature2Desc: "Real-time stock tracking, low stock alerts, automated reordering, multi-location support, and detailed inventory reports",
    feature3: "📊 Powerful Analytics & Reports",
    feature3Desc: "Comprehensive sales analytics, profit analysis, customer insights, trending products, and customizable business dashboards",
    feature4: "👥 Customer Management",
    feature4Desc: "Customer profiles, purchase history, loyalty points, targeted promotions, and customer behavior analytics",
    feature5: "🌐 Multi-Language & Currency",
    feature5Desc: "Complete Myanmar and English interface with MMK, USD, THB currency support and localized number formats",
    feature6: "📱 Mobile & Cross-Platform",
    feature6Desc: "Works seamlessly on smartphones, tablets, and computers with offline capabilities and cloud synchronization",
    footer: "Built with ❤️ for Myanmar businesses"
  })

  const [myanmarContent, setMyanmarContent] = useState<HomepageContent>({
    title: "BitsTech POS",
    subtitle: "မြန်မာနိုင်ငံရှိ လုပ်ငန်းများအတွက် ပြီးပြည့်စုံသော ရောင်းချရာနေရာ စနစ်",
    description: "အချိန်နှင့်တပြေးညီ ကုန်စာရင်း၊ ငွေပေးချေမှု နည်းလမ်းများစွာ (KBZ Pay, Wave Money, AYA Pay) နှင့် အစွမ်းထက် ခွဲခြမ်းစိတ်ဖြာမှုများပါရှိသော တော်လှန်ရေးဆန်သော POS ဖြေရှင်းချက်။ မြန်မာ လုပ်ငန်းရှင်များအတွက် အထူးတလည် တည်ဆောက်ထားပါသည်။",
    getStarted: "စတင်ရန်",
    tryDemo: "စမ်းကြည့်ရန်",
    login: "ဝင်ရောက်ရန်",
    features: "သင့်လုပ်ငန်းအတွက် ပြီးပြည့်စုံသော POS လုပ်ဆောင်ချက်များ",
    feature1: "🏪 အဆင့်မြင့် POS ဆိုင်ကောင်တာ",
    feature1Desc: "လျှပ်စီးမြန်နှုန်းရှိ ငွေတောင်းခံမှု၊ ဘားကုဒ် စကင်နာ၊ ဘောင်ချာ ပရင့်ထုတ်မှု နှင့် ငွေသား၊ KBZ Pay၊ Wave Money၊ AYA Pay ငွေပေးချေမှု ပံ့ပိုးမှု",
    feature2: "📦 စမတ် ကုန်စာရင်း စီမံခန့်ခွဲမှု",
    feature2Desc: "အချိန်နှင့်တပြေးညီ ကုန်စတော့ ခြေရာခံမှု၊ ကုန်နည်းသတိပေးချက်များ၊ အလိုအလျောက် ပြန်လည်မှာယူမှု၊ နေရာများစွာ ပံ့ပိုးမှု နှင့် အသေးစိတ် ကုန်စာရင်း အစီရင်ခံစာများ",
    feature3: "📊 အစွမ်းထက် ခွဲခြမ်းစိတ်ဖြာမှု နှင့် အစီရင်ခံစာများ",
    feature3Desc: "ပြီးပြည့်စုံသော ရောင်းအား ခွဲခြမ်းစိတ်ဖြာမှု၊ အမြတ်ခွဲခြမ်းစိတ်ဖြာမှု၊ ဖောက်သည် အချက်အလက်များ၊ ရေပန်းစားသော ကုန်ပစ္စည်းများ နှင့် စိတ်ကြိုက်ပြင်ဆင်နိုင်သော လုပ်ငန်း ဒက်ရှ်ဘုတ်များ",
    feature4: "👥 ဖောက်သည် စီမံခန့်ခွဲမှု",
    feature4Desc: "ဖောက်သည် ပရိုဖိုင်များ၊ ဝယ်ယူမှု သမိုင်း၊ သစ္စာရှိမှု အမှတ်များ၊ ပစ်မှတ်ထားသော ပရိုမိုးရှင်းများ နှင့် ဖောက်သည် အပြုအမူ ခွဲခြမ်းစိတ်ဖြာမှု",
    feature5: "🌐 ဘာသာစကားများစွာ နှင့် ငွေကြေး",
    feature5Desc: "မြန်မာနှင့် အင်္ဂလိပ်ဘာသာ အပြည့်အဝ အင်တာဖေ့စ်နှင့် MMK၊ USD၊ THB ငွေကြေး ပံ့ပိုးမှု နှင့် ဒေသဆိုင်ရာ နံပါတ်ပုံစံများ",
    feature6: "📱 မိုဘိုင်း နှင့် ပလပ်ဖောင်းများစွာ",
    feature6Desc: "စမတ်ဖုန်း၊ တက်ဘလက် နှင့် ကွန်ပျူတာများတွင် ပြေပြစ်စွာ အလုပ်လုပ်ပြီး အင်တာနက်မရှိချိန် အသုံးပြုနိုင်မှု နှင့် cloud ထပ်တူပြုမှု",
    footer: "မြန်မာနိုင်ငံရှိ လုပ်ငန်းများအတွက် ချစ်ခြင်းမေတ္တာဖြင့် ❤️ တည်ဆောက်ထားပါသည်"
  })

  const [isSaving, setIsSaving] = useState(false)
  const [activeTab, setActiveTab] = useState('english')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    // Load saved content from localStorage
    const savedEnglish = localStorage.getItem('homepage-content-en')
    const savedMyanmar = localStorage.getItem('homepage-content-mm')
    
    if (savedEnglish) {
      setEnglishContent(JSON.parse(savedEnglish))
    }
    if (savedMyanmar) {
      setMyanmarContent(JSON.parse(savedMyanmar))
    }
  }, [])

  const handleSave = async () => {
    setIsSaving(true)
    try {
      // Save to localStorage
      localStorage.setItem('homepage-content-en', JSON.stringify(englishContent))
      localStorage.setItem('homepage-content-mm', JSON.stringify(myanmarContent))
      
      // Here you would typically save to your backend API
      // await api.saveHomepageContent({ en: englishContent, mm: myanmarContent })
      
      toast.success(language === 'mm' ? 'အောင်မြင်စွာ သိမ်းဆည်းပြီးပါပြီ!' : 'Content saved successfully!')
    } catch (error) {
      toast.error(language === 'mm' ? 'သိမ်းဆည်းရာတွင် အမှားရှိပါသည်' : 'Error saving content')
    } finally {
      setIsSaving(false)
    }
  }

  const handleReset = () => {
    if (confirm(language === 'mm' ? 'မူလအခြေအနေသို့ ပြန်လည်သတ်မှတ်မှာ သေချာပါသလား?' : 'Are you sure you want to reset to default content?')) {
      localStorage.removeItem('homepage-content-en')
      localStorage.removeItem('homepage-content-mm')
      window.location.reload()
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            {/* Back Button */}
            <Button
              variant="outline"
              onClick={() => router.push('/settings')}
              className="flex items-center gap-2 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
            >
              <ArrowLeft className="h-4 w-4" />
              <span className="hidden sm:inline">
                {language === 'mm' ? 'Settings သို့ ပြန်သွားရန်' : 'Back to Settings'}
              </span>
            </Button>

            <div className="p-3 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-xl text-white">
              <Home className="h-6 w-6" />
            </div>
            <div>
              <h1 className="text-3xl font-bold">
                {language === 'mm' ? 'ပင်မစာမျက်နှာ အကြောင်းအရာ' : 'Homepage Content'}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {language === 'mm'
                  ? 'ပင်မစာမျက်နှာရှိ စာသားများ နှင့် အကြောင်းအရာများကို ပြင်ဆင်ပါ'
                  : 'Edit homepage text and content for both languages'
                }
              </p>
            </div>
          </div>
          
          <div className="flex gap-3">
            <Button variant="outline" onClick={handleReset}>
              <RotateCcw className="h-4 w-4 mr-2" />
              {language === 'mm' ? 'မူလအခြေအနေ' : 'Reset'}
            </Button>
            <Button onClick={handleSave} disabled={isSaving}>
              <Save className="h-4 w-4 mr-2" />
              {isSaving 
                ? (language === 'mm' ? 'သိမ်းဆည်းနေသည်...' : 'Saving...') 
                : (language === 'mm' ? 'သိမ်းဆည်းရန်' : 'Save Changes')
              }
            </Button>
          </div>
        </div>

        {/* Content Editor */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Languages className="h-5 w-5" />
              {language === 'mm' ? 'ဘာသာစကား အလိုက် အကြောင်းအရာ' : 'Content by Language'}
            </CardTitle>
            <CardDescription>
              {language === 'mm' 
                ? 'အင်္ဂလိပ်နှင့် မြန်မာဘာသာ နှစ်မျိုးလုံးအတွက် အကြောင်းအရာများကို ပြင်ဆင်ပါ'
                : 'Edit content for both English and Myanmar languages'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="english">English</TabsTrigger>
                <TabsTrigger value="myanmar">မြန်မာ</TabsTrigger>
              </TabsList>
              
              <TabsContent value="english" className="space-y-6 mt-6">
                {/* Main Content Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Main Content</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="en-title">Title</Label>
                        <Input
                          id="en-title"
                          value={englishContent.title}
                          onChange={(e) => setEnglishContent({...englishContent, title: e.target.value})}
                          placeholder="BitsTech POS"
                        />
                      </div>
                      <div>
                        <Label htmlFor="en-subtitle">Subtitle</Label>
                        <Input
                          id="en-subtitle"
                          value={englishContent.subtitle}
                          onChange={(e) => setEnglishContent({...englishContent, subtitle: e.target.value})}
                          placeholder="Complete Point of Sale System..."
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="en-description">Description</Label>
                      <Textarea
                        id="en-description"
                        value={englishContent.description}
                        onChange={(e) => setEnglishContent({...englishContent, description: e.target.value})}
                        placeholder="Revolutionary POS solution..."
                        rows={3}
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="en-getStarted">Get Started Button</Label>
                        <Input
                          id="en-getStarted"
                          value={englishContent.getStarted}
                          onChange={(e) => setEnglishContent({...englishContent, getStarted: e.target.value})}
                          placeholder="Get Started"
                        />
                      </div>
                      <div>
                        <Label htmlFor="en-tryDemo">Try Demo Button</Label>
                        <Input
                          id="en-tryDemo"
                          value={englishContent.tryDemo}
                          onChange={(e) => setEnglishContent({...englishContent, tryDemo: e.target.value})}
                          placeholder="Try Demo"
                        />
                      </div>
                      <div>
                        <Label htmlFor="en-login">Login Button</Label>
                        <Input
                          id="en-login"
                          value={englishContent.login}
                          onChange={(e) => setEnglishContent({...englishContent, login: e.target.value})}
                          placeholder="Login"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Features Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Features Section</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="en-features">Features Title</Label>
                      <Input
                        id="en-features"
                        value={englishContent.features}
                        onChange={(e) => setEnglishContent({...englishContent, features: e.target.value})}
                        placeholder="Complete POS Features for Your Business"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Feature 1 */}
                      <div className="space-y-2">
                        <Label htmlFor="en-feature1">Feature 1 Title</Label>
                        <Input
                          id="en-feature1"
                          value={englishContent.feature1}
                          onChange={(e) => setEnglishContent({...englishContent, feature1: e.target.value})}
                          placeholder="🏪 Advanced POS Terminal"
                        />
                        <Label htmlFor="en-feature1Desc">Feature 1 Description</Label>
                        <Textarea
                          id="en-feature1Desc"
                          value={englishContent.feature1Desc}
                          onChange={(e) => setEnglishContent({...englishContent, feature1Desc: e.target.value})}
                          placeholder="Lightning-fast checkout..."
                          rows={2}
                        />
                      </div>

                      {/* Feature 2 */}
                      <div className="space-y-2">
                        <Label htmlFor="en-feature2">Feature 2 Title</Label>
                        <Input
                          id="en-feature2"
                          value={englishContent.feature2}
                          onChange={(e) => setEnglishContent({...englishContent, feature2: e.target.value})}
                          placeholder="📦 Smart Inventory Management"
                        />
                        <Label htmlFor="en-feature2Desc">Feature 2 Description</Label>
                        <Textarea
                          id="en-feature2Desc"
                          value={englishContent.feature2Desc}
                          onChange={(e) => setEnglishContent({...englishContent, feature2Desc: e.target.value})}
                          placeholder="Real-time stock tracking..."
                          rows={2}
                        />
                      </div>

                      {/* Feature 3 */}
                      <div className="space-y-2">
                        <Label htmlFor="en-feature3">Feature 3 Title</Label>
                        <Input
                          id="en-feature3"
                          value={englishContent.feature3}
                          onChange={(e) => setEnglishContent({...englishContent, feature3: e.target.value})}
                          placeholder="📊 Powerful Analytics & Reports"
                        />
                        <Label htmlFor="en-feature3Desc">Feature 3 Description</Label>
                        <Textarea
                          id="en-feature3Desc"
                          value={englishContent.feature3Desc}
                          onChange={(e) => setEnglishContent({...englishContent, feature3Desc: e.target.value})}
                          placeholder="Comprehensive sales analytics..."
                          rows={2}
                        />
                      </div>

                      {/* Feature 4 */}
                      <div className="space-y-2">
                        <Label htmlFor="en-feature4">Feature 4 Title</Label>
                        <Input
                          id="en-feature4"
                          value={englishContent.feature4}
                          onChange={(e) => setEnglishContent({...englishContent, feature4: e.target.value})}
                          placeholder="👥 Customer Management"
                        />
                        <Label htmlFor="en-feature4Desc">Feature 4 Description</Label>
                        <Textarea
                          id="en-feature4Desc"
                          value={englishContent.feature4Desc}
                          onChange={(e) => setEnglishContent({...englishContent, feature4Desc: e.target.value})}
                          placeholder="Customer profiles, purchase history..."
                          rows={2}
                        />
                      </div>

                      {/* Feature 5 */}
                      <div className="space-y-2">
                        <Label htmlFor="en-feature5">Feature 5 Title</Label>
                        <Input
                          id="en-feature5"
                          value={englishContent.feature5}
                          onChange={(e) => setEnglishContent({...englishContent, feature5: e.target.value})}
                          placeholder="🌐 Multi-Language & Currency"
                        />
                        <Label htmlFor="en-feature5Desc">Feature 5 Description</Label>
                        <Textarea
                          id="en-feature5Desc"
                          value={englishContent.feature5Desc}
                          onChange={(e) => setEnglishContent({...englishContent, feature5Desc: e.target.value})}
                          placeholder="Complete Myanmar and English interface..."
                          rows={2}
                        />
                      </div>

                      {/* Feature 6 */}
                      <div className="space-y-2">
                        <Label htmlFor="en-feature6">Feature 6 Title</Label>
                        <Input
                          id="en-feature6"
                          value={englishContent.feature6}
                          onChange={(e) => setEnglishContent({...englishContent, feature6: e.target.value})}
                          placeholder="📱 Mobile & Cross-Platform"
                        />
                        <Label htmlFor="en-feature6Desc">Feature 6 Description</Label>
                        <Textarea
                          id="en-feature6Desc"
                          value={englishContent.feature6Desc}
                          onChange={(e) => setEnglishContent({...englishContent, feature6Desc: e.target.value})}
                          placeholder="Works seamlessly on smartphones..."
                          rows={2}
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Footer Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Footer</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div>
                      <Label htmlFor="en-footer">Footer Text</Label>
                      <Input
                        id="en-footer"
                        value={englishContent.footer}
                        onChange={(e) => setEnglishContent({...englishContent, footer: e.target.value})}
                        placeholder="Built with ❤️ for Myanmar businesses"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
              
              <TabsContent value="myanmar" className="space-y-6 mt-6">
                {/* Main Content Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">အဓိက အကြောင်းအရာ</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <Label htmlFor="mm-title">ခေါင်းစဉ်</Label>
                        <Input
                          id="mm-title"
                          value={myanmarContent.title}
                          onChange={(e) => setMyanmarContent({...myanmarContent, title: e.target.value})}
                          placeholder="BitsTech POS"
                          className="font-myanmar"
                        />
                      </div>
                      <div>
                        <Label htmlFor="mm-subtitle">ခေါင်းစဉ်ငယ်</Label>
                        <Input
                          id="mm-subtitle"
                          value={myanmarContent.subtitle}
                          onChange={(e) => setMyanmarContent({...myanmarContent, subtitle: e.target.value})}
                          placeholder="မြန်မာနိုင်ငံရှိ လုပ်ငန်းများအတွက်..."
                          className="font-myanmar"
                        />
                      </div>
                    </div>

                    <div>
                      <Label htmlFor="mm-description">ဖော်ပြချက်</Label>
                      <Textarea
                        id="mm-description"
                        value={myanmarContent.description}
                        onChange={(e) => setMyanmarContent({...myanmarContent, description: e.target.value})}
                        placeholder="အချိန်နှင့်တပြေးညီ ကုန်စာရင်း..."
                        rows={3}
                        className="font-myanmar"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                      <div>
                        <Label htmlFor="mm-getStarted">စတင်ရန် ခလုတ်</Label>
                        <Input
                          id="mm-getStarted"
                          value={myanmarContent.getStarted}
                          onChange={(e) => setMyanmarContent({...myanmarContent, getStarted: e.target.value})}
                          placeholder="စတင်ရန်"
                          className="font-myanmar"
                        />
                      </div>
                      <div>
                        <Label htmlFor="mm-tryDemo">စမ်းကြည့်ရန် ခလုတ်</Label>
                        <Input
                          id="mm-tryDemo"
                          value={myanmarContent.tryDemo}
                          onChange={(e) => setMyanmarContent({...myanmarContent, tryDemo: e.target.value})}
                          placeholder="စမ်းကြည့်ရန်"
                          className="font-myanmar"
                        />
                      </div>
                      <div>
                        <Label htmlFor="mm-login">ဝင်ရောက်ရန် ခလုတ်</Label>
                        <Input
                          id="mm-login"
                          value={myanmarContent.login}
                          onChange={(e) => setMyanmarContent({...myanmarContent, login: e.target.value})}
                          placeholder="ဝင်ရောက်ရန်"
                          className="font-myanmar"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Features Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">လုပ်ဆောင်ချက်များ အပိုင်း</CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div>
                      <Label htmlFor="mm-features">လုပ်ဆောင်ချက်များ ခေါင်းစဉ်</Label>
                      <Input
                        id="mm-features"
                        value={myanmarContent.features}
                        onChange={(e) => setMyanmarContent({...myanmarContent, features: e.target.value})}
                        placeholder="သင့်လုပ်ငန်းအတွက် ပြီးပြည့်စုံသော POS လုပ်ဆောင်ချက်များ"
                        className="font-myanmar"
                      />
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {/* Feature 1 */}
                      <div className="space-y-2">
                        <Label htmlFor="mm-feature1">လုပ်ဆောင်ချက် ၁ ခေါင်းစဉ်</Label>
                        <Input
                          id="mm-feature1"
                          value={myanmarContent.feature1}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature1: e.target.value})}
                          placeholder="🏪 အဆင့်မြင့် POS ဆိုင်ကောင်တာ"
                          className="font-myanmar"
                        />
                        <Label htmlFor="mm-feature1Desc">လုပ်ဆောင်ချက် ၁ ဖော်ပြချက်</Label>
                        <Textarea
                          id="mm-feature1Desc"
                          value={myanmarContent.feature1Desc}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature1Desc: e.target.value})}
                          placeholder="လျှပ်စီးမြန်နှုန်းရှိ ငွေတောင်းခံမှု..."
                          rows={2}
                          className="font-myanmar"
                        />
                      </div>

                      {/* Feature 2 */}
                      <div className="space-y-2">
                        <Label htmlFor="mm-feature2">လုပ်ဆောင်ချက် ၂ ခေါင်းစဉ်</Label>
                        <Input
                          id="mm-feature2"
                          value={myanmarContent.feature2}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature2: e.target.value})}
                          placeholder="📦 စမတ် ကုန်စာရင်း စီမံခန့်ခွဲမှု"
                          className="font-myanmar"
                        />
                        <Label htmlFor="mm-feature2Desc">လုပ်ဆောင်ချက် ၂ ဖော်ပြချက်</Label>
                        <Textarea
                          id="mm-feature2Desc"
                          value={myanmarContent.feature2Desc}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature2Desc: e.target.value})}
                          placeholder="အချိန်နှင့်တပြေးညီ ကုန်စတော့ ခြေရာခံမှု..."
                          rows={2}
                          className="font-myanmar"
                        />
                      </div>

                      {/* Feature 3 */}
                      <div className="space-y-2">
                        <Label htmlFor="mm-feature3">လုပ်ဆောင်ချက် ၃ ခေါင်းစဉ်</Label>
                        <Input
                          id="mm-feature3"
                          value={myanmarContent.feature3}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature3: e.target.value})}
                          placeholder="📊 အစွမ်းထက် ခွဲခြမ်းစိတ်ဖြာမှု နှင့် အစီရင်ခံစာများ"
                          className="font-myanmar"
                        />
                        <Label htmlFor="mm-feature3Desc">လုပ်ဆောင်ချက် ၃ ဖော်ပြချက်</Label>
                        <Textarea
                          id="mm-feature3Desc"
                          value={myanmarContent.feature3Desc}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature3Desc: e.target.value})}
                          placeholder="ပြီးပြည့်စုံသော ရောင်းအား ခွဲခြမ်းစိတ်ဖြာမှု..."
                          rows={2}
                          className="font-myanmar"
                        />
                      </div>

                      {/* Feature 4 */}
                      <div className="space-y-2">
                        <Label htmlFor="mm-feature4">လုပ်ဆောင်ချက် ၄ ခေါင်းစဉ်</Label>
                        <Input
                          id="mm-feature4"
                          value={myanmarContent.feature4}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature4: e.target.value})}
                          placeholder="👥 ဖောက်သည် စီမံခန့်ခွဲမှု"
                          className="font-myanmar"
                        />
                        <Label htmlFor="mm-feature4Desc">လုပ်ဆောင်ချက် ၄ ဖော်ပြချက်</Label>
                        <Textarea
                          id="mm-feature4Desc"
                          value={myanmarContent.feature4Desc}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature4Desc: e.target.value})}
                          placeholder="ဖောက်သည် ပရိုဖိုင်များ၊ ဝယ်ယူမှု သမိုင်း..."
                          rows={2}
                          className="font-myanmar"
                        />
                      </div>

                      {/* Feature 5 */}
                      <div className="space-y-2">
                        <Label htmlFor="mm-feature5">လုပ်ဆောင်ချက် ၅ ခေါင်းစဉ်</Label>
                        <Input
                          id="mm-feature5"
                          value={myanmarContent.feature5}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature5: e.target.value})}
                          placeholder="🌐 ဘာသာစကားများစွာ နှင့် ငွေကြေး"
                          className="font-myanmar"
                        />
                        <Label htmlFor="mm-feature5Desc">လုပ်ဆောင်ချက် ၅ ဖော်ပြချက်</Label>
                        <Textarea
                          id="mm-feature5Desc"
                          value={myanmarContent.feature5Desc}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature5Desc: e.target.value})}
                          placeholder="မြန်မာနှင့် အင်္ဂလိပ်ဘာသာ အပြည့်အဝ အင်တာဖေ့စ်..."
                          rows={2}
                          className="font-myanmar"
                        />
                      </div>

                      {/* Feature 6 */}
                      <div className="space-y-2">
                        <Label htmlFor="mm-feature6">လုပ်ဆောင်ချက် ၆ ခေါင်းစဉ်</Label>
                        <Input
                          id="mm-feature6"
                          value={myanmarContent.feature6}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature6: e.target.value})}
                          placeholder="📱 မိုဘိုင်း နှင့် ပလပ်ဖောင်းများစွာ"
                          className="font-myanmar"
                        />
                        <Label htmlFor="mm-feature6Desc">လုပ်ဆောင်ချက် ၆ ဖော်ပြချက်</Label>
                        <Textarea
                          id="mm-feature6Desc"
                          value={myanmarContent.feature6Desc}
                          onChange={(e) => setMyanmarContent({...myanmarContent, feature6Desc: e.target.value})}
                          placeholder="စမတ်ဖုန်း၊ တက်ဘလက် နှင့် ကွန်ပျူတာများတွင်..."
                          rows={2}
                          className="font-myanmar"
                        />
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Footer Section */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Footer</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div>
                      <Label htmlFor="mm-footer">Footer စာသား</Label>
                      <Input
                        id="mm-footer"
                        value={myanmarContent.footer}
                        onChange={(e) => setMyanmarContent({...myanmarContent, footer: e.target.value})}
                        placeholder="မြန်မာနိုင်ငံရှိ လုပ်ငန်းများအတွက် ချစ်ခြင်းမေတ္တာဖြင့် ❤️ တည်ဆောက်ထားပါသည်"
                        className="font-myanmar"
                      />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
