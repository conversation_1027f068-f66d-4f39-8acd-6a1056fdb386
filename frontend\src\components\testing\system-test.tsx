'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useSettings } from '@/contexts/settings-context'
import { useTheme } from '@/contexts/theme-context'
import { useAuth } from '@/contexts/auth-context'
import apiClient from '@/lib/api'
import {
  CheckCircle,
  XCircle,
  Clock,
  RefreshCw,
  Globe,
  Palette,
  DollarSign,
  Server,
  Database,
  Wifi,
  Settings,
  User,
  ShoppingCart
} from 'lucide-react'

interface TestResult {
  name: string
  status: 'pending' | 'success' | 'error'
  message: string
  duration?: number
}

interface SystemTestProps {
  language: 'en' | 'mm'
}

export function SystemTest({ language }: SystemTestProps) {
  const [tests, setTests] = useState<TestResult[]>([])
  const [isRunning, setIsRunning] = useState(false)
  const { setLanguage, setCurrency, formatCurrency } = useSettings()
  const { theme, setTheme, colorScheme, setColorScheme } = useTheme()
  const { user } = useAuth()

  const testSuites = [
    {
      name: language === 'mm' ? 'ဘာသာစကား ပြောင်းလဲမှု' : 'Language Switching',
      icon: Globe,
      test: async () => {
        const originalLang = language
        setLanguage(originalLang === 'en' ? 'mm' : 'en')
        await new Promise(resolve => setTimeout(resolve, 500))
        setLanguage(originalLang)
        return 'Language switching works correctly'
      }
    },
    {
      name: language === 'mm' ? 'အပြင်အဆင် ပြောင်းလဲမှု' : 'Theme Switching',
      icon: Palette,
      test: async () => {
        const originalTheme = theme
        setTheme(originalTheme === 'light' ? 'dark' : 'light')
        await new Promise(resolve => setTimeout(resolve, 500))
        setTheme(originalTheme)
        return 'Theme switching works correctly'
      }
    },
    {
      name: language === 'mm' ? 'အရောင် အစီအစဉ်' : 'Color Scheme',
      icon: Palette,
      test: async () => {
        const originalScheme = colorScheme
        const schemes = ['blue', 'green', 'purple', 'orange', 'pink', 'indigo']
        const newScheme = schemes.find(s => s !== originalScheme) || 'blue'
        setColorScheme(newScheme as any)
        await new Promise(resolve => setTimeout(resolve, 500))
        setColorScheme(originalScheme)
        return 'Color scheme switching works correctly'
      }
    },
    {
      name: language === 'mm' ? 'ငွေကြေး ပြောင်းလဲမှု' : 'Currency Switching',
      icon: DollarSign,
      test: async () => {
        setCurrency('USD')
        const usdFormat = formatCurrency(100000)
        setCurrency('MMK')
        const mmkFormat = formatCurrency(100000)
        return `Currency formatting: ${mmkFormat} / ${usdFormat}`
      }
    },
    {
      name: language === 'mm' ? 'Backend ချိတ်ဆက်မှု' : 'Backend Connection',
      icon: Server,
      test: async () => {
        try {
          const health = await apiClient.healthCheck()
          if (health.status === 'ok' || health.status === 'OK') {
            return `Backend status: ${health.status} - Connected`
          } else if (health.status === 'error' || health.offline) {
            return `Backend status: Offline - Using mock data`
          } else {
            return `Backend status: ${health.status || 'Unknown'}`
          }
        } catch (error) {
          return `Backend status: Offline - Using mock data`
        }
      }
    },
    {
      name: language === 'mm' ? 'အသုံးပြုသူ အချက်အလက်' : 'User Authentication',
      icon: User,
      test: async () => {
        if (user) {
          return `Authenticated as: ${user.firstName} ${user.lastName} (${user.role})`
        }
        throw new Error('User not authenticated')
      }
    },
    {
      name: language === 'mm' ? 'ကုန်ပစ္စည်း API' : 'Products API',
      icon: ShoppingCart,
      test: async () => {
        const products = await apiClient.getProducts({ limit: 1 })
        return `Products API: ${products.success ? 'Working' : 'Failed'}`
      }
    },
    {
      name: language === 'mm' ? 'အမျိုးအစား API' : 'Categories API',
      icon: Database,
      test: async () => {
        const categories = await apiClient.getCategories({ limit: 1 })
        return `Categories API: ${categories.success ? 'Working' : 'Failed'}`
      }
    },
    {
      name: language === 'mm' ? 'ရောင်းအား API' : 'Sales API',
      icon: ShoppingCart,
      test: async () => {
        const sales = await apiClient.getSales({ limit: 1 })
        return `Sales API: ${sales.success ? 'Working' : 'Failed'}`
      }
    },
    {
      name: language === 'mm' ? 'ဆက်တင်များ API' : 'Settings API',
      icon: Settings,
      test: async () => {
        const settings = await apiClient.getSettings()
        return `Settings API: ${settings.success ? 'Working' : 'Failed'}`
      }
    }
  ]

  const runTests = async () => {
    setIsRunning(true)
    setTests([])

    for (const suite of testSuites) {
      const startTime = Date.now()
      
      // Add pending test
      setTests(prev => [...prev, {
        name: suite.name,
        status: 'pending',
        message: language === 'mm' ? 'စမ်းသပ်နေသည်...' : 'Testing...'
      }])

      try {
        const result = await suite.test()
        const duration = Date.now() - startTime

        // Update with success
        setTests(prev => prev.map(test => 
          test.name === suite.name 
            ? { ...test, status: 'success', message: result, duration }
            : test
        ))
      } catch (error: any) {
        const duration = Date.now() - startTime

        // Update with error
        setTests(prev => prev.map(test => 
          test.name === suite.name 
            ? { ...test, status: 'error', message: error.message, duration }
            : test
        ))
      }

      // Small delay between tests
      await new Promise(resolve => setTimeout(resolve, 200))
    }

    setIsRunning(false)
  }

  const getStatusIcon = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-600" />
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />
      case 'pending':
        return <Clock className="h-5 w-5 text-yellow-600 animate-pulse" />
    }
  }

  const getStatusColor = (status: TestResult['status']) => {
    switch (status) {
      case 'success':
        return 'bg-green-100 text-green-800 border-green-200'
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200'
      case 'pending':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200'
    }
  }

  const successCount = tests.filter(t => t.status === 'success').length
  const errorCount = tests.filter(t => t.status === 'error').length
  const totalTests = testSuites.length

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Wifi className="h-6 w-6 text-blue-600" />
          {language === 'mm' ? 'စနစ် စမ်းသပ်မှု' : 'System Testing'}
        </CardTitle>
        <CardDescription>
          {language === 'mm' 
            ? 'BitsTech POS စနစ်၏ လုပ်ဆောင်မှုများကို စမ်းသပ်ပါ'
            : 'Test all BitsTech POS system functionalities'
          }
        </CardDescription>
      </CardHeader>

      <CardContent className="space-y-6">
        {/* Test Summary */}
        {tests.length > 0 && (
          <div className="grid grid-cols-3 gap-4">
            <div className="text-center p-4 bg-green-50 rounded-lg">
              <div className="text-2xl font-bold text-green-600">{successCount}</div>
              <div className="text-sm text-green-700">
                {language === 'mm' ? 'အောင်မြင်' : 'Passed'}
              </div>
            </div>
            <div className="text-center p-4 bg-red-50 rounded-lg">
              <div className="text-2xl font-bold text-red-600">{errorCount}</div>
              <div className="text-sm text-red-700">
                {language === 'mm' ? 'မအောင်မြင်' : 'Failed'}
              </div>
            </div>
            <div className="text-center p-4 bg-blue-50 rounded-lg">
              <div className="text-2xl font-bold text-blue-600">{totalTests}</div>
              <div className="text-sm text-blue-700">
                {language === 'mm' ? 'စုစုပေါင်း' : 'Total'}
              </div>
            </div>
          </div>
        )}

        {/* Run Tests Button */}
        <div className="flex justify-center">
          <Button 
            onClick={runTests} 
            disabled={isRunning}
            size="lg"
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            {isRunning ? (
              <>
                <RefreshCw className="h-5 w-5 mr-2 animate-spin" />
                {language === 'mm' ? 'စမ်းသပ်နေသည်...' : 'Running Tests...'}
              </>
            ) : (
              <>
                <RefreshCw className="h-5 w-5 mr-2" />
                {language === 'mm' ? 'စမ်းသပ်မှု စတင်ရန်' : 'Run All Tests'}
              </>
            )}
          </Button>
        </div>

        {/* Test Results */}
        {tests.length > 0 && (
          <div className="space-y-3">
            <h3 className="text-lg font-semibold">
              {language === 'mm' ? 'စမ်းသပ်မှု ရလဒ်များ' : 'Test Results'}
            </h3>
            
            <div className="space-y-2">
              {tests.map((test, index) => {
                const suite = testSuites[index]
                const Icon = suite.icon
                
                return (
                  <div key={test.name} className="flex items-center gap-3 p-3 border rounded-lg">
                    <Icon className="h-5 w-5 text-gray-600" />
                    <div className="flex-1">
                      <div className="font-medium">{test.name}</div>
                      <div className="text-sm text-gray-600">{test.message}</div>
                    </div>
                    <div className="flex items-center gap-2">
                      {test.duration && (
                        <span className="text-xs text-gray-500">
                          {test.duration}ms
                        </span>
                      )}
                      <Badge className={getStatusColor(test.status)}>
                        {getStatusIcon(test.status)}
                      </Badge>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
