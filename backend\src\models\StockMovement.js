const mongoose = require('mongoose');

const stockMovementSchema = new mongoose.Schema({
    product: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product',
        required: [true, 'Product is required']
    },
    type: {
        type: String,
        enum: ['in', 'out', 'adjustment', 'transfer', 'return', 'damage', 'expired'],
        required: [true, 'Movement type is required']
    },
    quantity: {
        type: Number,
        required: [true, 'Quantity is required'],
        min: [0, 'Quantity cannot be negative']
    },
    previousQuantity: {
        type: Number,
        required: [true, 'Previous quantity is required'],
        min: [0, 'Previous quantity cannot be negative']
    },
    newQuantity: {
        type: Number,
        required: [true, 'New quantity is required'],
        min: [0, 'New quantity cannot be negative']
    },
    unitCost: {
        type: Number,
        default: 0,
        min: [0, 'Unit cost cannot be negative']
    },
    totalCost: {
        type: Number,
        default: 0,
        min: [0, 'Total cost cannot be negative']
    },
    reason: {
        type: String,
        required: [true, 'Reason is required'],
        trim: true,
        maxlength: [500, 'Reason cannot exceed 500 characters']
    },
    reference: {
        type: String,
        trim: true,
        maxlength: [100, 'Reference cannot exceed 100 characters']
    },
    referenceType: {
        type: String,
        enum: ['sale', 'purchase', 'adjustment', 'transfer', 'return', 'manual'],
        default: 'manual'
    },
    referenceId: {
        type: mongoose.Schema.Types.ObjectId,
        refPath: 'referenceModel'
    },
    referenceModel: {
        type: String,
        enum: ['Sale', 'PurchaseOrder', 'StockAdjustment', 'Transfer']
    },
    location: {
        warehouse: {
            type: String,
            default: 'main'
        },
        section: {
            type: String,
            default: 'general'
        },
        bin: String
    },
    supplier: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Supplier'
    },
    customer: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Customer'
    },
    batchNumber: {
        type: String,
        trim: true
    },
    expiryDate: {
        type: Date
    },
    serialNumbers: [{
        type: String,
        trim: true
    }],
    notes: {
        type: String,
        maxlength: [1000, 'Notes cannot exceed 1000 characters']
    },
    isReversed: {
        type: Boolean,
        default: false
    },
    reversedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    reversedAt: {
        type: Date
    },
    reversalReason: {
        type: String,
        maxlength: [500, 'Reversal reason cannot exceed 500 characters']
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Created by user is required']
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes
stockMovementSchema.index({ product: 1, createdAt: -1 });
stockMovementSchema.index({ type: 1, createdAt: -1 });
stockMovementSchema.index({ referenceType: 1, referenceId: 1 });
stockMovementSchema.index({ createdBy: 1, createdAt: -1 });
stockMovementSchema.index({ isReversed: 1 });

// Virtual for movement direction
stockMovementSchema.virtual('direction').get(function() {
    return ['in', 'return'].includes(this.type) ? 'in' : 'out';
});

// Virtual for value change
stockMovementSchema.virtual('valueChange').get(function() {
    const direction = this.direction === 'in' ? 1 : -1;
    return this.quantity * this.unitCost * direction;
});

// Static methods
stockMovementSchema.statics.getProductHistory = function(productId, options = {}) {
    const {
        startDate,
        endDate,
        type,
        limit = 50,
        page = 1
    } = options;

    const query = { product: productId, isReversed: false };

    if (startDate || endDate) {
        query.createdAt = {};
        if (startDate) query.createdAt.$gte = new Date(startDate);
        if (endDate) query.createdAt.$lte = new Date(endDate);
    }

    if (type) {
        query.type = type;
    }

    return this.find(query)
        .populate('product', 'name sku')
        .populate('createdBy', 'firstName lastName')
        .populate('supplier', 'name code')
        .populate('customer', 'name phone')
        .sort({ createdAt: -1 })
        .limit(limit)
        .skip((page - 1) * limit);
};

stockMovementSchema.statics.getMovementSummary = function(options = {}) {
    const {
        startDate,
        endDate,
        productId,
        type
    } = options;

    const matchStage = { isReversed: false };

    if (startDate || endDate) {
        matchStage.createdAt = {};
        if (startDate) matchStage.createdAt.$gte = new Date(startDate);
        if (endDate) matchStage.createdAt.$lte = new Date(endDate);
    }

    if (productId) {
        matchStage.product = mongoose.Types.ObjectId(productId);
    }

    if (type) {
        matchStage.type = type;
    }

    return this.aggregate([
        { $match: matchStage },
        {
            $group: {
                _id: '$type',
                totalQuantity: { $sum: '$quantity' },
                totalValue: { $sum: '$totalCost' },
                count: { $sum: 1 },
                avgQuantity: { $avg: '$quantity' },
                avgValue: { $avg: '$totalCost' }
            }
        },
        { $sort: { totalValue: -1 } }
    ]);
};

// Instance methods
stockMovementSchema.methods.reverse = function(userId, reason) {
    if (this.isReversed) {
        throw new Error('Movement is already reversed');
    }

    this.isReversed = true;
    this.reversedBy = userId;
    this.reversedAt = new Date();
    this.reversalReason = reason;
    this.updatedBy = userId;

    return this.save();
};

// Pre-save middleware
stockMovementSchema.pre('save', function(next) {
    if (this.isNew) {
        // Calculate total cost if not provided
        if (!this.totalCost && this.unitCost) {
            this.totalCost = this.quantity * this.unitCost;
        }

        // Generate reference if not provided
        if (!this.reference) {
            const timestamp = Date.now().toString().slice(-6);
            this.reference = `MOV-${this.type.toUpperCase()}-${timestamp}`;
        }
    }
    next();
});

// Post-save middleware to update product stock
stockMovementSchema.post('save', async function(doc) {
    if (doc.isNew && !doc.isReversed) {
        try {
            const Product = mongoose.model('Product');
            await Product.findByIdAndUpdate(
                doc.product,
                { 
                    'inventory.quantity': doc.newQuantity,
                    'inventory.lastMovement': doc._id,
                    updatedAt: new Date()
                }
            );
        } catch (error) {
            console.error('Error updating product stock:', error);
        }
    }
});

module.exports = mongoose.model('StockMovement', stockMovementSchema);
