'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import {
  ArrowLeft,
  Edit,
  Trash2,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Shield,
  ShieldCheck,
  UserCheck,
  Activity,
  Clock,
  DollarSign,
  Users,
  Settings,
  Key,
  AlertTriangle
} from 'lucide-react'

interface User {
  _id: string
  name: string
  email: string
  phone: string
  role: 'admin' | 'manager' | 'cashier'
  status: 'active' | 'inactive' | 'suspended'
  avatar?: string
  createdAt: string
  lastLogin?: string
  permissions: string[]
  department?: string
  salary?: number
  address?: string
  emergencyContact?: string
  employeeId?: string
  dateOfBirth?: string
  hireDate?: string
  notes?: string
}

interface ActivityLog {
  _id: string
  action: string
  timestamp: string
  details: string
  ipAddress?: string
}

export default function UserDetailPage() {
  const { user: currentUser, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const userId = params.id as string
  
  const [user, setUser] = useState<User | null>(null)
  const [activityLogs, setActivityLogs] = useState<ActivityLog[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated && userId) {
      fetchUserDetails()
      fetchActivityLogs()
    }
  }, [isAuthenticated, userId])

  const fetchUserDetails = async () => {
    try {
      setLoading(true)
      
      // Mock user data
      const mockUser: User = {
        _id: userId,
        name: 'Store Manager',
        email: '<EMAIL>',
        phone: '+95-9-234567890',
        role: 'manager',
        status: 'active',
        createdAt: '2024-01-02T00:00:00Z',
        lastLogin: '2024-01-14T09:15:00Z',
        permissions: ['inventory', 'sales', 'reports'],
        department: 'Operations',
        salary: 1200000,
        address: 'No. 123, Main Street, Mandalay, Myanmar',
        emergencyContact: '+95-9-876543210',
        employeeId: 'EMP-002',
        dateOfBirth: '1990-05-15T00:00:00Z',
        hireDate: '2024-01-02T00:00:00Z',
        notes: 'Experienced manager with 5+ years in retail operations. Excellent leadership skills and customer service orientation.'
      }

      setUser(mockUser)
    } catch (error) {
      console.error('Error fetching user details:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchActivityLogs = async () => {
    try {
      // Mock activity logs
      const mockLogs: ActivityLog[] = [
        {
          _id: '1',
          action: 'Login',
          timestamp: '2024-01-14T09:15:00Z',
          details: 'User logged in successfully',
          ipAddress: '*************'
        },
        {
          _id: '2',
          action: 'Inventory Update',
          timestamp: '2024-01-14T09:30:00Z',
          details: 'Updated stock levels for Samsung Monitor',
          ipAddress: '*************'
        },
        {
          _id: '3',
          action: 'Sales Report',
          timestamp: '2024-01-14T10:45:00Z',
          details: 'Generated daily sales report',
          ipAddress: '*************'
        },
        {
          _id: '4',
          action: 'User Management',
          timestamp: '2024-01-13T16:20:00Z',
          details: 'Updated cashier permissions',
          ipAddress: '*************'
        },
        {
          _id: '5',
          action: 'Logout',
          timestamp: '2024-01-13T18:00:00Z',
          details: 'User logged out',
          ipAddress: '*************'
        }
      ]

      setActivityLogs(mockLogs)
    } catch (error) {
      console.error('Error fetching activity logs:', error)
    }
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <ShieldCheck className="h-5 w-5 text-red-600" />
      case 'manager': return <Shield className="h-5 w-5 text-blue-600" />
      case 'cashier': return <UserCheck className="h-5 w-5 text-green-600" />
      default: return <Users className="h-5 w-5 text-gray-600" />
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge className="bg-red-100 text-red-800">Administrator</Badge>
      case 'manager':
        return <Badge className="bg-blue-100 text-blue-800">Manager</Badge>
      case 'cashier':
        return <Badge className="bg-green-100 text-green-800">Cashier</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800">Inactive</Badge>
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800">Suspended</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} MMK`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getPermissionBadges = (permissions: string[]) => {
    const permissionLabels: { [key: string]: string } = {
      'all': 'All Access',
      'inventory': 'Inventory',
      'sales': 'Sales',
      'reports': 'Reports',
      'pos': 'POS Terminal',
      'users': 'User Management'
    }

    return permissions.map(permission => (
      <Badge key={permission} variant="outline" className="text-xs">
        {permissionLabels[permission] || permission}
      </Badge>
    ))
  }

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated || !user) {
    return null
  }

  const language = currentUser?.preferences?.language as 'en' | 'mm' || 'en'

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/users')}
            className="hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Users'}
          </Button>
        </div>

        {/* User Profile Header */}
        <Card>
          <CardContent className="p-8">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-6">
                <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-2xl">
                  {user.name.charAt(0).toUpperCase()}
                </div>
                
                <div>
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-3xl font-bold">{user.name}</h1>
                    {getRoleIcon(user.role)}
                    {getRoleBadge(user.role)}
                    {getStatusBadge(user.status)}
                  </div>
                  
                  <div className="flex items-center gap-6 text-gray-600 mb-3">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      <span>{user.email}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      <span>{user.phone}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>{user.employeeId}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <span className="text-sm font-medium text-gray-700">
                      {language === 'mm' ? 'အခွင့်အရေးများ' : 'Permissions'}:
                    </span>
                    <div className="flex gap-1">
                      {getPermissionBadges(user.permissions)}
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  onClick={() => router.push(`/users/${user._id}/edit`)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ပြင်ဆင်ရန်' : 'Edit User'}
                </Button>
                
                <Button
                  variant="outline"
                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                >
                  <Trash2 className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ဖျက်ရန်' : 'Delete User'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Personal Information */}
          <div className="lg:col-span-2 space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5 text-blue-600" />
                  {language === 'mm' ? 'ကိုယ်ရေးကိုယ်တာ အချက်အလက်များ' : 'Personal Information'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'ဌာနခွဲ' : 'Department'}
                    </label>
                    <p className="text-lg">{user.department}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'လစာ' : 'Salary'}
                    </label>
                    <p className="text-lg font-semibold text-green-600">
                      {formatCurrency(user.salary || 0)}
                    </p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'မွေးနေ့' : 'Date of Birth'}
                    </label>
                    <p className="text-lg">
                      {user.dateOfBirth ? formatDate(user.dateOfBirth) : 'Not provided'}
                    </p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'အလုပ်ဝင်သည့်ရက်' : 'Hire Date'}
                    </label>
                    <p className="text-lg">
                      {user.hireDate ? formatDate(user.hireDate) : formatDate(user.createdAt)}
                    </p>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {language === 'mm' ? 'လိပ်စာ' : 'Address'}
                  </label>
                  <p className="text-lg">{user.address || 'Not provided'}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {language === 'mm' ? 'အရေးပေါ် ဆက်သွယ်ရန်' : 'Emergency Contact'}
                  </label>
                  <p className="text-lg">{user.emergencyContact || 'Not provided'}</p>
                </div>
                
                {user.notes && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'မှတ်ချက်များ' : 'Notes'}
                    </label>
                    <p className="text-lg">{user.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Activity Logs */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Activity className="h-5 w-5 text-green-600" />
                  {language === 'mm' ? 'လှုပ်ရှားမှု မှတ်တမ်းများ' : 'Activity Logs'}
                </CardTitle>
                <CardDescription>
                  {language === 'mm' 
                    ? 'နောက်ဆုံး အသုံးပြုသူ လှုပ်ရှားမှုများ'
                    : 'Recent user activities and actions'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {activityLogs.map((log) => (
                    <div key={log._id} className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                      <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                        <Activity className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">{log.action}</h4>
                          <span className="text-sm text-gray-500">
                            {formatDateTime(log.timestamp)}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600 mt-1">{log.details}</p>
                        {log.ipAddress && (
                          <p className="text-xs text-gray-500 mt-1">IP: {log.ipAddress}</p>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Stats & Actions */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5 text-purple-600" />
                  {language === 'mm' ? 'အချိန်အခါ အချက်အလက်များ' : 'Time Information'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {language === 'mm' ? 'အကောင့် ဖန်တီးသည့်ရက်' : 'Account Created'}
                  </label>
                  <p className="text-lg">{formatDate(user.createdAt)}</p>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {language === 'mm' ? 'နောက်ဆုံး ဝင်ရောက်ချိန်' : 'Last Login'}
                  </label>
                  <p className="text-lg">
                    {user.lastLogin ? formatDateTime(user.lastLogin) : 'Never'}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5 text-orange-600" />
                  {language === 'mm' ? 'အမြန် လုပ်ဆောင်ချက်များ' : 'Quick Actions'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button className="w-full justify-start" variant="outline">
                  <Key className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'စကားဝှက် ပြန်လည်သတ်မှတ်' : 'Reset Password'}
                </Button>
                
                <Button className="w-full justify-start" variant="outline">
                  <Shield className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'အခွင့်အရေးများ ပြင်ဆင်' : 'Edit Permissions'}
                </Button>
                
                <Button className="w-full justify-start" variant="outline">
                  <Activity className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'လှုပ်ရှားမှု မှတ်တမ်း ထုတ်ယူ' : 'Export Activity Log'}
                </Button>
                
                <Button 
                  className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50" 
                  variant="outline"
                >
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'အကောင့် ရပ်ဆိုင်း' : 'Suspend Account'}
                </Button>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
