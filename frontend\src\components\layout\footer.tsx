'use client'

import { useState, useEffect } from 'react'
import { useSettings } from '@/contexts/settings-context'
import apiClient from '@/lib/api'
import { Heart, Globe, Mail, Phone, MapPin } from 'lucide-react'

interface FooterData {
  companyName: string
  description: string
  email: string
  phone: string
  address: string
  website: string
  socialLinks: {
    facebook?: string
    twitter?: string
    linkedin?: string
    instagram?: string
  }
  copyright: string
  version: string
}

export function Footer() {
  const { language } = useSettings()
  const [footerData, setFooterData] = useState<FooterData>({
    companyName: 'BitsTech POS',
    description: 'Modern Point of Sale System for Your Business',
    email: '<EMAIL>',
    phone: '+95 9 ***********',
    address: 'Yangon, Myanmar',
    website: 'https://bitstech.com',
    socialLinks: {
      facebook: 'https://facebook.com/bitstech',
      twitter: 'https://twitter.com/bitstech',
      linkedin: 'https://linkedin.com/company/bitstech'
    },
    copyright: '© 2024 BitsTech. All rights reserved.',
    version: 'v1.0.0'
  })

  const [footerSettings, setFooterSettings] = useState({
    showSocialLinks: true,
    showContactInfo: true,
    showCompanyInfo: true,
    backgroundColor: '#1F2937',
    textColor: '#FFFFFF',
    footerStyle: 'default'
  })

  useEffect(() => {
    loadFooterData()
  }, [])

  const loadFooterData = async () => {
    try {
      // Load footer-specific settings first
      // Use fallback since API method doesn't exist
      const footerResponse = { success: false, data: {} }
      if (footerResponse.success && footerResponse.data) {
        const footerData = footerResponse.data
        setFooterData(prev => ({
          ...prev,
          companyName: (footerData as any).companyName || prev.companyName,
          description: (footerData as any).description || prev.description,
          email: (footerData as any).email || prev.email,
          phone: (footerData as any).phone || prev.phone,
          address: (footerData as any).address || prev.address,
          website: (footerData as any).website || prev.website,
          socialLinks: (footerData as any).socialLinks || prev.socialLinks,
          copyright: (footerData as any).copyright || prev.copyright,
          version: (footerData as any).version || prev.version
        }))
        setFooterSettings(prev => ({
          ...prev,
          showSocialLinks: (footerData as any).showSocialLinks ?? prev.showSocialLinks,
          showContactInfo: (footerData as any).showContactInfo ?? prev.showContactInfo,
          showCompanyInfo: (footerData as any).showCompanyInfo ?? prev.showCompanyInfo,
          backgroundColor: (footerData as any).backgroundColor || prev.backgroundColor,
          textColor: (footerData as any).textColor || prev.textColor,
          footerStyle: (footerData as any).footerStyle || prev.footerStyle
        }))
      } else {
        // Fallback to localStorage
        const storedFooter = localStorage.getItem('bitstech_footer_settings')
        if (storedFooter) {
          try {
            const parsed = JSON.parse(storedFooter)
            setFooterData(prev => ({
              ...prev,
              companyName: parsed.companyName || prev.companyName,
              description: parsed.description || prev.description,
              email: parsed.email || prev.email,
              phone: parsed.phone || prev.phone,
              address: parsed.address || prev.address,
              website: parsed.website || prev.website,
              socialLinks: parsed.socialLinks || prev.socialLinks,
              copyright: parsed.copyright || prev.copyright,
              version: parsed.version || prev.version
            }))
            setFooterSettings(prev => ({
              ...prev,
              showSocialLinks: parsed.showSocialLinks ?? prev.showSocialLinks,
              showContactInfo: parsed.showContactInfo ?? prev.showContactInfo,
              showCompanyInfo: parsed.showCompanyInfo ?? prev.showCompanyInfo,
              backgroundColor: parsed.backgroundColor || prev.backgroundColor,
              textColor: parsed.textColor || prev.textColor,
              footerStyle: parsed.footerStyle || prev.footerStyle
            }))
          } catch (error) {
            console.error('Error parsing stored footer settings:', error)
          }
        }

        // Try to load from company settings as fallback
        // Use fallback since API method doesn't exist
        const companyResponse = { success: false, data: {} }
        if (companyResponse.success && companyResponse.data) {
          const company = companyResponse.data
          setFooterData(prev => ({
            ...prev,
            companyName: (company as any).name || prev.companyName,
            description: (company as any).description || prev.description,
            email: (company as any).email || prev.email,
            phone: (company as any).phone || prev.phone,
            address: (company as any).address || prev.address,
            website: (company as any).website || prev.website
          }))
        } else {
          // Final fallback to company localStorage
          const storedCompany = localStorage.getItem('bitstech_company_info')
          if (storedCompany) {
            try {
              const parsed = JSON.parse(storedCompany)
              setFooterData(prev => ({
                ...prev,
                companyName: parsed.name || prev.companyName,
                description: parsed.description || prev.description,
                email: parsed.email || prev.email,
                phone: parsed.phone || prev.phone,
                address: parsed.address || prev.address,
                website: parsed.website || prev.website
              }))
            } catch (error) {
              console.error('Error parsing stored company info:', error)
            }
          }
        }
      }
    } catch (error) {
      console.error('Error loading footer data:', error)
    }
  }

  const t = {
    en: {
      quickLinks: 'Quick Links',
      dashboard: 'Dashboard',
      products: 'Products',
      sales: 'Sales',
      customers: 'Customers',
      reports: 'Reports',
      settings: 'Settings',
      contact: 'Contact Info',
      followUs: 'Follow Us',
      madeWith: 'Made with',
      by: 'by',
      allRightsReserved: 'All rights reserved',
      version: 'Version'
    },
    mm: {
      quickLinks: 'လျင်မြန်သော လင့်များ',
      dashboard: 'ဒက်ရှ်ဘုတ်',
      products: 'ကုန်ပစ္စည်းများ',
      sales: 'ရောင်းချမှုများ',
      customers: 'ဖောက်သည်များ',
      reports: 'အစီရင်ခံစာများ',
      settings: 'ဆက်တင်များ',
      contact: 'ဆက်သွယ်ရေး အချက်အလက်',
      followUs: 'ကျွန်ုပ်တို့ကို လိုက်ပါ',
      madeWith: 'ဖြင့် ပြုလုပ်ထားသည်',
      by: 'မှ',
      allRightsReserved: 'မူပိုင်ခွင့် အားလုံး ရယူထားသည်',
      version: 'ဗားရှင်း'
    }
  }

  const text = t[language as keyof typeof t] || t.en

  const quickLinks = [
    { name: text.dashboard, href: '/dashboard' },
    { name: text.products, href: '/products' },
    { name: text.sales, href: '/sales' },
    { name: text.customers, href: '/customers' },
    { name: text.reports, href: '/reports' },
    { name: text.settings, href: '/settings' }
  ]

  return (
    <footer
      className="text-white"
      style={{
        backgroundColor: footerSettings.backgroundColor,
        color: footerSettings.textColor
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className={`grid gap-8 ${
          footerSettings.footerStyle === 'minimal'
            ? 'grid-cols-1 md:grid-cols-2'
            : footerSettings.footerStyle === 'detailed'
            ? 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
            : 'grid-cols-1 md:grid-cols-2 lg:grid-cols-4'
        }`}>
          {/* Company Info */}
          {footerSettings.showCompanyInfo && (
            <div className="lg:col-span-2">
              <div className="flex items-center gap-2 mb-4">
                <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-sm">B</span>
                </div>
                <h3 className="text-xl font-bold">{footerData.companyName}</h3>
              </div>
              <p className="opacity-80 mb-4 max-w-md">
                {footerData.description}
              </p>
              {footerSettings.showContactInfo && (
                <div className="space-y-2">
                  {footerData.email && (
                    <div className="flex items-center gap-2 opacity-80">
                      <Mail className="h-4 w-4" />
                      <a
                        href={`mailto:${footerData.email}`}
                        className="hover:opacity-100 transition-opacity"
                      >
                        {footerData.email}
                      </a>
                    </div>
                  )}
                  {footerData.phone && (
                    <div className="flex items-center gap-2 opacity-80">
                      <Phone className="h-4 w-4" />
                      <a
                        href={`tel:${footerData.phone}`}
                        className="hover:opacity-100 transition-opacity"
                      >
                        {footerData.phone}
                      </a>
                    </div>
                  )}
                  {footerData.address && (
                    <div className="flex items-center gap-2 opacity-80">
                      <MapPin className="h-4 w-4" />
                      <span>{footerData.address}</span>
                    </div>
                  )}
                  {footerData.website && (
                    <div className="flex items-center gap-2 opacity-80">
                      <Globe className="h-4 w-4" />
                      <a
                        href={footerData.website}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="hover:opacity-100 transition-opacity"
                      >
                        {footerData.website}
                      </a>
                    </div>
                  )}
                </div>
              )}
            </div>
          )}

          {/* Quick Links */}
          <div>
            <h4 className="text-lg font-semibold mb-4">{text.quickLinks}</h4>
            <ul className="space-y-2">
              {quickLinks.map((link) => (
                <li key={link.href}>
                  <a
                    href={link.href}
                    className="text-gray-300 hover:text-white transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Social Links */}
          {footerSettings.showSocialLinks && (
            <div>
              <h4 className="text-lg font-semibold mb-4">{text.followUs}</h4>
              <div className="space-y-3">
                <p className="opacity-80 text-sm">
                  {language === 'mm'
                    ? 'ကျွန်ုပ်တို့နှင့် ဆက်သွယ်ပါ'
                    : 'Get in touch with us'
                  }
                </p>

                {/* Social Links */}
                <div className="flex gap-3">
                  {footerData.socialLinks.facebook && (
                    <a
                      href={footerData.socialLinks.facebook}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors"
                      title="Facebook"
                    >
                      <span className="text-white text-xs font-bold">f</span>
                    </a>
                  )}
                  {footerData.socialLinks.twitter && (
                    <a
                      href={footerData.socialLinks.twitter}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 bg-sky-500 rounded-full flex items-center justify-center hover:bg-sky-600 transition-colors"
                      title="Twitter"
                    >
                      <span className="text-white text-xs font-bold">𝕏</span>
                    </a>
                  )}
                  {footerData.socialLinks.linkedin && (
                    <a
                      href={footerData.socialLinks.linkedin}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 bg-blue-700 rounded-full flex items-center justify-center hover:bg-blue-800 transition-colors"
                      title="LinkedIn"
                    >
                      <span className="text-white text-xs font-bold">in</span>
                    </a>
                  )}
                  {footerData.socialLinks.instagram && (
                    <a
                      href={footerData.socialLinks.instagram}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 bg-pink-600 rounded-full flex items-center justify-center hover:bg-pink-700 transition-colors"
                      title="Instagram"
                    >
                      <span className="text-white text-xs font-bold">ig</span>
                    </a>
                  )}
                  {(footerData.socialLinks as any).youtube && (
                    <a
                      href={(footerData.socialLinks as any).youtube}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="w-8 h-8 bg-red-600 rounded-full flex items-center justify-center hover:bg-red-700 transition-colors"
                      title="YouTube"
                    >
                      <span className="text-white text-xs font-bold">yt</span>
                    </a>
                  )}
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-opacity-20 mt-8 pt-8" style={{ borderColor: footerSettings.textColor }}>
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex items-center gap-2 opacity-80 text-sm">
              <span>{text.madeWith}</span>
              <Heart className="h-4 w-4 text-red-500" />
              <span>{text.by} BitsTech Team</span>
            </div>

            <div className="flex items-center gap-4 opacity-80 text-sm">
              <span>{footerData.copyright}</span>
              <span>•</span>
              <span>{text.version} {footerData.version}</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
