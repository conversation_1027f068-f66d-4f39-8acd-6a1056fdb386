'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import {
  Bell,
  X,
  CheckCircle,
  AlertTriangle,
  Package,
  Clock,
  TrendingDown,
  Archive,
  RefreshCw,
  Settings,
  MoreVertical
} from 'lucide-react'

interface Notification {
  _id: string
  type: 'low_stock' | 'out_of_stock' | 'expiry_warning' | 'reorder_point' | 'overstock' | 'system'
  severity: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  productName?: string
  sku?: string
  currentStock?: number
  reorderPoint?: number
  timestamp: string
  isRead: boolean
  actionRequired: boolean
  actionUrl?: string
}

interface NotificationCenterProps {
  isOpen: boolean
  onClose: () => void
  language: 'en' | 'mm'
}

export function NotificationCenter({ isOpen, onClose, language }: NotificationCenterProps) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [loading, setLoading] = useState(true)
  const [filter, setFilter] = useState<'all' | 'unread' | 'critical'>('all')

  useEffect(() => {
    if (isOpen) {
      fetchNotifications()
    }
  }, [isOpen])

  const fetchNotifications = async () => {
    try {
      setLoading(true)
      
      // Mock notifications data
      const mockNotifications: Notification[] = [
        {
          _id: '1',
          type: 'out_of_stock',
          severity: 'critical',
          title: language === 'mm' ? 'စတော့ ကုန်သွားပြီ' : 'Out of Stock Alert',
          message: language === 'mm' 
            ? 'HP LaserJet Pro လုံးဝ ကုန်သွားပြီ။ ချက်ချင်း ပြန်မှာရန် လိုအပ်သည်။'
            : 'HP LaserJet Pro is completely out of stock. Immediate reorder required.',
          productName: 'HP LaserJet Pro',
          sku: 'PRI001',
          currentStock: 0,
          reorderPoint: 5,
          timestamp: new Date(Date.now() - 5 * 60 * 1000).toISOString(), // 5 minutes ago
          isRead: false,
          actionRequired: true,
          actionUrl: '/purchase-orders/new'
        },
        {
          _id: '2',
          type: 'low_stock',
          severity: 'high',
          title: language === 'mm' ? 'နည်းသော စတော့' : 'Low Stock Warning',
          message: language === 'mm' 
            ? 'ASUS VivoBook 15 စတော့ နည်းနေပြီ။ ၃ ခုသာ ကျန်တော့သည်။'
            : 'ASUS VivoBook 15 stock is running low. Only 3 units remaining.',
          productName: 'ASUS VivoBook 15',
          sku: 'LAP001',
          currentStock: 3,
          reorderPoint: 10,
          timestamp: new Date(Date.now() - 15 * 60 * 1000).toISOString(), // 15 minutes ago
          isRead: false,
          actionRequired: true,
          actionUrl: '/purchase-orders/new'
        },
        {
          _id: '3',
          type: 'expiry_warning',
          severity: 'medium',
          title: language === 'mm' ? 'သက်တမ်း ကုန်ခါနီး' : 'Expiry Warning',
          message: language === 'mm' 
            ? 'Antivirus Software License ၃၂ ရက်အတွင်း သက်တမ်း ကုန်မည်။'
            : 'Antivirus Software License expires in 32 days.',
          productName: 'Antivirus Software License',
          sku: 'SOF001',
          timestamp: new Date(Date.now() - 30 * 60 * 1000).toISOString(), // 30 minutes ago
          isRead: true,
          actionRequired: false
        },
        {
          _id: '4',
          type: 'reorder_point',
          severity: 'medium',
          title: language === 'mm' ? 'ပြန်မှာရန် အချိန်' : 'Reorder Point Reached',
          message: language === 'mm' 
            ? 'Samsung 27" Monitor ပြန်မှာရန် အချိန် ရောက်နေပြီ။'
            : 'Samsung 27" Monitor has reached its reorder point.',
          productName: 'Samsung 27" Monitor',
          sku: 'MON001',
          currentStock: 8,
          reorderPoint: 5,
          timestamp: new Date(Date.now() - 45 * 60 * 1000).toISOString(), // 45 minutes ago
          isRead: true,
          actionRequired: true,
          actionUrl: '/purchase-orders/new'
        },
        {
          _id: '5',
          type: 'system',
          severity: 'low',
          title: language === 'mm' ? 'စနစ် အပ်ဒိတ်' : 'System Update',
          message: language === 'mm' 
            ? 'စတော့ အချက်အလက်များ အပ်ဒိတ် လုပ်ပြီးပါပြီ။'
            : 'Inventory data has been successfully updated.',
          timestamp: new Date(Date.now() - 60 * 60 * 1000).toISOString(), // 1 hour ago
          isRead: true,
          actionRequired: false
        }
      ]

      setNotifications(mockNotifications)
    } catch (error) {
      console.error('Error fetching notifications:', error)
    } finally {
      setLoading(false)
    }
  }

  const markAsRead = (notificationId: string) => {
    setNotifications(prev => prev.map(notification =>
      notification._id === notificationId 
        ? { ...notification, isRead: true }
        : notification
    ))
  }

  const markAllAsRead = () => {
    setNotifications(prev => prev.map(notification => ({
      ...notification,
      isRead: true
    })))
  }

  const dismissNotification = (notificationId: string) => {
    setNotifications(prev => prev.filter(notification => notification._id !== notificationId))
  }

  const getNotificationIcon = (type: string) => {
    switch (type) {
      case 'low_stock': return <TrendingDown className="h-4 w-4" />
      case 'out_of_stock': return <X className="h-4 w-4" />
      case 'expiry_warning': return <Clock className="h-4 w-4" />
      case 'reorder_point': return <RefreshCw className="h-4 w-4" />
      case 'overstock': return <Archive className="h-4 w-4" />
      case 'system': return <Settings className="h-4 w-4" />
      default: return <Bell className="h-4 w-4" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const formatTimeAgo = (timestamp: string) => {
    const now = new Date()
    const time = new Date(timestamp)
    const diffInMinutes = Math.floor((now.getTime() - time.getTime()) / (1000 * 60))

    if (diffInMinutes < 1) {
      return language === 'mm' ? 'ယခုလေး' : 'Just now'
    } else if (diffInMinutes < 60) {
      return language === 'mm' ? `${diffInMinutes} မိနစ် အရင်` : `${diffInMinutes}m ago`
    } else if (diffInMinutes < 1440) {
      const hours = Math.floor(diffInMinutes / 60)
      return language === 'mm' ? `${hours} နာရီ အရင်` : `${hours}h ago`
    } else {
      const days = Math.floor(diffInMinutes / 1440)
      return language === 'mm' ? `${days} ရက် အရင်` : `${days}d ago`
    }
  }

  const filteredNotifications = notifications.filter(notification => {
    switch (filter) {
      case 'unread': return !notification.isRead
      case 'critical': return notification.severity === 'critical'
      default: return true
    }
  })

  const unreadCount = notifications.filter(n => !n.isRead).length
  const criticalCount = notifications.filter(n => n.severity === 'critical').length

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/50 flex items-start justify-end z-50 p-4">
      <Card className="w-full max-w-md max-h-[90vh] overflow-hidden">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'သတိပေးချက်များ' : 'Notifications'}
                {unreadCount > 0 && (
                  <Badge className="bg-red-100 text-red-800 text-xs">
                    {unreadCount}
                  </Badge>
                )}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'စတော့ နှင့် စနစ် သတိပေးချက်များ'
                  : 'Inventory and system alerts'
                }
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>

          {/* Filter Tabs */}
          <div className="flex gap-2 mt-3">
            <Button
              variant={filter === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('all')}
              className="text-xs"
            >
              {language === 'mm' ? 'အားလုံး' : 'All'} ({notifications.length})
            </Button>
            <Button
              variant={filter === 'unread' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('unread')}
              className="text-xs"
            >
              {language === 'mm' ? 'မဖတ်ရသေး' : 'Unread'} ({unreadCount})
            </Button>
            <Button
              variant={filter === 'critical' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter('critical')}
              className="text-xs"
            >
              {language === 'mm' ? 'အရေးကြီး' : 'Critical'} ({criticalCount})
            </Button>
          </div>

          {/* Actions */}
          {unreadCount > 0 && (
            <div className="flex justify-end mt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={markAllAsRead}
                className="text-xs text-blue-600 hover:text-blue-700"
              >
                <CheckCircle className="h-3 w-3 mr-1" />
                {language === 'mm' ? 'အားလုံး ဖတ်ပြီး' : 'Mark all read'}
              </Button>
            </div>
          )}
        </CardHeader>

        <CardContent className="p-0 overflow-y-auto max-h-[60vh]">
          {loading ? (
            <div className="flex items-center justify-center p-8">
              <div className="animate-spin rounded-full h-8 w-8 border-2 border-blue-600 border-t-transparent"></div>
            </div>
          ) : filteredNotifications.length === 0 ? (
            <div className="text-center p-8">
              <Bell className="h-12 w-12 text-gray-400 mx-auto mb-3" />
              <h3 className="font-medium text-gray-900 dark:text-white mb-1">
                {language === 'mm' ? 'သတိပေးချက် မရှိပါ' : 'No Notifications'}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {language === 'mm' 
                  ? 'လက်ရှိ သတိပေးချက် မရှိပါ'
                  : 'You\'re all caught up!'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-1">
              {filteredNotifications.map((notification) => (
                <div
                  key={notification._id}
                  className={`p-4 border-b border-gray-100 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors ${
                    !notification.isRead ? 'bg-blue-50 dark:bg-blue-900/20' : ''
                  }`}
                >
                  <div className="flex items-start gap-3">
                    <div className={`p-2 rounded-lg ${getSeverityColor(notification.severity)}`}>
                      {getNotificationIcon(notification.type)}
                    </div>
                    
                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-sm">{notification.title}</h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                            {notification.message}
                          </p>
                          
                          {notification.productName && (
                            <div className="flex items-center gap-2 mt-2 text-xs text-gray-500">
                              <Package className="h-3 w-3" />
                              <span>{notification.productName} ({notification.sku})</span>
                              {notification.currentStock !== undefined && (
                                <>
                                  <span>•</span>
                                  <span>
                                    {language === 'mm' ? 'လက်ရှိ' : 'Stock'}: {notification.currentStock}
                                  </span>
                                </>
                              )}
                            </div>
                          )}
                          
                          <div className="flex items-center justify-between mt-2">
                            <span className="text-xs text-gray-500">
                              {formatTimeAgo(notification.timestamp)}
                            </span>
                            
                            {notification.actionRequired && notification.actionUrl && (
                              <Button
                                variant="outline"
                                size="sm"
                                className="text-xs h-6"
                                onClick={() => {
                                  // Navigate to action URL
                                  window.location.href = notification.actionUrl!
                                }}
                              >
                                {language === 'mm' ? 'ဆောင်ရွက်ရန်' : 'Take Action'}
                              </Button>
                            )}
                          </div>
                        </div>
                        
                        <div className="flex flex-col gap-1 ml-2">
                          {!notification.isRead && (
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => markAsRead(notification._id)}
                              className="h-6 w-6 p-0"
                            >
                              <CheckCircle className="h-3 w-3" />
                            </Button>
                          )}
                          
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => dismissNotification(notification._id)}
                            className="h-6 w-6 p-0 text-gray-400 hover:text-red-600"
                          >
                            <X className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
