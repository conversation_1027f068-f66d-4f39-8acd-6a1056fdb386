'use client'

import { useState, useEffect, useRef } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import {
  Camera,
  Scan,
  X,
  CheckCircle,
  AlertCircle,
  Zap,
  Package,
  Search,
  Smartphone,
  Flashlight,
  RotateCcw
} from 'lucide-react'

interface BarcodeScannerProps {
  isOpen: boolean
  onClose: () => void
  onScan: (barcode: string, product?: any) => void
  language: 'en' | 'mm'
}

interface ScannedProduct {
  _id: string
  name: string
  barcode: string
  price: number
  category: string
  stock: number
}

export function BarcodeScanner({ isOpen, onClose, onScan, language }: BarcodeScannerProps) {
  const [isScanning, setIsScanning] = useState(false)
  const [manualBarcode, setManualBarcode] = useState('')
  const [lastScanned, setLastScanned] = useState<string>('')
  const [scannedProduct, setScannedProduct] = useState<ScannedProduct | null>(null)
  const [flashlightOn, setFlashlightOn] = useState(false)
  const [scanHistory, setScanHistory] = useState<string[]>([])
  const videoRef = useRef<HTMLVideoElement>(null)
  const streamRef = useRef<MediaStream | null>(null)

  useEffect(() => {
    if (isOpen) {
      startCamera()
    } else {
      stopCamera()
    }

    return () => {
      stopCamera()
    }
  }, [isOpen])

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'environment', // Use back camera
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      })
      
      streamRef.current = stream
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
    } catch (error) {
      console.error('Error accessing camera:', error)
    }
  }

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop())
      streamRef.current = null
    }
  }

  const toggleFlashlight = async () => {
    if (streamRef.current) {
      const videoTrack = streamRef.current.getVideoTracks()[0]
      if (videoTrack && 'torch' in videoTrack.getCapabilities()) {
        try {
          await videoTrack.applyConstraints({
            advanced: [{ torch: !flashlightOn } as any]
          })
          setFlashlightOn(!flashlightOn)
        } catch (error) {
          console.error('Error toggling flashlight:', error)
        }
      }
    }
  }

  const simulateBarcodeScan = () => {
    setIsScanning(true)
    
    // Simulate scanning delay
    setTimeout(() => {
      // Mock barcode data
      const mockBarcodes = [
        { barcode: '1234567890123', product: { _id: '1', name: 'ASUS VivoBook 15', barcode: '1234567890123', price: 1200000, category: 'Laptops', stock: 25 }},
        { barcode: '2345678901234', product: { _id: '2', name: 'Samsung 27" Monitor', barcode: '2345678901234', price: 450000, category: 'Monitors', stock: 18 }},
        { barcode: '3456789012345', product: { _id: '3', name: 'HP LaserJet Pro', barcode: '3456789012345', price: 380000, category: 'Printers', stock: 12 }},
        { barcode: '4567890123456', product: { _id: '4', name: 'Kingston 1TB SSD', barcode: '4567890123456', price: 180000, category: 'Storage', stock: 30 }},
        { barcode: '5678901234567', product: { _id: '5', name: 'Logitech Wireless Mouse', barcode: '5678901234567', price: 25000, category: 'Accessories', stock: 45 }}
      ]
      
      const randomScan = mockBarcodes[Math.floor(Math.random() * mockBarcodes.length)]
      
      setLastScanned(randomScan.barcode)
      setScannedProduct(randomScan.product)
      setScanHistory(prev => [randomScan.barcode, ...prev.slice(0, 4)])
      setIsScanning(false)
      
      // Auto-add to cart after 1 second
      setTimeout(() => {
        onScan(randomScan.barcode, randomScan.product)
        setScannedProduct(null)
      }, 1000)
    }, 2000)
  }

  const handleManualScan = () => {
    if (manualBarcode.trim()) {
      setLastScanned(manualBarcode)
      setScanHistory(prev => [manualBarcode, ...prev.slice(0, 4)])
      onScan(manualBarcode)
      setManualBarcode('')
    }
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} MMK`
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black/80 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-md max-h-[90vh] overflow-hidden">
        <CardHeader className="pb-3">
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Scan className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'ဘားကုဒ် စကင်နာ' : 'Barcode Scanner'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'ကုန်ပစ္စည်း ဘားကုဒ် ကို စကင်လုပ်ပါ'
                  : 'Scan product barcodes to add to cart'
                }
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Camera View */}
          <div className="relative aspect-video bg-black rounded-lg overflow-hidden">
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className="w-full h-full object-cover"
            />
            
            {/* Scanning Overlay */}
            <div className="absolute inset-0 flex items-center justify-center">
              <div className="relative">
                {/* Scanning Frame */}
                <div className="w-64 h-40 border-2 border-white rounded-lg relative">
                  <div className="absolute top-0 left-0 w-6 h-6 border-t-4 border-l-4 border-blue-500 rounded-tl-lg"></div>
                  <div className="absolute top-0 right-0 w-6 h-6 border-t-4 border-r-4 border-blue-500 rounded-tr-lg"></div>
                  <div className="absolute bottom-0 left-0 w-6 h-6 border-b-4 border-l-4 border-blue-500 rounded-bl-lg"></div>
                  <div className="absolute bottom-0 right-0 w-6 h-6 border-b-4 border-r-4 border-blue-500 rounded-br-lg"></div>
                  
                  {/* Scanning Line */}
                  {isScanning && (
                    <div className="absolute inset-x-0 top-1/2 h-0.5 bg-red-500 animate-pulse"></div>
                  )}
                </div>
                
                {/* Status Text */}
                <div className="absolute -bottom-8 left-1/2 transform -translate-x-1/2 text-white text-sm text-center">
                  {isScanning 
                    ? (language === 'mm' ? 'စကင်နေသည်...' : 'Scanning...')
                    : (language === 'mm' ? 'ဘားကုဒ် ကို ဘောင်အတွင်း ထားပါ' : 'Place barcode within frame')
                  }
                </div>
              </div>
            </div>

            {/* Camera Controls */}
            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
              <Button
                size="sm"
                variant="secondary"
                onClick={toggleFlashlight}
                className="bg-black/50 hover:bg-black/70 text-white border-white/30"
              >
                <Flashlight className={`h-4 w-4 ${flashlightOn ? 'text-yellow-400' : ''}`} />
              </Button>
              
              <Button
                size="sm"
                variant="secondary"
                onClick={() => window.location.reload()}
                className="bg-black/50 hover:bg-black/70 text-white border-white/30"
              >
                <RotateCcw className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Scan Button */}
          <Button
            onClick={simulateBarcodeScan}
            disabled={isScanning}
            className="w-full bg-blue-600 hover:bg-blue-700"
            size="lg"
          >
            {isScanning ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'စကင်နေသည်...' : 'Scanning...'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Camera className="h-4 w-4" />
                {language === 'mm' ? 'စကင်လုပ်ရန်' : 'Start Scanning'}
              </div>
            )}
          </Button>

          {/* Manual Input */}
          <div className="space-y-2">
            <label className="text-sm font-medium">
              {language === 'mm' ? 'လက်ဖြင့် ရိုက်ထည့်ရန်' : 'Manual Entry'}
            </label>
            <div className="flex gap-2">
              <Input
                placeholder={language === 'mm' ? 'ဘားကုဒ် ရိုက်ထည့်ပါ' : 'Enter barcode manually'}
                value={manualBarcode}
                onChange={(e) => setManualBarcode(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && handleManualScan()}
              />
              <Button onClick={handleManualScan} disabled={!manualBarcode.trim()}>
                <Search className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Scanned Product */}
          {scannedProduct && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">
                  {language === 'mm' ? 'ရှာတွေ့ပြီး!' : 'Product Found!'}
                </span>
              </div>
              <div className="space-y-1">
                <h4 className="font-semibold">{scannedProduct.name}</h4>
                <p className="text-sm text-gray-600">{scannedProduct.category}</p>
                <p className="text-lg font-bold text-green-600">
                  {formatCurrency(scannedProduct.price)}
                </p>
                <Badge variant="outline" className="text-xs">
                  {language === 'mm' ? 'စတော့' : 'Stock'}: {scannedProduct.stock}
                </Badge>
              </div>
            </div>
          )}

          {/* Scan History */}
          {scanHistory.length > 0 && (
            <div className="space-y-2">
              <label className="text-sm font-medium">
                {language === 'mm' ? 'စကင် မှတ်တမ်း' : 'Recent Scans'}
              </label>
              <div className="space-y-1 max-h-24 overflow-y-auto">
                {scanHistory.map((barcode, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm p-2 bg-gray-50 rounded">
                    <Package className="h-4 w-4 text-gray-400" />
                    <span className="font-mono">{barcode}</span>
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => onScan(barcode)}
                      className="ml-auto h-6 px-2"
                    >
                      <Zap className="h-3 w-3" />
                    </Button>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Tips */}
          <div className="text-xs text-gray-600 space-y-1">
            <p className="flex items-center gap-1">
              <AlertCircle className="h-3 w-3" />
              {language === 'mm' 
                ? 'ဘားကုဒ် ကို ကင်မရာ ရှေ့တွင် ရှင်းရှင်းလင်းလင်း ထားပါ'
                : 'Hold barcode steady and clear in front of camera'
              }
            </p>
            <p className="flex items-center gap-1">
              <Smartphone className="h-3 w-3" />
              {language === 'mm' 
                ? 'အလင်း လုံလောက်သော နေရာတွင် စကင်လုပ်ပါ'
                : 'Ensure good lighting for better scanning'
              }
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
