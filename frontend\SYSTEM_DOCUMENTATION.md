# 🏪 BitsTech POS System - Complete Documentation

## 📋 Table of Contents
1. [System Overview](#system-overview)
2. [Architecture](#architecture)
3. [System Flow Diagrams](#system-flow-diagrams)
4. [Database Schema](#database-schema)
5. [API Endpoints](#api-endpoints)
6. [Features & Functionality](#features--functionality)
7. [User Interface](#user-interface)
8. [Installation & Setup](#installation--setup)
9. [Usage Guide](#usage-guide)
10. [Troubleshooting](#troubleshooting)

---

## 🎯 System Overview

BitsTech POS System is a comprehensive Point of Sale solution built with modern web technologies, featuring real-time database integration, advanced analytics, and multi-language support.

### 🔧 Technology Stack
- **Frontend**: Next.js 14, React 18, TypeScript
- **Backend**: Node.js, Express.js
- **Database**: MongoDB with real-time synchronization
- **UI Framework**: Tailwind CSS, Shadcn/UI
- **State Management**: React Context API
- **Authentication**: JWT-based authentication
- **Language Support**: English & Myanmar (Burmese)

### 🌟 Key Features
- ✅ Real-time POS operations
- ✅ Advanced inventory management
- ✅ Customer relationship management
- ✅ Supplier management with performance tracking
- ✅ AI-powered sales forecasting
- ✅ Comprehensive reporting & analytics
- ✅ Multi-currency support (USD, MMK, THB)
- ✅ Invoice generation with 10+ templates
- ✅ User management & role-based access
- ✅ Complete settings configuration
- ✅ Data backup & restore functionality

---

## 🏗️ Architecture

### System Architecture Diagram
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (Next.js)     │◄──►│   (Node.js)     │◄──►│   (MongoDB)     │
│                 │    │                 │    │                 │
│ • React Pages   │    │ • REST API      │    │ • Collections   │
│ • Components    │    │ • Auth Service  │    │ • Indexes       │
│ • Context API   │    │ • Business Logic│    │ • Aggregations  │
│ • UI Library    │    │ • Data Layer    │    │ • Real-time     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Component Architecture
```
src/
├── app/                    # Next.js App Router
│   ├── dashboard/         # Dashboard & Analytics
│   ├── pos/              # Point of Sale
│   ├── products/         # Product Management
│   ├── customers/        # Customer Management
│   ├── suppliers/        # Supplier Management
│   ├── inventory/        # Inventory Management
│   ├── sales/           # Sales Management
│   ├── reports/         # Reports & Analytics
│   ├── settings/        # System Settings
│   └── users/           # User Management
├── components/
│   ├── ui/              # Reusable UI Components
│   ├── layout/          # Layout Components
│   └── forms/           # Form Components
├── contexts/            # React Context Providers
├── lib/                 # Utilities & API Client
└── types/              # TypeScript Definitions
```

---

## 🔄 System Flow Diagrams

### 1. POS Transaction Flow
```mermaid
graph TD
    A[Start POS Session] --> B[Scan/Select Products]
    B --> C[Add to Cart]
    C --> D{More Products?}
    D -->|Yes| B
    D -->|No| E[Select Customer]
    E --> F[Apply Discounts/Tax]
    F --> G[Choose Payment Method]
    G --> H[Process Payment]
    H --> I[Generate Receipt]
    I --> J[Update Inventory]
    J --> K[Save Transaction]
    K --> L[Print Receipt]
    L --> M[End Transaction]
```

### 2. Inventory Management Flow
```mermaid
graph TD
    A[Product Creation] --> B[Set Stock Levels]
    B --> C[Monitor Inventory]
    C --> D{Stock Low?}
    D -->|Yes| E[Generate Alert]
    E --> F[Create Purchase Order]
    F --> G[Receive Stock]
    G --> H[Update Inventory]
    H --> C
    D -->|No| I[Continue Monitoring]
    I --> C
```

### 3. User Authentication Flow
```mermaid
graph TD
    A[User Login] --> B[Validate Credentials]
    B --> C{Valid?}
    C -->|Yes| D[Generate JWT Token]
    D --> E[Set User Context]
    E --> F[Redirect to Dashboard]
    C -->|No| G[Show Error Message]
    G --> A
    F --> H[Access Protected Routes]
    H --> I{Token Valid?}
    I -->|Yes| J[Allow Access]
    I -->|No| K[Redirect to Login]
    K --> A
```

### 4. Data Synchronization Flow
```mermaid
graph TD
    A[User Action] --> B[API Request]
    B --> C[Backend Processing]
    C --> D[Database Update]
    D --> E[Real-time Sync]
    E --> F[Update UI State]
    F --> G[Notify Other Components]
    G --> H[Refresh Related Data]
```

---

## 🗄️ Database Schema

### Core Collections

#### Products Collection
```javascript
{
  _id: ObjectId,
  name: String,
  description: String,
  sku: String,
  barcode: String,
  category: {
    _id: ObjectId,
    name: String,
    color: String
  },
  price: Number,
  cost: Number,
  inventory: {
    quantity: Number,
    minQuantity: Number,
    maxQuantity: Number,
    unit: String
  },
  image: String,
  tags: [String],
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### Sales Collection
```javascript
{
  _id: ObjectId,
  saleNumber: String,
  customer: {
    _id: ObjectId,
    name: String,
    email: String,
    phone: String
  },
  items: [{
    product: ObjectId,
    name: String,
    quantity: Number,
    price: Number,
    total: Number
  }],
  subtotal: Number,
  discount: Number,
  tax: Number,
  shipping: Number,
  total: Number,
  paymentMethod: String,
  status: String,
  createdAt: Date,
  updatedAt: Date
}
```

#### Customers Collection
```javascript
{
  _id: ObjectId,
  name: String,
  email: String,
  phone: String,
  address: String,
  dateOfBirth: Date,
  loyaltyPoints: Number,
  totalSpent: Number,
  totalOrders: Number,
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

#### Suppliers Collection
```javascript
{
  _id: ObjectId,
  name: String,
  code: String,
  email: String,
  phone: String,
  address: String,
  contactPerson: String,
  rating: Number,
  totalOrders: Number,
  totalOrderValue: Number,
  lastOrderDate: Date,
  isActive: Boolean,
  createdAt: Date,
  updatedAt: Date
}
```

---

## 🔌 API Endpoints

### Authentication
- `POST /auth/login` - User login
- `POST /auth/logout` - User logout
- `GET /auth/me` - Get current user
- `POST /auth/refresh` - Refresh token

### Products
- `GET /products` - Get all products
- `GET /products/:id` - Get product by ID
- `POST /products` - Create new product
- `PUT /products/:id` - Update product
- `DELETE /products/:id` - Delete product

### Sales
- `GET /sales` - Get all sales
- `GET /sales/:id` - Get sale by ID
- `POST /sales` - Create new sale
- `PUT /sales/:id` - Update sale
- `DELETE /sales/:id` - Delete sale

### Customers
- `GET /customers` - Get all customers
- `GET /customers/:id` - Get customer by ID
- `POST /customers` - Create new customer
- `PUT /customers/:id` - Update customer
- `DELETE /customers/:id` - Delete customer

### Suppliers
- `GET /suppliers` - Get all suppliers
- `GET /suppliers/:id` - Get supplier by ID
- `POST /suppliers` - Create new supplier
- `PUT /suppliers/:id` - Update supplier
- `DELETE /suppliers/:id` - Delete supplier

### Inventory
- `GET /inventory` - Get inventory status
- `POST /inventory/adjust` - Adjust inventory
- `GET /inventory/alerts` - Get low stock alerts

### Reports
- `GET /reports/sales` - Sales reports
- `GET /reports/inventory` - Inventory reports
- `GET /reports/customers` - Customer reports
- `POST /reports/export` - Export reports

### Settings
- `GET /settings/company` - Get company settings
- `PUT /settings/company` - Update company settings
- `GET /settings/tax-currency` - Get tax & currency settings
- `PUT /settings/tax-currency` - Update tax & currency settings
- `GET /settings/notifications` - Get notification settings
- `PUT /settings/notifications` - Update notification settings

---

## ⚡ Features & Functionality

### 1. Dashboard
- **Real-time KPIs**: Sales, revenue, customers, inventory
- **Interactive Charts**: Sales trends, category distribution
- **Quick Actions**: New sale, add product, view reports
- **Recent Activities**: Latest sales, low stock alerts
- **Performance Metrics**: Daily, weekly, monthly comparisons

### 2. Point of Sale (POS)
- **Product Catalog**: Real-time product search and selection
- **Cart Management**: Add, remove, update quantities
- **Customer Selection**: Search existing or create new customers
- **Payment Processing**: Multiple payment methods
- **Receipt Generation**: Instant receipt printing/download
- **Inventory Updates**: Real-time stock adjustments

### 3. Product Management
- **Product CRUD**: Create, read, update, delete products
- **Category Management**: Organize products by categories
- **Inventory Tracking**: Stock levels, min/max quantities
- **Pricing Management**: Cost, selling price, profit margins
- **Barcode Generation**: Automatic barcode creation
- **Product Images**: Upload and manage product photos
- **Bulk Operations**: Import/export product data

### 4. Customer Management
- **Customer Database**: Complete customer information
- **Purchase History**: Track customer transactions
- **Loyalty System**: Points and tier management
- **Analytics**: Customer behavior and preferences
- **Communication**: Email and SMS integration

### 5. Supplier Management
- **Supplier Database**: Comprehensive supplier information
- **Performance Tracking**: Order history and ratings
- **Dynamic Rating System**: AI-powered supplier scoring
- **Purchase Orders**: Create and manage orders
- **Payment Tracking**: Outstanding balances

### 6. Inventory Management
- **Stock Monitoring**: Real-time inventory levels
- **Low Stock Alerts**: Automated notifications
- **Stock Adjustments**: Manual inventory corrections
- **Valuation Reports**: Inventory value calculations
- **Movement Tracking**: Stock in/out history

### 7. Sales & Analytics
- **Sales Tracking**: Complete transaction history
- **Revenue Analytics**: Detailed financial reports
- **Forecasting**: AI-powered sales predictions
- **Performance Metrics**: KPI tracking and analysis
- **Trend Analysis**: Historical data insights

### 8. Reporting System
- **Pre-built Reports**: Sales, inventory, customer reports
- **Custom Reports**: Build your own reports
- **Export Options**: PDF, Excel, CSV formats
- **Scheduled Reports**: Automated report generation
- **Visual Analytics**: Charts and graphs

### 9. Settings & Configuration
- **Company Information**: Business details and branding
- **Tax & Currency**: Multi-currency and tax settings
- **User Management**: Role-based access control
- **Notifications**: Email, SMS, push notifications
- **Appearance**: Theme and UI customization
- **Data Management**: Backup, restore, export/import

---

## 🎨 User Interface

### Design Principles
- **Modern & Clean**: Minimalist design with focus on functionality
- **Responsive**: Works on desktop, tablet, and mobile devices
- **Accessible**: WCAG compliant with keyboard navigation
- **Multi-language**: English and Myanmar language support
- **Dark Mode**: Light and dark theme options

### Color Scheme
- **Primary**: Blue (#3B82F6)
- **Secondary**: Gray (#6B7280)
- **Success**: Green (#10B981)
- **Warning**: Yellow (#F59E0B)
- **Error**: Red (#EF4444)
- **Info**: Purple (#8B5CF6)

### Typography
- **Primary Font**: Inter
- **Fallback Fonts**: Roboto, Open Sans, Poppins
- **Myanmar Font**: Noto Sans Myanmar

### Layout Structure
```
┌─────────────────────────────────────────────────────────┐
│                    Header Navigation                     │
├─────────────┬───────────────────────────────────────────┤
│             │                                           │
│   Sidebar   │              Main Content                 │
│ Navigation  │                                           │
│             │                                           │
│             │                                           │
├─────────────┴───────────────────────────────────────────┤
│                      Footer                             │
└─────────────────────────────────────────────────────────┘
```

---

## 🚀 Installation & Setup

### Prerequisites
- Node.js 18+ 
- MongoDB 5+
- npm or yarn package manager

### Installation Steps

1. **Clone Repository**
```bash
git clone https://github.com/your-repo/bitstech-pos.git
cd bitstech-pos
```

2. **Install Dependencies**
```bash
# Frontend
cd frontend
npm install

# Backend
cd ../backend
npm install
```

3. **Environment Configuration**
```bash
# Frontend (.env.local)
NEXT_PUBLIC_API_URL=http://localhost:3001
NEXT_PUBLIC_APP_NAME=BitsTech POS

# Backend (.env)
PORT=3001
MONGODB_URI=mongodb://localhost:27017/bitstech
JWT_SECRET=your-secret-key
```

4. **Database Setup**
```bash
# Start MongoDB
mongod

# Create database and collections
mongo bitstech
```

5. **Start Development Servers**
```bash
# Frontend (Terminal 1)
cd frontend
npm run dev

# Backend (Terminal 2)
cd backend
npm run dev
```

6. **Access Application**
- Frontend: http://localhost:3000
- Backend API: http://localhost:3001

### Production Deployment

1. **Build Frontend**
```bash
cd frontend
npm run build
npm start
```

2. **Deploy Backend**
```bash
cd backend
npm run build
npm start
```

3. **Database Configuration**
- Use MongoDB Atlas for cloud deployment
- Configure connection strings
- Set up database indexes

---

## 📖 Usage Guide

### Getting Started

1. **Initial Setup**
   - Access the application at http://localhost:3000
   - Login with default credentials
   - Complete company information setup
   - Configure tax and currency settings

2. **Adding Products**
   - Navigate to Products page
   - Click "Add New Product"
   - Fill in product details
   - Set pricing and inventory levels
   - Save product

3. **Making Sales**
   - Go to POS page
   - Search and add products to cart
   - Select or create customer
   - Choose payment method
   - Complete transaction

4. **Managing Inventory**
   - Monitor stock levels in Inventory page
   - Set up low stock alerts
   - Create purchase orders when needed
   - Adjust inventory as required

5. **Viewing Reports**
   - Access Reports page
   - Select report type and date range
   - View analytics and insights
   - Export reports as needed

### Advanced Features

1. **Forecasting**
   - Use AI-powered sales predictions
   - Analyze trends and patterns
   - Plan inventory based on forecasts

2. **Customer Analytics**
   - Track customer behavior
   - Implement loyalty programs
   - Segment customers by value

3. **Supplier Management**
   - Monitor supplier performance
   - Compare supplier ratings
   - Optimize procurement decisions

---

## 🔧 Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Check MongoDB service status
   - Verify connection string
   - Ensure database permissions

2. **API Request Failures**
   - Check backend server status
   - Verify API endpoints
   - Review network connectivity

3. **Authentication Issues**
   - Clear browser cache
   - Check JWT token validity
   - Verify user credentials

4. **Performance Issues**
   - Monitor database queries
   - Optimize API responses
   - Check network latency

### Error Codes

- **400**: Bad Request - Invalid input data
- **401**: Unauthorized - Authentication required
- **403**: Forbidden - Insufficient permissions
- **404**: Not Found - Resource not found
- **500**: Internal Server Error - Server-side error

### Support

For technical support and bug reports:
- Email: <EMAIL>
- Documentation: /settings/documentation
- GitHub Issues: Create issue in repository

---

## 📊 System Metrics

### Performance Benchmarks
- **Page Load Time**: < 2 seconds
- **API Response Time**: < 500ms
- **Database Query Time**: < 100ms
- **Real-time Updates**: < 1 second

### Scalability
- **Concurrent Users**: 100+
- **Products**: 10,000+
- **Transactions**: 1,000,000+
- **Storage**: Unlimited (MongoDB)

### Security Features
- **Authentication**: JWT-based
- **Authorization**: Role-based access
- **Data Encryption**: In transit and at rest
- **Audit Logging**: Complete activity tracking
- **Backup**: Automated daily backups

---

## 🔄 Version History

### v1.0.0 (Current)
- ✅ Complete POS functionality
- ✅ Real-time database integration
- ✅ Advanced analytics and forecasting
- ✅ Multi-language support
- ✅ Comprehensive settings system
- ✅ Data management and backup

### Roadmap
- **v1.1.0**: Mobile app integration
- **v1.2.0**: Advanced reporting
- **v1.3.0**: Multi-location support
- **v2.0.0**: E-commerce integration

---

## 🔄 System Integration Diagrams

### Complete System Flow
```mermaid
graph TB
    subgraph "Frontend Layer"
        A[Next.js App]
        B[React Components]
        C[Context Providers]
        D[UI Library]
    end

    subgraph "API Layer"
        E[REST API]
        F[Authentication]
        G[Business Logic]
        H[Data Validation]
    end

    subgraph "Database Layer"
        I[MongoDB]
        J[Collections]
        K[Indexes]
        L[Aggregations]
    end

    subgraph "External Services"
        M[Email Service]
        N[SMS Service]
        O[Payment Gateway]
        P[Backup Service]
    end

    A --> E
    B --> E
    C --> E
    E --> F
    E --> G
    G --> H
    H --> I
    I --> J
    I --> K
    I --> L
    G --> M
    G --> N
    G --> O
    G --> P
```

### Data Flow Architecture
```mermaid
graph LR
    subgraph "User Interface"
        A[User Action]
        B[Form Input]
        C[Button Click]
    end

    subgraph "State Management"
        D[React State]
        E[Context API]
        F[Local Storage]
    end

    subgraph "API Communication"
        G[API Client]
        H[HTTP Requests]
        I[Response Handling]
    end

    subgraph "Backend Processing"
        J[Route Handler]
        K[Middleware]
        L[Business Logic]
        M[Database Query]
    end

    subgraph "Data Storage"
        N[MongoDB]
        O[Collections]
        P[Real-time Sync]
    end

    A --> D
    B --> D
    C --> D
    D --> E
    E --> F
    D --> G
    G --> H
    H --> I
    I --> J
    J --> K
    K --> L
    L --> M
    M --> N
    N --> O
    O --> P
    P --> E
```

### Security Architecture
```mermaid
graph TD
    subgraph "Client Security"
        A[HTTPS/TLS]
        B[JWT Storage]
        C[Input Validation]
        D[XSS Protection]
    end

    subgraph "API Security"
        E[Authentication]
        F[Authorization]
        G[Rate Limiting]
        H[CORS Policy]
    end

    subgraph "Database Security"
        I[Connection Encryption]
        J[Access Control]
        K[Data Encryption]
        L[Audit Logging]
    end

    A --> E
    B --> F
    C --> G
    D --> H
    E --> I
    F --> J
    G --> K
    H --> L
```

## 📈 Performance Optimization

### Frontend Optimization
- **Code Splitting**: Dynamic imports for route-based splitting
- **Image Optimization**: Next.js Image component with lazy loading
- **Bundle Analysis**: Webpack bundle analyzer for size optimization
- **Caching Strategy**: Browser caching and service workers
- **Minification**: CSS and JavaScript minification

### Backend Optimization
- **Database Indexing**: Optimized indexes for frequent queries
- **Query Optimization**: Aggregation pipelines and efficient queries
- **Caching Layer**: Redis for session and data caching
- **Connection Pooling**: MongoDB connection pool management
- **Response Compression**: Gzip compression for API responses

### Database Optimization
```javascript
// Optimized Indexes
db.products.createIndex({ "name": "text", "description": "text" })
db.products.createIndex({ "category._id": 1, "isActive": 1 })
db.sales.createIndex({ "createdAt": -1 })
db.sales.createIndex({ "customer._id": 1, "createdAt": -1 })
db.customers.createIndex({ "email": 1 }, { unique: true })
db.suppliers.createIndex({ "code": 1 }, { unique: true })

// Aggregation Pipeline Example
db.sales.aggregate([
  { $match: { createdAt: { $gte: startDate, $lte: endDate } } },
  { $group: {
      _id: { $dateToString: { format: "%Y-%m-%d", date: "$createdAt" } },
      totalSales: { $sum: "$total" },
      orderCount: { $sum: 1 }
    }
  },
  { $sort: { "_id": 1 } }
])
```

## 🔐 Security Implementation

### Authentication Flow
```javascript
// JWT Token Structure
{
  "header": {
    "alg": "HS256",
    "typ": "JWT"
  },
  "payload": {
    "userId": "user_id",
    "email": "<EMAIL>",
    "role": "admin",
    "permissions": ["read", "write", "delete"],
    "iat": 1640995200,
    "exp": 1641081600
  }
}

// Token Validation Middleware
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization']
  const token = authHeader && authHeader.split(' ')[1]

  if (!token) {
    return res.status(401).json({ error: 'Access token required' })
  }

  jwt.verify(token, process.env.JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json({ error: 'Invalid token' })
    }
    req.user = user
    next()
  })
}
```

### Data Validation
```javascript
// Input Validation Schema
const productSchema = {
  name: {
    type: 'string',
    required: true,
    minLength: 2,
    maxLength: 100
  },
  price: {
    type: 'number',
    required: true,
    minimum: 0
  },
  category: {
    type: 'string',
    required: true,
    pattern: '^[0-9a-fA-F]{24}$' // MongoDB ObjectId
  }
}

// Sanitization
const sanitizeInput = (input) => {
  return input
    .trim()
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
    .replace(/[<>]/g, '')
}
```

## 🧪 Testing Strategy

### Unit Testing
```javascript
// Product Service Test
describe('ProductService', () => {
  test('should create product with valid data', async () => {
    const productData = {
      name: 'Test Product',
      price: 100,
      category: '507f1f77bcf86cd799439011'
    }

    const result = await ProductService.create(productData)

    expect(result.success).toBe(true)
    expect(result.data.name).toBe('Test Product')
  })

  test('should reject invalid product data', async () => {
    const invalidData = { name: '', price: -10 }

    const result = await ProductService.create(invalidData)

    expect(result.success).toBe(false)
    expect(result.error).toContain('validation')
  })
})
```

### Integration Testing
```javascript
// API Integration Test
describe('Products API', () => {
  test('GET /products should return product list', async () => {
    const response = await request(app)
      .get('/api/products')
      .set('Authorization', `Bearer ${validToken}`)
      .expect(200)

    expect(response.body.success).toBe(true)
    expect(Array.isArray(response.body.data)).toBe(true)
  })
})
```

### End-to-End Testing
```javascript
// E2E Test with Playwright
test('complete POS transaction', async ({ page }) => {
  await page.goto('/pos')

  // Add product to cart
  await page.click('[data-testid="product-1"]')
  await page.click('[data-testid="add-to-cart"]')

  // Select customer
  await page.click('[data-testid="select-customer"]')
  await page.fill('[data-testid="customer-search"]', 'John Doe')

  // Process payment
  await page.click('[data-testid="checkout"]')
  await page.click('[data-testid="cash-payment"]')
  await page.click('[data-testid="complete-sale"]')

  // Verify success
  await expect(page.locator('[data-testid="success-message"]')).toBeVisible()
})
```

## 🔄 Deployment Guide

### Production Environment Setup

#### Docker Configuration
```dockerfile
# Frontend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build
EXPOSE 3000
CMD ["npm", "start"]

# Backend Dockerfile
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
EXPOSE 3001
CMD ["npm", "start"]
```

#### Docker Compose
```yaml
version: '3.8'
services:
  frontend:
    build: ./frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:3001
    depends_on:
      - backend

  backend:
    build: ./backend
    ports:
      - "3001:3001"
    environment:
      - MONGODB_URI=mongodb://mongo:27017/bitstech
      - JWT_SECRET=${JWT_SECRET}
    depends_on:
      - mongo

  mongo:
    image: mongo:5
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

volumes:
  mongo_data:
```

#### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_cache_bypass $http_upgrade;
    }

    location /api {
        proxy_pass http://localhost:3001;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }
}
```

## 📊 Monitoring & Analytics

### Application Monitoring
```javascript
// Performance Monitoring
const performanceMonitor = {
  trackPageLoad: (pageName, loadTime) => {
    console.log(`Page ${pageName} loaded in ${loadTime}ms`)
    // Send to analytics service
  },

  trackAPICall: (endpoint, duration, status) => {
    console.log(`API ${endpoint}: ${duration}ms (${status})`)
    // Send to monitoring service
  },

  trackError: (error, context) => {
    console.error('Application Error:', error, context)
    // Send to error tracking service
  }
}

// Usage Analytics
const analytics = {
  trackUserAction: (action, data) => {
    // Track user interactions
    console.log(`User action: ${action}`, data)
  },

  trackBusinessMetric: (metric, value) => {
    // Track business KPIs
    console.log(`Business metric ${metric}: ${value}`)
  }
}
```

### Health Checks
```javascript
// Backend Health Check
app.get('/health', async (req, res) => {
  const health = {
    status: 'OK',
    timestamp: new Date().toISOString(),
    services: {
      database: await checkDatabaseConnection(),
      memory: process.memoryUsage(),
      uptime: process.uptime()
    }
  }

  res.json(health)
})

// Database Connection Check
const checkDatabaseConnection = async () => {
  try {
    await mongoose.connection.db.admin().ping()
    return { status: 'connected', latency: '< 10ms' }
  } catch (error) {
    return { status: 'disconnected', error: error.message }
  }
}
```

## 🔧 Maintenance & Support

### Backup Strategy
```javascript
// Automated Backup Script
const createBackup = async () => {
  const timestamp = new Date().toISOString().split('T')[0]
  const backupName = `bitstech-backup-${timestamp}`

  try {
    // Export collections
    const collections = ['products', 'sales', 'customers', 'suppliers']
    const backupData = {}

    for (const collection of collections) {
      backupData[collection] = await db.collection(collection).find({}).toArray()
    }

    // Save to file
    fs.writeFileSync(`./backups/${backupName}.json`, JSON.stringify(backupData, null, 2))

    // Upload to cloud storage
    await uploadToCloudStorage(`./backups/${backupName}.json`)

    console.log(`✅ Backup created: ${backupName}`)
  } catch (error) {
    console.error('❌ Backup failed:', error)
  }
}

// Schedule daily backups
cron.schedule('0 2 * * *', createBackup) // 2 AM daily
```

### Log Management
```javascript
// Structured Logging
const logger = {
  info: (message, data = {}) => {
    console.log(JSON.stringify({
      level: 'info',
      message,
      data,
      timestamp: new Date().toISOString()
    }))
  },

  error: (message, error = {}) => {
    console.error(JSON.stringify({
      level: 'error',
      message,
      error: error.message || error,
      stack: error.stack,
      timestamp: new Date().toISOString()
    }))
  }
}
```

---

*This comprehensive documentation covers all aspects of the BitsTech POS System. For additional support, please refer to the in-app help system or contact technical support.*

**Last Updated**: December 2024
**Version**: 1.0.0
**Maintained by**: BitsTech Development Team
