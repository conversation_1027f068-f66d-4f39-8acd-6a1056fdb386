const Sale = require('../models/Sale');
const Product = require('../models/Product');
const User = require('../models/User');
const asyncHandler = require('../utils/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get all sales
// @route   GET /api/sales
// @access  Private
const getSales = asyncHandler(async (req, res, next) => {
    // Build query
    let query = {};
    
    // Filter by date range
    if (req.query.startDate || req.query.endDate) {
        query.createdAt = {};
        if (req.query.startDate) {
            query.createdAt.$gte = new Date(req.query.startDate);
        }
        if (req.query.endDate) {
            query.createdAt.$lte = new Date(req.query.endDate);
        }
    }
    
    // Filter by cashier
    if (req.query.cashier) {
        query.cashier = req.query.cashier;
    }
    
    // Filter by status
    if (req.query.status) {
        query.status = req.query.status;
    }
    
    // Filter by payment method
    if (req.query.paymentMethod) {
        query.paymentMethod = req.query.paymentMethod;
    }
    
    // Search by sale number or customer name
    if (req.query.search) {
        query.$or = [
            { saleNumber: new RegExp(req.query.search, 'i') },
            { 'customer.name': new RegExp(req.query.search, 'i') },
            { 'customer.phone': new RegExp(req.query.search, 'i') }
        ];
    }
    
    // Pagination
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 20;
    const startIndex = (page - 1) * limit;
    
    // Sort
    let sort = {};
    if (req.query.sortBy) {
        const parts = req.query.sortBy.split(':');
        sort[parts[0]] = parts[1] === 'desc' ? -1 : 1;
    } else {
        sort = { createdAt: -1 };
    }
    
    try {
        const sales = await Sale.find(query)
            .populate('cashier', 'firstName lastName username')
            .populate('items.product', 'name sku')
            .sort(sort)
            .skip(startIndex)
            .limit(limit);
            
        const total = await Sale.countDocuments(query);
        
        // Pagination result
        const pagination = {};
        
        if (startIndex + limit < total) {
            pagination.next = {
                page: page + 1,
                limit
            };
        }
        
        if (startIndex > 0) {
            pagination.prev = {
                page: page - 1,
                limit
            };
        }
        
        res.status(200).json({
            success: true,
            count: sales.length,
            total,
            pagination,
            data: sales
        });
    } catch (error) {
        return next(new ErrorResponse('Error fetching sales', 500));
    }
});

// @desc    Get single sale
// @route   GET /api/sales/:id
// @access  Private
const getSale = asyncHandler(async (req, res, next) => {
    const sale = await Sale.findById(req.params.id)
        .populate('cashier', 'firstName lastName username email')
        .populate('items.product', 'name sku category')
        .populate('refund.refundedBy', 'firstName lastName username');
        
    if (!sale) {
        return next(new ErrorResponse(`Sale not found with id of ${req.params.id}`, 404));
    }
    
    res.status(200).json({
        success: true,
        data: sale
    });
});

// @desc    Create new sale
// @route   POST /api/sales
// @access  Private (Cashier, Manager, Admin)
const createSale = asyncHandler(async (req, res, next) => {
    const {
        items,
        customer,
        paymentMethod,
        paymentDetails,
        amountPaid,
        currency = 'MMK',
        notes
    } = req.body;
    
    // Validate items
    if (!items || items.length === 0) {
        return next(new ErrorResponse('Sale must have at least one item', 400));
    }
    
    // Calculate totals and validate products
    let subtotal = 0;
    let totalTax = 0;
    let totalDiscount = 0;
    const saleItems = [];
    
    for (const item of items) {
        // Get product details
        const product = await Product.findById(item.productId);
        if (!product) {
            return next(new ErrorResponse(`Product not found with id ${item.productId}`, 404));
        }
        
        // Check stock availability
        if (product.inventory.quantity < item.quantity) {
            return next(new ErrorResponse(`Insufficient stock for ${product.name}. Available: ${product.inventory.quantity}`, 400));
        }
        
        // Calculate item totals
        const itemSubtotal = product.price * item.quantity;
        const itemDiscount = item.discount || 0;
        const itemTax = (itemSubtotal - itemDiscount) * (product.taxRate / 100);
        const itemTotal = itemSubtotal - itemDiscount + itemTax;
        
        saleItems.push({
            product: product._id,
            productName: product.name,
            sku: product.sku,
            quantity: item.quantity,
            unitPrice: product.price,
            totalPrice: itemTotal,
            discount: itemDiscount,
            tax: itemTax
        });
        
        subtotal += itemSubtotal;
        totalDiscount += itemDiscount;
        totalTax += itemTax;
    }
    
    const totalAmount = subtotal - totalDiscount + totalTax;
    
    // Validate payment
    if (amountPaid < totalAmount) {
        return next(new ErrorResponse('Amount paid is less than total amount', 400));
    }
    
    const changeAmount = amountPaid - totalAmount;
    
    // Create sale
    const sale = await Sale.create({
        cashier: req.user.id,
        customer,
        items: saleItems,
        subtotal,
        totalDiscount,
        totalTax,
        totalAmount,
        currency,
        paymentMethod,
        paymentDetails,
        amountPaid,
        changeAmount,
        notes,
        status: 'completed'
    });
    
    // Update product inventory
    for (const item of items) {
        await Product.findByIdAndUpdate(
            item.productId,
            { $inc: { 'inventory.quantity': -item.quantity } }
        );
    }
    
    // Populate sale data
    await sale.populate('cashier', 'firstName lastName username');
    await sale.populate('items.product', 'name sku category');
    
    res.status(201).json({
        success: true,
        data: sale
    });
});

// @desc    Update sale
// @route   PUT /api/sales/:id
// @access  Private (Manager, Admin)
const updateSale = asyncHandler(async (req, res, next) => {
    let sale = await Sale.findById(req.params.id);
    
    if (!sale) {
        return next(new ErrorResponse(`Sale not found with id of ${req.params.id}`, 404));
    }
    
    // Only allow updating certain fields
    const allowedFields = ['customer', 'notes', 'status'];
    const updateData = {};
    
    allowedFields.forEach(field => {
        if (req.body[field] !== undefined) {
            updateData[field] = req.body[field];
        }
    });
    
    sale = await Sale.findByIdAndUpdate(req.params.id, updateData, {
        new: true,
        runValidators: true
    }).populate('cashier', 'firstName lastName username')
      .populate('items.product', 'name sku');
    
    res.status(200).json({
        success: true,
        data: sale
    });
});

// @desc    Cancel sale
// @route   PUT /api/sales/:id/cancel
// @access  Private (Manager, Admin)
const cancelSale = asyncHandler(async (req, res, next) => {
    const sale = await Sale.findById(req.params.id);
    
    if (!sale) {
        return next(new ErrorResponse(`Sale not found with id of ${req.params.id}`, 404));
    }
    
    if (sale.status === 'cancelled') {
        return next(new ErrorResponse('Sale is already cancelled', 400));
    }
    
    if (sale.status === 'refunded') {
        return next(new ErrorResponse('Cannot cancel a refunded sale', 400));
    }
    
    // Restore inventory
    for (const item of sale.items) {
        await Product.findByIdAndUpdate(
            item.product,
            { $inc: { 'inventory.quantity': item.quantity } }
        );
    }
    
    // Update sale status
    sale.status = 'cancelled';
    await sale.save();
    
    res.status(200).json({
        success: true,
        data: sale
    });
});

// @desc    Get sales statistics
// @route   GET /api/sales/stats
// @access  Private
const getSalesStats = asyncHandler(async (req, res, next) => {
    const { period = 'today' } = req.query;
    
    let startDate, endDate;
    const now = new Date();
    
    switch (period) {
        case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
            break;
        case 'week':
            const weekStart = new Date(now.setDate(now.getDate() - now.getDay()));
            startDate = new Date(weekStart.getFullYear(), weekStart.getMonth(), weekStart.getDate());
            endDate = new Date();
            break;
        case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 1);
            break;
        case 'year':
            startDate = new Date(now.getFullYear(), 0, 1);
            endDate = new Date(now.getFullYear() + 1, 0, 1);
            break;
        default:
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
            endDate = new Date(now.getFullYear(), now.getMonth(), now.getDate() + 1);
    }
    
    const stats = await Sale.aggregate([
        {
            $match: {
                createdAt: { $gte: startDate, $lt: endDate },
                status: { $in: ['completed'] }
            }
        },
        {
            $group: {
                _id: null,
                totalSales: { $sum: 1 },
                totalRevenue: { $sum: '$totalAmount' },
                totalItems: { $sum: '$totalItems' },
                averageOrderValue: { $avg: '$totalAmount' }
            }
        }
    ]);
    
    const result = stats[0] || {
        totalSales: 0,
        totalRevenue: 0,
        totalItems: 0,
        averageOrderValue: 0
    };
    
    res.status(200).json({
        success: true,
        data: {
            period,
            startDate,
            endDate,
            ...result
        }
    });
});

module.exports = {
    getSales,
    getSale,
    createSale,
    updateSale,
    cancelSale,
    getSalesStats
};
