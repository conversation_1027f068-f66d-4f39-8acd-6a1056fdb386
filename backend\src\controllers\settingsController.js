const Settings = require('../models/Settings');
const ExchangeRate = require('../models/ExchangeRate');
const User = require('../models/User');
const asyncHandler = require('../utils/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');
const fs = require('fs').promises;
const path = require('path');
const { exec } = require('child_process');
const { promisify } = require('util');
const nodemailer = require('nodemailer');

const execAsync = promisify(exec);

// @desc    Get all settings
// @route   GET /api/settings
// @access  Private
const getSettings = asyncHandler(async (req, res, next) => {
    const settings = await Settings.getCurrentSettings();
    const exchangeRates = await ExchangeRate.getCurrentRates();
    
    res.status(200).json({
        success: true,
        data: {
            settings,
            exchangeRates: exchangeRates.rates
        }
    });
});

// @desc    Update general settings
// @route   PUT /api/settings/general
// @access  Private (Admin, Manager)
const updateGeneralSettings = asyncHandler(async (req, res, next) => {
    const { storeName, storeEmail, storePhone, storeAddress } = req.body;
    
    const settings = await Settings.getCurrentSettings();
    
    const updatedSettings = await settings.updateSettings({
        storeName,
        storeEmail,
        storePhone,
        storeAddress
    }, req.user.id);
    
    res.status(200).json({
        success: true,
        data: updatedSettings,
        message: 'General settings updated successfully'
    });
});

// @desc    Update tax settings
// @route   PUT /api/settings/tax
// @access  Private (Admin, Manager)
const updateTaxSettings = asyncHandler(async (req, res, next) => {
    const { defaultTaxRate, taxName, taxNumber } = req.body;
    
    // Validate tax rate
    if (defaultTaxRate !== undefined && (defaultTaxRate < 0 || defaultTaxRate > 100)) {
        return next(new ErrorResponse('Tax rate must be between 0 and 100', 400));
    }
    
    const settings = await Settings.getCurrentSettings();
    
    const updatedSettings = await settings.updateSettings({
        defaultTaxRate,
        taxName,
        taxNumber
    }, req.user.id);
    
    res.status(200).json({
        success: true,
        data: updatedSettings,
        message: 'Tax settings updated successfully'
    });
});

// @desc    Update currency settings
// @route   PUT /api/settings/currency
// @access  Private (Admin, Manager)
const updateCurrencySettings = asyncHandler(async (req, res, next) => {
    const { 
        primaryCurrency, 
        currencyPosition, 
        decimalPlaces, 
        thousandSeparator, 
        decimalSeparator 
    } = req.body;
    
    // Validate currency
    if (primaryCurrency && !['MMK', 'USD', 'THB'].includes(primaryCurrency)) {
        return next(new ErrorResponse('Invalid primary currency', 400));
    }
    
    // Validate decimal places
    if (decimalPlaces !== undefined && (decimalPlaces < 0 || decimalPlaces > 4)) {
        return next(new ErrorResponse('Decimal places must be between 0 and 4', 400));
    }
    
    const settings = await Settings.getCurrentSettings();
    
    const updatedSettings = await settings.updateSettings({
        primaryCurrency,
        currencyPosition,
        decimalPlaces,
        thousandSeparator,
        decimalSeparator
    }, req.user.id);
    
    res.status(200).json({
        success: true,
        data: updatedSettings,
        message: 'Currency settings updated successfully'
    });
});

// @desc    Update theme settings
// @route   PUT /api/settings/theme
// @access  Private
const updateThemeSettings = asyncHandler(async (req, res, next) => {
    const { defaultTheme, defaultColorScheme, defaultLanguage } = req.body;
    
    // Validate theme
    if (defaultTheme && !['light', 'dark', 'system'].includes(defaultTheme)) {
        return next(new ErrorResponse('Invalid theme', 400));
    }
    
    // Validate color scheme
    if (defaultColorScheme && !['blue', 'green', 'purple', 'orange', 'pink', 'indigo'].includes(defaultColorScheme)) {
        return next(new ErrorResponse('Invalid color scheme', 400));
    }
    
    // Validate language
    if (defaultLanguage && !['en', 'mm', 'th'].includes(defaultLanguage)) {
        return next(new ErrorResponse('Invalid language', 400));
    }
    
    const settings = await Settings.getCurrentSettings();
    
    const updatedSettings = await settings.updateSettings({
        defaultTheme,
        defaultColorScheme,
        defaultLanguage
    }, req.user.id);
    
    res.status(200).json({
        success: true,
        data: updatedSettings,
        message: 'Theme settings updated successfully'
    });
});

// @desc    Update receipt settings
// @route   PUT /api/settings/receipt
// @access  Private (Admin, Manager)
const updateReceiptSettings = asyncHandler(async (req, res, next) => {
    const { receiptHeader, receiptFooter, showLogo, logoUrl } = req.body;
    
    const settings = await Settings.getCurrentSettings();
    
    const updatedSettings = await settings.updateSettings({
        receiptHeader,
        receiptFooter,
        showLogo,
        logoUrl
    }, req.user.id);
    
    res.status(200).json({
        success: true,
        data: updatedSettings,
        message: 'Receipt settings updated successfully'
    });
});

// @desc    Update inventory settings
// @route   PUT /api/settings/inventory
// @access  Private (Admin, Manager)
const updateInventorySettings = asyncHandler(async (req, res, next) => {
    const { 
        lowStockThreshold, 
        enableLowStockAlerts, 
        autoReorderEnabled, 
        autoReorderQuantity 
    } = req.body;
    
    // Validate threshold
    if (lowStockThreshold !== undefined && lowStockThreshold < 0) {
        return next(new ErrorResponse('Low stock threshold must be non-negative', 400));
    }
    
    // Validate auto reorder quantity
    if (autoReorderQuantity !== undefined && autoReorderQuantity < 1) {
        return next(new ErrorResponse('Auto reorder quantity must be at least 1', 400));
    }
    
    const settings = await Settings.getCurrentSettings();
    
    const updatedSettings = await settings.updateSettings({
        lowStockThreshold,
        enableLowStockAlerts,
        autoReorderEnabled,
        autoReorderQuantity
    }, req.user.id);
    
    res.status(200).json({
        success: true,
        data: updatedSettings,
        message: 'Inventory settings updated successfully'
    });
});

// @desc    Update POS settings
// @route   PUT /api/settings/pos
// @access  Private (Admin, Manager)
const updatePOSSettings = asyncHandler(async (req, res, next) => {
    const { 
        enableBarcode, 
        enableCustomerDisplay, 
        enableReceiptPrinting, 
        defaultPaymentMethod 
    } = req.body;
    
    // Validate payment method
    const validPaymentMethods = ['cash', 'card', 'kbz_pay', 'wave_money', 'nug_pay', 'thailand_bank'];
    if (defaultPaymentMethod && !validPaymentMethods.includes(defaultPaymentMethod)) {
        return next(new ErrorResponse('Invalid payment method', 400));
    }
    
    const settings = await Settings.getCurrentSettings();
    
    const updatedSettings = await settings.updateSettings({
        enableBarcode,
        enableCustomerDisplay,
        enableReceiptPrinting,
        defaultPaymentMethod
    }, req.user.id);
    
    res.status(200).json({
        success: true,
        data: updatedSettings,
        message: 'POS settings updated successfully'
    });
});

// @desc    Reset settings to default
// @route   POST /api/settings/reset
// @access  Private (Admin)
const resetSettings = asyncHandler(async (req, res, next) => {
    // Delete current settings
    await Settings.deleteMany({});
    
    // Create new default settings
    const defaultSettings = new Settings({});
    await defaultSettings.save();
    
    res.status(200).json({
        success: true,
        data: defaultSettings,
        message: 'Settings reset to default values'
    });
});

// @desc    Update notification settings
// @route   PUT /api/settings/notifications
// @access  Private
const updateNotificationSettings = asyncHandler(async (req, res, next) => {
    const { emailNotifications, smsNotifications, pushNotifications } = req.body;

    const settings = await Settings.getCurrentSettings();

    const updatedSettings = await settings.updateSettings({
        emailNotifications,
        smsNotifications,
        pushNotifications
    }, req.user.id);

    res.status(200).json({
        success: true,
        data: updatedSettings,
        message: 'Notification settings updated successfully'
    });
});

// @desc    Update security settings
// @route   PUT /api/settings/security
// @access  Private (Admin)
const updateSecuritySettings = asyncHandler(async (req, res, next) => {
    const {
        sessionTimeout,
        requirePasswordChange,
        passwordExpiryDays
    } = req.body;

    // Validate session timeout
    if (sessionTimeout !== undefined && (sessionTimeout < 5 || sessionTimeout > 480)) {
        return next(new ErrorResponse('Session timeout must be between 5 and 480 minutes', 400));
    }

    // Validate password expiry days
    if (passwordExpiryDays !== undefined && (passwordExpiryDays < 30 || passwordExpiryDays > 365)) {
        return next(new ErrorResponse('Password expiry days must be between 30 and 365', 400));
    }

    const settings = await Settings.getCurrentSettings();

    const updatedSettings = await settings.updateSettings({
        sessionTimeout,
        requirePasswordChange,
        passwordExpiryDays
    }, req.user.id);

    res.status(200).json({
        success: true,
        data: updatedSettings,
        message: 'Security settings updated successfully'
    });
});

// @desc    Backup database
// @route   POST /api/settings/backup
// @access  Private (Admin)
const backupDatabase = asyncHandler(async (req, res, next) => {
    try {
        const backupDir = path.join(__dirname, '../../backups');

        // Create backup directory if it doesn't exist
        try {
            await fs.access(backupDir);
        } catch {
            await fs.mkdir(backupDir, { recursive: true });
        }

        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupFile = path.join(backupDir, `backup-${timestamp}.json`);

        // Simple backup - in production, use proper database backup tools
        const settings = await Settings.find({});
        const users = await User.find({}).select('-password');
        const exchangeRates = await ExchangeRate.find({});

        const backupData = {
            timestamp: new Date(),
            settings,
            users,
            exchangeRates,
            version: '1.0.0'
        };

        await fs.writeFile(backupFile, JSON.stringify(backupData, null, 2));

        res.status(200).json({
            success: true,
            message: 'Database backup created successfully',
            data: {
                filename: path.basename(backupFile),
                size: (await fs.stat(backupFile)).size,
                timestamp: new Date()
            }
        });
    } catch (error) {
        console.error('Backup error:', error);
        return next(new ErrorResponse('Failed to create backup', 500));
    }
});

// @desc    Get backup history
// @route   GET /api/settings/backup-history
// @access  Private (Admin)
const getBackupHistory = asyncHandler(async (req, res, next) => {
    try {
        const backupDir = path.join(__dirname, '../../backups');

        try {
            const files = await fs.readdir(backupDir);
            const backupFiles = files.filter(file => file.startsWith('backup-') && file.endsWith('.json'));

            const backups = await Promise.all(
                backupFiles.map(async (file) => {
                    const filePath = path.join(backupDir, file);
                    const stats = await fs.stat(filePath);
                    return {
                        filename: file,
                        size: stats.size,
                        created: stats.birthtime,
                        modified: stats.mtime
                    };
                })
            );

            // Sort by creation date (newest first)
            backups.sort((a, b) => new Date(b.created) - new Date(a.created));

            res.status(200).json({
                success: true,
                data: backups,
                count: backups.length
            });
        } catch (error) {
            // Backup directory doesn't exist
            res.status(200).json({
                success: true,
                data: [],
                count: 0
            });
        }
    } catch (error) {
        console.error('Get backup history error:', error);
        return next(new ErrorResponse('Failed to get backup history', 500));
    }
});

// @desc    Test notification
// @route   POST /api/settings/test-notification
// @access  Private (Admin)
const testNotification = asyncHandler(async (req, res, next) => {
    const { type, recipient } = req.body;

    try {
        switch (type) {
            case 'email':
                // Test email notification
                const transporter = nodemailer.createTransporter({
                    host: process.env.SMTP_HOST || 'localhost',
                    port: process.env.SMTP_PORT || 587,
                    secure: false,
                    auth: {
                        user: process.env.SMTP_USER,
                        pass: process.env.SMTP_PASS
                    }
                });

                await transporter.sendMail({
                    from: process.env.SMTP_FROM || 'BitsTech POS <<EMAIL>>',
                    to: recipient || req.user.email,
                    subject: 'Test Notification - BitsTech POS',
                    text: 'This is a test notification from BitsTech POS system.',
                    html: '<p>This is a test notification from <strong>BitsTech POS</strong> system.</p>'
                });
                break;

            case 'sms':
                // SMS testing would require SMS service integration
                // For now, just simulate success
                break;

            case 'push':
                // Push notification testing would require push service integration
                // For now, just simulate success
                break;

            default:
                return next(new ErrorResponse('Invalid notification type', 400));
        }

        res.status(200).json({
            success: true,
            message: `Test ${type} notification sent successfully`
        });
    } catch (error) {
        console.error('Test notification error:', error);
        return next(new ErrorResponse(`Failed to send test ${type} notification`, 500));
    }
});

// @desc    Get security logs
// @route   GET /api/settings/security-logs
// @access  Private (Admin)
const getSecurityLogs = asyncHandler(async (req, res, next) => {
    // In a real application, you would have a SecurityLog model
    // For now, return mock data
    const mockLogs = [
        {
            id: '1',
            timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 minutes ago
            event: 'User Login',
            user: req.user.email,
            ip: req.ip || '127.0.0.1',
            status: 'success',
            details: 'Successful login'
        },
        {
            id: '2',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
            event: 'Settings Update',
            user: req.user.email,
            ip: req.ip || '127.0.0.1',
            status: 'success',
            details: 'Updated company settings'
        },
        {
            id: '3',
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
            event: 'Failed Login',
            user: '<EMAIL>',
            ip: '*************',
            status: 'failed',
            details: 'Invalid credentials'
        }
    ];

    res.status(200).json({
        success: true,
        data: mockLogs,
        count: mockLogs.length
    });
});

module.exports = {
    getSettings,
    updateGeneralSettings,
    updateTaxSettings,
    updateCurrencySettings,
    updateThemeSettings,
    updateReceiptSettings,
    updateInventorySettings,
    updatePOSSettings,
    updateNotificationSettings,
    updateSecuritySettings,
    backupDatabase,
    getBackupHistory,
    testNotification,
    getSecurityLogs,
    resetSettings
};
