// Settings Sync Testing Module
// This module tests real-time settings synchronization across tabs and components

import { getSettingsSync } from './settings-sync';

interface TestResult {
  test: string;
  passed: boolean;
  message: string;
  duration: number;
}

class SettingsSyncTester {
  private results: TestResult[] = [];
  private settingsSync: any;

  constructor() {
    this.settingsSync = getSettingsSync();
  }

  private async runTest(testName: string, testFn: () => Promise<boolean>): Promise<TestResult> {
    const startTime = Date.now();
    
    try {
      const passed = await testFn();
      const duration = Date.now() - startTime;
      
      const result: TestResult = {
        test: testName,
        passed,
        message: passed ? 'Test passed' : 'Test failed',
        duration
      };
      
      this.results.push(result);
      return result;
    } catch (error) {
      const duration = Date.now() - startTime;
      const result: TestResult = {
        test: testName,
        passed: false,
        message: `Test error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        duration
      };
      
      this.results.push(result);
      return result;
    }
  }

  // Test 1: Settings sync initialization
  async testInitialization(): Promise<TestResult> {
    return this.runTest('Settings Sync Initialization', async () => {
      if (!this.settingsSync) {
        throw new Error('Settings sync not initialized');
      }
      
      // Check if settings sync has required methods
      const requiredMethods = ['updateSettings', 'getSettings', 'subscribe', 'unsubscribe'];
      for (const method of requiredMethods) {
        if (typeof this.settingsSync[method] !== 'function') {
          throw new Error(`Missing method: ${method}`);
        }
      }
      
      return true;
    });
  }

  // Test 2: Settings update and retrieval
  async testSettingsUpdateAndRetrieval(): Promise<TestResult> {
    return this.runTest('Settings Update and Retrieval', async () => {
      const testSettings = {
        theme: 'dark',
        language: 'mm',
        currency: 'USD'
      };
      
      // Update settings
      await this.settingsSync.updateSettings(testSettings);
      
      // Wait a bit for sync
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Retrieve settings
      const retrievedSettings = this.settingsSync.getSettings();
      
      // Check if settings were updated correctly
      return (
        retrievedSettings.theme === testSettings.theme &&
        retrievedSettings.language === testSettings.language &&
        retrievedSettings.currency === testSettings.currency
      );
    });
  }

  // Test 3: Real-time sync across components
  async testRealTimeSync(): Promise<TestResult> {
    return this.runTest('Real-time Sync Across Components', async () => {
      let syncReceived = false;
      const testSettings = {
        theme: 'light',
        language: 'en',
        currency: 'MMK'
      };
      
      // Subscribe to settings changes
      const unsubscribe = this.settingsSync.subscribe((settings: any) => {
        if (
          settings.theme === testSettings.theme &&
          settings.language === testSettings.language &&
          settings.currency === testSettings.currency
        ) {
          syncReceived = true;
        }
      });
      
      // Update settings
      await this.settingsSync.updateSettings(testSettings);
      
      // Wait for sync
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Cleanup
      unsubscribe();
      
      return syncReceived;
    });
  }

  // Test 4: Multiple subscribers
  async testMultipleSubscribers(): Promise<TestResult> {
    return this.runTest('Multiple Subscribers', async () => {
      let subscriber1Received = false;
      let subscriber2Received = false;
      let subscriber3Received = false;
      
      const testSettings = {
        theme: 'system',
        language: 'th',
        currency: 'THB'
      };
      
      // Create multiple subscribers
      const unsubscribe1 = this.settingsSync.subscribe(() => {
        subscriber1Received = true;
      });
      
      const unsubscribe2 = this.settingsSync.subscribe(() => {
        subscriber2Received = true;
      });
      
      const unsubscribe3 = this.settingsSync.subscribe(() => {
        subscriber3Received = true;
      });
      
      // Update settings
      await this.settingsSync.updateSettings(testSettings);
      
      // Wait for sync
      await new Promise(resolve => setTimeout(resolve, 200));
      
      // Cleanup
      unsubscribe1();
      unsubscribe2();
      unsubscribe3();
      
      return subscriber1Received && subscriber2Received && subscriber3Received;
    });
  }

  // Test 5: Settings persistence
  async testSettingsPersistence(): Promise<TestResult> {
    return this.runTest('Settings Persistence', async () => {
      const testSettings = {
        theme: 'dark',
        language: 'mm',
        currency: 'USD',
        testValue: 'persistence-test'
      };
      
      // Update settings
      await this.settingsSync.updateSettings(testSettings);
      
      // Wait for persistence
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Check localStorage
      const storedSettings = localStorage.getItem('bitstech_settings');
      if (!storedSettings) {
        throw new Error('Settings not persisted to localStorage');
      }
      
      const parsedSettings = JSON.parse(storedSettings);
      return parsedSettings.testValue === 'persistence-test';
    });
  }

  // Test 6: Error handling
  async testErrorHandling(): Promise<TestResult> {
    return this.runTest('Error Handling', async () => {
      try {
        // Try to update with invalid settings
        await this.settingsSync.updateSettings(null);
        return false; // Should have thrown an error
      } catch (error) {
        // Error handling worked correctly
        return true;
      }
    });
  }

  // Test 7: Performance test
  async testPerformance(): Promise<TestResult> {
    return this.runTest('Performance Test', async () => {
      const iterations = 100;
      const startTime = Date.now();
      
      // Perform multiple rapid updates
      for (let i = 0; i < iterations; i++) {
        await this.settingsSync.updateSettings({
          theme: i % 2 === 0 ? 'light' : 'dark',
          testIteration: i
        });
      }
      
      const duration = Date.now() - startTime;
      
      // Should complete within reasonable time (less than 5 seconds)
      return duration < 5000;
    });
  }

  // Test 8: Cross-tab simulation
  async testCrossTabSimulation(): Promise<TestResult> {
    return this.runTest('Cross-tab Simulation', async () => {
      let storageEventReceived = false;
      
      // Listen for storage events (simulates cross-tab communication)
      const handleStorageEvent = (event: StorageEvent) => {
        if (event.key === 'bitstech_settings') {
          storageEventReceived = true;
        }
      };
      
      window.addEventListener('storage', handleStorageEvent);
      
      // Simulate external tab updating settings
      const testSettings = {
        theme: 'light',
        language: 'en',
        crossTabTest: true
      };
      
      // Manually trigger storage event (simulates another tab)
      localStorage.setItem('bitstech_settings', JSON.stringify(testSettings));
      window.dispatchEvent(new StorageEvent('storage', {
        key: 'bitstech_settings',
        newValue: JSON.stringify(testSettings),
        storageArea: localStorage
      }));
      
      // Wait for event processing
      await new Promise(resolve => setTimeout(resolve, 100));
      
      // Cleanup
      window.removeEventListener('storage', handleStorageEvent);
      
      return storageEventReceived;
    });
  }

  // Run all tests
  async runAllTests(): Promise<TestResult[]> {
    console.log('🧪 Starting Settings Sync Tests...');
    
    this.results = [];
    
    await this.testInitialization();
    await this.testSettingsUpdateAndRetrieval();
    await this.testRealTimeSync();
    await this.testMultipleSubscribers();
    await this.testSettingsPersistence();
    await this.testErrorHandling();
    await this.testPerformance();
    await this.testCrossTabSimulation();
    
    return this.results;
  }

  // Generate test report
  generateReport(): string {
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.passed).length;
    const failedTests = totalTests - passedTests;
    const totalDuration = this.results.reduce((sum, r) => sum + r.duration, 0);
    
    let report = '\n📊 Settings Sync Test Report\n';
    report += '=' .repeat(50) + '\n';
    report += `Total Tests: ${totalTests}\n`;
    report += `Passed: ${passedTests} ✅\n`;
    report += `Failed: ${failedTests} ❌\n`;
    report += `Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%\n`;
    report += `Total Duration: ${totalDuration}ms\n\n`;
    
    report += 'Test Details:\n';
    report += '-' .repeat(50) + '\n';
    
    this.results.forEach((result, index) => {
      const status = result.passed ? '✅' : '❌';
      report += `${index + 1}. ${result.test} ${status}\n`;
      report += `   Duration: ${result.duration}ms\n`;
      if (!result.passed) {
        report += `   Error: ${result.message}\n`;
      }
      report += '\n';
    });
    
    return report;
  }
}

// Export for use in development/testing
export { SettingsSyncTester };

// Auto-run tests in development mode
if (process.env.NODE_ENV === 'development' && typeof window !== 'undefined') {
  // Add a global function to run tests
  (window as any).testSettingsSync = async () => {
    const tester = new SettingsSyncTester();
    const results = await tester.runAllTests();
    const report = tester.generateReport();
    console.log(report);
    return results;
  };
  
  console.log('🧪 Settings Sync Tester loaded. Run testSettingsSync() in console to test.');
}
