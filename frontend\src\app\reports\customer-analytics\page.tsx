'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/contexts/currency-context'
import {
  Users,
  TrendingUp,
  TrendingDown,
  ArrowLeft,
  Download,
  Star,
  Heart,
  ShoppingCart,
  DollarSign,
  Calendar,
  Target,
  Award,
  UserCheck,
  Clock,
  MapPin
} from 'lucide-react'

interface CustomerSegment {
  id: string
  name: string
  nameLocal: string
  count: number
  percentage: number
  avgSpend: number
  color: string
}

interface TopCustomer {
  _id: string
  name: string
  email: string
  phone: string
  totalSpent: number
  totalOrders: number
  avgOrderValue: number
  lastPurchase: string
  loyaltyPoints: number
  segment: string
  location: string
}

interface CustomerMetrics {
  totalCustomers: number
  newCustomers: number
  returningCustomers: number
  avgLifetimeValue: number
  avgOrderValue: number
  retentionRate: number
  churnRate: number
  loyaltyMembers: number
}

export default function CustomerAnalyticsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [dateRange, setDateRange] = useState('month')
  const [segmentFilter, setSegmentFilter] = useState('all')
  const [loading, setLoading] = useState(true)

  const [customerMetrics, setCustomerMetrics] = useState<CustomerMetrics>({
    totalCustomers: 567,
    newCustomers: 89,
    returningCustomers: 478,
    avgLifetimeValue: 2450000,
    avgOrderValue: 124000,
    retentionRate: 78.5,
    churnRate: 12.3,
    loyaltyMembers: 234
  })

  const [customerSegments, setCustomerSegments] = useState<CustomerSegment[]>([
    {
      id: 'vip',
      name: 'VIP Customers',
      nameLocal: 'VIP ဖောက်သည်များ',
      count: 45,
      percentage: 7.9,
      avgSpend: 5200000,
      color: 'bg-purple-100 text-purple-800'
    },
    {
      id: 'loyal',
      name: 'Loyal Customers',
      nameLocal: 'သစ္စာရှိ ဖောက်သည်များ',
      count: 189,
      percentage: 33.3,
      avgSpend: 1850000,
      color: 'bg-blue-100 text-blue-800'
    },
    {
      id: 'regular',
      name: 'Regular Customers',
      nameLocal: 'ပုံမှန် ဖောက်သည်များ',
      count: 234,
      percentage: 41.3,
      avgSpend: 980000,
      color: 'bg-green-100 text-green-800'
    },
    {
      id: 'new',
      name: 'New Customers',
      nameLocal: 'ဖောက်သည် အသစ်များ',
      count: 99,
      percentage: 17.5,
      avgSpend: 450000,
      color: 'bg-yellow-100 text-yellow-800'
    }
  ])

  const [topCustomers, setTopCustomers] = useState<TopCustomer[]>([
    {
      _id: '1',
      name: 'Mg Thura Aung',
      email: '<EMAIL>',
      phone: '+95-9-123456789',
      totalSpent: 8750000,
      totalOrders: 23,
      avgOrderValue: 380435,
      lastPurchase: '2024-01-14T15:30:00Z',
      loyaltyPoints: 875,
      segment: 'VIP',
      location: 'Yangon'
    },
    {
      _id: '2',
      name: 'Daw Khin Mya',
      email: '<EMAIL>',
      phone: '+95-9-987654321',
      totalSpent: 6200000,
      totalOrders: 18,
      avgOrderValue: 344444,
      lastPurchase: '2024-01-13T10:15:00Z',
      loyaltyPoints: 620,
      segment: 'VIP',
      location: 'Mandalay'
    },
    {
      _id: '3',
      name: 'Ko Zaw Min',
      email: '<EMAIL>',
      phone: '+95-9-456789123',
      totalSpent: 4850000,
      totalOrders: 15,
      avgOrderValue: 323333,
      lastPurchase: '2024-01-12T14:20:00Z',
      loyaltyPoints: 485,
      segment: 'Loyal',
      location: 'Yangon'
    },
    {
      _id: '4',
      name: 'Ma Hnin Thazin',
      email: '<EMAIL>',
      phone: '+95-9-789123456',
      totalSpent: 3900000,
      totalOrders: 12,
      avgOrderValue: 325000,
      lastPurchase: '2024-01-11T16:45:00Z',
      loyaltyPoints: 390,
      segment: 'Loyal',
      location: 'Naypyidaw'
    },
    {
      _id: '5',
      name: 'U Kyaw Soe',
      email: '<EMAIL>',
      phone: '+95-9-321654987',
      totalSpent: 2750000,
      totalOrders: 9,
      avgOrderValue: 305556,
      lastPurchase: '2024-01-10T11:30:00Z',
      loyaltyPoints: 275,
      segment: 'Regular',
      location: 'Yangon'
    }
  ])

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchCustomerAnalytics()
    }
  }, [isAuthenticated, dateRange, segmentFilter])

  const fetchCustomerAnalytics = async () => {
    try {
      setLoading(true)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock data would be fetched here based on filters
      // For now, using static data
    } catch (error) {
      console.error('Error fetching customer analytics:', error)
    } finally {
      setLoading(false)
    }
  }



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric'
    })
  }

  const getSegmentIcon = (segmentId: string) => {
    switch (segmentId) {
      case 'vip': return <Award className="h-5 w-5 text-purple-600" />
      case 'loyal': return <Heart className="h-5 w-5 text-blue-600" />
      case 'regular': return <UserCheck className="h-5 w-5 text-green-600" />
      case 'new': return <Star className="h-5 w-5 text-yellow-600" />
      default: return <Users className="h-5 w-5 text-gray-600" />
    }
  }

  const getSegmentBadge = (segment: string) => {
    switch (segment) {
      case 'VIP': return <Badge className="bg-purple-100 text-purple-800">VIP</Badge>
      case 'Loyal': return <Badge className="bg-blue-100 text-blue-800">Loyal</Badge>
      case 'Regular': return <Badge className="bg-green-100 text-green-800">Regular</Badge>
      case 'New': return <Badge className="bg-yellow-100 text-yellow-800">New</Badge>
      default: return <Badge variant="outline">{segment}</Badge>
    }
  }

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/reports')}
            className="hover:bg-orange-50 dark:hover:bg-orange-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Reports'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-600 to-red-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Users className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'ဖောက်သည် ခွဲခြမ်းစိတ်ဖြာမှု' : 'Customer Analytics'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm' 
                      ? 'ဖောက်သည် အပြုအမူ နှင့် ဝယ်ယူမှု ပုံစံများ ခွဲခြမ်းစိတ်ဖြာမှု'
                      : 'Customer behavior and purchase patterns analysis'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="w-40 bg-white/20 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="week">{language === 'mm' ? 'ဒီအပတ်' : 'This Week'}</SelectItem>
                    <SelectItem value="month">{language === 'mm' ? 'ဒီလ' : 'This Month'}</SelectItem>
                    <SelectItem value="quarter">{language === 'mm' ? 'ဒီသုံးလ' : 'This Quarter'}</SelectItem>
                  </SelectContent>
                </Select>
                <Button className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                  <Download className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ထုတ်ယူရန်' : 'Export'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း ဖောက်သည်များ' : 'Total Customers'}
                  </p>
                  <p className="text-2xl font-bold">{customerMetrics.totalCustomers}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600">+{customerMetrics.newCustomers} new</span>
                  </div>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'ပျမ်းမျှ တစ်သက် တန်ဖိုး' : 'Avg Lifetime Value'}
                  </p>
                  <p className="text-2xl font-bold">{formatCurrency(customerMetrics.avgLifetimeValue)}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'mm' ? 'တစ်ဦးချင်း' : 'Per customer'}
                  </p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'ထိန်းသိမ်းမှု နှုန်း' : 'Retention Rate'}
                  </p>
                  <p className="text-2xl font-bold">{customerMetrics.retentionRate}%</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'mm' ? 'ပြန်လာသော ဖောက်သည်များ' : 'Returning customers'}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-xl">
                  <Heart className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'သစ္စာရှိမှု အဖွဲ့ဝင်များ' : 'Loyalty Members'}
                  </p>
                  <p className="text-2xl font-bold">{customerMetrics.loyaltyMembers}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {Math.round((customerMetrics.loyaltyMembers / customerMetrics.totalCustomers) * 100)}% of total
                  </p>
                </div>
                <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-xl">
                  <Award className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Customer Segments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'ဖောက်သည် အုပ်စုများ' : 'Customer Segments'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'ဖောက်သည်များ၏ အမျိုးအစား ခွဲခြမ်းစိတ်ဖြာမှု'
                  : 'Customer categorization and behavior analysis'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {customerSegments.map((segment) => (
                  <div key={segment.id} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        {getSegmentIcon(segment.id)}
                        <span className="font-medium">
                          {language === 'mm' ? segment.nameLocal : segment.name}
                        </span>
                      </div>
                      <div className="text-right">
                        <span className="font-semibold">{segment.count}</span>
                        <span className="text-sm text-gray-600 ml-2">({segment.percentage}%)</span>
                      </div>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-600 h-2 rounded-full transition-all duration-500" 
                        style={{ width: `${segment.percentage}%` }}
                      ></div>
                    </div>
                    <div className="flex justify-between text-sm text-gray-600">
                      <span>{language === 'mm' ? 'ပျမ်းမျှ ဝယ်ယူမှု' : 'Avg Spend'}</span>
                      <span className="font-medium">{formatCurrency(segment.avgSpend)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Top Customers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-600" />
                {language === 'mm' ? 'ထိပ်တန်း ဖောက်သည်များ' : 'Top Customers'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'အများဆုံး ဝယ်ယူသော ဖောက်သည်များ'
                  : 'Highest spending customers'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topCustomers.slice(0, 5).map((customer, index) => (
                  <div key={customer._id} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-yellow-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{customer.name}</h4>
                        <div className="flex items-center gap-2">
                          {getSegmentBadge(customer.segment)}
                          <span className="text-xs text-gray-600">{customer.totalOrders} orders</span>
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-yellow-600">{formatCurrency(customer.totalSpent)}</p>
                      <p className="text-xs text-gray-600">{customer.loyaltyPoints} points</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Customer List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5 text-green-600" />
              {language === 'mm' ? 'အသေးစိত် ဖောက်သည် စာရင်း' : 'Detailed Customer List'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topCustomers.map((customer) => (
                <div key={customer._id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow">
                  <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                    {/* Customer Info */}
                    <div className="lg:col-span-2">
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 bg-blue-600 text-white rounded-full flex items-center justify-center font-bold">
                          {customer.name.charAt(0)}
                        </div>
                        <div>
                          <h4 className="font-semibold">{customer.name}</h4>
                          <p className="text-sm text-gray-600">{customer.email}</p>
                          <div className="flex items-center gap-2 mt-1">
                            {getSegmentBadge(customer.segment)}
                            <div className="flex items-center gap-1 text-xs text-gray-600">
                              <MapPin className="h-3 w-3" />
                              {customer.location}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Metrics */}
                    <div className="text-center">
                      <p className="text-lg font-bold">{formatCurrency(customer.totalSpent)}</p>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'စုစုပေါင်း ဝယ်ယူမှု' : 'Total Spent'}</p>
                    </div>

                    <div className="text-center">
                      <p className="text-lg font-bold">{customer.totalOrders}</p>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'အမှာစာများ' : 'Orders'}</p>
                    </div>

                    <div className="text-center">
                      <p className="text-lg font-bold">{formatCurrency(customer.avgOrderValue)}</p>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'ပျမ်းမျှ တန်ဖိုး' : 'Avg Order'}</p>
                    </div>

                    {/* Last Purchase & Loyalty */}
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 mb-1">
                        <Clock className="h-3 w-3 text-gray-500" />
                        <span className="text-sm">{formatDate(customer.lastPurchase)}</span>
                      </div>
                      <div className="flex items-center justify-center gap-1">
                        <Award className="h-3 w-3 text-yellow-600" />
                        <span className="text-sm font-medium">{customer.loyaltyPoints} pts</span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
