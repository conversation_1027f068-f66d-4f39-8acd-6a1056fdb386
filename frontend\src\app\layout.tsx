import type { Metadata } from "next";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";
import { SettingsProvider } from "@/contexts/settings-context";
import { ThemeProvider } from "@/contexts/theme-context";
import { ThemeCustomizationProvider } from "@/contexts/theme-customization-context";
import { <PERSON><PERSON>rencyProvider } from "@/contexts/currency-context";
import { NotificationProvider } from "@/contexts/notification-context";

export const metadata: Metadata = {
  title: "BitsTech POS System",
  description: "Modern Point-of-Sale Solution for Myanmar Businesses",
  icons: {
    icon: '/favicon.ico',
    apple: '/icons/apple-touch-icon.png',
  },
  manifest: '/manifest.json',
};

export const viewport = {
  width: 'device-width',
  initialScale: 1.0,
  maximumScale: 1.0,
  userScalable: false,
  shrinkToFit: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <head>
        <script
          dangerouslySetInnerHTML={{
            __html: `
              // Prevent flash of unstyled content
              (function() {
                try {
                  const theme = localStorage.getItem('bitstech-theme') || 'light';
                  const language = localStorage.getItem('bitstech-language') || 'mm';

                  if (theme === 'dark' || (theme === 'system' && window.matchMedia('(prefers-color-scheme: dark)').matches)) {
                    document.documentElement.classList.add('dark');
                    document.body.classList.add('dark');
                    document.documentElement.setAttribute('data-theme', 'dark');
                    document.body.setAttribute('data-theme', 'dark');
                  } else {
                    document.documentElement.classList.add('light');
                    document.body.classList.add('light');
                    document.documentElement.setAttribute('data-theme', 'light');
                    document.body.setAttribute('data-theme', 'light');
                  }

                  document.documentElement.setAttribute('data-language', language);
                  document.body.setAttribute('data-language', language);
                  document.documentElement.classList.add('lang-' + language);
                  document.body.classList.add('lang-' + language);
                } catch (e) {
                  console.error('Theme initialization error:', e);
                }
              })();
            `,
          }}
        />
      </head>
      <body
        className="font-sans antialiased transition-colors duration-300"
        suppressHydrationWarning
      >
        <CurrencyProvider>
          <ThemeProvider>
            <ThemeCustomizationProvider>
              <SettingsProvider>
                <AuthProvider>
                  <NotificationProvider>
                    {children}
                  </NotificationProvider>
                </AuthProvider>
              </SettingsProvider>
            </ThemeCustomizationProvider>
          </ThemeProvider>
        </CurrencyProvider>
      </body>
    </html>
  );
}
