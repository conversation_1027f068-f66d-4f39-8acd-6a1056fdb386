'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useAuth } from '@/contexts/auth-context'
import { useSettings } from '@/contexts/settings-context'
import apiClient from '@/lib/api'
import {
  Building2,
  ArrowLeft,
  Save,
  Check,
  Upload,
  Mail,
  Phone,
  MapPin,
  Globe,
  FileText,
  X
} from 'lucide-react'

export default function CompanySettingsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)

  const [companyInfo, setCompanyInfo] = useState({
    name: 'BitsTech POS',
    subtitle: 'POS System',
    email: '<EMAIL>',
    phone: '+95-9-***********',
    address: 'Yangon, Myanmar',
    website: 'https://bitstech.com',
    taxNumber: 'TAX-001-2024',
    registrationNumber: 'REG-001-2024',
    description: 'Computer & Accessories Store',
    logo: ''
  })

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchCompanyInfo()
    }
  }, [isAuthenticated])

  const fetchCompanyInfo = async () => {
    try {
      // Load from localStorage first
      const savedInfo = localStorage.getItem('bitstech_company_info')
      if (savedInfo) {
        const parsed = JSON.parse(savedInfo)
        setCompanyInfo(parsed)
        return
      }

      // Fallback to API if no local data
      const response = await apiClient.getSettings()
      if (response.success && response.data.settings) {
        const settings = response.data.settings
        const info = {
          name: settings.storeName || 'BitsTech POS',
          subtitle: settings.storeSubtitle || 'POS System',
          email: settings.storeEmail || '<EMAIL>',
          phone: settings.storePhone || '+95-9-***********',
          address: settings.storeAddress || 'Yangon, Myanmar',
          website: settings.website || 'https://bitstech.com',
          taxNumber: settings.taxNumber || 'TAX-001-2024',
          registrationNumber: settings.registrationNumber || 'REG-001-2024',
          description: settings.description || 'Computer & Accessories Store',
          logo: settings.logoUrl || ''
        }
        setCompanyInfo(info)
        localStorage.setItem('bitstech_company_info', JSON.stringify(info))
      }
    } catch (error) {
      console.warn('Failed to fetch company info, using defaults:', error)
    }
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Save to localStorage immediately
      localStorage.setItem('bitstech_company_info', JSON.stringify(companyInfo))

      // Trigger real-time update for sidebar and header
      window.dispatchEvent(new CustomEvent('companyInfoUpdated'))

      // Try to save to API
      try {
        await apiClient.updateGeneralSettings({
          storeName: companyInfo.name,
          storeSubtitle: companyInfo.subtitle,
          storeEmail: companyInfo.email,
          storePhone: companyInfo.phone,
          storeAddress: companyInfo.address,
          website: companyInfo.website,
          taxNumber: companyInfo.taxNumber,
          registrationNumber: companyInfo.registrationNumber,
          description: companyInfo.description,
          logoUrl: companyInfo.logo
        })
      } catch (apiError) {
        console.warn('API save failed, saved locally:', apiError)
      }

      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error('Error updating company info:', error)
      setSaved(false)
    } finally {
      setSaving(false)
    }
  }

  const updateField = (field: string, value: string) => {
    setCompanyInfo(prev => ({ ...prev, [field]: value }))
  }

  const handleLogoUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    // Validate file type
    if (!file.type.startsWith('image/')) {
      alert(language === 'mm' ? 'ပုံဖိုင်သာ ရွေးချယ်ပါ' : 'Please select an image file')
      return
    }

    // Validate file size (max 2MB)
    if (file.size > 2 * 1024 * 1024) {
      alert(language === 'mm' ? 'ဖိုင်အရွယ်အစား 2MB ထက် မကျော်ရပါ' : 'File size must be less than 2MB')
      return
    }

    try {
      // Convert to base64 for preview
      const reader = new FileReader()
      reader.onload = (e) => {
        const base64 = e.target?.result as string
        updateField('logo', base64)
      }
      reader.readAsDataURL(file)
    } catch (error) {
      console.error('Error uploading logo:', error)
      alert(language === 'mm' ? 'လိုဂို တင်ရာတွင် အမှားရှိသည်' : 'Error uploading logo')
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 to-cyan-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Building2 className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'ကုမ္ပဏီ အချက်အလက်များ' : 'Company Information'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm'
                      ? 'သင့်လုပ်ငန်း အသေးစိတ်များ၊ လိုဂို နှင့် ဆက်သွယ်ရေး အချက်အလက်များ စီမံခန့်ခွဲပါ'
                      : 'Manage your business details, logo, and contact information'
                    }
                  </p>
                </div>
              </div>
              <Button
                onClick={handleSave}
                disabled={saving}
                className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              >
                {saving ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                ) : saved ? (
                  <Check className="h-4 w-4 mr-2" />
                ) : (
                  <Save className="h-4 w-4 mr-2" />
                )}
                {saving
                  ? (language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...')
                  : saved
                  ? (language === 'mm' ? 'သိမ်းပြီး' : 'Saved!')
                  : (language === 'mm' ? 'သိမ်းရန်' : 'Save Changes')
                }
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အခြေခံ အချက်အလက်များ' : 'Basic Information'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ကုမ္ပဏီ အမည်၊ ဖော်ပြချက် နှင့် လိုဂို'
                  : 'Company name, description, and logo'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>{language === 'mm' ? 'ကုမ္ပဏီ အမည်' : 'Company Name'}</Label>
                <Input
                  value={companyInfo.name || ''}
                  onChange={(e) => updateField('name', e.target.value)}
                  placeholder="BitsTech POS"
                />
              </div>

              <div className="space-y-2">
                <Label>{language === 'mm' ? 'ခေါင်းစဉ်ခွဲ' : 'Subtitle'}</Label>
                <Input
                  value={companyInfo.subtitle || ''}
                  onChange={(e) => updateField('subtitle', e.target.value)}
                  placeholder="POS System"
                />
              </div>

              <div className="space-y-2">
                <Label>{language === 'mm' ? 'ဖော်ပြချက်' : 'Description'}</Label>
                <Textarea
                  value={companyInfo.description || ''}
                  onChange={(e) => updateField('description', e.target.value)}
                  placeholder="Computer & Accessories Store"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label>{language === 'mm' ? 'လိုဂို URL' : 'Logo URL'}</Label>
                <div className="space-y-3">
                  <div className="flex gap-2">
                    <Input
                      value={companyInfo.logo || ''}
                      onChange={(e) => updateField('logo', e.target.value)}
                      placeholder="https://example.com/logo.png"
                    />
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleLogoUpload}
                      className="hidden"
                      id="logo-upload"
                      title="Upload company logo"
                      aria-label="Upload company logo"
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => document.getElementById('logo-upload')?.click()}
                      title={language === 'mm' ? 'လိုဂို တင်ရန်' : 'Upload Logo'}
                    >
                      <Upload className="h-4 w-4" />
                    </Button>
                  </div>
                  {companyInfo.logo && (
                    <div className="flex items-center gap-3 p-3 border rounded-lg">
                      <img
                        src={companyInfo.logo}
                        alt="Company Logo"
                        className="w-12 h-12 object-contain rounded"
                        onError={(e) => {
                          e.currentTarget.style.display = 'none'
                        }}
                      />
                      <div className="flex-1">
                        <p className="text-sm font-medium">
                          {language === 'mm' ? 'လိုဂို ပုံ' : 'Logo Preview'}
                        </p>
                        <p className="text-xs text-gray-500">
                          {companyInfo.logo.startsWith('data:')
                            ? (language === 'mm' ? 'တင်ထားသော ပုံ' : 'Uploaded Image')
                            : (language === 'mm' ? 'URL မှ ပုံ' : 'Image from URL')
                          }
                        </p>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => updateField('logo', '')}
                        className="text-red-600 hover:text-red-700"
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Mail className="h-5 w-5 text-green-600" />
                {language === 'mm' ? 'ဆက်သွယ်ရေး အချက်အလက်များ' : 'Contact Information'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'အီးမေးလ်၊ ဖုန်းနံပါတ် နှင့် လိပ်စာ'
                  : 'Email, phone number, and address'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  {language === 'mm' ? 'အီးမေးလ်' : 'Email'}
                </Label>
                <Input
                  type="email"
                  value={companyInfo.email || ''}
                  onChange={(e) => updateField('email', e.target.value)}
                  placeholder="<EMAIL>"
                />
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Phone className="h-4 w-4" />
                  {language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone Number'}
                </Label>
                <Input
                  value={companyInfo.phone || ''}
                  onChange={(e) => updateField('phone', e.target.value)}
                  placeholder="+95-9-***********"
                />
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  {language === 'mm' ? 'လိပ်စာ' : 'Address'}
                </Label>
                <Textarea
                  value={companyInfo.address || ''}
                  onChange={(e) => updateField('address', e.target.value)}
                  placeholder="Yangon, Myanmar"
                  rows={3}
                />
              </div>

              <div className="space-y-2">
                <Label className="flex items-center gap-2">
                  <Globe className="h-4 w-4" />
                  {language === 'mm' ? 'ဝက်ဘ်ဆိုဒ်' : 'Website'}
                </Label>
                <Input
                  value={companyInfo.website || ''}
                  onChange={(e) => updateField('website', e.target.value)}
                  placeholder="https://bitstech.com"
                />
              </div>
            </CardContent>
          </Card>

          {/* Legal Information */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'ဥပဒေဆိုင်ရာ အချက်အလက်များ' : 'Legal Information'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'အခွန်နံပါတ်၊ မှတ်ပုံတင်နံပါတ် နှင့် အခြား ဥပဒေဆိုင်ရာ အချက်အလက်များ'
                  : 'Tax number, registration number, and other legal details'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label>{language === 'mm' ? 'အခွန်နံပါတ်' : 'Tax Number'}</Label>
                <Input
                  value={companyInfo.taxNumber || ''}
                  onChange={(e) => updateField('taxNumber', e.target.value)}
                  placeholder="TAX-001-2024"
                />
              </div>

              <div className="space-y-2">
                <Label>{language === 'mm' ? 'မှတ်ပုံတင်နံပါတ်' : 'Registration Number'}</Label>
                <Input
                  value={companyInfo.registrationNumber || ''}
                  onChange={(e) => updateField('registrationNumber', e.target.value)}
                  placeholder="REG-001-2024"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
