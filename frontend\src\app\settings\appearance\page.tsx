'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useSettings, Theme } from '@/contexts/settings-context'
import { ThemeCustomizer } from '@/components/ThemeCustomizer'
import {
  Palette,
  ArrowLeft,
  Save,
  Check,
  Sun,
  Moon,
  Monitor,
  Sparkles,
  Eye,
  Zap,
  Star,
  Settings
} from 'lucide-react'

interface ThemeOption {
  id: string
  name: string
  nameLocal: string
  description: string
  descriptionLocal: string
  preview: string
  gradient: string
  icon: any
  isNew?: boolean
  isPro?: boolean
}

interface ColorScheme {
  id: string
  name: string
  nameLocal: string
  primary: string
  secondary: string
  accent: string
  background: string
}

export default function AppearanceSettingsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language, theme, setTheme } = useSettings()
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)

  const [selectedTheme, setSelectedTheme] = useState<Theme>(theme)
  const [selectedColorScheme, setSelectedColorScheme] = useState('blue')
  const [enableAnimations, setEnableAnimations] = useState(true)
  const [compactMode, setCompactMode] = useState(false)

  const themeOptions: ThemeOption[] = [
    {
      id: 'light',
      name: 'Light Mode',
      nameLocal: 'အလင်း မုဒ်',
      description: 'Clean and bright interface',
      descriptionLocal: 'သန့်ရှင်းပြီး တောက်ပသော မျက်နှာပြင်',
      preview: 'bg-white border-gray-200',
      gradient: 'from-yellow-400 to-orange-500',
      icon: Sun
    },
    {
      id: 'dark',
      name: 'Dark Mode',
      nameLocal: 'အမှောင် မုဒ်',
      description: 'Easy on the eyes, perfect for night use',
      descriptionLocal: 'မျက်လုံးကို သက်သာစေပြီး ညဘက် အသုံးပြုရန် ကောင်းမွန်',
      preview: 'bg-gray-900 border-gray-700',
      gradient: 'from-blue-600 to-purple-600',
      icon: Moon
    },
    {
      id: 'system',
      name: 'System Default',
      nameLocal: 'စနစ် မူလ',
      description: 'Follows your device settings',
      descriptionLocal: 'သင့်ကိရိယာ ဆက်တင်များကို လိုက်နာသည်',
      preview: 'bg-gradient-to-br from-blue-50 to-purple-50 border-blue-200',
      gradient: 'from-green-500 to-blue-500',
      icon: Monitor,
      isNew: true
    }
  ]

  const colorSchemes: ColorScheme[] = [
    {
      id: 'blue',
      name: 'Ocean Blue',
      nameLocal: 'သမုဒ္ဒရာ အပြာ',
      primary: '#3B82F6',
      secondary: '#1E40AF',
      accent: '#06B6D4',
      background: '#F0F9FF'
    },
    {
      id: 'purple',
      name: 'Royal Purple',
      nameLocal: 'ဘုရင့် ခရမ်းရောင်',
      primary: '#8B5CF6',
      secondary: '#7C3AED',
      accent: '#A855F7',
      background: '#FAF5FF'
    },
    {
      id: 'green',
      name: 'Forest Green',
      nameLocal: 'တောအစိမ်း',
      primary: '#10B981',
      secondary: '#059669',
      accent: '#34D399',
      background: '#F0FDF4'
    },
    {
      id: 'orange',
      name: 'Sunset Orange',
      nameLocal: 'နေဝင် လိမ္မော်ရောင်',
      primary: '#F59E0B',
      secondary: '#D97706',
      accent: '#FBBF24',
      background: '#FFFBEB'
    },
    {
      id: 'pink',
      name: 'Cherry Blossom',
      nameLocal: 'ချယ်ရီ ပန်း',
      primary: '#EC4899',
      secondary: '#DB2777',
      accent: '#F472B6',
      background: '#FDF2F8'
    },
    {
      id: 'teal',
      name: 'Tropical Teal',
      nameLocal: 'အပူပိုင်း စိမ်းပြာ',
      primary: '#14B8A6',
      secondary: '#0F766E',
      accent: '#5EEAD4',
      background: '#F0FDFA'
    }
  ]

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  const handleSave = async () => {
    setSaving(true)
    try {
      // Apply theme immediately
      setTheme(selectedTheme)

      // Simulate API call to save other settings
      await new Promise(resolve => setTimeout(resolve, 1500))

      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error('Error saving settings:', error)
    } finally {
      setSaving(false)
    }
  }

  // Update selected theme when context theme changes
  useEffect(() => {
    setSelectedTheme(theme)
  }, [theme])

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  const selectedScheme = colorSchemes.find(scheme => scheme.id === selectedColorScheme)

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-pink-50 dark:hover:bg-pink-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-pink-600 to-purple-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Palette className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {language === 'mm' ? 'အသွင်အပြင် နှင့် အပြင်အဆင်' : 'Appearance & Theme'}
                </h1>
                <p className="text-white/80 mt-1">
                  {language === 'mm'
                    ? 'သင့် POS စနစ်၏ အသွင်အပြင်ကို စိတ်ကြိုက် ပြုလုပ်ပါ'
                    : 'Customize the look and feel of your POS system'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Theme Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အပြင်အဆင် မုဒ်' : 'Theme Mode'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'အလင်း သို့မဟုတ် အမှောင် မုဒ် ရွေးချယ်ပါ'
                  : 'Choose between light, dark, or system theme'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {themeOptions.map((theme) => {
                const Icon = theme.icon
                return (
                  <div
                    key={theme.id}
                    className={`relative p-5 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:shadow-lg group ${
                      selectedTheme === theme.id
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-200'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800/50'
                    }`}
                    onClick={() => setSelectedTheme(theme.id as Theme)}
                  >
                    <div className="flex items-center gap-4">
                      <div className={`p-3 rounded-xl bg-gradient-to-r ${theme.gradient} text-white shadow-lg group-hover:shadow-xl transition-shadow`}>
                        <Icon className="h-6 w-6" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-lg">
                            {language === 'mm' ? theme.nameLocal : theme.name}
                          </h3>
                          {theme.isNew && (
                            <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs px-2 py-1">
                              {language === 'mm' ? 'အသစ်' : 'New'}
                            </Badge>
                          )}
                          {selectedTheme === theme.id && (
                            <div className="ml-auto w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                              <Check className="w-4 h-4 text-white" />
                            </div>
                          )}
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {language === 'mm' ? theme.descriptionLocal : theme.description}
                        </p>
                      </div>
                      <div className={`w-20 h-14 rounded-lg ${theme.preview} border-2 border-gray-300 shadow-sm`}></div>
                    </div>
                  </div>
                )
              })}
            </CardContent>
          </Card>

          {/* Color Schemes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'အရောင် အစီအစဉ်' : 'Color Scheme'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'သင့်လုပ်ငန်း အတွက် အရောင် ပုံစံ ရွေးချယ်ပါ'
                  : 'Select a color palette for your business'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {colorSchemes.map((scheme) => (
                  <div
                    key={scheme.id}
                    className={`relative p-4 border-2 rounded-xl cursor-pointer transition-all duration-300 hover:shadow-lg ${
                      selectedColorScheme === scheme.id
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 ring-2 ring-blue-200'
                        : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedColorScheme(scheme.id)}
                  >
                    <div className="space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex gap-1">
                          <div
                            className="w-5 h-5 rounded-full border-2 border-white shadow-sm"
                            style={{ backgroundColor: scheme.primary }}
                          ></div>
                          <div
                            className="w-5 h-5 rounded-full border-2 border-white shadow-sm"
                            style={{ backgroundColor: scheme.secondary }}
                          ></div>
                          <div
                            className="w-5 h-5 rounded-full border-2 border-white shadow-sm"
                            style={{ backgroundColor: scheme.accent }}
                          ></div>
                        </div>
                        {selectedColorScheme === scheme.id && (
                          <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center">
                            <Check className="w-4 h-4 text-white" />
                          </div>
                        )}
                      </div>
                      <div>
                        <h4 className="font-medium text-sm">
                          {language === 'mm' ? scheme.nameLocal : scheme.name}
                        </h4>
                        <div className="mt-2 h-3 rounded-full overflow-hidden flex">
                          <div className="flex-1" style={{ backgroundColor: scheme.primary }}></div>
                          <div className="flex-1" style={{ backgroundColor: scheme.secondary }}></div>
                          <div className="flex-1" style={{ backgroundColor: scheme.accent }}></div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {/* Color Preview */}
              {selectedScheme && (
                <div className="mt-6 p-6 rounded-xl border border-gray-200 dark:border-gray-700 bg-gradient-to-br from-gray-50 to-white dark:from-gray-800 dark:to-gray-900">
                  <Label className="text-sm font-medium mb-4 block flex items-center gap-2">
                    <Sparkles className="w-4 h-4" />
                    {language === 'mm' ? 'အရောင် နမူနာ' : 'Color Preview'}
                  </Label>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    <div className="text-center">
                      <div
                        className="w-full h-16 rounded-xl mb-3 shadow-sm border-2 border-white"
                        style={{ backgroundColor: selectedScheme.primary }}
                      ></div>
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Primary</span>
                      <p className="text-xs text-gray-500 mt-1">{selectedScheme.primary}</p>
                    </div>
                    <div className="text-center">
                      <div
                        className="w-full h-16 rounded-xl mb-3 shadow-sm border-2 border-white"
                        style={{ backgroundColor: selectedScheme.secondary }}
                      ></div>
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Secondary</span>
                      <p className="text-xs text-gray-500 mt-1">{selectedScheme.secondary}</p>
                    </div>
                    <div className="text-center">
                      <div
                        className="w-full h-16 rounded-xl mb-3 shadow-sm border-2 border-white"
                        style={{ backgroundColor: selectedScheme.accent }}
                      ></div>
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Accent</span>
                      <p className="text-xs text-gray-500 mt-1">{selectedScheme.accent}</p>
                    </div>
                    <div className="text-center">
                      <div
                        className="w-full h-16 rounded-xl mb-3 shadow-sm border-2 border-gray-200"
                        style={{ backgroundColor: selectedScheme.background }}
                      ></div>
                      <span className="text-xs font-medium text-gray-700 dark:text-gray-300">Background</span>
                      <p className="text-xs text-gray-500 mt-1">{selectedScheme.background}</p>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Interface Options */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Zap className="h-5 w-5 text-yellow-600" />
                {language === 'mm' ? 'မျက်နှာပြင် ရွေးချယ်မှုများ' : 'Interface Options'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'အပိုင်းအစများ နှင့် လုပ်ဆောင်မှု ရွေးချယ်မှုများ'
                  : 'Additional interface and performance options'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Animations */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">
                        {language === 'mm' ? 'လှုပ်ရှားမှု အကျိုးသက်ရောက်မှုများ' : 'Animations & Effects'}
                      </Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {language === 'mm'
                          ? 'ချောမွေ့သော အကူးအပြောင်းများ နှင့် အကျိုးသက်ရောက်မှုများ'
                          : 'Smooth transitions and visual effects'
                        }
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={enableAnimations}
                        onChange={(e) => setEnableAnimations(e.target.checked)}
                        className="sr-only peer"
                        aria-label={language === 'mm' ? 'လှုပ်ရှားမှု အကျိုးသက်ရောက်မှုများ' : 'Animations & Effects'}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>

                {/* Compact Mode */}
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="font-medium">
                        {language === 'mm' ? 'ကျစ်လစ် မုဒ်' : 'Compact Mode'}
                      </Label>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {language === 'mm'
                          ? 'ပိုမို ကျစ်လစ်သော မျက်နှာပြင် အပြင်အဆင်'
                          : 'More condensed interface layout'
                        }
                      </p>
                    </div>
                    <label className="relative inline-flex items-center cursor-pointer">
                      <input
                        type="checkbox"
                        checked={compactMode}
                        onChange={(e) => setCompactMode(e.target.checked)}
                        className="sr-only peer"
                        aria-label={language === 'mm' ? 'ကျစ်လစ် မုဒ်' : 'Compact Mode'}
                      />
                      <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                    </label>
                  </div>
                </div>
              </div>

              {/* Preview Section */}
              <div className="mt-6 p-6 border border-gray-200 dark:border-gray-700 rounded-xl">
                <Label className="text-sm font-medium mb-4 block">
                  {language === 'mm' ? 'လက်ရှိ အပြင်အဆင် နမူနာ' : 'Current Theme Preview'}
                </Label>
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div
                      className="w-8 h-8 rounded-lg"
                      style={{ backgroundColor: selectedScheme?.primary }}
                    ></div>
                    <div className="flex-1">
                      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 mb-2"></div>
                      <div className="h-3 bg-gray-100 dark:bg-gray-800 rounded w-1/2"></div>
                    </div>
                    <Star className="h-5 w-5 text-yellow-500" />
                  </div>
                  <div className="flex gap-2">
                    <div
                      className="px-3 py-1 rounded-lg text-white text-sm"
                      style={{ backgroundColor: selectedScheme?.primary }}
                    >
                      Primary Button
                    </div>
                    <div
                      className="px-3 py-1 rounded-lg text-white text-sm"
                      style={{ backgroundColor: selectedScheme?.secondary }}
                    >
                      Secondary
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
          {/* Advanced Theme Customizer */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-indigo-600" />
                {language === 'mm' ? 'အဆင့်မြင့် Theme ပြင်ဆင်ရန်' : 'Advanced Theme Customizer'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'အရောင်များ၊ စာလုံးများ၊ အပြင်အဆင် နှင့် အကျိုးသက်ရောက်မှုများကို အသေးစိတ် ပြင်ဆင်ပါ'
                  : 'Fine-tune colors, fonts, layout, and effects to match your brand'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ThemeCustomizer />
            </CardContent>
          </Card>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-gradient-to-r from-pink-600 to-purple-600 hover:from-pink-700 hover:to-purple-700 text-white px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
              </div>
            ) : saved ? (
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းပြီး' : 'Saved'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းရန်' : 'Apply Theme'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
