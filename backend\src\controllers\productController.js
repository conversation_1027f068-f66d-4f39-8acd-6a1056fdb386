// Real MongoDB models
const Product = require('../models/Product');
const Category = require('../models/Category');
const asyncHandler = require('../utils/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get all products
// @route   GET /api/products
// @access  Private
const getProducts = asyncHandler(async (req, res, next) => {
    try {
        // Build query
        let query = {};

        // Filter by category
        if (req.query.category) {
            query.category = req.query.category;
        }

        // Filter by active status
        if (req.query.isActive !== undefined) {
            query.isActive = req.query.isActive === 'true';
        }

        // Filter by featured status
        if (req.query.isFeatured !== undefined) {
            query.isFeatured = req.query.isFeatured === 'true';
        }

        // Search functionality
        if (req.query.search) {
            const searchTerm = req.query.search;
            query.$or = [
                { name: { $regex: searchTerm, $options: 'i' } },
                { sku: { $regex: searchTerm, $options: 'i' } },
                { description: { $regex: searchTerm, $options: 'i' } }
            ];
        }

        // Pagination
        const page = parseInt(req.query.page, 10) || 1;
        const limit = parseInt(req.query.limit, 10) || 20;
        const startIndex = (page - 1) * limit;

        // Execute query
        const products = await Product.find(query)
            .populate('category', 'name description color icon')
            .sort({ createdAt: -1 })
            .skip(startIndex)
            .limit(limit);

        // Get total count for pagination
        const total = await Product.countDocuments(query);

        // Pagination result
        const pagination = {};

        if (startIndex + limit < total) {
            pagination.next = {
                page: page + 1,
                limit
            };
        }

        if (startIndex > 0) {
            pagination.prev = {
                page: page - 1,
                limit
            };
        }

        res.status(200).json({
            success: true,
            source: 'MongoDB',
            count: products.length,
            total,
            pagination,
            data: products
        });
    } catch (error) {
        console.error('Error fetching products:', error);
        return next(new ErrorResponse('Error fetching products', 500));
    }
});

// @desc    Get single product
// @route   GET /api/products/:id
// @access  Private
const getProduct = asyncHandler(async (req, res, next) => {
    const product = await Product.findById(req.params.id)
        .populate('category', 'name description color icon parent');
        
    if (!product) {
        return next(new ErrorResponse(`Product not found with id of ${req.params.id}`, 404));
    }
    
    res.status(200).json({
        success: true,
        data: product
    });
});

// @desc    Create new product
// @route   POST /api/products
// @access  Private (Admin, Manager)
const createProduct = asyncHandler(async (req, res, next) => {
    // Check if category exists
    const category = await Category.findById(req.body.category);
    if (!category) {
        return next(new ErrorResponse('Category not found', 404));
    }
    
    // Check if SKU already exists
    const existingProduct = await Product.findOne({ sku: req.body.sku });
    if (existingProduct) {
        return next(new ErrorResponse('Product with this SKU already exists', 400));
    }
    
    // Check if barcode already exists (if provided)
    if (req.body.barcode) {
        const existingBarcode = await Product.findOne({ barcode: req.body.barcode });
        if (existingBarcode) {
            return next(new ErrorResponse('Product with this barcode already exists', 400));
        }
    }
    
    const product = await Product.create(req.body);
    
    // Populate category information
    await product.populate('category', 'name color icon');
    
    res.status(201).json({
        success: true,
        data: product
    });
});

// @desc    Update product
// @route   PUT /api/products/:id
// @access  Private (Admin, Manager)
const updateProduct = asyncHandler(async (req, res, next) => {
    let product = await Product.findById(req.params.id);
    
    if (!product) {
        return next(new ErrorResponse(`Product not found with id of ${req.params.id}`, 404));
    }
    
    // Check if category exists (if being updated)
    if (req.body.category) {
        const category = await Category.findById(req.body.category);
        if (!category) {
            return next(new ErrorResponse('Category not found', 404));
        }
    }
    
    // Check if SKU already exists (if being updated)
    if (req.body.sku && req.body.sku !== product.sku) {
        const existingProduct = await Product.findOne({ sku: req.body.sku });
        if (existingProduct) {
            return next(new ErrorResponse('Product with this SKU already exists', 400));
        }
    }
    
    // Check if barcode already exists (if being updated)
    if (req.body.barcode && req.body.barcode !== product.barcode) {
        const existingBarcode = await Product.findOne({ barcode: req.body.barcode });
        if (existingBarcode) {
            return next(new ErrorResponse('Product with this barcode already exists', 400));
        }
    }
    
    product = await Product.findByIdAndUpdate(req.params.id, req.body, {
        new: true,
        runValidators: true
    }).populate('category', 'name color icon');
    
    res.status(200).json({
        success: true,
        data: product
    });
});

// @desc    Delete product
// @route   DELETE /api/products/:id
// @access  Private (Admin, Manager)
const deleteProduct = asyncHandler(async (req, res, next) => {
    const product = await Product.findById(req.params.id);
    
    if (!product) {
        return next(new ErrorResponse(`Product not found with id of ${req.params.id}`, 404));
    }
    
    await product.deleteOne();
    
    res.status(200).json({
        success: true,
        data: {}
    });
});

// @desc    Search products
// @route   GET /api/products/search
// @access  Private
const searchProducts = asyncHandler(async (req, res, next) => {
    const { q, category, minPrice, maxPrice, inStock } = req.query;
    
    if (!q) {
        return next(new ErrorResponse('Search query is required', 400));
    }
    
    let query = {
        $text: { $search: q }
    };
    
    // Additional filters
    if (category) {
        query.category = category;
    }
    
    if (minPrice || maxPrice) {
        query.price = {};
        if (minPrice) query.price.$gte = parseFloat(minPrice);
        if (maxPrice) query.price.$lte = parseFloat(maxPrice);
    }
    
    if (inStock === 'true') {
        query['inventory.quantity'] = { $gt: 0 };
    }
    
    const products = await Product.find(query)
        .populate('category', 'name color icon')
        .sort({ score: { $meta: 'textScore' } })
        .limit(50);
    
    res.status(200).json({
        success: true,
        count: products.length,
        data: products
    });
});

module.exports = {
    getProducts,
    getProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    searchProducts
};
