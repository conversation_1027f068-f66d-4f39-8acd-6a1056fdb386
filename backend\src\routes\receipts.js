const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
    emailReceipt,
    markAsPrinted,
    getReceipt
} = require('../controllers/receiptController');

const router = express.Router();

// Get receipt data
router.get('/:saleId', protect, getReceipt);

// Email receipt
router.post('/email/:saleId', protect, emailReceipt);

// Mark as printed
router.put('/print/:saleId', protect, markAsPrinted);

module.exports = router;
