/**
 * Hook for components to easily respond to theme and language changes
 */

import { useEffect, useState } from 'react'
import { useTheme } from '@/contexts/theme-context'
import { getThemeLanguageClasses, getCurrentThemeFromDOM, getCurrentLanguageFromDOM } from '@/lib/theme-utils'

export function useThemeLanguage() {
  const { theme, language, actualTheme } = useTheme()
  const [forceUpdate, setForceUpdate] = useState(0)

  // Listen for theme and language changes
  useEffect(() => {
    const handleThemeChange = () => {
      setForceUpdate(prev => prev + 1)
    }

    const handleLanguageChange = () => {
      setForceUpdate(prev => prev + 1)
    }

    window.addEventListener('theme-changed', handleThemeChange)
    window.addEventListener('language-changed', handleLanguageChange)

    return () => {
      window.removeEventListener('theme-changed', handleThemeChange)
      window.removeEventListener('language-changed', handleLanguageChange)
    }
  }, [])

  // Get current theme and language from DOM as fallback
  const currentTheme = actualTheme || getCurrentThemeFromDOM()
  const currentLanguage = language || getCurrentLanguageFromDOM()

  // Helper function to get classes with theme and language support
  const getClasses = (baseClasses: string) => {
    return getThemeLanguageClasses(baseClasses, currentTheme, currentLanguage)
  }

  // Helper function to check if Myanmar language is active
  const isMyanmarLanguage = currentLanguage === 'mm'

  // Helper function to check if dark theme is active
  const isDarkTheme = currentTheme === 'dark'

  return {
    theme: currentTheme,
    language: currentLanguage,
    isMyanmarLanguage,
    isDarkTheme,
    getClasses,
    forceUpdate // Can be used to force re-renders when needed
  }
}

/**
 * Hook specifically for text elements that need language-aware styling
 */
export function useLanguageText() {
  const { language, isMyanmarLanguage, getClasses } = useThemeLanguage()

  const getTextClasses = (baseClasses: string = '') => {
    const languageClasses = isMyanmarLanguage ? 'font-myanmar' : ''
    return getClasses(`${baseClasses} ${languageClasses}`.trim())
  }

  return {
    language,
    isMyanmarLanguage,
    getTextClasses
  }
}

/**
 * Hook for theme-aware styling
 */
export function useThemeStyles() {
  const { theme, isDarkTheme, getClasses } = useThemeLanguage()

  const getThemeClasses = (lightClasses: string, darkClasses: string) => {
    const themeSpecificClasses = isDarkTheme ? darkClasses : lightClasses
    return getClasses(themeSpecificClasses)
  }

  return {
    theme,
    isDarkTheme,
    getThemeClasses,
    getClasses
  }
}
