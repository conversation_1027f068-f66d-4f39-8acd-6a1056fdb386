'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useAuth } from '@/contexts/auth-context'
import apiClient from '@/lib/api'
import {
  ArrowLeft,
  Plus,
  Trash2,
  Search,
  Package,
  Building,
  Calendar,
  DollarSign,
  AlertTriangle,
  Save,
  Send
} from 'lucide-react'

interface Product {
  _id: string
  name: string
  sku: string
  category: string
  cost: number
  price: number
  inventory: {
    quantity: number
    minStock: number
  }
}

interface Supplier {
  _id: string
  name: string
  code: string
  contactPerson: {
    name: string
    title?: string
    email?: string
    phone?: string
    mobile?: string
  }
  company: {
    address: {
      street: string
      city: string
      state: string
      country: string
      zipCode: string
    }
    phone?: string
    fax?: string
    website?: string
    taxId?: string
    registrationNumber?: string
  }
  paymentTerms: string
  customPaymentTerms?: string
  creditLimit?: number
  currency: string
  categories?: string[]
  products?: any[]
  rating: number
  notes?: string
  isActive: boolean
  lastOrderDate?: Date
  totalOrders: number
  totalOrderValue: number
}

interface OrderItem {
  id: string
  product: string
  productName: string
  sku: string
  description: string
  quantity: number
  unitCost: number
  totalCost: number
  notes: string
}

export default function NewPurchaseOrderPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()

  // Form state
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [products, setProducts] = useState<Product[]>([])
  const [selectedSupplier, setSelectedSupplier] = useState('')
  const [expectedDeliveryDate, setExpectedDeliveryDate] = useState('')
  const [priority, setPriority] = useState('normal')
  const [items, setItems] = useState<OrderItem[]>([
    {
      id: `item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      product: '',
      productName: '',
      sku: '',
      description: '',
      quantity: 1,
      unitCost: 0,
      totalCost: 0,
      notes: ''
    }
  ])
  const [taxRate, setTaxRate] = useState(0)
  const [shippingCost, setShippingCost] = useState(0)
  const [discountAmount, setDiscountAmount] = useState(0)
  const [paymentTerms, setPaymentTerms] = useState('net_30')
  const [shippingMethod, setShippingMethod] = useState('delivery')
  const [notes, setNotes] = useState('')
  const [internalNotes, setInternalNotes] = useState('')

  // UI state
  const [saving, setSaving] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchSuppliers()
      fetchProducts()
    }
  }, [isAuthenticated])

  const fetchSuppliers = async () => {
    try {
      const response = await apiClient.getSuppliers()
      setSuppliers(response.data || [])
    } catch (error) {
      console.error('Error fetching suppliers:', error)
      setSuppliers([])
    }
  }

  const fetchProducts = async () => {
    try {
      const response = await apiClient.getProducts()
      setProducts(response.data || [])
    } catch (error) {
      console.error('Error fetching products:', error)
      setProducts([])
    }
  }

  const addItem = () => {
    const newItem: OrderItem = {
      id: `item_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      product: '',
      productName: '',
      sku: '',
      description: '',
      quantity: 1,
      unitCost: 0,
      totalCost: 0,
      notes: ''
    }
    setItems([...items, newItem])
  }

  const removeItem = (itemId: string) => {
    const newItems = items.filter(item => item.id !== itemId)
    setItems(newItems)
  }

  const updateItem = (itemId: string, field: keyof OrderItem, value: any) => {
    const newItems = [...items]
    const itemIndex = newItems.findIndex(item => item.id === itemId)

    if (itemIndex === -1) return

    newItems[itemIndex] = { ...newItems[itemIndex], [field]: value }

    // Auto-calculate total cost
    if (field === 'quantity' || field === 'unitCost') {
      newItems[itemIndex].totalCost = newItems[itemIndex].quantity * newItems[itemIndex].unitCost
    }

    // Auto-fill product details when product is selected
    if (field === 'product') {
      const product = products.find(p => p._id === value)
      if (product) {
        newItems[itemIndex].productName = product.name
        newItems[itemIndex].sku = product.sku
        newItems[itemIndex].unitCost = product.cost
        newItems[itemIndex].totalCost = newItems[itemIndex].quantity * product.cost
      }
    }

    setItems(newItems)
  }

  const calculateSubtotal = () => {
    return items.reduce((total, item) => total + item.totalCost, 0)
  }

  const calculateTaxAmount = () => {
    return (calculateSubtotal() * taxRate) / 100
  }

  const calculateTotal = () => {
    return calculateSubtotal() + calculateTaxAmount() + shippingCost - discountAmount
  }

  const validateForm = () => {
    if (!selectedSupplier) {
      alert('Please select a supplier')
      return false
    }

    if (!expectedDeliveryDate) {
      alert('Please select expected delivery date')
      return false
    }

    if (items.length === 0) {
      alert('Please add at least one item')
      return false
    }

    for (let i = 0; i < items.length; i++) {
      const item = items[i]
      if (!item.product) {
        alert(`Please select a product for item ${i + 1}`)
        return false
      }
      if (item.quantity <= 0) {
        alert(`Please enter a valid quantity for item ${i + 1}`)
        return false
      }
      if (item.unitCost <= 0) {
        alert(`Please enter a valid unit cost for item ${i + 1}`)
        return false
      }
    }

    return true
  }

  const savePurchaseOrder = async (status: 'draft' | 'pending') => {
    if (!validateForm()) return

    setSaving(true)

    try {
      const orderData = {
        supplier: selectedSupplier,
        expectedDeliveryDate,
        priority,
        items: items.map(item => ({
          product: item.product,
          quantity: item.quantity,
          unitCost: item.unitCost,
          description: item.description,
          notes: item.notes
        })),
        taxRate,
        shippingCost,
        discountAmount,
        paymentTerms,
        shippingMethod,
        notes,
        internalNotes,
        status
      }

      const response = await apiClient.createPurchaseOrder(orderData)

      if (response.success) {
        alert(`Purchase order ${status === 'draft' ? 'saved as draft' : 'submitted for approval'} successfully!`)
        router.push('/purchase-orders')
      } else {
        alert('Error creating purchase order: ' + response.message)
      }
    } catch (error: any) {
      console.error('Error creating purchase order:', error)
      alert('Error creating purchase order: ' + (error.message || 'Unknown error'))
    } finally {
      setSaving(false)
    }
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const text = {
    en: {
      title: 'New Purchase Order',
      description: 'Create a new purchase order for supplier',
      back: 'Back to Purchase Orders',
      supplierInfo: 'Supplier Information',
      selectSupplier: 'Select Supplier',
      orderDetails: 'Order Details',
      expectedDelivery: 'Expected Delivery Date',
      priority: 'Priority',
      items: 'Order Items',
      addItem: 'Add Item',
      product: 'Product',
      selectProduct: 'Select Product',
      quantity: 'Quantity',
      unitCost: 'Unit Cost',
      totalCost: 'Total Cost',
      itemDescription: 'Description',
      itemNotes: 'Notes',
      remove: 'Remove',
      financialDetails: 'Financial Details',
      subtotal: 'Subtotal',
      taxRate: 'Tax Rate (%)',
      taxAmount: 'Tax Amount',
      shippingCost: 'Shipping Cost',
      discountAmount: 'Discount Amount',
      total: 'Total Amount',
      paymentTerms: 'Payment Terms',
      shippingMethod: 'Shipping Method',
      orderNotes: 'Order Notes',
      internalNotes: 'Internal Notes',
      saveAsDraft: 'Save as Draft',
      submitForApproval: 'Submit for Approval',
      priorities: {
        low: 'Low',
        normal: 'Normal',
        high: 'High',
        urgent: 'Urgent'
      },
      paymentTermsOptions: {
        cash: 'Cash',
        net_15: 'Net 15 Days',
        net_30: 'Net 30 Days',
        net_45: 'Net 45 Days',
        net_60: 'Net 60 Days',
        net_90: 'Net 90 Days'
      },
      shippingMethods: {
        pickup: 'Pickup',
        delivery: 'Delivery',
        courier: 'Courier',
        freight: 'Freight'
      }
    },
    mm: {
      title: 'ဝယ်ယူမှု အမှာစာ အသစ်',
      description: 'ပေးသွင်းသူအတွက် ဝယ်ယူမှု အမှာစာ အသစ် ဖန်တီးရန်',
      back: 'ဝယ်ယူမှု အမှာစာများ သို့ ပြန်သွားရန်',
      supplierInfo: 'ပေးသွင်းသူ အချက်အလက်',
      selectSupplier: 'ပေးသွင်းသူ ရွေးချယ်ရန်',
      orderDetails: 'အမှာစာ အသေးစိတ်',
      expectedDelivery: 'မျှော်လင့်ထားသော ပေးပို့မှု ရက်စွဲ',
      priority: 'ဦးစားပေးမှု',
      items: 'အမှာစာ ပစ္စည်းများ',
      addItem: 'ပစ္စည်း ထည့်ရန်',
      product: 'ပစ္စည်း',
      selectProduct: 'ပစ္စည်း ရွေးချယ်ရန်',
      quantity: 'အရေအတွက်',
      unitCost: 'တစ်ခုချင်း ကုန်ကျစရိတ်',
      totalCost: 'စုစုပေါင်း ကုန်ကျစရိတ်',
      itemDescription: 'ဖော်ပြချက်',
      itemNotes: 'မှတ်ချက်များ',
      remove: 'ဖယ်ရှားရန်',
      financialDetails: 'ငွေကြေး အသေးစိတ်',
      subtotal: 'စုစုပေါင်း',
      taxRate: 'အခွန်နှုန်း (%)',
      taxAmount: 'အခွန် ပမာဏ',
      shippingCost: 'ပို့ဆောင်ခ',
      discountAmount: 'လျှော့စျေး ပမာဏ',
      total: 'စုစုပေါင်း ပမာဏ',
      paymentTerms: 'ငွေပေးချေမှု စည်းကမ်းများ',
      shippingMethod: 'ပို့ဆောင်မှု နည်းလမ်း',
      orderNotes: 'အမှာစာ မှတ်ချက်များ',
      internalNotes: 'အတွင်းပိုင်း မှတ်ချက်များ',
      saveAsDraft: 'မူကြမ်းအဖြစ် သိမ်းရန်',
      submitForApproval: 'အတည်ပြုချက်အတွက် တင်သွင်းရန်',
      priorities: {
        low: 'နိမ့်',
        normal: 'ပုံမှန်',
        high: 'မြင့်',
        urgent: 'အရေးပေါ်'
      },
      paymentTermsOptions: {
        cash: 'လက်ငင်း',
        net_15: '15 ရက် အတွင်း',
        net_30: '30 ရက် အတွင်း',
        net_45: '45 ရက် အတွင်း',
        net_60: '60 ရက် အတွင်း',
        net_90: '90 ရက် အတွင်း'
      },
      shippingMethods: {
        pickup: 'လာယူရန်',
        delivery: 'ပို့ဆောင်ရန်',
        courier: 'ကူရီယာ',
        freight: 'ကုန်စည်ပို့ဆောင်ရေး'
      }
    }
  }

  const t = text[language]

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/purchase-orders')}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t.back}
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {t.title}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {t.description}
              </p>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Supplier Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building className="h-5 w-5" />
                  {t.supplierInfo}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <Label htmlFor="supplier">{t.selectSupplier}</Label>
                  <select
                    id="supplier"
                    value={selectedSupplier}
                    onChange={(e) => setSelectedSupplier(e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                    required
                    aria-label={t.selectSupplier}
                  >
                    <option value="">{t.selectSupplier}</option>
                    {suppliers.map((supplier) => (
                      <option key={supplier._id} value={supplier._id}>
                        {supplier.name} ({supplier.code})
                      </option>
                    ))}
                  </select>
                </div>
              </CardContent>
            </Card>

            {/* Order Details */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calendar className="h-5 w-5" />
                  {t.orderDetails}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="expectedDeliveryDate">{t.expectedDelivery}</Label>
                    <Input
                      id="expectedDeliveryDate"
                      type="date"
                      value={expectedDeliveryDate}
                      onChange={(e) => setExpectedDeliveryDate(e.target.value)}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="priority">{t.priority}</Label>
                    <select
                      id="priority"
                      value={priority}
                      onChange={(e) => setPriority(e.target.value)}
                      className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                      aria-label={t.priority}
                    >
                      <option value="low">{t.priorities.low}</option>
                      <option value="normal">{t.priorities.normal}</option>
                      <option value="high">{t.priorities.high}</option>
                      <option value="urgent">{t.priorities.urgent}</option>
                    </select>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Order Items */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Package className="h-5 w-5" />
                    {t.items}
                  </div>
                  <Button onClick={addItem} size="sm">
                    <Plus className="h-4 w-4 mr-2" />
                    {t.addItem}
                  </Button>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {items.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    <Package className="h-12 w-12 mx-auto mb-4 opacity-50" />
                    <p>No items added yet. Click &quot;Add Item&quot; to start.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {items.map((item, index) => (
                      <div key={item.id} className="border rounded-lg p-4 space-y-4">
                        <div className="flex items-center justify-between">
                          <h4 className="font-medium">Item {index + 1}</h4>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeItem(item.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          <div className="lg:col-span-3">
                            <Label>{t.product}</Label>
                            <select
                              value={item.product}
                              onChange={(e) => updateItem(item.id, 'product', e.target.value)}
                              className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                              required
                              aria-label={t.selectProduct}
                            >
                              <option value="">{t.selectProduct}</option>
                              {products.map((product) => (
                                <option key={product._id} value={product._id}>
                                  {product.name} ({product.sku}) - {product.cost.toLocaleString()} MMK
                                </option>
                              ))}
                            </select>
                          </div>

                          <div>
                            <Label>{t.quantity}</Label>
                            <Input
                              type="number"
                              min="1"
                              value={item.quantity}
                              onChange={(e) => updateItem(item.id, 'quantity', parseInt(e.target.value) || 0)}
                              required
                            />
                          </div>

                          <div>
                            <Label>{t.unitCost}</Label>
                            <Input
                              type="number"
                              min="0"
                              step="0.01"
                              value={item.unitCost}
                              onChange={(e) => updateItem(item.id, 'unitCost', parseFloat(e.target.value) || 0)}
                              required
                            />
                          </div>

                          <div>
                            <Label>{t.totalCost}</Label>
                            <Input
                              type="number"
                              value={item.totalCost}
                              readOnly
                              className="bg-gray-50"
                            />
                          </div>

                          <div className="lg:col-span-3">
                            <Label>{t.itemDescription}</Label>
                            <Input
                              value={item.description}
                              onChange={(e) => updateItem(item.id, 'description', e.target.value)}
                              placeholder="Optional description"
                            />
                          </div>

                          <div className="lg:col-span-3">
                            <Label>{t.itemNotes}</Label>
                            <Input
                              value={item.notes}
                              onChange={(e) => updateItem(item.id, 'notes', e.target.value)}
                              placeholder="Optional notes"
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Financial Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  {t.financialDetails}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span>{t.subtotal}:</span>
                    <span className="font-medium">{calculateSubtotal().toLocaleString()} MMK</span>
                  </div>

                  <div>
                    <Label htmlFor="taxRate">{t.taxRate}</Label>
                    <Input
                      id="taxRate"
                      type="number"
                      min="0"
                      max="100"
                      step="0.1"
                      value={taxRate}
                      onChange={(e) => setTaxRate(parseFloat(e.target.value) || 0)}
                    />
                  </div>

                  <div className="flex justify-between">
                    <span>{t.taxAmount}:</span>
                    <span className="font-medium">{calculateTaxAmount().toLocaleString()} MMK</span>
                  </div>

                  <div>
                    <Label htmlFor="shippingCost">{t.shippingCost}</Label>
                    <Input
                      id="shippingCost"
                      type="number"
                      min="0"
                      step="0.01"
                      value={shippingCost}
                      onChange={(e) => setShippingCost(parseFloat(e.target.value) || 0)}
                    />
                  </div>

                  <div>
                    <Label htmlFor="discountAmount">{t.discountAmount}</Label>
                    <Input
                      id="discountAmount"
                      type="number"
                      min="0"
                      step="0.01"
                      value={discountAmount}
                      onChange={(e) => setDiscountAmount(parseFloat(e.target.value) || 0)}
                    />
                  </div>

                  <div className="border-t pt-2">
                    <div className="flex justify-between text-lg font-bold">
                      <span>{t.total}:</span>
                      <span>{calculateTotal().toLocaleString()} MMK</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Additional Details */}
            <Card>
              <CardContent className="space-y-4 pt-6">
                <div>
                  <Label htmlFor="paymentTerms">{t.paymentTerms}</Label>
                  <select
                    id="paymentTerms"
                    value={paymentTerms}
                    onChange={(e) => setPaymentTerms(e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                    aria-label={t.paymentTerms}
                  >
                    <option value="cash">{t.paymentTermsOptions.cash}</option>
                    <option value="net_15">{t.paymentTermsOptions.net_15}</option>
                    <option value="net_30">{t.paymentTermsOptions.net_30}</option>
                    <option value="net_45">{t.paymentTermsOptions.net_45}</option>
                    <option value="net_60">{t.paymentTermsOptions.net_60}</option>
                    <option value="net_90">{t.paymentTermsOptions.net_90}</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="shippingMethod">{t.shippingMethod}</Label>
                  <select
                    id="shippingMethod"
                    value={shippingMethod}
                    onChange={(e) => setShippingMethod(e.target.value)}
                    className="w-full mt-1 px-3 py-2 border border-gray-300 rounded-md"
                    aria-label={t.shippingMethod}
                  >
                    <option value="pickup">{t.shippingMethods.pickup}</option>
                    <option value="delivery">{t.shippingMethods.delivery}</option>
                    <option value="courier">{t.shippingMethods.courier}</option>
                    <option value="freight">{t.shippingMethods.freight}</option>
                  </select>
                </div>

                <div>
                  <Label htmlFor="notes">{t.orderNotes}</Label>
                  <Textarea
                    id="notes"
                    value={notes}
                    onChange={(e) => setNotes(e.target.value)}
                    placeholder="Order notes for supplier"
                    rows={3}
                  />
                </div>

                <div>
                  <Label htmlFor="internalNotes">{t.internalNotes}</Label>
                  <Textarea
                    id="internalNotes"
                    value={internalNotes}
                    onChange={(e) => setInternalNotes(e.target.value)}
                    placeholder="Internal notes (not visible to supplier)"
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Action Buttons */}
            <div className="space-y-2">
              <Button
                onClick={() => savePurchaseOrder('draft')}
                disabled={saving}
                variant="outline"
                className="w-full"
              >
                <Save className="h-4 w-4 mr-2" />
                {saving ? 'Saving...' : t.saveAsDraft}
              </Button>

              <Button
                onClick={() => savePurchaseOrder('pending')}
                disabled={saving}
                className="w-full"
              >
                <Send className="h-4 w-4 mr-2" />
                {saving ? 'Submitting...' : t.submitForApproval}
              </Button>
            </div>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
