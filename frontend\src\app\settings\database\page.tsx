'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useSettings, useTranslation } from '@/contexts/settings-context'
import apiClient from '@/lib/api'
import {
  Database,
  ArrowLeft,
  Save,
  Check,
  Download,
  Upload,
  RefreshCw,
  Trash2,
  HardDrive,
  Activity,
  AlertTriangle,
  CheckCircle,
  Clock,
  FileText,
  Archive,
  Settings,
  BarChart3,
  FileSpreadsheet,
  FileJson,
  Package,
  Import,
  RotateCcw,
  DollarSign,
  Users,
  Brain,
  ShoppingCart,
  ShoppingBag,
  Target
} from 'lucide-react'

interface DatabaseStats {
  totalSize: string
  tableCount: number
  recordCount: number
  lastBackup: string
  backupSize: string
  uptime: string
}

interface BackupFile {
  id: string
  filename: string
  size: string
  date: string
  type: 'auto' | 'manual'
  status: 'completed' | 'failed' | 'in-progress'
}

export default function DatabaseManagementPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isBackingUp, setIsBackingUp] = useState(false)
  const [isRestoring, setIsRestoring] = useState(false)
  const [isExporting, setIsExporting] = useState(false)
  const [isImporting, setIsImporting] = useState(false)
  const [isResetting, setIsResetting] = useState(false)

  const [databaseStats] = useState<DatabaseStats>({
    totalSize: '245.7 MB',
    tableCount: 15,
    recordCount: 12847,
    lastBackup: '2024-01-15 08:00:00',
    backupSize: '89.3 MB',
    uptime: '15 days, 6 hours'
  })

  const [backupSettings, setBackupSettings] = useState({
    autoBackup: true,
    backupFrequency: 'daily',
    backupTime: '02:00',
    retentionDays: 30,
    compressionEnabled: true,
    encryptBackups: true
  })

  const [maintenanceSettings, setMaintenanceSettings] = useState({
    autoOptimize: true,
    optimizeFrequency: 'weekly',
    cleanupLogs: true,
    logRetentionDays: 90,
    vacuumDatabase: true
  })

  const [backupFiles, setBackupFiles] = useState<BackupFile[]>([])

  // Load backup files
  const loadBackupFiles = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.getBackups()

      if (response.success) {
        // Transform API data to match UI interface
        const transformedBackups = response.data.map((backup: any) => ({
          id: backup.id,
          filename: backup.name,
          size: `${(backup.size / (1024 * 1024)).toFixed(1)} MB`,
          date: new Date(backup.timestamp).toLocaleString(),
          type: backup.type || 'manual',
          status: backup.status || 'completed'
        }))
        setBackupFiles(transformedBackups)
      } else {
        setError(response.message || 'Failed to load backup files')
      }
    } catch (error) {
      console.error('Error loading backup files:', error)
      setError('Failed to load backup files')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    } else if (isAuthenticated) {
      loadBackupFiles()
    }
  }, [isAuthenticated, isLoading, router])

  const updateBackupSetting = (field: string, value: any) => {
    setBackupSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const updateMaintenanceSetting = (field: string, value: any) => {
    setMaintenanceSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleManualBackup = async () => {
    setIsBackingUp(true)
    try {
      const response = await apiClient.createBackup({
        includeImages: true,
        compress: backupSettings.compressionEnabled
      })

      if (response.success) {
        alert(language === 'mm'
          ? 'Manual backup အောင်မြင်စွာ ပြီးစီးပါပြီ!'
          : 'Manual backup completed successfully!'
        )
        // Reload backup files list
        loadBackupFiles()
      } else {
        throw new Error(response.message || 'Backup failed')
      }
    } catch (error) {
      console.error('Backup error:', error)
      alert(language === 'mm'
        ? 'Backup မအောင်မြင်ပါ!'
        : 'Backup failed!'
      )
    } finally {
      setIsBackingUp(false)
    }
  }

  const handleRestore = async (backupId: string, filename: string) => {
    const confirmMessage = language === 'mm'
      ? `${filename} မှ ပြန်လည်ရယူမည်လား? ဤလုပ်ဆောင်ချက်သည် လက်ရှိ ဒေတာများကို အစားထိုးမည်ဖြစ်သည်။`
      : `Are you sure you want to restore from ${filename}? This will overwrite current data.`

    if (!confirm(confirmMessage)) {
      return
    }

    setIsRestoring(true)
    try {
      const response = await apiClient.restoreBackup(backupId)

      if (response.success) {
        alert(language === 'mm'
          ? 'Database ပြန်လည်ရယူမှု အောင်မြင်ပါသည်!'
          : 'Database restored successfully!'
        )
        // Reload page to reflect restored data
        window.location.reload()
      } else {
        throw new Error(response.message || 'Restore failed')
      }
    } catch (error) {
      console.error('Restore error:', error)
      alert(language === 'mm'
        ? 'ပြန်လည်ရယူမှု မအောင်မြင်ပါ!'
        : 'Restore failed!'
      )
    } finally {
      setIsRestoring(false)
    }
  }

  const handleDeleteBackup = async (backupId: string, filename: string) => {
    const confirmMessage = language === 'mm'
      ? `${filename} ကို ဖျက်မည်လား?`
      : `Delete backup ${filename}?`

    if (!confirm(confirmMessage)) {
      return
    }

    try {
      const response = await apiClient.deleteBackup(backupId)

      if (response.success) {
        alert(language === 'mm'
          ? 'Backup ဖျက်ပြီးပါပြီ'
          : 'Backup deleted successfully'
        )
        // Reload backup files list
        loadBackupFiles()
      } else {
        throw new Error(response.message || 'Delete failed')
      }
    } catch (error) {
      console.error('Delete backup error:', error)
      alert(language === 'mm'
        ? 'Backup ဖျက်မှု မအောင်မြင်ပါ'
        : 'Failed to delete backup'
      )
    }
  }

  const handleOptimizeDatabase = async () => {
    if (!confirm('This will optimize the database and may take several minutes. Continue?')) {
      return
    }

    try {
      await new Promise(resolve => setTimeout(resolve, 2000))
      alert('Database optimization completed!')
    } catch (error) {
      alert('Optimization failed!')
    }
  }

  const exportData = async (format: 'csv' | 'json') => {
    setIsExporting(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Mock data for export
      const mockData = {
        products: [
          { id: 1, name: 'Gaming Mouse', price: 45000, category: 'Accessories', stock: 25 },
          { id: 2, name: 'Mechanical Keyboard', price: 85000, category: 'Accessories', stock: 15 },
          { id: 3, name: 'Dell XPS 13', price: 1850000, category: 'Laptops', stock: 5 }
        ],
        sales: [
          { id: 1, date: '2024-01-15', total: 130000, customer: 'John Doe' },
          { id: 2, date: '2024-01-14', total: 85000, customer: 'Jane Smith' }
        ],
        customers: [
          { id: 1, name: 'John Doe', email: '<EMAIL>', phone: '+95 9 123 456 789' },
          { id: 2, name: 'Jane Smith', email: '<EMAIL>', phone: '+95 9 987 654 321' }
        ]
      }

      if (format === 'csv') {
        // Export as CSV
        const csvContent = Object.entries(mockData).map(([table, data]) => {
          if (data.length === 0) return `${table.toUpperCase()}\nNo data\n\n`

          const headers = Object.keys(data[0]).join(',')
          const rows = data.map(row => Object.values(row).join(',')).join('\n')
          return `${table.toUpperCase()}\n${headers}\n${rows}\n\n`
        }).join('')

        const blob = new Blob([csvContent], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `bitstech_data_${new Date().toISOString().slice(0, 10)}.csv`
        link.click()
        URL.revokeObjectURL(url)
      } else {
        // Export as JSON
        const jsonContent = JSON.stringify(mockData, null, 2)
        const blob = new Blob([jsonContent], { type: 'application/json' })
        const url = URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `bitstech_data_${new Date().toISOString().slice(0, 10)}.json`
        link.click()
        URL.revokeObjectURL(url)
      }

      alert(language === 'mm'
        ? `${format.toUpperCase()} ဖိုင် ထုတ်ယူပြီးပါပြီ`
        : `${format.toUpperCase()} export completed successfully`
      )
    } catch (error) {
      alert(language === 'mm'
        ? 'ဒေတာ ထုတ်ယူမှု မအောင်မြင်ပါ'
        : 'Data export failed'
      )
    } finally {
      setIsExporting(false)
    }
  }

  const importData = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    setIsImporting(true)
    const reader = new FileReader()

    reader.onload = async (e) => {
      try {
        const content = e.target?.result as string

        if (file.name.endsWith('.json')) {
          const data = JSON.parse(content)
          console.log('Imported JSON data:', data)
        } else if (file.name.endsWith('.csv')) {
          console.log('Imported CSV data:', content)
        }

        await new Promise(resolve => setTimeout(resolve, 2000))

        alert(language === 'mm'
          ? 'ဒေတာ သွင်းယူမှု အောင်မြင်ပါသည်'
          : 'Data import completed successfully'
        )
      } catch (error) {
        alert(language === 'mm'
          ? 'ဒေတာ သွင်းယူမှု မအောင်မြင်ပါ'
          : 'Data import failed'
        )
      } finally {
        setIsImporting(false)
      }
    }

    reader.readAsText(file)
    // Reset input
    event.target.value = ''
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error('Error saving database settings:', error)
    } finally {
      setSaving(false)
    }
  }

  // Data Reset Functions
  const handleResetAllData = async () => {
    const confirmMessage = language === 'mm'
      ? 'ဒေတာ အားလုံးကို ပြန်လည်သတ်မှတ်မည်လား? ဤလုပ်ဆောင်ချက်ကို ပြန်ပြင်၍မရပါ။'
      : 'Are you sure you want to reset ALL data? This action cannot be undone.'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetAllData()
      if (response.success) {
        window.alert(language === 'mm'
          ? 'ဒေတာ အားလုံး ပြန်လည်သတ်မှတ်ပြီးပါပြီ'
          : 'All data has been reset successfully'
        )
        // Refresh page to show reset data
        window.location.reload()
      } else {
        throw new Error('Reset failed')
      }
    } catch (error) {
      console.error('Reset error:', error)
      window.alert(language === 'mm'
        ? 'ဒေတာ ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ'
        : 'Data reset failed'
      )
    } finally {
      setIsResetting(false)
    }
  }

  const handleClearResetFlag = async () => {
    const confirmMessage = language === 'mm'
      ? 'Reset flag ကို ရှင်းလင်းမည်လား? ဒါဆိုရင် စနစ်က mock data အသစ်တွေ ပြန်ထုတ်ပေးနိုင်မယ်။'
      : 'Clear the reset flag? This will allow the system to generate new mock data again.'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.clearResetFlag()
      if (response.success) {
        window.alert(language === 'mm'
          ? 'Reset flag ရှင်းလင်းပြီးပါပြီ'
          : 'Reset flag cleared successfully'
        )
        // Refresh page to show changes
        window.location.reload()
      } else {
        throw new Error('Clear reset flag failed')
      }
    } catch (error) {
      console.error('Clear reset flag error:', error)
      window.alert(language === 'mm'
        ? 'Reset flag ရှင်းလင်းမှု မအောင်မြင်ပါ'
        : 'Clear reset flag failed'
      )
    } finally {
      setIsResetting(false)
    }
  }

  // Specific reset handlers
  const handleResetForecasting = async () => {
    const confirmMessage = language === 'mm'
      ? 'Forecasting data ကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Reset forecasting data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetForecastingData()
      if (response.success) {
        window.alert(response.message)
        window.location.reload()
      } else {
        throw new Error('Reset failed')
      }
    } catch (error) {
      console.error('Reset error:', error)
      window.alert(language === 'mm' ? 'ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ' : 'Reset failed')
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetPurchaseOrders = async () => {
    const confirmMessage = language === 'mm'
      ? 'Purchase Orders data ကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Reset purchase orders data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetPurchaseOrdersData()
      if (response.success) {
        window.alert(response.message)
        window.location.reload()
      } else {
        throw new Error('Reset failed')
      }
    } catch (error) {
      console.error('Reset error:', error)
      window.alert(language === 'mm' ? 'ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ' : 'Reset failed')
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetReports = async () => {
    const confirmMessage = language === 'mm'
      ? 'Reports data ကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Reset reports data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetReportsData()
      if (response.success) {
        window.alert(response.message)
        window.location.reload()
      } else {
        throw new Error('Reset failed')
      }
    } catch (error) {
      console.error('Reset error:', error)
      window.alert(language === 'mm' ? 'ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ' : 'Reset failed')
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetUsers = async () => {
    const confirmMessage = language === 'mm'
      ? 'Users data ကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Reset users data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetUsersData()
      if (response.success) {
        window.alert(response.message)
        window.location.reload()
      } else {
        throw new Error('Reset failed')
      }
    } catch (error) {
      console.error('Reset error:', error)
      window.alert(language === 'mm' ? 'ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ' : 'Reset failed')
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetAnalytics = async () => {
    const confirmMessage = language === 'mm'
      ? 'Analytics data ကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Reset analytics data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetAnalyticsData()
      if (response.success) {
        window.alert(response.message)
        window.location.reload()
      } else {
        throw new Error('Reset failed')
      }
    } catch (error) {
      console.error('Reset error:', error)
      window.alert(language === 'mm' ? 'ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ' : 'Reset failed')
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetKPIs = async () => {
    const confirmMessage = language === 'mm'
      ? 'KPI data ကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Reset KPI data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetKPIData()
      if (response.success) {
        window.alert(response.message)
        window.location.reload()
      } else {
        throw new Error('Reset failed')
      }
    } catch (error) {
      console.error('Reset error:', error)
      window.alert(language === 'mm' ? 'ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ' : 'Reset failed')
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetDashboard = async () => {
    const confirmMessage = language === 'mm'
      ? 'Dashboard ဒေတာကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Are you sure you want to reset Dashboard data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      localStorage.removeItem('bitstech_dashboard_data')
      localStorage.removeItem('bitstech_analytics_data')

      window.alert(language === 'mm'
        ? 'Dashboard ဒေတာ ပြန်လည်သတ်မှတ်ပြီးပါပြီ'
        : 'Dashboard data has been reset successfully'
      )
      // Refresh to show changes
      window.location.href = '/dashboard'
    } catch (error) {
      console.error('Dashboard reset error:', error)
      window.alert(language === 'mm'
        ? 'Dashboard ဒေတာ ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ'
        : 'Dashboard data reset failed'
      )
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetSales = async () => {
    const confirmMessage = language === 'mm'
      ? 'ရောင်းချမှု ဒေတာကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Are you sure you want to reset Sales data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetSalesData()
      if (response.success) {
        window.alert(language === 'mm'
          ? 'ရောင်းချမှု ဒေတာ ပြန်လည်သတ်မှတ်ပြီးပါပြီ'
          : 'Sales data has been reset successfully'
        )
      } else {
        throw new Error('Sales reset failed')
      }
    } catch (error) {
      console.error('Sales reset error:', error)
      window.alert(language === 'mm'
        ? 'ရောင်းချမှု ဒေတာ ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ'
        : 'Sales data reset failed'
      )
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetInventory = async () => {
    const confirmMessage = language === 'mm'
      ? 'စတော့ ဒေတာကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Are you sure you want to reset Inventory data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetInventoryData()
      if (response.success) {
        window.alert(language === 'mm'
          ? 'စတော့ ဒေတာ ပြန်လည်သတ်မှတ်ပြီးပါပြီ'
          : 'Inventory data has been reset successfully'
        )
      } else {
        throw new Error('Inventory reset failed')
      }
    } catch (error) {
      console.error('Inventory reset error:', error)
      window.alert(language === 'mm'
        ? 'စတော့ ဒေတာ ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ'
        : 'Inventory data reset failed'
      )
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetCustomers = async () => {
    const confirmMessage = language === 'mm'
      ? 'ဖောက်သည် ဒေတာကို ပြန်လည်သတ်မှတ်မည်လား?'
      : 'Are you sure you want to reset Customer data?'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      localStorage.removeItem('bitstech_customers')

      window.alert(language === 'mm'
        ? 'ဖောက်သည် ဒေတာ ပြန်လည်သတ်မှတ်ပြီးပါပြီ'
        : 'Customer data has been reset successfully'
      )
      window.location.reload()
    } catch (error) {
      console.error('Customer reset error:', error)
      window.alert(language === 'mm'
        ? 'ဖောက်သည် ဒေတာ ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ'
        : 'Customer data reset failed'
      )
    } finally {
      setIsResetting(false)
    }
  }

  const handleResetProducts = async () => {
    const confirmMessage = language === 'mm'
      ? 'ကုန်ပစ္စည်း ဒေတာကို ပြန်လည်သတ်မှတ်မည်လား? ဤလုပ်ဆောင်ချက်ကို ပြန်ပြင်၍မရပါ။'
      : 'Are you sure you want to reset Products data? This action cannot be undone.'

    if (!window.confirm(confirmMessage)) return

    setIsResetting(true)
    try {
      const response = await apiClient.resetProductsData()
      if (response.success) {
        window.alert(language === 'mm'
          ? 'ကုန်ပစ္စည်း ဒေတာ ပြန်လည်သတ်မှတ်ပြီးပါပြီ'
          : 'Products data has been reset successfully'
        )

        // Trigger storage event to notify other pages
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'bitstech_products',
          newValue: null,
          oldValue: localStorage.getItem('bitstech_products')
        }))

        window.location.reload()
      } else {
        throw new Error('Products reset failed')
      }
    } catch (error) {
      console.error('Products reset error:', error)
      window.alert(language === 'mm'
        ? 'ကုန်ပစ္စည်း ဒေတာ ပြန်လည်သတ်မှတ်မှု မအောင်မြင်ပါ'
        : 'Products data reset failed'
      )
    } finally {
      setIsResetting(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'completed': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'failed': return <AlertTriangle className="h-4 w-4 text-red-600" />
      case 'in-progress': return <Clock className="h-4 w-4 text-yellow-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600 bg-green-100 dark:bg-green-900/20'
      case 'failed': return 'text-red-600 bg-red-100 dark:bg-red-900/20'
      case 'in-progress': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20'
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-teal-50 dark:hover:bg-teal-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-teal-600 to-cyan-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Database className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {language === 'mm' ? 'ဒေတာ စီမံခန့်ခွဲမှု' : 'Data Management'}
                </h1>
                <p className="text-white/80 mt-1">
                  {language === 'mm'
                    ? 'အရန်သိမ်းမှု၊ ပြန်လည်ရယူမှု၊ ဒေတာ တင်သွင်း/ထုတ်ယူမှု နှင့် စနစ် ပြုပြင်ထိန်းသိမ်းမှု'
                    : 'Backup, restore, import/export data, and system maintenance'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Database Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <HardDrive className="h-8 w-8 text-blue-600" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း အရွယ်အစား' : 'Total Size'}
                  </p>
                  <p className="text-xl font-bold">{databaseStats.totalSize}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <FileText className="h-8 w-8 text-green-600" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'ဇယား အရေအတွက်' : 'Tables'}
                  </p>
                  <p className="text-xl font-bold">{databaseStats.tableCount}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <BarChart3 className="h-8 w-8 text-purple-600" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'မှတ်တမ်းများ' : 'Records'}
                  </p>
                  <p className="text-xl font-bold">{databaseStats.recordCount.toLocaleString()}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Archive className="h-8 w-8 text-orange-600" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'နောက်ဆုံး အရန်သိမ်းမှု' : 'Last Backup'}
                  </p>
                  <p className="text-sm font-bold">{databaseStats.lastBackup}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Download className="h-8 w-8 text-teal-600" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'အရန်သိမ်းမှု အရွယ်အစား' : 'Backup Size'}
                  </p>
                  <p className="text-xl font-bold">{databaseStats.backupSize}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center gap-3">
                <Activity className="h-8 w-8 text-red-600" />
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စနစ် အလုပ်လုပ်ချိန်' : 'Uptime'}
                  </p>
                  <p className="text-sm font-bold">{databaseStats.uptime}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Data Reset Section */}
        <Card className="border-red-200 dark:border-red-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <RotateCcw className="h-5 w-5" />
              {language === 'mm' ? 'ဒေတာ ပြန်လည်သတ်မှတ်မှု' : 'Data Reset'}
            </CardTitle>
            <CardDescription>
              {language === 'mm'
                ? 'စနစ်ကို အသစ်စတင်အသုံးပြုရန် ဒေတာများကို ပြန်လည်သတ်မှတ်ပါ'
                : 'Reset data to start fresh with the POS system'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Warning Message */}
              <div className="p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                <div className="flex items-start gap-3">
                  <AlertTriangle className="h-5 w-5 text-red-600 mt-0.5" />
                  <div>
                    <h4 className="font-medium text-red-800 dark:text-red-200">
                      {language === 'mm' ? 'သတိပေးချက်' : 'Warning'}
                    </h4>
                    <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                      {language === 'mm'
                        ? 'ဒေတာ ပြန်လည်သတ်မှတ်ခြင်းကို ပြန်ပြင်၍မရပါ။ ဤလုပ်ဆောင်ချက်မပြုလုပ်မီ အရန်သိမ်းမှု လုပ်ထားရန် အကြံပြုပါသည်။'
                        : 'Data reset cannot be undone. It is recommended to create a backup before performing this action.'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Reset Options */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {/* Reset All Data */}
                <div className="p-4 border border-red-200 dark:border-red-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <Trash2 className="h-5 w-5 text-red-600" />
                    <div>
                      <h4 className="font-medium text-red-800 dark:text-red-200">
                        {language === 'mm' ? 'ဒေတာ အားလုံး ပြန်လည်သတ်မှတ်' : 'Reset All Data'}
                      </h4>
                      <p className="text-xs text-red-600 dark:text-red-400">
                        {language === 'mm' ? 'အားလုံးကို ရှင်းလင်းမည်' : 'Clears everything'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={handleResetAllData}
                    disabled={isResetting}
                    className="w-full"
                  >
                    {isResetting ? (
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                    ) : (
                      <Trash2 className="h-4 w-4 mr-2" />
                    )}
                    {language === 'mm' ? 'အားလုံး ပြန်လည်သတ်မှတ်' : 'Reset All'}
                  </Button>
                </div>

                {/* Reset Dashboard */}
                <div className="p-4 border border-orange-200 dark:border-orange-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <BarChart3 className="h-5 w-5 text-orange-600" />
                    <div>
                      <h4 className="font-medium text-orange-800 dark:text-orange-200">
                        {language === 'mm' ? 'Dashboard ဒေတာ' : 'Dashboard Data'}
                      </h4>
                      <p className="text-xs text-orange-600 dark:text-orange-400">
                        {language === 'mm' ? 'စာရင်းဇယား ဒေတာများ' : 'Statistics & charts'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetDashboard}
                    disabled={isResetting}
                    className="w-full border-orange-300 text-orange-700 hover:bg-orange-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Reset Sales */}
                <div className="p-4 border border-blue-200 dark:border-blue-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <DollarSign className="h-5 w-5 text-blue-600" />
                    <div>
                      <h4 className="font-medium text-blue-800 dark:text-blue-200">
                        {language === 'mm' ? 'ရောင်းချမှု ဒေတာ' : 'Sales Data'}
                      </h4>
                      <p className="text-xs text-blue-600 dark:text-blue-400">
                        {language === 'mm' ? 'ရောင်းချမှု မှတ်တမ်းများ' : 'Sales records & transactions'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetSales}
                    disabled={isResetting}
                    className="w-full border-blue-300 text-blue-700 hover:bg-blue-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Reset Inventory */}
                <div className="p-4 border border-green-200 dark:border-green-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <Package className="h-5 w-5 text-green-600" />
                    <div>
                      <h4 className="font-medium text-green-800 dark:text-green-200">
                        {language === 'mm' ? 'စတော့ ဒေတာ' : 'Inventory Data'}
                      </h4>
                      <p className="text-xs text-green-600 dark:text-green-400">
                        {language === 'mm' ? 'ကုန်ပစ္စည်း နှင့် စတော့' : 'Products & stock levels'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetInventory}
                    disabled={isResetting}
                    className="w-full border-green-300 text-green-700 hover:bg-green-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Reset Products */}
                <div className="p-4 border border-teal-200 dark:border-teal-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <ShoppingBag className="h-5 w-5 text-teal-600" />
                    <div>
                      <h4 className="font-medium text-teal-800 dark:text-teal-200">
                        {language === 'mm' ? 'ကုန်ပစ္စည်း ဒေတာ' : 'Products Data'}
                      </h4>
                      <p className="text-xs text-teal-600 dark:text-teal-400">
                        {language === 'mm' ? 'ကုန်ပစ္စည်း ကတ်များ' : 'Product cards & information'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetProducts}
                    disabled={isResetting}
                    className="w-full border-teal-300 text-teal-700 hover:bg-teal-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Reset Customers */}
                <div className="p-4 border border-purple-200 dark:border-purple-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <Users className="h-5 w-5 text-purple-600" />
                    <div>
                      <h4 className="font-medium text-purple-800 dark:text-purple-200">
                        {language === 'mm' ? 'ဖောက်သည် ဒေတာ' : 'Customer Data'}
                      </h4>
                      <p className="text-xs text-purple-600 dark:text-purple-400">
                        {language === 'mm' ? 'ဖောက်သည် အချက်အလက်များ' : 'Customer information'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetCustomers}
                    disabled={isResetting}
                    className="w-full border-purple-300 text-purple-700 hover:bg-purple-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Clear Reset Flag */}
                <div className="p-4 border border-cyan-200 dark:border-cyan-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <RefreshCw className="h-5 w-5 text-cyan-600" />
                    <div>
                      <h4 className="font-medium text-cyan-800 dark:text-cyan-200">
                        {language === 'mm' ? 'Reset Flag ရှင်းလင်းရန်' : 'Clear Reset Flag'}
                      </h4>
                      <p className="text-xs text-cyan-600 dark:text-cyan-400">
                        {language === 'mm' ? 'Mock data ပြန်ထုတ်ပေးရန်' : 'Allow mock data generation'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleClearResetFlag}
                    disabled={isResetting}
                    className="w-full border-cyan-300 text-cyan-700 hover:bg-cyan-50"
                  >
                    <RefreshCw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ရှင်းလင်းရန်' : 'Clear Flag'}
                  </Button>
                </div>

                {/* Reset Forecasting Data */}
                <div className="p-4 border border-indigo-200 dark:border-indigo-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <Brain className="h-5 w-5 text-indigo-600" />
                    <div>
                      <h4 className="font-medium text-indigo-800 dark:text-indigo-200">
                        {language === 'mm' ? 'Forecasting ဒေတာ' : 'Forecasting Data'}
                      </h4>
                      <p className="text-xs text-indigo-600 dark:text-indigo-400">
                        {language === 'mm' ? 'ခန့်မှန်းချက် ဒေတာများ' : 'Prediction and forecast data'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetForecasting}
                    disabled={isResetting}
                    className="w-full border-indigo-300 text-indigo-700 hover:bg-indigo-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Reset Purchase Orders Data */}
                <div className="p-4 border border-amber-200 dark:border-amber-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <ShoppingCart className="h-5 w-5 text-amber-600" />
                    <div>
                      <h4 className="font-medium text-amber-800 dark:text-amber-200">
                        {language === 'mm' ? 'ဝယ်ယူမှု အမှာစာများ' : 'Purchase Orders'}
                      </h4>
                      <p className="text-xs text-amber-600 dark:text-amber-400">
                        {language === 'mm' ? 'ဝယ်ယူမှု အမှာစာ ဒေတာများ' : 'Purchase order data'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetPurchaseOrders}
                    disabled={isResetting}
                    className="w-full border-amber-300 text-amber-700 hover:bg-amber-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Reset Reports Data */}
                <div className="p-4 border border-emerald-200 dark:border-emerald-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <BarChart3 className="h-5 w-5 text-emerald-600" />
                    <div>
                      <h4 className="font-medium text-emerald-800 dark:text-emerald-200">
                        {language === 'mm' ? 'အစီရင်ခံစာများ' : 'Reports Data'}
                      </h4>
                      <p className="text-xs text-emerald-600 dark:text-emerald-400">
                        {language === 'mm' ? 'အစီရင်ခံစာ ဒေတာများ' : 'Analytics and reports data'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetReports}
                    disabled={isResetting}
                    className="w-full border-emerald-300 text-emerald-700 hover:bg-emerald-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Reset Users Data */}
                <div className="p-4 border border-rose-200 dark:border-rose-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <Users className="h-5 w-5 text-rose-600" />
                    <div>
                      <h4 className="font-medium text-rose-800 dark:text-rose-200">
                        {language === 'mm' ? 'အသုံးပြုသူ ဒေတာ' : 'Users Data'}
                      </h4>
                      <p className="text-xs text-rose-600 dark:text-rose-400">
                        {language === 'mm' ? 'အသုံးပြုသူ အချက်အလက်များ' : 'User information and permissions'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetUsers}
                    disabled={isResetting}
                    className="w-full border-rose-300 text-rose-700 hover:bg-rose-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Reset Analytics Data */}
                <div className="p-4 border border-purple-200 dark:border-purple-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <BarChart3 className="h-5 w-5 text-purple-600" />
                    <div>
                      <h4 className="font-medium text-purple-800 dark:text-purple-200">
                        {language === 'mm' ? 'Analytics ဒေတာ' : 'Analytics Data'}
                      </h4>
                      <p className="text-xs text-purple-600 dark:text-purple-400">
                        {language === 'mm' ? 'KPI နှင့် ခွဲခြမ်းစိတ်ဖြာမှု ဒေတာများ' : 'KPI and analytics data'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetAnalytics}
                    disabled={isResetting}
                    className="w-full border-purple-300 text-purple-700 hover:bg-purple-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>

                {/* Reset KPI Data */}
                <div className="p-4 border border-teal-200 dark:border-teal-800 rounded-lg">
                  <div className="flex items-center gap-3 mb-3">
                    <Target className="h-5 w-5 text-teal-600" />
                    <div>
                      <h4 className="font-medium text-teal-800 dark:text-teal-200">
                        {language === 'mm' ? 'KPI ဒေတာ' : 'KPI Data'}
                      </h4>
                      <p className="text-xs text-teal-600 dark:text-teal-400">
                        {language === 'mm' ? 'လုပ်ငန်း KPI များ' : 'Business KPI metrics'}
                      </p>
                    </div>
                  </div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleResetKPIs}
                    disabled={isResetting}
                    className="w-full border-teal-300 text-teal-700 hover:bg-teal-50"
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset'}
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Backup Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Archive className="h-5 w-5 text-teal-600" />
                {language === 'mm' ? 'အရန်သိမ်းမှု ဆက်တင်များ' : 'Backup Settings'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'အလိုအလျောက် အရန်သိမ်းမှု နှင့် ဒေတာ ကာကွယ်မှု ဆက်တင်များ'
                  : 'Automatic backup and data protection settings'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Auto Backup */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {language === 'mm' ? 'အလိုအလျောက် အရန်သိမ်းမှု' : 'Automatic Backup'}
                  </span>
                  <input
                    type="checkbox"
                    checked={backupSettings.autoBackup}
                    onChange={(e) => updateBackupSetting('autoBackup', e.target.checked)}
                    className="rounded"
                    aria-label={language === 'mm' ? 'အလိုအလျောက် အရန်သိမ်းမှု' : 'Automatic Backup'}
                  />
                </div>

                {backupSettings.autoBackup && (
                  <div className="space-y-3 ml-4">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">
                        {language === 'mm' ? 'ကြိမ်နှုန်း' : 'Frequency'}
                      </span>
                      <select
                        className="p-1 border border-gray-300 rounded dark:border-gray-600 dark:bg-gray-800 text-sm"
                        value={backupSettings.backupFrequency}
                        onChange={(e) => updateBackupSetting('backupFrequency', e.target.value)}
                        aria-label={language === 'mm' ? 'ကြိမ်နှုန်း' : 'Frequency'}
                      >
                        <option value="hourly">{language === 'mm' ? 'နာရီတိုင်း' : 'Hourly'}</option>
                        <option value="daily">{language === 'mm' ? 'နေ့စဉ်' : 'Daily'}</option>
                        <option value="weekly">{language === 'mm' ? 'အပတ်စဉ်' : 'Weekly'}</option>
                      </select>
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">
                        {language === 'mm' ? 'အရန်သိမ်းမှု အချိန်' : 'Backup Time'}
                      </span>
                      <Input
                        type="time"
                        value={backupSettings.backupTime}
                        onChange={(e) => updateBackupSetting('backupTime', e.target.value)}
                        className="w-24 text-sm"
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <span className="text-sm">
                        {language === 'mm' ? 'သိမ်းဆည်းမှု ရက်များ' : 'Retention (days)'}
                      </span>
                      <Input
                        type="number"
                        value={backupSettings.retentionDays}
                        onChange={(e) => updateBackupSetting('retentionDays', parseInt(e.target.value) || 30)}
                        className="w-20 text-sm"
                        min="7"
                        max="365"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Backup Options */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">
                    {language === 'mm' ? 'ဖိသိပ်မှု ဖွင့်ရန်' : 'Enable Compression'}
                  </span>
                  <input
                    type="checkbox"
                    checked={backupSettings.compressionEnabled}
                    onChange={(e) => updateBackupSetting('compressionEnabled', e.target.checked)}
                    className="rounded"
                    aria-label={language === 'mm' ? 'ဖိသိပ်မှု ဖွင့်ရန်' : 'Enable Compression'}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm">
                    {language === 'mm' ? 'အရန်သိမ်းမှု ကုဒ်ဝှက်ခြင်း' : 'Encrypt Backups'}
                  </span>
                  <input
                    type="checkbox"
                    checked={backupSettings.encryptBackups}
                    onChange={(e) => updateBackupSetting('encryptBackups', e.target.checked)}
                    className="rounded"
                    aria-label={language === 'mm' ? 'အရန်သိမ်းမှု ကုဒ်ဝှက်ခြင်း' : 'Encrypt Backups'}
                  />
                </div>
              </div>

              {/* Manual Backup */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  onClick={handleManualBackup}
                  disabled={isBackingUp}
                  className="w-full bg-gradient-to-r from-teal-600 to-cyan-600 hover:from-teal-700 hover:to-cyan-700"
                >
                  {isBackingUp ? (
                    <div className="flex items-center gap-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                      {language === 'mm' ? 'အရန်သိမ်းနေသည်...' : 'Creating Backup...'}
                    </div>
                  ) : (
                    <div className="flex items-center gap-2">
                      <Download className="h-4 w-4" />
                      {language === 'mm' ? 'ယခုပင် အရန်သိမ်းရန်' : 'Create Backup Now'}
                    </div>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Maintenance Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Settings className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'ပြုပြင်ထိန်းသိမ်းမှု ဆက်တင်များ' : 'Maintenance Settings'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ဒေတာဘေ့စ် ပြုပြင်ထိန်းသိမ်းမှု နှင့် ပိုမို ကောင်းမွန်အောင် လုပ်ခြင်း'
                  : 'Database maintenance and optimization settings'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Auto Optimization */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm font-medium">
                    {language === 'mm' ? 'အလিုအလျောက် ပိုမို ကောင်းမွန်အောင် လုပ်ခြင်း' : 'Auto Optimization'}
                  </span>
                  <input
                    type="checkbox"
                    checked={maintenanceSettings.autoOptimize}
                    onChange={(e) => updateMaintenanceSetting('autoOptimize', e.target.checked)}
                    className="rounded"
                    aria-label={language === 'mm' ? 'အလိုအလျောက် ပိုမို ကောင်းမွန်အောင် လုပ်ခြင်း' : 'Auto Optimization'}
                  />
                </div>

                {maintenanceSettings.autoOptimize && (
                  <div className="flex items-center justify-between ml-4">
                    <span className="text-sm">
                      {language === 'mm' ? 'ကြိမ်နှုန်း' : 'Frequency'}
                    </span>
                    <select
                      className="p-1 border border-gray-300 rounded dark:border-gray-600 dark:bg-gray-800 text-sm"
                      value={maintenanceSettings.optimizeFrequency}
                      onChange={(e) => updateMaintenanceSetting('optimizeFrequency', e.target.value)}
                      aria-label={language === 'mm' ? 'ကြိမ်နှုန်း' : 'Frequency'}
                    >
                      <option value="daily">{language === 'mm' ? 'နေ့စဉ်' : 'Daily'}</option>
                      <option value="weekly">{language === 'mm' ? 'အပတ်စဉ်' : 'Weekly'}</option>
                      <option value="monthly">{language === 'mm' ? 'လစဉ်' : 'Monthly'}</option>
                    </select>
                  </div>
                )}
              </div>

              {/* Log Management */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm">
                    {language === 'mm' ? 'မှတ်တမ်းများ သန့်ရှင်းရေး' : 'Cleanup Logs'}
                  </span>
                  <input
                    type="checkbox"
                    checked={maintenanceSettings.cleanupLogs}
                    onChange={(e) => updateMaintenanceSetting('cleanupLogs', e.target.checked)}
                    className="rounded"
                    aria-label={language === 'mm' ? 'မှတ်တမ်းများ သန့်ရှင်းရေး' : 'Cleanup Logs'}
                  />
                </div>

                {maintenanceSettings.cleanupLogs && (
                  <div className="flex items-center justify-between ml-4">
                    <span className="text-sm">
                      {language === 'mm' ? 'မှတ်တမ်း သိမ်းဆည်းမှု (ရက်)' : 'Log Retention (days)'}
                    </span>
                    <Input
                      type="number"
                      value={maintenanceSettings.logRetentionDays}
                      onChange={(e) => updateMaintenanceSetting('logRetentionDays', parseInt(e.target.value) || 90)}
                      className="w-20 text-sm"
                      min="30"
                      max="365"
                    />
                  </div>
                )}

                <div className="flex items-center justify-between">
                  <span className="text-sm">
                    {language === 'mm' ? 'ဒေတာဘေ့စ် Vacuum' : 'Vacuum Database'}
                  </span>
                  <input
                    type="checkbox"
                    checked={maintenanceSettings.vacuumDatabase}
                    onChange={(e) => updateMaintenanceSetting('vacuumDatabase', e.target.checked)}
                    className="rounded"
                    aria-label={language === 'mm' ? 'ဒေတာဘေ့စ် Vacuum' : 'Vacuum Database'}
                  />
                </div>
              </div>

              {/* Manual Optimization */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <Button
                  onClick={handleOptimizeDatabase}
                  variant="outline"
                  className="w-full"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ယခုပင် ပိုမို ကောင်းမွန်အောင် လုပ်ရန်' : 'Optimize Now'}
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Data Export/Import */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-orange-600" />
                {language === 'mm' ? 'ဒေတာ တင်သွင်း/ထုတ်ယူမှု' : 'Data Import/Export'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ဒေတာကို CSV သို့မဟုတ် JSON ဖိုင်အဖြစ် တင်သွင်း/ထုတ်ယူပါ'
                  : 'Import/export data as CSV or JSON files'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Export Section */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'ဒေတာ ထုတ်ယူမှု' : 'Export Data'}
                </Label>

                <div className="grid grid-cols-2 gap-3">
                  <Button
                    onClick={() => exportData('csv')}
                    disabled={isExporting}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <FileSpreadsheet className="h-4 w-4" />
                    {isExporting ? (
                      language === 'mm' ? 'ထုတ်ယူနေသည်...' : 'Exporting...'
                    ) : (
                      language === 'mm' ? 'CSV ထုတ်ယူ' : 'Export CSV'
                    )}
                  </Button>

                  <Button
                    onClick={() => exportData('json')}
                    disabled={isExporting}
                    variant="outline"
                    className="flex items-center gap-2"
                  >
                    <FileJson className="h-4 w-4" />
                    {isExporting ? (
                      language === 'mm' ? 'ထုတ်ယူနေသည်...' : 'Exporting...'
                    ) : (
                      language === 'mm' ? 'JSON ထုတ်ယူ' : 'Export JSON'
                    )}
                  </Button>
                </div>

                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {language === 'mm'
                    ? 'ထုတ်ယူမှုတွင် ကုန်ပစ္စည်းများ၊ ရောင်းချမှုများ နှင့် ဖောက်သည်များ ပါဝင်ပါသည်'
                    : 'Export includes products, sales, and customer data'
                  }
                </p>
              </div>

              {/* Import Section */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'ဒေတာ တင်သွင်းမှု' : 'Import Data'}
                </Label>

                <div className="relative">
                  <input
                    type="file"
                    accept=".csv,.json"
                    onChange={importData}
                    className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                    id="import-data"
                    disabled={isImporting}
                    aria-label={language === 'mm' ? 'ဒေတာ တင်သွင်းမှု' : 'Import Data'}
                  />
                  <Button
                    variant="outline"
                    className="w-full flex items-center gap-2"
                    disabled={isImporting}
                    onClick={() => document.getElementById('import-data')?.click()}
                  >
                    <Import className="h-4 w-4" />
                    {isImporting ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"></div>
                        {language === 'mm' ? 'တင်သွင်းနေသည်...' : 'Importing...'}
                      </div>
                    ) : (
                      language === 'mm' ? 'ဖိုင် ရွေးချယ်ပါ (CSV/JSON)' : 'Choose File (CSV/JSON)'
                    )}
                  </Button>
                </div>

                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {language === 'mm'
                    ? 'CSV သို့မဟုတ် JSON ဖိုင်များကို လက်ခံပါသည်။ ရှိပြီးသား ဒေတာများကို အစားထိုးပါမည်'
                    : 'Accepts CSV or JSON files. Existing data will be replaced'
                  }
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Backup Files */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Archive className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အရန်သိမ်းမှု ဖိုင်များ' : 'Backup Files'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ရရှိနိုင်သော အရန်သိမ်းမှု ဖိုင်များ နှင့် ပြန်လည်ရယူမှု ရွေးချယ်မှုများ'
                  : 'Available backup files and restore options'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {backupFiles.map((backup) => (
                  <div key={backup.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex items-center gap-4">
                      {getStatusIcon(backup.status)}
                      <div>
                        <div className="font-medium text-sm">{backup.filename}</div>
                        <div className="flex items-center gap-4 text-xs text-gray-600 dark:text-gray-400">
                          <span>{backup.size}</span>
                          <span>{backup.date}</span>
                          <Badge variant="outline" className="text-xs">
                            {backup.type}
                          </Badge>
                          <Badge className={`text-xs ${getStatusColor(backup.status)}`}>
                            {backup.status}
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2">
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-blue-600 hover:text-blue-700"
                      >
                        <Download className="h-4 w-4" />
                      </Button>
                      {backup.status === 'completed' && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleRestore(backup.id, backup.filename)}
                          disabled={isRestoring}
                          className="text-green-600 hover:text-green-700"
                        >
                          <Upload className="h-4 w-4" />
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteBackup(backup.id, backup.filename)}
                        className="text-red-600 hover:text-red-700"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-gradient-to-r from-teal-600 to-cyan-600 hover:from-teal-700 hover:to-cyan-700 text-white px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
              </div>
            ) : saved ? (
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းပြီး' : 'Saved'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းရန်' : 'Save Settings'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
