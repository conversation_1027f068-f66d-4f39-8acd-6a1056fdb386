'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import apiClient from '@/lib/api'
import {
  Package,
  Search,
  Plus,
  Minus,
  X,
  Save,
  Send,
  AlertTriangle,
  TrendingUp,
  TrendingDown,
  Edit,
  RefreshCw,
  Clock,
  FileText
} from 'lucide-react'

interface Product {
  _id: string
  name: string
  sku: string
  barcode: string
  category: {
    _id: string
    name: string
    color: string
  }
  price: number
  cost: number
  currency: string
  inventory: {
    quantity: number
    minQuantity: number
    maxQuantity: number
    unit: string
  }
}

interface AdjustmentItem {
  product: Product
  currentQuantity: number
  adjustmentQuantity: number
  newQuantity: number
  unitCost: number
  totalCost: number
  notes: string
}

interface AdjustmentForm {
  type: string
  reason: string
  notes: string
  items: AdjustmentItem[]
}

export default function NewAdjustmentPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [searchQuery, setSearchQuery] = useState('')
  const [searchResults, setSearchResults] = useState<Product[]>([])
  const [showSearch, setShowSearch] = useState(false)
  const [loading, setLoading] = useState(false)
  const [saving, setSaving] = useState(false)

  const [formData, setFormData] = useState<AdjustmentForm>({
    type: 'correction',
    reason: '',
    notes: '',
    items: []
  })

  const [errors, setErrors] = useState<Record<string, string>>({})

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchProducts()
    }
  }, [isAuthenticated])

  useEffect(() => {
    if (searchQuery.length >= 2) {
      const filtered = products.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.barcode.includes(searchQuery)
      )
      setSearchResults(filtered.slice(0, 10)) // Limit to 10 results
      setShowSearch(true)
    } else {
      setSearchResults([])
      setShowSearch(false)
    }
  }, [searchQuery, products])

  const fetchProducts = async () => {
    try {
      setLoading(true)
      const response = await apiClient.getProducts()
      setProducts(response.data || [])
    } catch (error) {
      console.error('Error fetching products:', error)
      setProducts([])
    } finally {
      setLoading(false)
    }
  }

  const addProductToAdjustment = (product: Product) => {
    // Check if product already exists
    const existingIndex = formData.items.findIndex(item => item.product._id === product._id)

    if (existingIndex >= 0) {
      // Product already exists, just clear search
      setSearchQuery('')
      setShowSearch(false)
      return
    }

    const newItem: AdjustmentItem = {
      product,
      currentQuantity: product.inventory?.quantity || 0,
      adjustmentQuantity: 0,
      newQuantity: product.inventory?.quantity || 0,
      unitCost: product.cost || 0,
      totalCost: 0,
      notes: ''
    }

    setFormData(prev => ({
      ...prev,
      items: [...prev.items, newItem]
    }))

    setSearchQuery('')
    setShowSearch(false)
  }

  const removeProductFromAdjustment = (index: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.filter((_, i) => i !== index)
    }))
  }

  const updateAdjustmentQuantity = (index: number, adjustmentQuantity: number) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => {
        if (i === index) {
          const newQuantity = item.currentQuantity + adjustmentQuantity
          const totalCost = adjustmentQuantity * item.unitCost

          return {
            ...item,
            adjustmentQuantity,
            newQuantity: Math.max(0, newQuantity), // Prevent negative quantities
            totalCost
          }
        }
        return item
      })
    }))
  }

  const updateItemNotes = (index: number, notes: string) => {
    setFormData(prev => ({
      ...prev,
      items: prev.items.map((item, i) => {
        if (i === index) {
          return { ...item, notes }
        }
        return item
      })
    }))
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}

    if (!formData.type) {
      newErrors.type = 'Adjustment type is required'
    }

    if (!formData.reason.trim()) {
      newErrors.reason = 'Reason is required'
    }

    if (formData.items.length === 0) {
      newErrors.items = 'At least one product must be added'
    }

    // Validate each item
    formData.items.forEach((item, index) => {
      if (item.adjustmentQuantity === 0) {
        newErrors[`item_${index}_quantity`] = 'Adjustment quantity cannot be zero'
      }

      if (item.newQuantity < 0) {
        newErrors[`item_${index}_negative`] = 'New quantity cannot be negative'
      }
    })

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async (status: 'draft' | 'pending') => {
    if (!validateForm()) {
      return
    }

    try {
      setSaving(true)

      const adjustmentData = {
        type: formData.type,
        reason: formData.reason,
        notes: formData.notes,
        status,
        items: formData.items.map(item => ({
          product: item.product._id,
          productName: item.product.name,
          sku: item.product.sku,
          currentQuantity: item.currentQuantity,
          adjustmentQuantity: item.adjustmentQuantity,
          newQuantity: item.newQuantity,
          unitCost: item.unitCost,
          totalCost: item.totalCost,
          notes: item.notes
        }))
      }

      // Create adjustment via API
      await apiClient.createStockAdjustment(adjustmentData)

      // Redirect to adjustments list
      router.push('/inventory/adjustments')
    } catch (error) {
      console.error('Error saving adjustment:', error)
      // You can add toast notification here
    } finally {
      setSaving(false)
    }
  }

  const getTotalCostImpact = () => {
    return formData.items.reduce((total, item) => total + item.totalCost, 0)
  }

  const getTotalItems = () => {
    return formData.items.reduce((total, item) => total + Math.abs(item.adjustmentQuantity), 0)
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'increase': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'decrease': return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'damage': return <AlertTriangle className="h-4 w-4 text-orange-600" />
      case 'theft': return <X className="h-4 w-4 text-red-600" />
      case 'expired': return <Clock className="h-4 w-4 text-gray-600" />
      case 'return': return <RefreshCw className="h-4 w-4 text-blue-600" />
      case 'transfer': return <Package className="h-4 w-4 text-purple-600" />
      default: return <Edit className="h-4 w-4 text-gray-600" />
    }
  }

  const formatPrice = (price: number, currency: string = 'MMK') => {
    return `${price.toLocaleString()} ${currency}`
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const text = {
    en: {
      title: 'New Stock Adjustment',
      description: 'Create a new inventory adjustment',
      adjustmentDetails: 'Adjustment Details',
      type: 'Adjustment Type',
      reason: 'Reason',
      reasonPlaceholder: 'Enter reason for adjustment...',
      notes: 'Additional Notes',
      notesPlaceholder: 'Optional additional notes...',
      productSelection: 'Product Selection',
      searchPlaceholder: 'Search products by name, SKU, or barcode...',
      addedProducts: 'Added Products',
      product: 'Product',
      currentStock: 'Current Stock',
      adjustment: 'Adjustment',
      newStock: 'New Stock',
      unitCost: 'Unit Cost',
      totalCost: 'Total Cost',
      itemNotes: 'Item Notes',
      summary: 'Adjustment Summary',
      totalItems: 'Total Items',
      totalImpact: 'Total Cost Impact',
      saveDraft: 'Save as Draft',
      submitApproval: 'Submit for Approval',
      cancel: 'Cancel',
      saving: 'Saving...',
      noProducts: 'No products added yet',
      noProductsDesc: 'Search and add products to adjust their stock levels',
      types: {
        increase: 'Stock Increase',
        decrease: 'Stock Decrease',
        correction: 'Stock Correction',
        damage: 'Damage',
        theft: 'Theft',
        expired: 'Expired',
        return: 'Return',
        transfer: 'Transfer'
      }
    },
    mm: {
      title: 'စတော့ ပြင်ဆင်မှု အသစ်',
      description: 'စတော့ ပြင်ဆင်မှု အသစ် ဖန်တီးရန်',
      adjustmentDetails: 'ပြင်ဆင်မှု အသေးစိတ်',
      type: 'ပြင်ဆင်မှု အမျိုးအစား',
      reason: 'အကြောင်းပြချက်',
      reasonPlaceholder: 'ပြင်ဆင်မှု အကြောင်းပြချက် ရေးပါ...',
      notes: 'နောက်ထပ် မှတ်ချက်များ',
      notesPlaceholder: 'နောက်ထပ် မှတ်ချက်များ (ရွေးချယ်ခွင့်ရှိ)...',
      productSelection: 'ကုန်ပစ္စည်း ရွေးချယ်မှု',
      searchPlaceholder: 'ကုန်ပစ္စည်း အမည်၊ SKU သို့မဟုတ် ဘားကုဒ်ဖြင့် ရှာရန်...',
      addedProducts: 'ထည့်ပြီးသား ကုန်ပစ္စည်းများ',
      product: 'ကုန်ပစ္စည်း',
      currentStock: 'လက်ရှိ စတော့',
      adjustment: 'ပြင်ဆင်မှု',
      newStock: 'စတော့ အသစ်',
      unitCost: 'ယူနစ် ကုန်ကျစရိတ်',
      totalCost: 'စုစုပေါင်း ကုန်ကျစရိတ်',
      itemNotes: 'ပစ္စည်း မှတ်ချက်',
      summary: 'ပြင်ဆင်မှု အနှစ်ချုပ်',
      totalItems: 'စုစုပေါင်း ပစ္စည်းများ',
      totalImpact: 'စုစုပေါင်း ကုန်ကျစရိတ် သက်ရောက်မှု',
      saveDraft: 'မူကြမ်းအဖြစ် သိမ်းရန်',
      submitApproval: 'အတည်ပြုချက်အတွက် တင်သွင်းရန်',
      cancel: 'မလုပ်တော့ပါ',
      saving: 'သိမ်းနေသည်...',
      noProducts: 'ကုန်ပစ္စည်း မထည့်ရသေးပါ',
      noProductsDesc: 'ကုန်ပစ္စည်းများ ရှာပြီး ၎င်းတို့၏ စတော့ အဆင့်များကို ပြင်ဆင်ရန် ထည့်ပါ',
      types: {
        increase: 'စတော့ တိုးခြင်း',
        decrease: 'စတော့ လျှော့ခြင်း',
        correction: 'စတော့ ပြင်ဆင်ခြင်း',
        damage: 'ပျက်စီးမှု',
        theft: 'ခိုးယူမှု',
        expired: 'သက်တမ်းကုန်',
        return: 'ပြန်အမ်း',
        transfer: 'လွှဲပြောင်း'
      }
    }
  }

  const t = text[language]

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t.title}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t.description}
            </p>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.back()}>
              {t.cancel}
            </Button>
            <Button
              variant="outline"
              onClick={() => handleSave('draft')}
              disabled={saving}
            >
              {saving ? t.saving : t.saveDraft}
            </Button>
            <Button
              onClick={() => handleSave('pending')}
              disabled={saving}
            >
              {saving ? t.saving : t.submitApproval}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Form */}
          <div className="lg:col-span-2 space-y-6">
            {/* Adjustment Details */}
            <Card>
              <CardHeader>
                <CardTitle>{t.adjustmentDetails}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="type">{t.type} *</Label>
                  <select
                    id="type"
                    value={formData.type}
                    onChange={(e) => setFormData(prev => ({ ...prev, type: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm"
                    aria-label="Select adjustment type"
                    title="Select adjustment type"
                  >
                    <option value="correction">{t.types.correction}</option>
                    <option value="increase">{t.types.increase}</option>
                    <option value="decrease">{t.types.decrease}</option>
                    <option value="damage">{t.types.damage}</option>
                    <option value="theft">{t.types.theft}</option>
                    <option value="expired">{t.types.expired}</option>
                    <option value="return">{t.types.return}</option>
                    <option value="transfer">{t.types.transfer}</option>
                  </select>
                  {errors.type && <p className="text-sm text-red-600">{errors.type}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reason">{t.reason} *</Label>
                  <Textarea
                    id="reason"
                    value={formData.reason}
                    onChange={(e) => setFormData(prev => ({ ...prev, reason: e.target.value }))}
                    placeholder={t.reasonPlaceholder}
                    rows={3}
                    className={errors.reason ? 'border-red-500' : ''}
                  />
                  {errors.reason && <p className="text-sm text-red-600">{errors.reason}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="notes">{t.notes}</Label>
                  <Textarea
                    id="notes"
                    value={formData.notes}
                    onChange={(e) => setFormData(prev => ({ ...prev, notes: e.target.value }))}
                    placeholder={t.notesPlaceholder}
                    rows={2}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Product Selection */}
            <Card>
              <CardHeader>
                <CardTitle>{t.productSelection}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={t.searchPlaceholder}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />

                  {/* Search Results Dropdown */}
                  {showSearch && searchResults.length > 0 && (
                    <div className="absolute z-10 w-full mt-1 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-md shadow-lg max-h-60 overflow-y-auto">
                      {searchResults.map((product) => (
                        <div
                          key={product._id}
                          className="p-3 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-200 dark:border-gray-600 last:border-b-0"
                          onClick={() => addProductToAdjustment(product)}
                        >
                          <div className="flex items-center justify-between">
                            <div>
                              <div className="font-medium text-gray-900 dark:text-white">
                                {product.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                SKU: {product.sku} | Stock: {product.inventory?.quantity || 0} {product.inventory?.unit || 'pcs'}
                              </div>
                            </div>
                            <Badge
                              variant="outline"
                              style={{
                                borderColor: product.category?.color,
                                color: product.category?.color
                              }}
                              title={`Category: ${product.category?.name}`}
                            >
                              {product.category?.name}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
                {errors.items && <p className="text-sm text-red-600 mt-2">{errors.items}</p>}
              </CardContent>
            </Card>

            {/* Added Products */}
            <Card>
              <CardHeader>
                <CardTitle>{t.addedProducts}</CardTitle>
              </CardHeader>
              <CardContent>
                {formData.items.length === 0 ? (
                  <div className="text-center py-8">
                    <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                      {t.noProducts}
                    </h3>
                    <p className="text-gray-500">
                      {t.noProductsDesc}
                    </p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {formData.items.map((item, index) => (
                      <div key={item.product._id} className="border border-gray-200 dark:border-gray-700 rounded-lg p-4">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex-1">
                            <div className="font-medium text-gray-900 dark:text-white">
                              {item.product.name}
                            </div>
                            <div className="text-sm text-gray-500">
                              SKU: {item.product.sku}
                            </div>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeProductFromAdjustment(index)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>

                        <div className="grid grid-cols-2 md:grid-cols-5 gap-4 mb-4">
                          <div>
                            <Label className="text-xs text-gray-500">{t.currentStock}</Label>
                            <div className="text-sm font-medium">
                              {item.currentQuantity} {item.product.inventory?.unit || 'pcs'}
                            </div>
                          </div>

                          <div>
                            <Label className="text-xs text-gray-500">{t.adjustment}</Label>
                            <div className="flex items-center gap-1">
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateAdjustmentQuantity(index, item.adjustmentQuantity - 1)}
                                className="h-8 w-8 p-0"
                              >
                                <Minus className="h-3 w-3" />
                              </Button>
                              <Input
                                type="number"
                                value={item.adjustmentQuantity}
                                onChange={(e) => updateAdjustmentQuantity(index, parseInt(e.target.value) || 0)}
                                className="h-8 w-16 text-center text-sm"
                              />
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => updateAdjustmentQuantity(index, item.adjustmentQuantity + 1)}
                                className="h-8 w-8 p-0"
                              >
                                <Plus className="h-3 w-3" />
                              </Button>
                            </div>
                            {errors[`item_${index}_quantity`] && (
                              <p className="text-xs text-red-600 mt-1">{errors[`item_${index}_quantity`]}</p>
                            )}
                            {errors[`item_${index}_negative`] && (
                              <p className="text-xs text-red-600 mt-1">{errors[`item_${index}_negative`]}</p>
                            )}
                          </div>

                          <div>
                            <Label className="text-xs text-gray-500">{t.newStock}</Label>
                            <div className={`text-sm font-medium ${
                              item.newQuantity < 0 ? 'text-red-600' :
                              item.newQuantity <= (item.product.inventory?.minQuantity || 0) ? 'text-yellow-600' :
                              'text-green-600'
                            }`}>
                              {item.newQuantity} {item.product.inventory?.unit || 'pcs'}
                            </div>
                          </div>

                          <div>
                            <Label className="text-xs text-gray-500">{t.unitCost}</Label>
                            <div className="text-sm">
                              {formatPrice(item.unitCost)}
                            </div>
                          </div>

                          <div>
                            <Label className="text-xs text-gray-500">{t.totalCost}</Label>
                            <div className={`text-sm font-medium ${
                              item.totalCost >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {item.totalCost >= 0 ? '+' : ''}{formatPrice(item.totalCost)}
                            </div>
                          </div>
                        </div>

                        <div>
                          <Label className="text-xs text-gray-500">{t.itemNotes}</Label>
                          <Input
                            placeholder="Optional notes for this item..."
                            value={item.notes}
                            onChange={(e) => updateItemNotes(index, e.target.value)}
                            className="mt-1"
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Summary Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  {getTypeIcon(formData.type)}
                  {t.summary}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t.totalItems}:</span>
                  <span className="font-medium">{getTotalItems()}</span>
                </div>

                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t.totalImpact}:</span>
                  <span className={`font-medium ${
                    getTotalCostImpact() >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {getTotalCostImpact() >= 0 ? '+' : ''}{formatPrice(getTotalCostImpact())}
                  </span>
                </div>

                <div className="border-t pt-4">
                  <div className="text-sm text-gray-500 mb-2">Products:</div>
                  {formData.items.length === 0 ? (
                    <div className="text-sm text-gray-400">No products added</div>
                  ) : (
                    <div className="space-y-2">
                      {formData.items.map((item) => (
                        <div key={item.product._id} className="text-sm">
                          <div className="font-medium truncate">{item.product.name}</div>
                          <div className="text-gray-500">
                            {item.adjustmentQuantity >= 0 ? '+' : ''}{item.adjustmentQuantity} {item.product.inventory?.unit || 'pcs'}
                          </div>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
