// User Types
export interface User {
  id: string;
  username: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  phone?: string;
  role: UserRole;
  isActive: boolean;
  avatar?: string;
  lastLogin?: Date;
  preferences: UserPreferences;
  permissions: Permission[];
  createdAt: Date;
  updatedAt: Date;
}

export type UserRole = 'admin' | 'manager' | 'cashier';

export interface UserPreferences {
  language: Language;
  currency: Currency;
  theme: Theme;
}

export interface Permission {
  module: string;
  actions: string[];
}

// Product Types
export interface Product {
  id: string;
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  category: string;
  price: number;
  cost: number;
  currency: Currency;
  taxRate: number;
  images: ProductImage[];
  variants: ProductVariant[];
  inventory: ProductInventory;
  supplier?: string;
  isActive: boolean;
  isFeatured: boolean;
  tags: string[];
  metadata: ProductMetadata;
  profitMargin: number;
  stockStatus: StockStatus;
  createdAt: Date;
  updatedAt: Date;
}

export interface ProductImage {
  url: string;
  alt?: string;
  isPrimary: boolean;
}

export interface ProductVariant {
  name: string;
  value: string;
  price?: number;
  sku?: string;
  barcode?: string;
}

export interface ProductInventory {
  quantity: number;
  minQuantity: number;
  maxQuantity: number;
  unit: string;
}

export interface ProductMetadata {
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
    unit: string;
  };
  color?: string;
  material?: string;
  brand?: string;
}

export type StockStatus = 'in_stock' | 'low_stock' | 'out_of_stock';

// Category Types
export interface Category {
  id: string;
  name: string;
  description?: string;
  slug: string;
  parent?: string;
  image?: string;
  color: string;
  icon: string;
  isActive: boolean;
  sortOrder: number;
  subcategories?: Category[];
  productsCount: number;
  createdAt: Date;
  updatedAt: Date;
}

// Sale Types
export interface Sale {
  id: string;
  saleNumber: string;
  cashier: string;
  customer?: Customer;
  items: SaleItem[];
  subtotal: number;
  totalDiscount: number;
  totalTax: number;
  totalAmount: number;
  currency: Currency;
  paymentMethod: PaymentMethod;
  paymentDetails?: PaymentDetails;
  amountPaid: number;
  changeAmount: number;
  status: SaleStatus;
  notes?: string;
  receipt: ReceiptInfo;
  refund: RefundInfo;
  totalItems: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface SaleItem {
  product: string;
  productName: string;
  sku: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discount: number;
  tax: number;
}

export interface Customer {
  name?: string;
  email?: string;
  phone?: string;
  address?: string;
}

export interface PaymentDetails {
  cardType?: string;
  cardLast4?: string;
  transactionId?: string;
  reference?: string;
}

export interface ReceiptInfo {
  printed: boolean;
  emailed: boolean;
  number?: string;
}

export interface RefundInfo {
  isRefunded: boolean;
  refundAmount: number;
  refundDate?: Date;
  refundReason?: string;
  refundedBy?: string;
}

export type PaymentMethod = 'cash' | 'card' | 'digital' | 'bank_transfer' | 'other';
export type SaleStatus = 'pending' | 'completed' | 'cancelled' | 'refunded';

// Common Types
export type Language = 'en' | 'mm';
export type Currency = 'MMK' | 'THB' | 'USD';
export type Theme = 'light' | 'dark' | 'auto';

// API Response Types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T = any> extends ApiResponse<T[]> {
  pagination: {
    page: number;
    limit: number;
    total: number;
    pages: number;
  };
}

// Form Types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  firstName: string;
  lastName: string;
  role?: UserRole;
}

export interface ProductForm {
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  category: string;
  price: number;
  cost: number;
  currency: Currency;
  taxRate: number;
  inventory: {
    quantity: number;
    minQuantity: number;
    maxQuantity: number;
    unit: string;
  };
  isActive: boolean;
  isFeatured: boolean;
  tags: string[];
}

// Cart Types
export interface CartItem {
  product: Product;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  discount: number;
  tax: number;
}

export interface Cart {
  items: CartItem[];
  subtotal: number;
  totalDiscount: number;
  totalTax: number;
  totalAmount: number;
  itemCount: number;
}
