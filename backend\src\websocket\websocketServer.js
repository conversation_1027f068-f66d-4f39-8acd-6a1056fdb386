const WebSocket = require('ws');
const jwt = require('jsonwebtoken');

class WebSocketServer {
  constructor(server) {
    this.wss = new WebSocket.Server({
      server,
      path: '/ws',
      perMessageDeflate: false,
      clientTracking: true,
      verifyClient: (info) => {
        // Allow all origins in development
        console.log('🔍 WebSocket connection from:', info.origin || 'unknown origin')
        return true
      }
    });

    this.clients = new Map(); // Store client connections with metadata
    this.channels = new Map(); // Store channel subscriptions

    this.setupWebSocketServer();
    console.log('🚀 WebSocket server initialized on /ws');
  }

  setupWebSocketServer() {
    this.wss.on('connection', (ws, req) => {
      const clientIP = req.socket.remoteAddress;
      const userAgent = req.headers['user-agent'];
      console.log(`🔌 New WebSocket connection established from ${clientIP}`);

      // Generate unique client ID
      const clientId = this.generateClientId();

      // Store client connection
      this.clients.set(clientId, {
        ws,
        channels: new Set(),
        authenticated: false,
        userId: null,
        connectedAt: new Date(),
        ip: clientIP,
        userAgent: userAgent
      });

      // Set up ping/pong for connection health
      ws.isAlive = true;
      ws.on('pong', () => {
        ws.isAlive = true;
      });

      // Handle incoming messages
      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message.toString());
          console.log(`📨 Message from client ${clientId}:`, data.type);
          this.handleMessage(clientId, data);
        } catch (error) {
          console.error('❌ Error parsing WebSocket message:', error);
          this.sendError(clientId, 'Invalid message format');
        }
      });

      // Handle connection close
      ws.on('close', (code, reason) => {
        console.log(`🔌 WebSocket connection closed for client: ${clientId}, code: ${code}, reason: ${reason}`);
        this.removeClient(clientId);
      });

      // Handle errors
      ws.on('error', (error) => {
        console.error(`❌ WebSocket error for client ${clientId}:`, error);
        this.removeClient(clientId);
      });

      // Send welcome message
      this.sendMessage(clientId, {
        type: 'welcome',
        clientId,
        message: 'Connected to BitsTech WebSocket server',
        timestamp: Date.now()
      });
    });

    // Set up ping interval to check connection health
    const pingInterval = setInterval(() => {
      this.wss.clients.forEach((ws) => {
        if (ws.isAlive === false) {
          console.log('🔌 Terminating dead WebSocket connection');
          return ws.terminate();
        }

        ws.isAlive = false;
        ws.ping();
      });
    }, 30000); // Ping every 30 seconds

    this.wss.on('close', () => {
      clearInterval(pingInterval);
    });
  }

  handleMessage(clientId, data) {
    const client = this.clients.get(clientId);
    if (!client) return;

    console.log(`📨 Message from client ${clientId}:`, data);

    switch (data.type) {
      case 'authenticate':
        this.authenticateClient(clientId, data.token);
        break;
      
      case 'subscribe':
        this.subscribeToChannel(clientId, data.channel);
        break;
      
      case 'unsubscribe':
        this.unsubscribeFromChannel(clientId, data.channel);
        break;
      
      case 'ping':
        this.sendMessage(clientId, { type: 'pong', timestamp: Date.now() });
        break;
      
      default:
        console.warn(`⚠️ Unknown message type: ${data.type}`);
        this.sendError(clientId, `Unknown message type: ${data.type}`);
    }
  }

  authenticateClient(clientId, token) {
    try {
      if (!token) {
        // Allow unauthenticated connections for now
        const client = this.clients.get(clientId);
        if (client) {
          client.authenticated = true;
          this.sendMessage(clientId, {
            type: 'authenticated',
            success: true,
            message: 'Connected without authentication'
          });
        }
        return;
      }

      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-secret-key');
      const client = this.clients.get(clientId);
      
      if (client) {
        client.authenticated = true;
        client.userId = decoded.id;
        
        this.sendMessage(clientId, {
          type: 'authenticated',
          success: true,
          userId: decoded.id,
          message: 'Authentication successful'
        });
        
        console.log(`✅ Client ${clientId} authenticated as user ${decoded.id}`);
      }
    } catch (error) {
      console.error('❌ Authentication failed:', error);
      this.sendError(clientId, 'Authentication failed');
    }
  }

  subscribeToChannel(clientId, channel) {
    const client = this.clients.get(clientId);
    if (!client) return;

    // Add client to channel
    if (!this.channels.has(channel)) {
      this.channels.set(channel, new Set());
    }
    
    this.channels.get(channel).add(clientId);
    client.channels.add(channel);
    
    this.sendMessage(clientId, {
      type: 'subscribed',
      channel,
      message: `Subscribed to ${channel}`
    });
    
    console.log(`📡 Client ${clientId} subscribed to channel: ${channel}`);
  }

  unsubscribeFromChannel(clientId, channel) {
    const client = this.clients.get(clientId);
    if (!client) return;

    if (this.channels.has(channel)) {
      this.channels.get(channel).delete(clientId);
      if (this.channels.get(channel).size === 0) {
        this.channels.delete(channel);
      }
    }
    
    client.channels.delete(channel);
    
    this.sendMessage(clientId, {
      type: 'unsubscribed',
      channel,
      message: `Unsubscribed from ${channel}`
    });
    
    console.log(`📡 Client ${clientId} unsubscribed from channel: ${channel}`);
  }

  // Broadcast message to all clients in a channel
  broadcastToChannel(channel, message) {
    const channelClients = this.channels.get(channel);
    if (!channelClients) return;

    let sentCount = 0;
    channelClients.forEach(clientId => {
      if (this.sendMessage(clientId, message)) {
        sentCount++;
      }
    });
    
    console.log(`📡 Broadcasted to ${sentCount} clients in channel: ${channel}`);
    return sentCount;
  }

  // Send message to specific client
  sendMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client || client.ws.readyState !== WebSocket.OPEN) {
      return false;
    }

    try {
      client.ws.send(JSON.stringify(message));
      return true;
    } catch (error) {
      console.error(`❌ Error sending message to client ${clientId}:`, error);
      this.removeClient(clientId);
      return false;
    }
  }

  sendError(clientId, error) {
    this.sendMessage(clientId, {
      type: 'error',
      error,
      timestamp: Date.now()
    });
  }

  removeClient(clientId) {
    const client = this.clients.get(clientId);
    if (!client) return;

    // Remove from all channels
    client.channels.forEach(channel => {
      if (this.channels.has(channel)) {
        this.channels.get(channel).delete(clientId);
        if (this.channels.get(channel).size === 0) {
          this.channels.delete(channel);
        }
      }
    });

    // Remove client
    this.clients.delete(clientId);
    console.log(`🗑️ Client ${clientId} removed`);
  }

  generateClientId() {
    return `client_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  // Public methods for broadcasting updates
  broadcastSalesUpdate(salesData) {
    this.broadcastToChannel('dashboard-updates', {
      type: 'sales-update',
      todaySales: salesData.todaySales,
      recentSales: salesData.recentSales,
      timestamp: Date.now()
    });
  }

  broadcastInventoryUpdate(inventoryData) {
    this.broadcastToChannel('dashboard-updates', {
      type: 'inventory-update',
      totalProducts: inventoryData.totalProducts,
      lowStockCount: inventoryData.lowStockCount,
      lowStockItems: inventoryData.lowStockItems,
      inventoryAlerts: inventoryData.inventoryAlerts,
      timestamp: Date.now()
    });

    this.broadcastToChannel('inventory-updates', {
      type: 'inventory-update',
      ...inventoryData,
      timestamp: Date.now()
    });
  }

  broadcastProductSold(productId, quantitySold, productName) {
    this.broadcastToChannel('inventory-updates', {
      type: 'product-sold',
      productId,
      quantitySold,
      productName,
      timestamp: Date.now()
    });
  }

  broadcastLowStockAlert(productId, productName, currentStock, minStock) {
    this.broadcastToChannel('inventory-updates', {
      type: 'low-stock-alert',
      productId,
      productName,
      currentStock,
      minStock,
      timestamp: Date.now()
    });
  }

  broadcastInvoiceUpdate(saleId, saleNumber, updates) {
    this.broadcastToChannel('invoice-updates', {
      type: 'invoice-update',
      saleId,
      saleNumber,
      updates,
      timestamp: Date.now()
    });
  }

  broadcastCompanySettingsUpdate(settings) {
    this.broadcastToChannel('invoice-updates', {
      type: 'company-settings-update',
      settings,
      timestamp: Date.now()
    });
  }

  broadcastCustomizationUpdate(customization) {
    this.broadcastToChannel('invoice-updates', {
      type: 'customization-update',
      customization,
      timestamp: Date.now()
    });
  }

  // Get server statistics
  getStats() {
    const channelStats = {};
    this.channels.forEach((clients, channel) => {
      channelStats[channel] = clients.size;
    });

    return {
      totalClients: this.clients.size,
      totalChannels: this.channels.size,
      channelStats,
      uptime: process.uptime()
    };
  }
}

module.exports = WebSocketServer;
