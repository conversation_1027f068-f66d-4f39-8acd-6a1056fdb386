const express = require('express');
const { body } = require('express-validator');
const {
    getStockAdjustments,
    getStockAdjustment,
    createStockAdjustment,
    updateStockAdjustment,
    approveStockAdjustment,
    applyStockAdjustment,
    deleteStockAdjustment,
    getAdjustmentStats
} = require('../controllers/stockAdjustmentController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createAdjustmentValidation = [
    body('type')
        .isIn(['increase', 'decrease', 'correction', 'damage', 'theft', 'expired', 'return', 'transfer'])
        .withMessage('Invalid adjustment type'),
    body('reason')
        .notEmpty()
        .trim()
        .isLength({ min: 5, max: 500 })
        .withMessage('Reason must be between 5 and 500 characters'),
    body('items')
        .isArray({ min: 1 })
        .withMessage('At least one item is required'),
    body('items.*.product')
        .isMongoId()
        .withMessage('Valid product ID is required'),
    body('items.*.adjustmentQuantity')
        .isInt({ min: -999999, max: 999999 })
        .withMessage('Adjustment quantity must be a valid integer'),
    body('items.*.unitCost')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Unit cost must be a positive number'),
    body('status')
        .optional()
        .isIn(['draft', 'pending'])
        .withMessage('Status must be draft or pending')
];

const updateAdjustmentValidation = [
    body('type')
        .optional()
        .isIn(['increase', 'decrease', 'correction', 'damage', 'theft', 'expired', 'return', 'transfer'])
        .withMessage('Invalid adjustment type'),
    body('reason')
        .optional()
        .trim()
        .isLength({ min: 5, max: 500 })
        .withMessage('Reason must be between 5 and 500 characters'),
    body('items')
        .optional()
        .isArray({ min: 1 })
        .withMessage('At least one item is required'),
    body('status')
        .optional()
        .isIn(['draft', 'pending'])
        .withMessage('Status must be draft or pending')
];

const approvalValidation = [
    body('approved')
        .isBoolean()
        .withMessage('Approved must be true or false'),
    body('comments')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Comments cannot exceed 1000 characters')
];

// Apply authentication to all routes
router.use(protect);

// Routes

// GET /api/stock-adjustments - Get all adjustments
router.get('/', getStockAdjustments);

// GET /api/stock-adjustments/stats - Get adjustment statistics
router.get('/stats', getAdjustmentStats);

// GET /api/stock-adjustments/:id - Get single adjustment
router.get('/:id', getStockAdjustment);

// POST /api/stock-adjustments - Create new adjustment
router.post('/', createAdjustmentValidation, createStockAdjustment);

// PUT /api/stock-adjustments/:id - Update adjustment (draft only)
router.put('/:id', updateAdjustmentValidation, updateStockAdjustment);

// POST /api/stock-adjustments/:id/approve - Approve/reject adjustment (managers only)
router.post('/:id/approve', authorize('admin', 'manager'), approvalValidation, approveStockAdjustment);

// POST /api/stock-adjustments/:id/apply - Apply adjustment to inventory (managers only)
router.post('/:id/apply', authorize('admin', 'manager'), applyStockAdjustment);

// DELETE /api/stock-adjustments/:id - Delete adjustment (draft only)
router.delete('/:id', deleteStockAdjustment);

module.exports = router;
