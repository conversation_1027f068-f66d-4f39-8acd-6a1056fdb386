const mongoose = require('mongoose');

const ProductSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Please add a product name'],
        trim: true,
        maxlength: [100, 'Product name cannot be more than 100 characters']
    },
    description: {
        type: String,
        maxlength: [500, 'Description cannot be more than 500 characters']
    },
    sku: {
        type: String,
        required: [true, 'Please add a SKU'],
        unique: true,
        trim: true,
        maxlength: [50, 'SKU cannot be more than 50 characters']
    },
    barcode: {
        type: String,
        unique: true,
        sparse: true,
        trim: true
    },
    category: {
        type: mongoose.Schema.ObjectId,
        ref: 'Category',
        required: [true, 'Please add a category']
    },
    price: {
        type: Number,
        required: [true, 'Please add a price'],
        min: [0, 'Price cannot be negative']
    },
    cost: {
        type: Number,
        required: [true, 'Please add a cost'],
        min: [0, 'Cost cannot be negative']
    },
    currency: {
        type: String,
        enum: ['MMK', 'THB', 'USD'],
        default: 'MMK'
    },
    taxRate: {
        type: Number,
        default: 0,
        min: [0, 'Tax rate cannot be negative'],
        max: [100, 'Tax rate cannot be more than 100%']
    },
    images: [{
        url: String,
        alt: String,
        isPrimary: {
            type: Boolean,
            default: false
        }
    }],
    variants: [{
        name: String,
        value: String,
        price: Number,
        sku: String,
        barcode: String
    }],
    inventory: {
        quantity: {
            type: Number,
            required: [true, 'Please add quantity'],
            min: [0, 'Quantity cannot be negative'],
            default: 0
        },
        minQuantity: {
            type: Number,
            default: 5,
            min: [0, 'Minimum quantity cannot be negative']
        },
        maxQuantity: {
            type: Number,
            default: 1000,
            min: [0, 'Maximum quantity cannot be negative']
        },
        unit: {
            type: String,
            default: 'piece',
            maxlength: [20, 'Unit cannot be more than 20 characters']
        }
    },
    supplier: {
        type: mongoose.Schema.ObjectId,
        ref: 'Supplier'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    isFeatured: {
        type: Boolean,
        default: false
    },
    tags: [{
        type: String,
        trim: true
    }],
    metadata: {
        weight: Number,
        dimensions: {
            length: Number,
            width: Number,
            height: Number,
            unit: {
                type: String,
                default: 'cm'
            }
        },
        color: String,
        material: String,
        brand: String
    },
    seo: {
        title: String,
        description: String,
        keywords: [String]
    }
}, {
    timestamps: true
});

// Create index for search
ProductSchema.index({
    name: 'text',
    description: 'text',
    sku: 'text',
    barcode: 'text'
});

// Virtual for profit margin
ProductSchema.virtual('profitMargin').get(function() {
    if (this.cost === 0) return 0;
    return ((this.price - this.cost) / this.cost * 100).toFixed(2);
});

// Virtual for stock status
ProductSchema.virtual('stockStatus').get(function() {
    if (this.inventory.quantity === 0) return 'out_of_stock';
    if (this.inventory.quantity <= this.inventory.minQuantity) return 'low_stock';
    return 'in_stock';
});

// Ensure virtual fields are serialized
ProductSchema.set('toJSON', {
    virtuals: true
});

module.exports = mongoose.model('Product', ProductSchema);
