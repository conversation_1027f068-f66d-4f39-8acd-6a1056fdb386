// AI Forecasting and Analytics Routes
const express = require('express');
const router = express.Router();
const { protect, authorize, auditLog } = require('../middleware/auth');
const aiService = require('../services/aiService');

// @desc    Get AI models information
// @route   GET /api/ai/models
// @access  Private (Manager/Admin only)
router.get('/models', protect, authorize('admin', 'manager'), async (req, res) => {
    try {
        const { modelName } = req.query;
        const result = await aiService.getModelInfo(modelName);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Initialize AI service
// @route   POST /api/ai/initialize
// @access  Private (Admin only)
router.post('/initialize', protect, authorize('admin'), async (req, res) => {
    try {
        const result = await aiService.initialize();
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Predict sales forecast
// @route   POST /api/ai/predict/sales
// @access  Private (Manager/Admin only)
router.post('/predict/sales', protect, authorize('admin', 'manager'), auditLog('ai_sales_prediction'), async (req, res) => {
    try {
        const inputData = req.body;
        
        // Validate input data
        if (!inputData || Object.keys(inputData).length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Input data is required for sales prediction'
            });
        }

        const result = await aiService.predictSales(inputData);
        
        // Log prediction request
        console.log(`🤖 Sales prediction requested by ${req.user.email}:`, {
            input: inputData,
            result: result.success ? result.prediction : 'failed'
        });

        res.json(result);
    } catch (error) {
        console.error('❌ Sales prediction error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Optimize inventory levels
// @route   POST /api/ai/optimize/inventory
// @access  Private (Manager/Admin only)
router.post('/optimize/inventory', protect, authorize('admin', 'manager'), auditLog('ai_inventory_optimization'), async (req, res) => {
    try {
        const productData = req.body;
        
        if (!productData || Object.keys(productData).length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Product data is required for inventory optimization'
            });
        }

        const result = await aiService.optimizeInventory(productData);
        
        // Log optimization request
        console.log(`📦 Inventory optimization requested by ${req.user.email}:`, {
            productId: productData.productId || 'unknown',
            currentStock: productData.currentStock,
            result: result.success ? result.optimalStock : 'failed'
        });

        res.json(result);
    } catch (error) {
        console.error('❌ Inventory optimization error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Analyze customer segment
// @route   POST /api/ai/analyze/customer
// @access  Private (Manager/Admin only)
router.post('/analyze/customer', protect, authorize('admin', 'manager'), auditLog('ai_customer_analysis'), async (req, res) => {
    try {
        const customerData = req.body;
        
        if (!customerData || Object.keys(customerData).length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Customer data is required for analysis'
            });
        }

        const result = await aiService.analyzeCustomer(customerData);
        
        // Log analysis request
        console.log(`👥 Customer analysis requested by ${req.user.email}:`, {
            customerId: customerData.customerId || 'unknown',
            result: result.success ? result.segment : 'failed'
        });

        res.json(result);
    } catch (error) {
        console.error('❌ Customer analysis error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Generate business insights
// @route   POST /api/ai/insights
// @access  Private (Manager/Admin only)
router.post('/insights', protect, authorize('admin', 'manager'), auditLog('ai_generate_insights'), async (req, res) => {
    try {
        const { salesData, inventoryData, customerData } = req.body;
        
        if (!salesData && !inventoryData && !customerData) {
            return res.status(400).json({
                success: false,
                error: 'At least one data type (sales, inventory, or customer) is required'
            });
        }

        const result = await aiService.generateInsights(salesData, inventoryData, customerData);
        
        // Log insights generation
        console.log(`💡 Business insights generated by ${req.user.email}:`, {
            dataTypes: {
                sales: !!salesData,
                inventory: !!inventoryData,
                customer: !!customerData
            },
            insightsCount: result.success ? result.insights.length : 0
        });

        res.json(result);
    } catch (error) {
        console.error('❌ Insights generation error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Batch predictions for multiple products
// @route   POST /api/ai/predict/batch
// @access  Private (Manager/Admin only)
router.post('/predict/batch', protect, authorize('admin', 'manager'), auditLog('ai_batch_prediction'), async (req, res) => {
    try {
        const { predictions } = req.body;
        
        if (!predictions || !Array.isArray(predictions) || predictions.length === 0) {
            return res.status(400).json({
                success: false,
                error: 'Predictions array is required'
            });
        }

        const results = [];
        
        for (const prediction of predictions) {
            try {
                let result;
                
                switch (prediction.type) {
                    case 'sales':
                        result = await aiService.predictSales(prediction.data);
                        break;
                    case 'inventory':
                        result = await aiService.optimizeInventory(prediction.data);
                        break;
                    case 'customer':
                        result = await aiService.analyzeCustomer(prediction.data);
                        break;
                    default:
                        result = {
                            success: false,
                            error: `Unknown prediction type: ${prediction.type}`
                        };
                }
                
                results.push({
                    id: prediction.id || `prediction_${results.length + 1}`,
                    type: prediction.type,
                    ...result
                });
            } catch (error) {
                results.push({
                    id: prediction.id || `prediction_${results.length + 1}`,
                    type: prediction.type,
                    success: false,
                    error: error.message
                });
            }
        }
        
        const successCount = results.filter(r => r.success).length;
        
        // Log batch prediction
        console.log(`🔄 Batch prediction completed by ${req.user.email}:`, {
            total: predictions.length,
            successful: successCount,
            failed: predictions.length - successCount
        });

        res.json({
            success: true,
            message: `Processed ${predictions.length} predictions`,
            results: results,
            summary: {
                total: predictions.length,
                successful: successCount,
                failed: predictions.length - successCount
            }
        });
    } catch (error) {
        console.error('❌ Batch prediction error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get AI service status
// @route   GET /api/ai/status
// @access  Private (Manager/Admin only)
router.get('/status', protect, authorize('admin', 'manager'), async (req, res) => {
    try {
        const modelsInfo = await aiService.getModelInfo();
        
        res.json({
            success: true,
            status: {
                isInitialized: aiService.isInitialized,
                modelsLoaded: modelsInfo.success ? modelsInfo.models.length : 0,
                models: modelsInfo.success ? modelsInfo.models : [],
                lastChecked: new Date().toISOString()
            }
        });
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get prediction history
// @route   GET /api/ai/history
// @access  Private (Manager/Admin only)
router.get('/history', protect, authorize('admin', 'manager'), async (req, res) => {
    try {
        const { page = 1, limit = 20, type, startDate, endDate } = req.query;
        
        // In a real implementation, this would query the database
        // For now, return mock data
        const mockHistory = {
            success: true,
            data: [],
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: 0,
                pages: 0
            },
            filters: {
                type,
                startDate,
                endDate
            }
        };

        res.json(mockHistory);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get AI analytics and performance metrics
// @route   GET /api/ai/analytics
// @access  Private (Admin only)
router.get('/analytics', protect, authorize('admin'), async (req, res) => {
    try {
        const { period = '30d' } = req.query;
        
        // Mock analytics data
        const analytics = {
            success: true,
            period: period,
            data: {
                totalPredictions: 0,
                accuracyRate: 0,
                modelPerformance: {},
                usageByType: {},
                trends: [],
                recommendations: []
            },
            generatedAt: new Date().toISOString()
        };

        res.json(analytics);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
