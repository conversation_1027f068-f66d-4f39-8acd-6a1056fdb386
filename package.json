{"name": "bitstech-pos-system", "version": "1.0.0", "description": "Modern Point of Sale System for Myanmar businesses", "main": "index.js", "scripts": {"dev": "concurrently \"npm run dev:frontend\" \"npm run dev:backend\"", "dev:frontend": "cd frontend && npm run dev", "dev:backend": "cd backend && npm run dev", "build": "npm run build:frontend && npm run build:backend", "build:frontend": "cd frontend && npm run build", "build:backend": "cd backend && npm run build", "start": "concurrently \"npm run start:frontend\" \"npm run start:backend\"", "start:frontend": "cd frontend && npm start", "start:backend": "cd backend && npm start", "install:all": "npm install && npm run install:frontend && npm run install:backend && npm run install:shared", "install:frontend": "cd frontend && npm install", "install:backend": "cd backend && npm install", "install:shared": "cd shared && npm install", "test": "npm run test:frontend && npm run test:backend", "test:frontend": "cd frontend && npm test", "test:backend": "cd backend && npm test", "lint": "npm run lint:frontend && npm run lint:backend", "lint:frontend": "cd frontend && npm run lint", "lint:backend": "cd backend && npm run lint", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md}\"", "clean": "rimraf node_modules frontend/node_modules backend/node_modules shared/node_modules", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "docker:logs": "docker-compose logs -f", "setup": "npm run install:all && npm run setup:env", "setup:env": "cp .env.example .env && echo 'Please update .env file with your configuration'", "db:migrate": "cd backend && npm run db:migrate", "db:seed": "cd backend && npm run db:seed", "db:reset": "cd backend && npm run db:reset", "backup": "cd backend && npm run backup", "docs:generate": "cd docs/diagrams && npm run generate-pdf", "postinstall": "echo 'Run npm run setup to initialize the project'"}, "keywords": ["pos", "point-of-sale", "myanmar", "retail", "inventory", "sales", "react", "nodejs", "typescript"], "author": "BitesTech Development Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/your-org/bites-tech-pos.git"}, "bugs": {"url": "https://github.com/your-org/bites-tech-pos/issues"}, "homepage": "https://github.com/your-org/bites-tech-pos#readme", "devDependencies": {"concurrently": "^8.2.2", "prettier": "^3.0.3", "rimraf": "^5.0.5"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "workspaces": ["frontend", "backend", "shared"], "dependencies": {"node-fetch": "^3.3.2", "ws": "^8.18.2"}}