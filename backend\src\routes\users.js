const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
    getUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser,
    updateUserStatus,
    changeUserPassword,
    getUserStats
} = require('../controllers/userController');

const router = express.Router();

// All routes require authentication
router.use(protect);

// User statistics (Admin, Manager, Supervisor)
router.get('/stats', authorize('admin', 'manager', 'supervisor'), getUserStats);

// CRUD routes with role-based access
router.route('/')
    .get(authorize('admin', 'manager', 'supervisor'), getUsers) // Allow supervisors to view users
    .post(authorize('admin', 'manager'), createUser); // Only admin/manager can create

router.route('/:id')
    .get(authorize('admin', 'manager', 'supervisor'), getUser) // Allow supervisors to view user details
    .put(authorize('admin', 'manager'), updateUser) // Only admin/manager can edit
    .delete(authorize('admin'), deleteUser); // Only admin can delete

// User status management (Admin, Manager only)
router.put('/:id/status', authorize('admin', 'manager'), updateUserStatus);

// Password management (Admin only)
router.put('/:id/password', authorize('admin'), changeUserPassword);

module.exports = router;
