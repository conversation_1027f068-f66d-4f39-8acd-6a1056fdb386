# MongoDB Setup Guide for BitesTech POS

## 📋 Overview
MongoDB installation and setup guide for Windows development environment.

## 🚀 Quick Installation

### Method 1: MongoDB Community Server (Recommended)

#### Step 1: Download MongoDB
1. Visit: https://www.mongodb.com/try/download/community
2. Select:
   - **Version**: 7.0.x (Current)
   - **Platform**: Windows
   - **Package**: msi

#### Step 2: Install MongoDB
1. Run the downloaded `.msi` file
2. Choose "Complete" installation
3. **Important**: Check "Install MongoDB as a Service"
4. **Important**: Check "Install MongoDB Compass" (GUI tool)
5. Complete the installation

#### Step 3: Verify Installation
```powershell
# Check if MongoDB service is running
Get-Service -Name MongoDB

# Test MongoDB connection
mongo --version
# or
mongod --version
```

### Method 2: MongoDB Atlas (Cloud Database)

#### Step 1: Create Account
1. Visit: https://www.mongodb.com/atlas
2. Sign up for free account
3. Create a new cluster (Free tier available)

#### Step 2: Get Connection String
1. Go to "Database" → "Connect"
2. Choose "Connect your application"
3. Copy the connection string
4. Replace `<password>` with your database password

#### Step 3: Update Environment Variables
```env
# In .env file
MONGODB_URI=mongodb+srv://username:<EMAIL>/bitestech_pos
```

## 🔧 Configuration

### Local MongoDB Setup

#### Default Connection String
```env
MONGODB_URI=mongodb://localhost:27017/bitestech_pos
```

#### Start MongoDB Service (if not auto-started)
```powershell
# Start MongoDB service
net start MongoDB

# Stop MongoDB service
net stop MongoDB
```

### Environment Variables
Update your `.env` file:
```env
# Database Configuration
MONGODB_URI=mongodb://localhost:27017/bitestech_pos
MONGODB_DB_NAME=bitestech_pos

# Alternative for MongoDB Atlas
# MONGODB_URI=mongodb+srv://username:<EMAIL>/bitestech_pos
```

## 🌱 Database Seeding

### Run Database Seeding
```powershell
# Navigate to backend directory
cd C:\xampp\htdocs\BitesTech\backend

# Set Node.js path
$env:PATH = "C:\Program Files\nodejs;" + $env:PATH

# Run seeding script
node src/scripts/seed.js
```

### Sample Data Created
- **Users**: Admin, Manager, Cashier accounts
- **Categories**: Beverages, Food, Desserts with subcategories
- **Products**: Coffee, tea, food items with inventory

### Default User Accounts
```
Admin:
  Email: <EMAIL>
  Password: admin123
  Role: Administrator

Manager:
  Email: <EMAIL>
  Password: manager123
  Role: Manager

Cashier:
  Email: <EMAIL>
  Password: cashier123
  Role: Cashier
```

## 🔍 MongoDB Compass (GUI Tool)

### Access Database
1. Open MongoDB Compass
2. Connect to: `mongodb://localhost:27017`
3. Browse `bitestech_pos` database
4. View collections: users, categories, products

### Useful Operations
- **View Data**: Browse collections and documents
- **Query Data**: Use filter queries
- **Import/Export**: Backup and restore data
- **Performance**: Monitor database performance

## 🚨 Troubleshooting

### Common Issues

#### 1. MongoDB Service Not Starting
```powershell
# Check Windows services
services.msc

# Look for "MongoDB" service
# Right-click → Start
```

#### 2. Connection Refused
- Ensure MongoDB service is running
- Check firewall settings
- Verify port 27017 is not blocked

#### 3. Authentication Failed
- Check username/password in connection string
- Ensure user has proper permissions

#### 4. Database Not Found
- Database will be created automatically on first connection
- Run seeding script to populate data

### Useful Commands
```powershell
# Check MongoDB status
Get-Service -Name MongoDB

# Test connection
mongo --eval "db.adminCommand('ismaster')"

# View MongoDB logs
Get-EventLog -LogName Application -Source MongoDB

# Restart MongoDB service
Restart-Service -Name MongoDB
```

## 📊 Database Schema

### Collections Structure
```
bitestech_pos/
├── users           # User accounts and authentication
├── categories      # Product categories and hierarchy
├── products        # Product catalog and inventory
├── sales          # Transaction records
└── settings       # System configuration
```

### Indexes (Auto-created)
- Users: email, username (unique)
- Products: sku, barcode (unique)
- Categories: slug (unique)
- Sales: saleNumber (unique)

## 🔐 Security Considerations

### Development Environment
- Default setup has no authentication
- Suitable for local development only

### Production Environment
- Enable authentication
- Create database users with limited permissions
- Use SSL/TLS connections
- Regular backups

### Backup Strategy
```powershell
# Create backup
mongodump --db bitestech_pos --out backup/

# Restore backup
mongorestore --db bitestech_pos backup/bitestech_pos/
```

## 📞 Support

### Resources
- **MongoDB Documentation**: https://docs.mongodb.com/
- **MongoDB University**: https://university.mongodb.com/
- **Community Forums**: https://community.mongodb.com/

### Quick Help
```powershell
# MongoDB help
mongo --help

# Database commands help
mongo --eval "help"
```

---

**Next Steps**: After MongoDB setup, run the seeding script to populate sample data and test the authentication system.
