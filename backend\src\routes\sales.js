const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
    getSales,
    getSale,
    createSale,
    updateSale,
    cancelSale,
    getSalesStats
} = require('../controllers/salesController');

const router = express.Router();

// Stats route (must be before /:id route)
router.get('/stats', protect, getSalesStats);

// CRUD routes
router.route('/')
    .get(protect, getSales)
    .post(protect, authorize('admin', 'manager', 'cashier'), createSale);

router.route('/:id')
    .get(protect, getSale)
    .put(protect, authorize('admin', 'manager'), updateSale);

// Cancel sale
router.put('/:id/cancel', protect, authorize('admin', 'manager'), cancelSale);

module.exports = router;
