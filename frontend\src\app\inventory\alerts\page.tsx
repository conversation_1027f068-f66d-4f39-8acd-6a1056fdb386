'use client'

import { useState, useEffect } from 'react'
import { use<PERSON>out<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/contexts/auth-context'
import {
  AlertTriangle,
  Bell,
  Package,
  Clock,
  Mail,
  MessageSquare,
  Settings,
  CheckCircle,
  X,
  Search,
  Filter,
  RefreshCw,
  Calendar,
  TrendingDown,
  Zap,
  Archive
} from 'lucide-react'

interface InventoryAlert {
  _id: string
  type: 'low_stock' | 'out_of_stock' | 'expiry_warning' | 'reorder_point' | 'overstock'
  severity: 'low' | 'medium' | 'high' | 'critical'
  productId: string
  productName: string
  sku: string
  category: string
  currentStock: number
  reorderPoint: number
  maxStock: number
  expiryDate?: string
  message: string
  createdAt: string
  isRead: boolean
  isResolved: boolean
  autoGenerated: boolean
}

interface NotificationSettings {
  emailEnabled: boolean
  smsEnabled: boolean
  pushEnabled: boolean
  soundEnabled: boolean
  lowStockThreshold: number
  expiryWarningDays: number
  quietHoursStart: string
  quietHoursEnd: string
}

export default function InventoryAlertsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [alerts, setAlerts] = useState<InventoryAlert[]>([])
  const [filteredAlerts, setFilteredAlerts] = useState<InventoryAlert[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState('all')
  const [severityFilter, setSeverityFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')

  const [notificationSettings, setNotificationSettings] = useState<NotificationSettings>({
    emailEnabled: true,
    smsEnabled: false,
    pushEnabled: true,
    soundEnabled: true,
    lowStockThreshold: 10,
    expiryWarningDays: 30,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00'
  })

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchInventoryAlerts()
    }
  }, [isAuthenticated])

  useEffect(() => {
    filterAlerts()
  }, [alerts, searchQuery, typeFilter, severityFilter, statusFilter])

  const fetchInventoryAlerts = async () => {
    try {
      setLoading(true)
      
      // Mock inventory alerts data
      const mockAlerts: InventoryAlert[] = [
        {
          _id: '1',
          type: 'low_stock',
          severity: 'high',
          productId: 'prod1',
          productName: 'ASUS VivoBook 15',
          sku: 'LAP001',
          category: 'Laptops',
          currentStock: 3,
          reorderPoint: 10,
          maxStock: 50,
          message: 'Stock level is below reorder point. Only 3 units remaining.',
          createdAt: '2024-01-14T10:30:00Z',
          isRead: false,
          isResolved: false,
          autoGenerated: true
        },
        {
          _id: '2',
          type: 'out_of_stock',
          severity: 'critical',
          productId: 'prod2',
          productName: 'HP LaserJet Pro',
          sku: 'PRI001',
          category: 'Printers',
          currentStock: 0,
          reorderPoint: 5,
          maxStock: 15,
          message: 'Product is completely out of stock. Immediate reorder required.',
          createdAt: '2024-01-14T09:15:00Z',
          isRead: false,
          isResolved: false,
          autoGenerated: true
        },
        {
          _id: '3',
          type: 'expiry_warning',
          severity: 'medium',
          productId: 'prod3',
          productName: 'Antivirus Software License',
          sku: 'SOF001',
          category: 'Software',
          currentStock: 25,
          reorderPoint: 10,
          maxStock: 100,
          expiryDate: '2024-02-15T00:00:00Z',
          message: 'Product expires in 32 days. Consider promotional pricing.',
          createdAt: '2024-01-14T08:45:00Z',
          isRead: true,
          isResolved: false,
          autoGenerated: true
        },
        {
          _id: '4',
          type: 'reorder_point',
          severity: 'medium',
          productId: 'prod4',
          productName: 'Samsung 27" Monitor',
          sku: 'MON001',
          category: 'Monitors',
          currentStock: 8,
          reorderPoint: 5,
          maxStock: 30,
          message: 'Stock approaching reorder point. Consider placing order soon.',
          createdAt: '2024-01-14T07:20:00Z',
          isRead: true,
          isResolved: false,
          autoGenerated: true
        },
        {
          _id: '5',
          type: 'overstock',
          severity: 'low',
          productId: 'prod5',
          productName: 'USB Cable Type-C',
          sku: 'CAB001',
          category: 'Accessories',
          currentStock: 150,
          reorderPoint: 20,
          maxStock: 100,
          message: 'Stock level exceeds maximum threshold. Consider promotional activities.',
          createdAt: '2024-01-13T16:10:00Z',
          isRead: true,
          isResolved: true,
          autoGenerated: true
        }
      ]

      setAlerts(mockAlerts)
    } catch (error) {
      console.error('Error fetching inventory alerts:', error)
    } finally {
      setLoading(false)
    }
  }

  const filterAlerts = () => {
    let filtered = alerts

    if (searchQuery) {
      filtered = filtered.filter(alert =>
        alert.productName.toLowerCase().includes(searchQuery.toLowerCase()) ||
        alert.sku.toLowerCase().includes(searchQuery.toLowerCase()) ||
        alert.category.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter(alert => alert.type === typeFilter)
    }

    if (severityFilter !== 'all') {
      filtered = filtered.filter(alert => alert.severity === severityFilter)
    }

    if (statusFilter !== 'all') {
      if (statusFilter === 'unread') {
        filtered = filtered.filter(alert => !alert.isRead)
      } else if (statusFilter === 'unresolved') {
        filtered = filtered.filter(alert => !alert.isResolved)
      } else if (statusFilter === 'resolved') {
        filtered = filtered.filter(alert => alert.isResolved)
      }
    }

    setFilteredAlerts(filtered)
  }

  const markAsRead = (alertId: string) => {
    setAlerts(prev => prev.map(alert =>
      alert._id === alertId ? { ...alert, isRead: true } : alert
    ))
  }

  const markAsResolved = (alertId: string) => {
    setAlerts(prev => prev.map(alert =>
      alert._id === alertId ? { ...alert, isResolved: true, isRead: true } : alert
    ))
  }

  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert._id !== alertId))
  }

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'low_stock': return <TrendingDown className="h-4 w-4" />
      case 'out_of_stock': return <X className="h-4 w-4" />
      case 'expiry_warning': return <Clock className="h-4 w-4" />
      case 'reorder_point': return <RefreshCw className="h-4 w-4" />
      case 'overstock': return <Archive className="h-4 w-4" />
      default: return <AlertTriangle className="h-4 w-4" />
    }
  }

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200'
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getTypeLabel = (type: string) => {
    switch (type) {
      case 'low_stock': return language === 'mm' ? 'နည်းသော စတော့' : 'Low Stock'
      case 'out_of_stock': return language === 'mm' ? 'စတော့ ကုန်' : 'Out of Stock'
      case 'expiry_warning': return language === 'mm' ? 'သက်တမ်း ကုန်ခါနီး' : 'Expiry Warning'
      case 'reorder_point': return language === 'mm' ? 'ပြန်မှာရန် အချိန်' : 'Reorder Point'
      case 'overstock': return language === 'mm' ? 'စတော့ များ' : 'Overstock'
      default: return type
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getAlertStats = () => {
    const total = alerts.length
    const unread = alerts.filter(a => !a.isRead).length
    const critical = alerts.filter(a => a.severity === 'critical').length
    const unresolved = alerts.filter(a => !a.isResolved).length

    return { total, unread, critical, unresolved }
  }



  if (!isAuthenticated) {
    return null
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'
  const stats = getAlertStats()

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-red-600 to-orange-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <AlertTriangle className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'စာရင်းဝင် သတိပေးချက်များ' : 'Inventory Alerts'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm' 
                      ? 'စတော့ အဆင့်များ နှင့် အလိုအလျောက် သတိပေးချက်များ'
                      : 'Stock levels monitoring and automatic notifications'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => router.push('/inventory/alerts/settings')}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <Settings className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ဆက်တင်များ' : 'Settings'}
                </Button>
                <Button
                  onClick={fetchInventoryAlerts}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ပြန်လည်ရယူ' : 'Refresh'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Alert Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း သတိပေးချက်များ' : 'Total Alerts'}
                  </p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                  <Bell className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'မဖတ်ရသေးသော' : 'Unread Alerts'}
                  </p>
                  <p className="text-2xl font-bold text-orange-600">{stats.unread}</p>
                </div>
                <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-xl">
                  <Mail className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'အရေးကြီး သတိပေးချက်များ' : 'Critical Alerts'}
                  </p>
                  <p className="text-2xl font-bold text-red-600">{stats.critical}</p>
                </div>
                <div className="p-3 bg-red-100 dark:bg-red-900/20 rounded-xl">
                  <Zap className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'မဖြေရှင်းရသေးသော' : 'Unresolved'}
                  </p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.unresolved}</p>
                </div>
                <div className="p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl">
                  <Clock className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={language === 'mm' ? 'ကုန်ပစ္စည်း အမည် သို့မဟုတ် SKU ရှာရန်...' : 'Search by product name or SKU...'}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-4">
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder={language === 'mm' ? 'အမျိုးအစား' : 'Type'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Types'}</SelectItem>
                    <SelectItem value="low_stock">{language === 'mm' ? 'နည်းသော စတော့' : 'Low Stock'}</SelectItem>
                    <SelectItem value="out_of_stock">{language === 'mm' ? 'စတော့ ကုန်' : 'Out of Stock'}</SelectItem>
                    <SelectItem value="expiry_warning">{language === 'mm' ? 'သက်တမ်း ကုန်ခါနီး' : 'Expiry Warning'}</SelectItem>
                    <SelectItem value="reorder_point">{language === 'mm' ? 'ပြန်မှာရန်' : 'Reorder Point'}</SelectItem>
                    <SelectItem value="overstock">{language === 'mm' ? 'စတော့ များ' : 'Overstock'}</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={severityFilter} onValueChange={setSeverityFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder={language === 'mm' ? 'အရေးကြီးမှု' : 'Severity'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Levels'}</SelectItem>
                    <SelectItem value="critical">{language === 'mm' ? 'အရေးကြီးဆုံး' : 'Critical'}</SelectItem>
                    <SelectItem value="high">{language === 'mm' ? 'မြင့်' : 'High'}</SelectItem>
                    <SelectItem value="medium">{language === 'mm' ? 'အလယ်အလတ်' : 'Medium'}</SelectItem>
                    <SelectItem value="low">{language === 'mm' ? 'နိမ့်' : 'Low'}</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder={language === 'mm' ? 'အခြေအနေ' : 'Status'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Status'}</SelectItem>
                    <SelectItem value="unread">{language === 'mm' ? 'မဖတ်ရသေး' : 'Unread'}</SelectItem>
                    <SelectItem value="unresolved">{language === 'mm' ? 'မဖြေရှင်းရသေး' : 'Unresolved'}</SelectItem>
                    <SelectItem value="resolved">{language === 'mm' ? 'ဖြေရှင်းပြီး' : 'Resolved'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Alerts List */}
        <div className="space-y-4">
          {filteredAlerts.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Bell className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {language === 'mm' ? 'သတိပေးချက် မရှိပါ' : 'No Alerts Found'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {language === 'mm' 
                    ? 'လက်ရှိ စစ်ထုတ်မှု အတိုင်း သတိပေးချက် မတွေ့ရပါ'
                    : 'No alerts match your current filters'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredAlerts.map((alert) => (
              <Card key={alert._id} className={`hover:shadow-lg transition-shadow ${!alert.isRead ? 'border-l-4 border-l-blue-500' : ''}`}>
                <CardContent className="p-6">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-3">
                        <div className={`p-2 rounded-lg ${getSeverityColor(alert.severity)}`}>
                          {getAlertIcon(alert.type)}
                        </div>
                        <div>
                          <h3 className="font-semibold text-lg">{alert.productName}</h3>
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <span>{alert.sku}</span>
                            <span>•</span>
                            <span>{alert.category}</span>
                            <span>•</span>
                            <Badge className={getSeverityColor(alert.severity)}>
                              {getTypeLabel(alert.type)}
                            </Badge>
                          </div>
                        </div>
                      </div>

                      <p className="text-gray-700 dark:text-gray-300 mb-3">
                        {alert.message}
                      </p>

                      <div className="flex items-center gap-4 text-sm text-gray-600">
                        <div className="flex items-center gap-1">
                          <Package className="h-4 w-4" />
                          <span>{language === 'mm' ? 'လက်ရှိ' : 'Current'}: {alert.currentStock}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <RefreshCw className="h-4 w-4" />
                          <span>{language === 'mm' ? 'ပြန်မှာရန်' : 'Reorder'}: {alert.reorderPoint}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Calendar className="h-4 w-4" />
                          <span>{formatDate(alert.createdAt)}</span>
                        </div>
                        {alert.expiryDate && (
                          <div className="flex items-center gap-1">
                            <Clock className="h-4 w-4" />
                            <span>{language === 'mm' ? 'သက်တမ်း' : 'Expires'}: {formatDate(alert.expiryDate)}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex flex-col gap-2 ml-4">
                      {!alert.isRead && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => markAsRead(alert._id)}
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {language === 'mm' ? 'ဖတ်ပြီး' : 'Mark Read'}
                        </Button>
                      )}
                      
                      {!alert.isResolved && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => markAsResolved(alert._id)}
                          className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
                        >
                          <CheckCircle className="h-4 w-4 mr-2" />
                          {language === 'mm' ? 'ဖြေရှင်း' : 'Resolve'}
                        </Button>
                      )}
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => dismissAlert(alert._id)}
                        className="bg-red-50 hover:bg-red-100 text-red-700 border-red-200"
                      >
                        <X className="h-4 w-4 mr-2" />
                        {language === 'mm' ? 'ဖယ်ရှား' : 'Dismiss'}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </MainLayout>
  )
}
