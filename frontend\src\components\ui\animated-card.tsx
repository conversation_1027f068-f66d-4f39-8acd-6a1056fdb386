'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils'

interface AnimatedCardProps {
  children: React.ReactNode
  className?: string
  hoverEffect?: 'lift' | 'glow' | 'scale' | 'rotate' | 'none'
  delay?: number
  duration?: number
}

export function AnimatedCard({ 
  children, 
  className, 
  hoverEffect = 'lift',
  delay = 0,
  duration = 300
}: AnimatedCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  const getHoverStyles = () => {
    switch (hoverEffect) {
      case 'lift':
        return 'hover:shadow-xl hover:-translate-y-1'
      case 'glow':
        return 'hover:shadow-2xl hover:shadow-blue-500/25'
      case 'scale':
        return 'hover:scale-105'
      case 'rotate':
        return 'hover:rotate-1'
      case 'none':
        return ''
      default:
        return 'hover:shadow-xl hover:-translate-y-1'
    }
  }

  return (
    <Card
      className={cn(
        'transition-all duration-300 ease-in-out cursor-pointer',
        'animate-in fade-in slide-in-from-bottom-4',
        getHoverStyles(),
        className
      )}
      style={{
        animationDelay: `${delay}ms`,
        animationDuration: `${duration}ms`
      }}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {children}
    </Card>
  )
}

interface AnimatedCounterProps {
  value: number
  duration?: number
  prefix?: string
  suffix?: string
  className?: string
}

export function AnimatedCounter({ 
  value, 
  duration = 2000, 
  prefix = '', 
  suffix = '',
  className 
}: AnimatedCounterProps) {
  const [displayValue, setDisplayValue] = useState(0)

  useState(() => {
    let startTime: number
    let startValue = 0

    const animate = (currentTime: number) => {
      if (!startTime) startTime = currentTime
      const progress = Math.min((currentTime - startTime) / duration, 1)
      
      // Easing function for smooth animation
      const easeOutQuart = 1 - Math.pow(1 - progress, 4)
      const currentValue = Math.floor(startValue + (value - startValue) * easeOutQuart)
      
      setDisplayValue(currentValue)
      
      if (progress < 1) {
        requestAnimationFrame(animate)
      }
    }
    
    requestAnimationFrame(animate)
  })

  return (
    <span className={cn('font-bold tabular-nums', className)}>
      {prefix}{displayValue.toLocaleString()}{suffix}
    </span>
  )
}

interface AnimatedProgressProps {
  value: number
  max?: number
  className?: string
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'pink'
  showPercentage?: boolean
  duration?: number
}

export function AnimatedProgress({ 
  value, 
  max = 100, 
  className,
  color = 'blue',
  showPercentage = false,
  duration = 1000
}: AnimatedProgressProps) {
  const percentage = Math.min((value / max) * 100, 100)
  
  const getColorClasses = () => {
    switch (color) {
      case 'blue':
        return 'bg-blue-500'
      case 'green':
        return 'bg-green-500'
      case 'purple':
        return 'bg-purple-500'
      case 'orange':
        return 'bg-orange-500'
      case 'pink':
        return 'bg-pink-500'
      default:
        return 'bg-blue-500'
    }
  }

  return (
    <div className={cn('space-y-2', className)}>
      <div className="w-full bg-gray-200 rounded-full h-2 dark:bg-gray-700 overflow-hidden">
        <div 
          className={cn(
            'h-2 rounded-full transition-all ease-out',
            getColorClasses()
          )}
          style={{ 
            width: `${percentage}%`,
            transitionDuration: `${duration}ms`
          }}
        />
      </div>
      {showPercentage && (
        <div className="text-right text-sm text-gray-600">
          <AnimatedCounter value={percentage} suffix="%" duration={duration} />
        </div>
      )}
    </div>
  )
}

interface PulseDotsProps {
  count?: number
  size?: 'sm' | 'md' | 'lg'
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'pink'
  className?: string
}

export function PulseDots({ 
  count = 3, 
  size = 'md',
  color = 'blue',
  className 
}: PulseDotsProps) {
  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-2 h-2'
      case 'md':
        return 'w-3 h-3'
      case 'lg':
        return 'w-4 h-4'
      default:
        return 'w-3 h-3'
    }
  }

  const getColorClasses = () => {
    switch (color) {
      case 'blue':
        return 'bg-blue-500'
      case 'green':
        return 'bg-green-500'
      case 'purple':
        return 'bg-purple-500'
      case 'orange':
        return 'bg-orange-500'
      case 'pink':
        return 'bg-pink-500'
      default:
        return 'bg-blue-500'
    }
  }

  return (
    <div className={cn('flex space-x-1', className)}>
      {Array.from({ length: count }).map((_, index) => (
        <div
          key={index}
          className={cn(
            'rounded-full animate-pulse',
            getSizeClasses(),
            getColorClasses()
          )}
          style={{
            animationDelay: `${index * 200}ms`,
            animationDuration: '1.4s'
          }}
        />
      ))}
    </div>
  )
}

interface FloatingActionButtonProps {
  onClick: () => void
  icon: React.ReactNode
  label?: string
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'
  color?: 'blue' | 'green' | 'purple' | 'orange' | 'pink'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

export function FloatingActionButton({
  onClick,
  icon,
  label,
  position = 'bottom-right',
  color = 'blue',
  size = 'md',
  className
}: FloatingActionButtonProps) {
  const getPositionClasses = () => {
    switch (position) {
      case 'bottom-right':
        return 'bottom-6 right-6'
      case 'bottom-left':
        return 'bottom-6 left-6'
      case 'top-right':
        return 'top-6 right-6'
      case 'top-left':
        return 'top-6 left-6'
      default:
        return 'bottom-6 right-6'
    }
  }

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'w-12 h-12'
      case 'md':
        return 'w-14 h-14'
      case 'lg':
        return 'w-16 h-16'
      default:
        return 'w-14 h-14'
    }
  }

  const getColorClasses = () => {
    switch (color) {
      case 'blue':
        return 'bg-blue-500 hover:bg-blue-600'
      case 'green':
        return 'bg-green-500 hover:bg-green-600'
      case 'purple':
        return 'bg-purple-500 hover:bg-purple-600'
      case 'orange':
        return 'bg-orange-500 hover:bg-orange-600'
      case 'pink':
        return 'bg-pink-500 hover:bg-pink-600'
      default:
        return 'bg-blue-500 hover:bg-blue-600'
    }
  }

  return (
    <div className={cn('fixed z-50', getPositionClasses())}>
      <button
        onClick={onClick}
        className={cn(
          'rounded-full text-white shadow-lg transition-all duration-300',
          'hover:shadow-xl hover:scale-110 active:scale-95',
          'flex items-center justify-center',
          getSizeClasses(),
          getColorClasses(),
          className
        )}
        title={label}
      >
        {icon}
      </button>
    </div>
  )
}

interface ShimmerProps {
  className?: string
  children?: React.ReactNode
}

export function Shimmer({ className, children }: ShimmerProps) {
  return (
    <div className={cn('relative overflow-hidden', className)}>
      {children}
      <div className="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/60 to-transparent" />
    </div>
  )
}

// Add shimmer animation to global CSS
const shimmerKeyframes = `
@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}
`

// Inject styles
if (typeof document !== 'undefined') {
  const style = document.createElement('style')
  style.textContent = shimmerKeyframes
  document.head.appendChild(style)
}
