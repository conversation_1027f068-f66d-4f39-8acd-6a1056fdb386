'use client'

import { useState, useEffect } from 'react'
import { createPortal } from 'react-dom'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'

import { useAuth } from '@/contexts/auth-context'
import { useSettings, useTranslation } from '@/contexts/settings-context'
import apiClient from '@/lib/api'
import { ArrowLeft, Save, Package, Plus, Palette, Hash, Upload, X, Image as ImageIcon } from 'lucide-react'

interface Category {
  _id: string
  name: string
  color: string
  icon: string
}

interface ProductForm {
  name: string
  description: string
  sku: string
  barcode: string
  category: string
  price: number
  cost: number
  currency: string
  taxRate: number
  inventory: {
    quantity: number
    minQuantity: number
    maxQuantity: number
    unit: string
  }
  isActive: boolean
  isFeatured: boolean
  tags: string
  images: {
    url: string
    alt: string
    isPrimary: boolean
  }[]
}

export default function NewProductPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { formatCurrency } = useSettings()
  const { t } = useTranslation()
  const router = useRouter()
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(false)
  const [errors, setErrors] = useState<{[key: string]: string}>({})

  // New Category Dialog States
  const [showNewCategoryDialog, setShowNewCategoryDialog] = useState(false)
  const [newCategoryLoading, setNewCategoryLoading] = useState(false)
  const [categorySelectKey, setCategorySelectKey] = useState(0)
  const [newCategoryData, setNewCategoryData] = useState({
    name: '',
    description: '',
    color: '#3B82F6',
    icon: 'folder'
  })

  const [formData, setFormData] = useState<ProductForm>({
    name: '',
    description: '',
    sku: '',
    barcode: '',
    category: '',
    price: 0,
    cost: 0,
    currency: 'MMK',
    taxRate: 5,
    inventory: {
      quantity: 0,
      minQuantity: 5,
      maxQuantity: 1000,
      unit: 'piece'
    },
    isActive: true,
    isFeatured: false,
    tags: '',
    images: []
  })

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      console.log('🚀 Component mounted, fetching categories...')
      fetchCategories()
    }
  }, [isAuthenticated])

  // Debug categories state changes
  useEffect(() => {
    console.log('📊 Categories state changed:', categories)
    console.log('📊 Categories count:', categories.length)
  }, [categories])

  const fetchCategories = async () => {
    try {
      console.log('🔄 Fetching categories from API...')
      const response = await apiClient.getCategories()
      console.log('📂 Categories API response:', response)
      console.log('📂 Response type:', typeof response)
      console.log('📂 Response.success:', response?.success)
      console.log('📂 Response.data:', response?.data)
      console.log('📂 Response.data type:', typeof response?.data)
      console.log('📂 Response.data isArray:', Array.isArray(response?.data))

      if (response && response.success && Array.isArray(response.data)) {
        console.log('✅ Categories fetched successfully:', response.data.length, 'categories')
        console.log('📋 Categories data:', response.data)
        setCategories(response.data)
        setCategorySelectKey(prev => prev + 1) // Force re-render
      } else {
        console.warn('⚠️ Categories response is not valid, using mock data')
        console.warn('⚠️ Response details:', { response, success: response?.success, data: response?.data })
        // Try to use mock data if API fails
        const mockCategories = [
          { _id: 'mock-1', name: 'Electronics', color: '#3B82F6', icon: 'laptop' },
          { _id: 'mock-2', name: 'Accessories', color: '#10B981', icon: 'mouse' },
          { _id: 'mock-3', name: 'Software', color: '#F59E0B', icon: 'harddrive' }
        ]
        console.log('🔄 Using mock categories:', mockCategories)
        setCategories(mockCategories)
        setCategorySelectKey(prev => prev + 1)
      }
    } catch (error) {
      console.error('❌ Error fetching categories:', error)
      console.error('❌ Error details:', (error as Error).message, (error as Error).stack)
      // Use mock data as fallback
      const mockCategories = [
        { _id: 'mock-1', name: 'Electronics', color: '#3B82F6', icon: 'laptop' },
        { _id: 'mock-2', name: 'Accessories', color: '#10B981', icon: 'mouse' },
        { _id: 'mock-3', name: 'Software', color: '#F59E0B', icon: 'harddrive' }
      ]
      console.log('🔄 Using fallback mock categories:', mockCategories)
      setCategories(mockCategories)
      setCategorySelectKey(prev => prev + 1)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => {
        const parentValue = prev[parent as keyof ProductForm]
        if (typeof parentValue === 'object' && parentValue !== null) {
          return {
            ...prev,
            [parent]: {
              ...parentValue,
              [child]: value
            }
          }
        }
        return prev
      })
    } else {
      // Special handling for category selection
      if (field === 'category') {
        const selectedCategory = categories.find(cat => (cat._id || (cat as any).id) === value)
        console.log('🏷️ Category selected in handleInputChange:', value)
        console.log('🏷️ Selected category object:', selectedCategory)

        setFormData(prev => ({
          ...prev,
          [field]: value,
          categoryName: selectedCategory?.name || (selectedCategory as any)?.title || ''
        }))
      } else {
        setFormData(prev => ({
          ...prev,
          [field]: value
        }))
      }
    }

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Product name is required'
    }

    if (!formData.sku.trim()) {
      newErrors.sku = 'SKU is required'
    }

    if (!formData.category) {
      newErrors.category = 'Category is required'
    }

    if (formData.price <= 0) {
      newErrors.price = 'Price must be greater than 0'
    }

    if (formData.cost < 0) {
      newErrors.cost = 'Cost cannot be negative'
    }

    if (formData.inventory.quantity < 0) {
      newErrors['inventory.quantity'] = 'Quantity cannot be negative'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    try {
      setLoading(true)
      setErrors({}) // Clear previous errors

      const productData = {
        ...formData,
        tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag)
      }

      console.log('Submitting product data:', productData)

      const response = await apiClient.createProduct(productData)

      console.log('Product creation response:', response)

      if (response.success) {
        // Trigger storage event to update products page
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'bitstech_products',
          newValue: localStorage.getItem('bitstech_products'),
          storageArea: localStorage
        }))

        // Show success message
        alert(user?.preferences?.language === 'mm'
          ? 'ကုန်ပစ္စည်း အောင်မြင်စွာ ဖန်တီးပြီးပါပြီ'
          : 'Product created successfully'
        )

        // Redirect to products list
        router.push('/products')
      } else {
        throw new Error(response.message || 'Failed to create product')
      }
    } catch (error: any) {
      console.error('Error creating product:', error)
      setErrors({
        general: error.message || (user?.preferences?.language === 'mm'
          ? 'ကုန်ပစ္စည်း ဖန်တီးမှု မအောင်မြင်ပါ'
          : 'Failed to create product'
        )
      })
    } finally {
      setLoading(false)
    }
  }

  const generateSKU = () => {
    const timestamp = Date.now().toString().slice(-6)
    const randomStr = Math.random().toString(36).substring(2, 5).toUpperCase()
    const categoryPrefix = categories.find(cat => cat._id === formData.category)?.name?.substring(0, 3).toUpperCase() || 'PRD'
    setFormData(prev => ({
      ...prev,
      sku: `${categoryPrefix}-${randomStr}-${timestamp}`
    }))
  }

  // Auto-generate SKU when component mounts or category changes
  useEffect(() => {
    if (!formData.sku && formData.category) {
      generateSKU()
    }
  }, [formData.category])

  // Auto-generate SKU when name changes (if SKU is empty)
  useEffect(() => {
    if (!formData.sku && formData.name && formData.name.length >= 2) {
      const namePrefix = formData.name.substring(0, 3).toUpperCase().replace(/[^A-Z0-9]/g, '') || 'PRD'
      const timestamp = Date.now().toString().slice(-6)
      const randomStr = Math.random().toString(36).substring(2, 3).toUpperCase()
      setFormData(prev => ({
        ...prev,
        sku: `${namePrefix}-${randomStr}${timestamp}`
      }))
    }
  }, [formData.name])

  // Auto-generate SKU on component mount if name is already filled
  useEffect(() => {
    if (!formData.sku && formData.name && formData.name.length >= 2) {
      const namePrefix = formData.name.substring(0, 3).toUpperCase().replace(/[^A-Z0-9]/g, '') || 'PRD'
      const timestamp = Date.now().toString().slice(-6)
      const randomStr = Math.random().toString(36).substring(2, 3).toUpperCase()
      setFormData(prev => ({
        ...prev,
        sku: `${namePrefix}-${randomStr}${timestamp}`
      }))
    }
  }, [])

  const generateBarcode = () => {
    // Generate EAN-13 compatible barcode (13 digits)
    const timestamp = Date.now().toString()
    const randomDigits = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    const barcode = `${timestamp.slice(-7)}${randomDigits}`.slice(0, 13)

    setFormData(prev => ({
      ...prev,
      barcode: barcode
    }))
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string
          setFormData(prev => ({
            ...prev,
            images: [...prev.images, {
              url: imageUrl,
              alt: file.name,
              isPrimary: prev.images.length === 0 // First image is primary
            }]
          }))
        }
        reader.readAsDataURL(file)
      }
    })

    // Reset input
    event.target.value = ''
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const setPrimaryImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.map((img, i) => ({
        ...img,
        isPrimary: i === index
      }))
    }))
  }

  const handleCreateCategory = async () => {
    // Validate category name
    if (!newCategoryData.name || !newCategoryData.name.trim()) {
      alert(user?.preferences?.language === 'mm' ? 'အမျိုးအစား အမည် လိုအပ်ပါသည်' : 'Category name is required')
      return
    }

    // Validate category name length
    if (newCategoryData.name.trim().length < 2) {
      alert(user?.preferences?.language === 'mm' ? 'အမျိုးအစား အမည် အနည်းဆုံး ၂ လုံး ရှိရမည်' : 'Category name must be at least 2 characters')
      return
    }

    setNewCategoryLoading(true)
    try {
      // Prepare category data with proper validation
      const categoryData = {
        name: newCategoryData.name.trim(),
        description: newCategoryData.description?.trim() || '',
        color: newCategoryData.color || '#3B82F6',
        icon: newCategoryData.icon || 'folder',
        isActive: true
      }

      console.log('Creating category with data:', categoryData)

      const response = await apiClient.createCategory(categoryData)
      console.log('Category creation response:', response)

      if (response.success && response.data) {
        const newCategory = response.data
        console.log('New category created successfully:', newCategory)

        // Add the new category to the existing categories list immediately
        const categoryToAdd = {
          _id: newCategory._id || newCategory.id,
          id: newCategory._id || newCategory.id,
          name: newCategory.name || categoryData.name,
          description: newCategory.description || categoryData.description,
          color: newCategory.color || categoryData.color,
          icon: newCategory.icon || categoryData.icon,
          isActive: true
        }

        setCategories(prev => {
          const updatedCategories = [...prev, categoryToAdd]
          console.log('✅ Added new category to list:', categoryToAdd)
          console.log('✅ Updated categories list:', updatedCategories)
          return updatedCategories
        })

        // Also refresh categories list from API to ensure consistency
        setTimeout(() => {
          fetchCategories()
        }, 500)

        // Select the new category immediately using the correct ID field
        const categoryId = newCategory._id || newCategory.id
        if (categoryId) {
          setFormData(prev => ({
            ...prev,
            category: categoryId
          }))
          console.log('Selected new category ID:', categoryId)
        }

        // Force re-render by updating the select key
        setCategorySelectKey(prev => prev + 1)

        // Reset dialog
        setNewCategoryData({
          name: '',
          description: '',
          color: '#3B82F6',
          icon: 'folder'
        })
        setShowNewCategoryDialog(false)

        // Show success message with actual category name
        const categoryName = newCategory.name || categoryData.name
        alert(user?.preferences?.language === 'mm'
          ? `အမျိုးအစား "${categoryName}" အောင်မြင်စွာ ဖန်တီးပြီး ရွေးချယ်ထားပါပြီ`
          : `Category "${categoryName}" created successfully and selected`
        )
      } else {
        throw new Error(response.message || 'Failed to create category - no data returned')
      }
    } catch (error: any) {
      console.error('Error creating category:', error)
      const errorMessage = error.response?.data?.message || error.message || 'Unknown error occurred'
      alert(user?.preferences?.language === 'mm'
        ? `အမျိုးအစား ဖန်တီးမှု မအောင်မြင်ပါ: ${errorMessage}`
        : `Failed to create category: ${errorMessage}`
      )
    } finally {
      setNewCategoryLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="outline"
            onClick={() => router.back()}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            {user?.preferences?.language === 'mm' ? 'ပြန်သွားရန်' : 'Back'}
          </Button>
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {user?.preferences?.language === 'mm' ? 'ကုန်ပစ္စည်းအသစ် ထည့်ရန်' : 'Add New Product'}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {user?.preferences?.language === 'mm'
                ? 'ကုန်ပစ္စည်းအသစ်၏ အချက်အလက်များ ဖြည့်သွင်းပါ'
                : 'Fill in the details for the new product'
              }
            </p>
          </div>
        </div>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* General Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5" />
                {user?.preferences?.language === 'mm' ? 'အခြေခံ အချက်အလက်များ' : 'Basic Information'}
              </CardTitle>
              <CardDescription>
                {user?.preferences?.language === 'mm'
                  ? 'ကုန်ပစ္စည်း၏ အခြေခံ အချက်အလက်များ'
                  : 'Basic details about the product'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {errors.general && (
                <div className="p-3 text-sm text-red-600 bg-red-50 dark:bg-red-900/20 rounded-md">
                  {errors.general}
                </div>
              )}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">
                    {user?.preferences?.language === 'mm' ? 'ကုန်ပစ္စည်း အမည်' : 'Product Name'} *
                  </Label>
                  <Input
                    id="name"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    placeholder={user?.preferences?.language === 'mm' ? 'ကုန်ပစ္စည်း အမည် ရေးပါ' : 'Enter product name'}
                    className={errors.name ? 'border-red-500' : ''}
                  />
                  {errors.name && <p className="text-sm text-red-600">{errors.name}</p>}
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="category">
                      {user?.preferences?.language === 'mm' ? 'အမျိုးအစား' : 'Category'} *
                    </Label>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-8"
                      onClick={() => {
                        console.log('Add New Category button clicked')
                        setShowNewCategoryDialog(true)
                        console.log('showNewCategoryDialog set to true')
                      }}
                    >
                      <Plus className="h-4 w-4 mr-1" />
                      {user?.preferences?.language === 'mm' ? 'အသစ်ထည့်' : 'Add New'}
                    </Button>
                  </div>
                  <Select
                    key={`category-select-${categorySelectKey}`}
                    value={formData.category}
                    onValueChange={(value) => handleInputChange('category', value)}
                  >
                    <SelectTrigger className={errors.category ? 'border-red-500' : ''}>
                      <SelectValue placeholder={user?.preferences?.language === 'mm' ? 'အမျိုးအစား ရွေးပါ' : 'Select category'} />
                    </SelectTrigger>
                    <SelectContent>
                      {/* Debug logs moved to useEffect */}
                      {categories.length === 0 ? (
                        <SelectItem value="no-categories" disabled>
                          {user?.preferences?.language === 'mm' ? 'အမျိုးအစားများ မရှိပါ' : 'No categories available'}
                        </SelectItem>
                      ) : (
                        categories.map((category, index) => {
                          const categoryId = category._id || (category as any).id
                          const categoryName = category.name || (category as any).title || `Category ${index + 1}`
                          console.log('🏷️ Rendering category:', categoryName, 'ID:', categoryId)
                          console.log('🏷️ Full category object:', category)
                          return (
                            <SelectItem key={categoryId || `category-${index}`} value={categoryId}>
                              <div className="flex items-center gap-2">
                                <span
                                  className="w-3 h-3 rounded-full"
                                  style={{ backgroundColor: category.color || '#3B82F6' }}
                                  aria-hidden="true"
                                ></span>
                                {categoryName}
                              </div>
                            </SelectItem>
                          )
                        })
                      )}
                    </SelectContent>
                  </Select>
                  {errors.category && <p className="text-sm text-red-600">{errors.category}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">
                  {user?.preferences?.language === 'mm' ? 'ဖော်ပြချက်' : 'Description'}
                </Label>
                <Textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={user?.preferences?.language === 'mm' ? 'ကုန်ပစ္စည်း ဖော်ပြချက်' : 'Product description'}
                  rows={3}
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="sku">
                    {user?.preferences?.language === 'mm' ? 'SKU ကုဒ်' : 'SKU Code'} *
                    <span className="text-xs text-gray-500 ml-2">
                      {user?.preferences?.language === 'mm' ? '(အလိုအလျောက်)' : '(Auto-generated)'}
                    </span>
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="sku"
                      value={formData.sku || ''}
                      onChange={(e) => handleInputChange('sku', e.target.value)}
                      placeholder={user?.preferences?.language === 'mm' ? 'PRD-XXX-123456' : 'PRD-XXX-123456'}
                      className={errors.sku ? 'border-red-500' : ''}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={generateSKU}
                      title={user?.preferences?.language === 'mm' ? 'SKU အသစ် ထုတ်ရန်' : 'Generate new SKU'}
                    >
                      <Hash className="h-4 w-4 mr-1" />
                      {user?.preferences?.language === 'mm' ? 'ထုတ်ပေးရန်' : 'Generate'}
                    </Button>
                  </div>
                  {errors.sku && <p className="text-sm text-red-600">{errors.sku}</p>}
                  <p className="text-xs text-gray-500">
                    {user?.preferences?.language === 'mm'
                      ? 'ကုန်ပစ္စည်း အမည် သို့မဟုတ် အမျိုးအစား ရွေးချယ်သောအခါ SKU အလိုအလျောက် ထုတ်ပေးမည်'
                      : 'SKU will be auto-generated when you enter product name or select category'
                    }
                  </p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="barcode">
                    {user?.preferences?.language === 'mm' ? 'ဘားကုဒ်' : 'Barcode'}
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="barcode"
                      value={formData.barcode}
                      onChange={(e) => handleInputChange('barcode', e.target.value)}
                      placeholder="1234567890123"
                    />
                    <Button type="button" variant="outline" onClick={generateBarcode}>
                      <Hash className="h-4 w-4 mr-1" />
                      {user?.preferences?.language === 'mm' ? 'ထုတ်ပေးရန်' : 'Generate'}
                    </Button>
                  </div>
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="tags">
                  {user?.preferences?.language === 'mm' ? 'တဂ်များ' : 'Tags'}
                </Label>
                <Input
                  id="tags"
                  value={formData.tags}
                  onChange={(e) => handleInputChange('tags', e.target.value)}
                  placeholder={user?.preferences?.language === 'mm' ? 'တဂ်များကို ကော်မာ(,) ဖြင့် ခွဲပါ' : 'Separate tags with commas'}
                />
              </div>

              {/* Image Upload Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <Label>
                    {user?.preferences?.language === 'mm' ? 'ကုန်ပစ္စည်း ပုံများ' : 'Product Images'}
                  </Label>
                  <div className="relative">
                    <input
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                      id="imageUpload"
                      title={user?.preferences?.language === 'mm' ? 'ပုံထည့်ရန်' : 'Upload Images'}
                      aria-label={user?.preferences?.language === 'mm' ? 'ပုံထည့်ရန်' : 'Upload Images'}
                    />
                    <Button type="button" variant="outline" size="sm" className="h-8">
                      <Upload className="h-4 w-4 mr-1" />
                      {user?.preferences?.language === 'mm' ? 'ပုံထည့်ရန်' : 'Upload Images'}
                    </Button>
                  </div>
                </div>

                {/* Image Preview Grid */}
                {formData.images.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {formData.images.map((image, index) => (
                      <div key={`image-${index}-${image.url}`} className="relative group">
                        <div className="aspect-square rounded-lg overflow-hidden border-2 border-gray-200 dark:border-gray-700">
                          <img
                            src={image.url}
                            alt={image.alt}
                            className="w-full h-full object-cover"
                          />
                        </div>

                        {/* Image Controls */}
                        <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center gap-2">
                          <Button
                            type="button"
                            size="sm"
                            variant={image.isPrimary ? "default" : "outline"}
                            onClick={() => setPrimaryImage(index)}
                            className="h-8 text-xs"
                          >
                            {image.isPrimary ? (
                              user?.preferences?.language === 'mm' ? 'အဓိက' : 'Primary'
                            ) : (
                              user?.preferences?.language === 'mm' ? 'အဓိကလုပ်' : 'Set Primary'
                            )}
                          </Button>
                          <Button
                            type="button"
                            size="sm"
                            variant="destructive"
                            onClick={() => removeImage(index)}
                            className="h-8 w-8 p-0"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>

                        {/* Primary Badge */}
                        {image.isPrimary && (
                          <div className="absolute top-2 left-2 bg-blue-600 text-white text-xs px-2 py-1 rounded">
                            {user?.preferences?.language === 'mm' ? 'အဓိက' : 'Primary'}
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}

                {/* Upload Instructions */}
                {formData.images.length === 0 && (
                  <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8 text-center">
                    <ImageIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <p className="text-gray-600 dark:text-gray-400 mb-2">
                      {user?.preferences?.language === 'mm'
                        ? 'ကုန်ပစ္စည်း ပုံများ ထည့်ရန်'
                        : 'Upload product images'
                      }
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-500">
                      {user?.preferences?.language === 'mm'
                        ? 'JPG, PNG, GIF ဖိုင်များ လက်ခံပါသည်'
                        : 'Supports JPG, PNG, GIF files'
                      }
                    </p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Pricing Information */}
          <Card>
            <CardHeader>
              <CardTitle>
                {user?.preferences?.language === 'mm' ? 'စျေးနှုန်း အချက်အလက်များ' : 'Pricing Information'}
              </CardTitle>
              <CardDescription>
                {user?.preferences?.language === 'mm'
                  ? 'ကုန်ပစ္စည်း၏ စျေးနှုန်း နှင့် ကုန်ကျစရိတ်'
                  : 'Product pricing and cost details'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="cost">
                    {user?.preferences?.language === 'mm' ? 'ကုန်ကျစရိတ်' : 'Cost Price'} *
                  </Label>
                  <Input
                    id="cost"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.cost}
                    onChange={(e) => handleInputChange('cost', parseFloat(e.target.value) || 0)}
                    className={errors.cost ? 'border-red-500' : ''}
                  />
                  {errors.cost && <p className="text-sm text-red-600">{errors.cost}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="price">
                    {user?.preferences?.language === 'mm' ? 'ရောင်းစျေး' : 'Selling Price'} *
                  </Label>
                  <Input
                    id="price"
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                    className={errors.price ? 'border-red-500' : ''}
                  />
                  {errors.price && <p className="text-sm text-red-600">{errors.price}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="currency">
                    {user?.preferences?.language === 'mm' ? 'ငွေကြေး' : 'Currency'}
                  </Label>
                  <Select value={formData.currency} onValueChange={(value) => handleInputChange('currency', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem key="currency-mmk" value="MMK">MMK (Myanmar Kyat)</SelectItem>
                      <SelectItem key="currency-usd" value="USD">USD (US Dollar)</SelectItem>
                      <SelectItem key="currency-thb" value="THB">THB (Thai Baht)</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="taxRate">
                    {user?.preferences?.language === 'mm' ? 'အခွန်နှုန်း (%)' : 'Tax Rate (%)'}
                  </Label>
                  <Input
                    id="taxRate"
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    value={formData.taxRate}
                    onChange={(e) => handleInputChange('taxRate', parseFloat(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label>
                    {user?.preferences?.language === 'mm' ? 'အမြတ်နှုန်း' : 'Profit Margin'}
                  </Label>
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-md">
                    <span className="text-lg font-medium text-green-600">
                      {formData.cost > 0 ? (((formData.price - formData.cost) / formData.cost * 100).toFixed(2)) : '0.00'}%
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Inventory Information */}
          <Card>
            <CardHeader>
              <CardTitle>
                {user?.preferences?.language === 'mm' ? 'စတော့ အချက်အလက်များ' : 'Inventory Information'}
              </CardTitle>
              <CardDescription>
                {user?.preferences?.language === 'mm'
                  ? 'ကုန်ပစ္စည်း၏ စတော့ နှင့် ပမာဏ အချက်အလက်များ'
                  : 'Stock and quantity details'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="quantity">
                    {user?.preferences?.language === 'mm' ? 'လက်ရှိ ပမာဏ' : 'Current Quantity'} *
                  </Label>
                  <Input
                    id="quantity"
                    type="number"
                    min="0"
                    value={formData.inventory.quantity}
                    onChange={(e) => handleInputChange('inventory.quantity', parseInt(e.target.value) || 0)}
                    className={errors['inventory.quantity'] ? 'border-red-500' : ''}
                  />
                  {errors['inventory.quantity'] && <p className="text-sm text-red-600">{errors['inventory.quantity']}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="minQuantity">
                    {user?.preferences?.language === 'mm' ? 'အနည်းဆုံး ပမာဏ' : 'Minimum Quantity'}
                  </Label>
                  <Input
                    id="minQuantity"
                    type="number"
                    min="0"
                    value={formData.inventory.minQuantity}
                    onChange={(e) => handleInputChange('inventory.minQuantity', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="maxQuantity">
                    {user?.preferences?.language === 'mm' ? 'အများဆုံး ပမာဏ' : 'Maximum Quantity'}
                  </Label>
                  <Input
                    id="maxQuantity"
                    type="number"
                    min="0"
                    value={formData.inventory.maxQuantity}
                    onChange={(e) => handleInputChange('inventory.maxQuantity', parseInt(e.target.value) || 0)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="unit">
                    {user?.preferences?.language === 'mm' ? 'ယူနစ်' : 'Unit'}
                  </Label>
                  <Select value={formData.inventory.unit} onValueChange={(value) => handleInputChange('inventory.unit', value)}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem key="unit-piece" value="piece">Piece</SelectItem>
                      <SelectItem key="unit-kg" value="kg">Kilogram</SelectItem>
                      <SelectItem key="unit-gram" value="gram">Gram</SelectItem>
                      <SelectItem key="unit-liter" value="liter">Liter</SelectItem>
                      <SelectItem key="unit-ml" value="ml">Milliliter</SelectItem>
                      <SelectItem key="unit-cup" value="cup">Cup</SelectItem>
                      <SelectItem key="unit-bottle" value="bottle">Bottle</SelectItem>
                      <SelectItem key="unit-pack" value="pack">Pack</SelectItem>
                      <SelectItem key="unit-box" value="box">Box</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Save Button */}
          <div className="flex justify-end gap-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
            >
              {user?.preferences?.language === 'mm' ? 'မလုပ်တော့ပါ' : 'Cancel'}
            </Button>
            <Button type="submit" disabled={loading}>
              {loading ? (
                <>
                  <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                  {user?.preferences?.language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
                </>
              ) : (
                <>
                  <Save className="mr-2 h-4 w-4" />
                  {user?.preferences?.language === 'mm' ? 'သိမ်းရန်' : 'Save Product'}
                </>
              )}
            </Button>
          </div>
        </form>
      </div>

      {/* New Category Modal */}
      {showNewCategoryDialog && typeof window !== 'undefined' && createPortal(
        <div
          className="fixed inset-0 flex items-center justify-center bg-black/60 backdrop-blur-sm"
          style={{
            position: 'fixed',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 99999
          }}
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowNewCategoryDialog(false)
            }
          }}
        >
          <div
            className="bg-white dark:bg-gray-800 rounded-xl shadow-2xl w-full max-w-md mx-4 p-6 border border-gray-200 dark:border-gray-700"
            style={{ zIndex: 100000 }}
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                {user?.preferences?.language === 'mm' ? 'အမျိုးအစား အသစ် ဖန်တီးရန်' : 'Create New Category'}
              </h3>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => setShowNewCategoryDialog(false)}
                className="h-8 w-8 p-0"
              >
                ×
              </Button>
            </div>

            <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
              {user?.preferences?.language === 'mm'
                ? 'အမျိုးအစား အသစ်၏ အချက်အလက်များ ဖြည့်သွင်းပါ'
                : 'Fill in the details for the new category'
              }
            </p>

            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="categoryName">
                  {user?.preferences?.language === 'mm' ? 'အမျိုးအစား အမည်' : 'Category Name'} *
                </Label>
                <Input
                  id="categoryName"
                  value={newCategoryData.name}
                  onChange={(e) => {
                    const value = e.target.value
                    setNewCategoryData(prev => ({ ...prev, name: value }))
                    console.log('Category name changed to:', value)
                  }}
                  placeholder={user?.preferences?.language === 'mm' ? 'အမျိုးအစား အမည် ရေးပါ' : 'Enter category name'}
                  required
                  minLength={2}
                />
                {newCategoryData.name && newCategoryData.name.trim().length < 2 && (
                  <p className="text-sm text-red-600">
                    {user?.preferences?.language === 'mm' ? 'အမည် အနည်းဆုံး ၂ လုံး ရှိရမည်' : 'Name must be at least 2 characters'}
                  </p>
                )}
              </div>

              <div className="space-y-2">
                <Label htmlFor="categoryDescription">
                  {user?.preferences?.language === 'mm' ? 'ဖော်ပြချက်' : 'Description'}
                </Label>
                <Textarea
                  id="categoryDescription"
                  value={newCategoryData.description}
                  onChange={(e) => setNewCategoryData(prev => ({ ...prev, description: e.target.value }))}
                  placeholder={user?.preferences?.language === 'mm' ? 'အမျိုးအစား ဖော်ပြချက်' : 'Category description'}
                  rows={2}
                />
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="categoryColor">
                    {user?.preferences?.language === 'mm' ? 'အရောင်' : 'Color'}
                  </Label>
                  <div className="flex items-center gap-2">
                    <Input
                      id="categoryColor"
                      type="color"
                      value={newCategoryData.color}
                      onChange={(e) => setNewCategoryData(prev => ({ ...prev, color: e.target.value }))}
                      className="w-12 h-10 p-1 border rounded"
                    />
                    <Input
                      value={newCategoryData.color}
                      onChange={(e) => setNewCategoryData(prev => ({ ...prev, color: e.target.value }))}
                      placeholder="#3B82F6"
                      className="flex-1"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="categoryIcon">
                    {user?.preferences?.language === 'mm' ? 'အိုင်ကွန်' : 'Icon'}
                  </Label>
                  <div className="relative">
                    <select
                      id="categoryIcon"
                      value={newCategoryData.icon}
                      onChange={(e) => {
                        console.log('Icon selected:', e.target.value)
                        setNewCategoryData(prev => ({ ...prev, icon: e.target.value }))
                      }}
                      className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      aria-label={user?.preferences?.language === 'mm' ? 'အမျိုးအစား အိုင်ကွန် ရွေးချယ်ရန်' : 'Select category icon'}
                    >
                      <option value="">Select icon</option>
                      <option value="folder">📁 Folder</option>
                      <option value="laptop">💻 Laptop</option>
                      <option value="keyboard">⌨️ Keyboard</option>
                      <option value="mouse">🖱️ Mouse</option>
                      <option value="monitor">🖥️ Monitor</option>
                      <option value="harddrive">💾 Hard Drive</option>
                      <option value="smartphone">📱 Phone</option>
                      <option value="headphones">🎧 Headphones</option>
                      <option value="camera">📷 Camera</option>
                      <option value="gamepad">🎮 Gaming</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-2 mt-6">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowNewCategoryDialog(false)}
              >
                {user?.preferences?.language === 'mm' ? 'မလုပ်တော့ပါ' : 'Cancel'}
              </Button>
              <Button
                type="button"
                onClick={handleCreateCategory}
                disabled={newCategoryLoading || !newCategoryData.name.trim() || newCategoryData.name.trim().length < 2}
              >
                {newCategoryLoading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    {user?.preferences?.language === 'mm' ? 'ဖန်တီးနေသည်...' : 'Creating...'}
                  </>
                ) : (
                  <>
                    <Plus className="mr-2 h-4 w-4" />
                    {user?.preferences?.language === 'mm' ? 'ဖန်တီးရန်' : 'Create'}
                  </>
                )}
              </Button>
            </div>
          </div>
        </div>,
        document.body
      )}
    </MainLayout>
  )
}
