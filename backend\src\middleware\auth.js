const jwt = require('jsonwebtoken');
const User = require('../models/User');
const rateLimit = require('express-rate-limit');

// Enhanced rate limiting for authentication
const authLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 5, // limit each IP to 5 requests per windowMs
    message: {
        success: false,
        error: 'Too many authentication attempts, please try again later'
    },
    standardHeaders: true,
    legacyHeaders: false,
});

// Protect routes - require authentication with enhanced security
const protect = async (req, res, next) => {
    try {
        let token;

        // Check for token in headers
        if (req.headers.authorization && req.headers.authorization.startsWith('Bearer')) {
            token = req.headers.authorization.split(' ')[1];
        }

        // Check for token in cookies (for web sessions)
        if (!token && req.cookies && req.cookies.token) {
            token = req.cookies.token;
        }

        // Check if token exists
        if (!token) {
            return res.status(401).json({
                success: false,
                error: 'Not authorized to access this route',
                code: 'NO_TOKEN'
            });
        }

        try {
            // Verify token with enhanced security
            const decoded = jwt.verify(token, process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production');

            // Check token expiration
            if (decoded.exp && Date.now() >= decoded.exp * 1000) {
                return res.status(401).json({
                    success: false,
                    error: 'Token has expired',
                    code: 'TOKEN_EXPIRED'
                });
            }

            // Find user by ID in MongoDB with enhanced fields
            const user = await User.findById(decoded.id).select('+lastLogin +loginAttempts +lockUntil');

            if (!user) {
                return res.status(401).json({
                    success: false,
                    error: 'User not found',
                    code: 'USER_NOT_FOUND'
                });
            }

            if (!user.isActive) {
                return res.status(401).json({
                    success: false,
                    error: 'User account is deactivated',
                    code: 'ACCOUNT_DEACTIVATED'
                });
            }

            // Check if account is locked
            if (user.lockUntil && user.lockUntil > Date.now()) {
                return res.status(401).json({
                    success: false,
                    error: 'Account is temporarily locked due to too many failed attempts',
                    code: 'ACCOUNT_LOCKED'
                });
            }

            // Update last activity
            user.lastActivity = new Date();
            await user.save();

            req.user = user;
            next();
        } catch (error) {
            if (error.name === 'JsonWebTokenError') {
                return res.status(401).json({
                    success: false,
                    error: 'Invalid token',
                    code: 'INVALID_TOKEN'
                });
            } else if (error.name === 'TokenExpiredError') {
                return res.status(401).json({
                    success: false,
                    error: 'Token has expired',
                    code: 'TOKEN_EXPIRED'
                });
            } else {
                return res.status(401).json({
                    success: false,
                    error: 'Not authorized to access this route',
                    code: 'AUTH_ERROR'
                });
            }
        }
    } catch (error) {
        next(error);
    }
};

// Grant access to specific roles with enhanced logging
const authorize = (...roles) => {
    return (req, res, next) => {
        if (!roles.includes(req.user.role)) {
            // Log unauthorized access attempt
            console.warn(`🚫 Unauthorized access attempt: User ${req.user.email} (${req.user.role}) tried to access ${req.method} ${req.originalUrl}`);

            return res.status(403).json({
                success: false,
                error: `User role ${req.user.role} is not authorized to access this route`,
                code: 'INSUFFICIENT_PERMISSIONS',
                requiredRoles: roles,
                userRole: req.user.role
            });
        }

        // Log successful authorization
        console.log(`✅ Authorized access: User ${req.user.email} (${req.user.role}) accessing ${req.method} ${req.originalUrl}`);
        next();
    };
};

// Enhanced session management
const refreshToken = async (req, res, next) => {
    try {
        const { refreshToken } = req.body;

        if (!refreshToken) {
            return res.status(401).json({
                success: false,
                error: 'Refresh token is required',
                code: 'NO_REFRESH_TOKEN'
            });
        }

        // Verify refresh token
        const decoded = jwt.verify(refreshToken, process.env.JWT_REFRESH_SECRET || 'your-refresh-secret-key');
        const user = await User.findById(decoded.id);

        if (!user || !user.isActive) {
            return res.status(401).json({
                success: false,
                error: 'Invalid refresh token',
                code: 'INVALID_REFRESH_TOKEN'
            });
        }

        // Generate new access token
        const newToken = jwt.sign(
            { id: user._id, email: user.email, role: user.role },
            process.env.JWT_SECRET || 'your-super-secret-jwt-key-change-this-in-production',
            { expiresIn: process.env.JWT_EXPIRE || '24h' }
        );

        res.json({
            success: true,
            token: newToken,
            user: {
                id: user._id,
                email: user.email,
                firstName: user.firstName,
                lastName: user.lastName,
                role: user.role
            }
        });
    } catch (error) {
        return res.status(401).json({
            success: false,
            error: 'Invalid refresh token',
            code: 'REFRESH_TOKEN_ERROR'
        });
    }
};

// Two-Factor Authentication middleware
const requireTwoFactor = async (req, res, next) => {
    try {
        if (req.user.twoFactorEnabled && !req.user.twoFactorVerified) {
            return res.status(403).json({
                success: false,
                error: 'Two-factor authentication required',
                code: 'TWO_FACTOR_REQUIRED'
            });
        }
        next();
    } catch (error) {
        next(error);
    }
};

// Audit logging middleware
const auditLog = (action) => {
    return async (req, res, next) => {
        try {
            // Log the action for audit purposes
            const auditEntry = {
                userId: req.user ? req.user._id : null,
                userEmail: req.user ? req.user.email : null,
                action: action,
                resource: req.originalUrl,
                method: req.method,
                ip: req.ip || req.connection.remoteAddress,
                userAgent: req.get('User-Agent'),
                timestamp: new Date(),
                requestBody: req.method !== 'GET' ? req.body : null
            };

            // In production, save to audit log collection
            console.log('📋 Audit Log:', JSON.stringify(auditEntry, null, 2));

            next();
        } catch (error) {
            next(error);
        }
    };
};

module.exports = {
    protect,
    authorize,
    authLimiter,
    refreshToken,
    requireTwoFactor,
    auditLog,
    auth: protect  // Alias for protect
};
