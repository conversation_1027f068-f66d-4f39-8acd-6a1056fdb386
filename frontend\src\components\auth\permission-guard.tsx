'use client'

import React from 'react'
import { useAuth } from '@/contexts/auth-context'
import { usePermissions } from '@/lib/permissions'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Shield, Lock, AlertTriangle } from 'lucide-react'

interface PermissionGuardProps {
  children: React.ReactNode
  permission?: string
  permissions?: string[]
  requireAll?: boolean
  fallback?: React.ReactNode
  showError?: boolean
}

export function PermissionGuard({
  children,
  permission,
  permissions = [],
  requireAll = false,
  fallback,
  showError = true
}: PermissionGuardProps) {
  const { user, isAuthenticated } = useAuth()
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions()

  // If not authenticated, don't show anything
  if (!isAuthenticated || !user) {
    return fallback || (showError ? (
      <Alert className="border-red-200 bg-red-50">
        <Lock className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          You must be logged in to access this feature.
        </AlertDescription>
      </Alert>
    ) : null)
  }

  // Check single permission
  if (permission && !hasPermission(permission)) {
    return fallback || (showError ? (
      <Alert className="border-amber-200 bg-amber-50">
        <Shield className="h-4 w-4 text-amber-600" />
        <AlertDescription className="text-amber-800">
          You don't have permission to access this feature.
          <br />
          <span className="text-sm text-amber-600">Required: {permission}</span>
        </AlertDescription>
      </Alert>
    ) : null)
  }

  // Check multiple permissions
  if (permissions.length > 0) {
    const hasAccess = requireAll 
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)

    if (!hasAccess) {
      return fallback || (showError ? (
        <Alert className="border-amber-200 bg-amber-50">
          <Shield className="h-4 w-4 text-amber-600" />
          <AlertDescription className="text-amber-800">
            You don't have permission to access this feature.
            <br />
            <span className="text-sm text-amber-600">
              Required: {permissions.join(requireAll ? ' AND ' : ' OR ')}
            </span>
          </AlertDescription>
        </Alert>
      ) : null)
    }
  }

  // User has required permissions
  return <>{children}</>
}

interface RoleGuardProps {
  children: React.ReactNode
  roles: string[]
  fallback?: React.ReactNode
  showError?: boolean
}

export function RoleGuard({
  children,
  roles,
  fallback,
  showError = true
}: RoleGuardProps) {
  const { user, isAuthenticated } = useAuth()

  if (!isAuthenticated || !user) {
    return fallback || (showError ? (
      <Alert className="border-red-200 bg-red-50">
        <Lock className="h-4 w-4 text-red-600" />
        <AlertDescription className="text-red-800">
          You must be logged in to access this feature.
        </AlertDescription>
      </Alert>
    ) : null)
  }

  if (!roles.includes(user.role)) {
    return fallback || (showError ? (
      <Alert className="border-amber-200 bg-amber-50">
        <AlertTriangle className="h-4 w-4 text-amber-600" />
        <AlertDescription className="text-amber-800">
          Your role ({user.role}) doesn't have access to this feature.
          <br />
          <span className="text-sm text-amber-600">
            Required roles: {roles.join(', ')}
          </span>
        </AlertDescription>
      </Alert>
    ) : null)
  }

  return <>{children}</>
}

// Higher-order component for permission-based rendering
export function withPermission<P extends object>(
  Component: React.ComponentType<P>,
  permission: string
) {
  return function PermissionWrappedComponent(props: P) {
    return (
      <PermissionGuard permission={permission}>
        <Component {...props} />
      </PermissionGuard>
    )
  }
}

// Higher-order component for role-based rendering
export function withRole<P extends object>(
  Component: React.ComponentType<P>,
  roles: string[]
) {
  return function RoleWrappedComponent(props: P) {
    return (
      <RoleGuard roles={roles}>
        <Component {...props} />
      </RoleGuard>
    )
  }
}

// Hook for conditional rendering based on permissions
export function useConditionalRender() {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermissions()
  const { user } = useAuth()

  return {
    renderIf: (condition: boolean, component: React.ReactNode) => 
      condition ? component : null,
    
    renderIfPermission: (permission: string, component: React.ReactNode) =>
      hasPermission(permission) ? component : null,
    
    renderIfAnyPermission: (permissions: string[], component: React.ReactNode) =>
      hasAnyPermission(permissions) ? component : null,
    
    renderIfAllPermissions: (permissions: string[], component: React.ReactNode) =>
      hasAllPermissions(permissions) ? component : null,
    
    renderIfRole: (roles: string[], component: React.ReactNode) =>
      user && roles.includes(user.role) ? component : null,
    
    renderIfAdmin: (component: React.ReactNode) =>
      user?.role === 'admin' ? component : null,
    
    renderIfManager: (component: React.ReactNode) =>
      user && ['admin', 'manager'].includes(user.role) ? component : null
  }
}

// Utility component for showing user role badge
export function UserRoleBadge({ className = '' }: { className?: string }) {
  const { user } = useAuth()
  
  if (!user) return null

  const roleColors = {
    admin: 'bg-red-100 text-red-800 border-red-200',
    manager: 'bg-blue-100 text-blue-800 border-blue-200',
    supervisor: 'bg-purple-100 text-purple-800 border-purple-200',
    cashier: 'bg-green-100 text-green-800 border-green-200',
    sales_associate: 'bg-gray-100 text-gray-800 border-gray-200'
  }

  const roleNames = {
    admin: 'Administrator',
    manager: 'Manager',
    supervisor: 'Supervisor',
    cashier: 'Cashier',
    sales_associate: 'Sales Associate'
  }

  return (
    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium border ${roleColors[user.role as keyof typeof roleColors] || roleColors.sales_associate} ${className}`}>
      {roleNames[user.role as keyof typeof roleNames] || user.role}
    </span>
  )
}

// Component for displaying user permissions
export function UserPermissionsList({ className = '' }: { className?: string }) {
  const { getUserPermissions } = usePermissions()
  const permissions = getUserPermissions()

  if (permissions.length === 0) {
    return (
      <div className={`text-sm text-gray-500 ${className}`}>
        No permissions assigned
      </div>
    )
  }

  return (
    <div className={`space-y-1 ${className}`}>
      <h4 className="text-sm font-medium text-gray-900">Permissions:</h4>
      <div className="flex flex-wrap gap-1">
        {permissions.map((permission) => (
          <span
            key={permission}
            className="inline-flex items-center px-2 py-1 rounded text-xs bg-gray-100 text-gray-700"
          >
            {permission}
          </span>
        ))}
      </div>
    </div>
  )
}
