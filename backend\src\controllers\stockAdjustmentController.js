const StockAdjustment = require('../models/StockAdjustment');
const Product = require('../models/Product');
const User = require('../models/User');
// const emailService = require('../services/emailService'); // Temporarily disabled
const { validationResult } = require('express-validator');

// Get all stock adjustments
const getStockAdjustments = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            type,
            status,
            search,
            startDate,
            endDate
        } = req.query;

        // Build filter object
        const filter = {};

        if (type) filter.type = type;
        if (status) filter.status = status;

        if (search) {
            filter.$or = [
                { adjustmentNumber: { $regex: search, $options: 'i' } },
                { reason: { $regex: search, $options: 'i' } }
            ];
        }

        if (startDate || endDate) {
            filter.adjustmentDate = {};
            if (startDate) filter.adjustmentDate.$gte = new Date(startDate);
            if (endDate) filter.adjustmentDate.$lte = new Date(endDate);
        }

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get adjustments with pagination
        const adjustments = await StockAdjustment.find(filter)
            .populate('createdBy', 'firstName lastName email')
            .populate('approvedBy', 'firstName lastName email')
            .populate('items.product', 'name sku barcode category')
            .sort({ adjustmentDate: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const total = await StockAdjustment.countDocuments(filter);

        res.json({
            success: true,
            data: adjustments,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / parseInt(limit))
            }
        });
    } catch (error) {
        console.error('Error fetching stock adjustments:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching stock adjustments',
            error: error.message
        });
    }
};

// Get single stock adjustment
const getStockAdjustment = async (req, res) => {
    try {
        const { id } = req.params;

        const adjustment = await StockAdjustment.findById(id)
            .populate('createdBy', 'firstName lastName email')
            .populate('approvedBy', 'firstName lastName email')
            .populate('items.product', 'name sku barcode category inventory');

        if (!adjustment) {
            return res.status(404).json({
                success: false,
                message: 'Stock adjustment not found'
            });
        }

        res.json({
            success: true,
            data: adjustment
        });
    } catch (error) {
        console.error('Error fetching stock adjustment:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching stock adjustment',
            error: error.message
        });
    }
};

// Create new stock adjustment
const createStockAdjustment = async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            type,
            reason,
            notes,
            items,
            status = 'draft'
        } = req.body;

        // Validate products exist and get current quantities
        const productIds = items.map(item => item.product);
        const products = await Product.find({ _id: { $in: productIds } });

        if (products.length !== productIds.length) {
            return res.status(400).json({
                success: false,
                message: 'One or more products not found'
            });
        }

        // Validate and prepare items
        const adjustmentItems = [];
        let totalCostImpact = 0;

        for (const item of items) {
            const product = products.find(p => p._id.toString() === item.product);

            if (!product) {
                return res.status(400).json({
                    success: false,
                    message: `Product ${item.product} not found`
                });
            }

            const currentQuantity = product.inventory.quantity;
            const adjustmentQuantity = parseInt(item.adjustmentQuantity);
            const newQuantity = currentQuantity + adjustmentQuantity;

            // Prevent negative stock (unless it's a correction)
            if (newQuantity < 0 && type !== 'correction') {
                return res.status(400).json({
                    success: false,
                    message: `Insufficient stock for ${product.name}. Current: ${currentQuantity}, Adjustment: ${adjustmentQuantity}`
                });
            }

            const unitCost = item.unitCost || product.cost;
            const totalCost = adjustmentQuantity * unitCost;
            totalCostImpact += totalCost;

            adjustmentItems.push({
                product: product._id,
                productName: product.name,
                sku: product.sku,
                currentQuantity,
                adjustmentQuantity,
                newQuantity: Math.max(0, newQuantity),
                unitCost,
                totalCost,
                notes: item.notes || ''
            });
        }

        // Create adjustment
        const adjustment = new StockAdjustment({
            type,
            reason,
            notes,
            items: adjustmentItems,
            status,
            totalCostImpact,
            createdBy: req.user.id
        });

        await adjustment.save();

        // Populate the created adjustment
        await adjustment.populate('createdBy', 'firstName lastName email');
        await adjustment.populate('items.product', 'name sku barcode category');

        // Send email notification if submitted for approval
        if (status === 'pending') {
            try {
                // Get all managers and admins
                const managers = await User.find({
                    role: { $in: ['admin', 'manager'] },
                    isActive: true
                }).select('firstName lastName email');

                if (managers.length > 0) {
                    // await emailService.sendAdjustmentSubmittedNotification(adjustment, managers);
                    console.log('Email notification would be sent to managers:', managers.map(m => m.email));
                }
            } catch (emailError) {
                console.error('Error sending email notification:', emailError);
                // Don't fail the request if email fails
            }
        }

        res.status(201).json({
            success: true,
            message: 'Stock adjustment created successfully',
            data: adjustment
        });
    } catch (error) {
        console.error('Error creating stock adjustment:', error);
        res.status(500).json({
            success: false,
            message: 'Error creating stock adjustment',
            error: error.message
        });
    }
};

// Update stock adjustment
const updateStockAdjustment = async (req, res) => {
    try {
        const { id } = req.params;
        const updates = req.body;

        const adjustment = await StockAdjustment.findById(id);

        if (!adjustment) {
            return res.status(404).json({
                success: false,
                message: 'Stock adjustment not found'
            });
        }

        // Only allow updates if status is draft
        if (adjustment.status !== 'draft') {
            return res.status(400).json({
                success: false,
                message: 'Only draft adjustments can be updated'
            });
        }

        // Update adjustment
        Object.assign(adjustment, updates);
        await adjustment.save();

        // Populate the updated adjustment
        await adjustment.populate('createdBy', 'firstName lastName email');
        await adjustment.populate('items.product', 'name sku barcode category');

        res.json({
            success: true,
            message: 'Stock adjustment updated successfully',
            data: adjustment
        });
    } catch (error) {
        console.error('Error updating stock adjustment:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating stock adjustment',
            error: error.message
        });
    }
};

// Approve stock adjustment
const approveStockAdjustment = async (req, res) => {
    try {
        const { id } = req.params;
        const { approved, comments } = req.body;

        const adjustment = await StockAdjustment.findById(id);

        if (!adjustment) {
            return res.status(404).json({
                success: false,
                message: 'Stock adjustment not found'
            });
        }

        if (adjustment.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: 'Only pending adjustments can be approved or rejected'
            });
        }

        if (approved) {
            await adjustment.approve(req.user.id);

            // Send approval notification
            try {
                const creator = await User.findById(adjustment.createdBy).select('firstName lastName email');
                if (creator) {
                    // await emailService.sendAdjustmentApprovedNotification(adjustment, creator);
                    console.log('Approval email would be sent to:', creator.email);
                }
            } catch (emailError) {
                console.error('Error sending approval email:', emailError);
            }
        } else {
            await adjustment.reject(comments || 'No reason provided');

            // Send rejection notification
            try {
                const creator = await User.findById(adjustment.createdBy).select('firstName lastName email');
                if (creator) {
                    // await emailService.sendAdjustmentRejectedNotification(adjustment, creator);
                    console.log('Rejection email would be sent to:', creator.email);
                }
            } catch (emailError) {
                console.error('Error sending rejection email:', emailError);
            }
        }

        res.json({
            success: true,
            message: `Stock adjustment ${approved ? 'approved' : 'rejected'} successfully`,
            data: adjustment
        });
    } catch (error) {
        console.error('Error approving stock adjustment:', error);
        res.status(500).json({
            success: false,
            message: 'Error processing approval',
            error: error.message
        });
    }
};

// Apply stock adjustment (update inventory)
const applyStockAdjustment = async (req, res) => {
    try {
        const { id } = req.params;

        const adjustment = await StockAdjustment.findById(id)
            .populate('items.product');

        if (!adjustment) {
            return res.status(404).json({
                success: false,
                message: 'Stock adjustment not found'
            });
        }

        if (adjustment.status !== 'approved') {
            return res.status(400).json({
                success: false,
                message: 'Only approved adjustments can be applied'
            });
        }

        // Start transaction to ensure data consistency
        const session = await Product.startSession();
        session.startTransaction();

        try {
            // Update product inventories
            for (const item of adjustment.items) {
                const product = await Product.findById(item.product._id).session(session);

                if (!product) {
                    throw new Error(`Product ${item.productName} not found`);
                }

                // Update inventory quantity
                product.inventory.quantity = item.newQuantity;
                await product.save({ session });
            }

            // Mark adjustment as applied
            await adjustment.apply();

            await session.commitTransaction();

            // Send applied notification
            try {
                const stakeholders = [];

                // Add creator
                const creator = await User.findById(adjustment.createdBy).select('firstName lastName email');
                if (creator) stakeholders.push(creator);

                // Add approver
                if (adjustment.approvedBy) {
                    const approver = await User.findById(adjustment.approvedBy).select('firstName lastName email');
                    if (approver) stakeholders.push(approver);
                }

                // Add managers
                const managers = await User.find({
                    role: { $in: ['admin', 'manager'] },
                    isActive: true
                }).select('firstName lastName email');

                stakeholders.push(...managers);

                // Remove duplicates
                const uniqueStakeholders = stakeholders.filter((stakeholder, index, self) =>
                    index === self.findIndex(s => s.email === stakeholder.email)
                );

                if (uniqueStakeholders.length > 0) {
                    // await emailService.sendAdjustmentAppliedNotification(adjustment, uniqueStakeholders);
                    console.log('Applied email would be sent to:', uniqueStakeholders.map(s => s.email));
                }
            } catch (emailError) {
                console.error('Error sending applied email:', emailError);
            }

            res.json({
                success: true,
                message: 'Stock adjustment applied successfully',
                data: adjustment
            });
        } catch (error) {
            await session.abortTransaction();
            throw error;
        } finally {
            session.endSession();
        }
    } catch (error) {
        console.error('Error applying stock adjustment:', error);
        res.status(500).json({
            success: false,
            message: 'Error applying stock adjustment',
            error: error.message
        });
    }
};

// Delete stock adjustment
const deleteStockAdjustment = async (req, res) => {
    try {
        const { id } = req.params;

        const adjustment = await StockAdjustment.findById(id);

        if (!adjustment) {
            return res.status(404).json({
                success: false,
                message: 'Stock adjustment not found'
            });
        }

        // Only allow deletion if status is draft
        if (adjustment.status !== 'draft') {
            return res.status(400).json({
                success: false,
                message: 'Only draft adjustments can be deleted'
            });
        }

        await StockAdjustment.findByIdAndDelete(id);

        res.json({
            success: true,
            message: 'Stock adjustment deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting stock adjustment:', error);
        res.status(500).json({
            success: false,
            message: 'Error deleting stock adjustment',
            error: error.message
        });
    }
};

// Get adjustment statistics
const getAdjustmentStats = async (req, res) => {
    try {
        const { startDate, endDate } = req.query;

        const matchStage = {};
        if (startDate || endDate) {
            matchStage.adjustmentDate = {};
            if (startDate) matchStage.adjustmentDate.$gte = new Date(startDate);
            if (endDate) matchStage.adjustmentDate.$lte = new Date(endDate);
        }

        const stats = await StockAdjustment.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: null,
                    totalAdjustments: { $sum: 1 },
                    totalCostImpact: { $sum: '$totalCostImpact' },
                    totalItems: { $sum: '$totalItems' },
                    byType: {
                        $push: {
                            type: '$type',
                            count: 1,
                            costImpact: '$totalCostImpact'
                        }
                    },
                    byStatus: {
                        $push: {
                            status: '$status',
                            count: 1
                        }
                    }
                }
            }
        ]);

        res.json({
            success: true,
            data: stats[0] || {
                totalAdjustments: 0,
                totalCostImpact: 0,
                totalItems: 0,
                byType: [],
                byStatus: []
            }
        });
    } catch (error) {
        console.error('Error fetching adjustment stats:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching adjustment statistics',
            error: error.message
        });
    }
};

module.exports = {
    getStockAdjustments,
    getStockAdjustment,
    createStockAdjustment,
    updateStockAdjustment,
    approveStockAdjustment,
    applyStockAdjustment,
    deleteStockAdjustment,
    getAdjustmentStats
};
