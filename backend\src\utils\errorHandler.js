const ErrorResponse = require('./errorResponse');

// Global error handler middleware
const errorHandler = (err, req, res, next) => {
    let error = { ...err };
    error.message = err.message;

    // Log error
    console.error('Error:', {
        message: err.message,
        stack: err.stack,
        url: req.originalUrl,
        method: req.method,
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
    });

    // Mongoose bad ObjectId
    if (err.name === 'CastError') {
        const message = 'Resource not found';
        error = new ErrorResponse(message, 404);
    }

    // Mongoose duplicate key
    if (err.code === 11000) {
        const message = 'Duplicate field value entered';
        error = new ErrorResponse(message, 400);
    }

    // Mongoose validation error
    if (err.name === 'ValidationError') {
        const message = Object.values(err.errors).map(val => val.message).join(', ');
        error = new ErrorResponse(message, 400);
    }

    // JWT errors
    if (err.name === 'JsonWebTokenError') {
        const message = 'Invalid token';
        error = new ErrorResponse(message, 401);
    }

    if (err.name === 'TokenExpiredError') {
        const message = 'Token expired';
        error = new ErrorResponse(message, 401);
    }

    // MongoDB connection errors
    if (err.name === 'MongoNetworkError') {
        const message = 'Database connection error';
        error = new ErrorResponse(message, 500);
    }

    // File upload errors
    if (err.code === 'LIMIT_FILE_SIZE') {
        const message = 'File too large';
        error = new ErrorResponse(message, 400);
    }

    if (err.code === 'LIMIT_UNEXPECTED_FILE') {
        const message = 'Unexpected file field';
        error = new ErrorResponse(message, 400);
    }

    // Rate limiting errors
    if (err.status === 429) {
        const message = 'Too many requests, please try again later';
        error = new ErrorResponse(message, 429);
    }

    // CORS errors
    if (err.message && err.message.includes('CORS')) {
        const message = 'Cross-origin request blocked';
        error = new ErrorResponse(message, 403);
    }

    // Permission errors
    if (err.message && err.message.includes('permission')) {
        const message = 'Insufficient permissions';
        error = new ErrorResponse(message, 403);
    }

    // Database timeout errors
    if (err.name === 'MongoTimeoutError') {
        const message = 'Database operation timed out';
        error = new ErrorResponse(message, 504);
    }

    // Send error response
    res.status(error.statusCode || 500).json({
        success: false,
        error: error.message || 'Server Error',
        ...(process.env.NODE_ENV === 'development' && {
            stack: err.stack,
            details: err
        })
    });
};

// Not found middleware
const notFound = (req, res, next) => {
    const error = new ErrorResponse(`Not found - ${req.originalUrl}`, 404);
    next(error);
};

// Async handler wrapper
const asyncHandler = (fn) => (req, res, next) => {
    Promise.resolve(fn(req, res, next)).catch(next);
};

// Validation error formatter
const formatValidationErrors = (errors) => {
    const formatted = {};
    
    Object.keys(errors).forEach(key => {
        if (errors[key].message) {
            formatted[key] = errors[key].message;
        }
    });
    
    return formatted;
};

// API response helper
const sendResponse = (res, statusCode, success, message, data = null, meta = null) => {
    const response = {
        success,
        message
    };

    if (data !== null) {
        response.data = data;
    }

    if (meta !== null) {
        response.meta = meta;
    }

    res.status(statusCode).json(response);
};

// Success response helper
const sendSuccess = (res, message, data = null, statusCode = 200, meta = null) => {
    sendResponse(res, statusCode, true, message, data, meta);
};

// Error response helper
const sendError = (res, message, statusCode = 500, errors = null) => {
    const response = {
        success: false,
        error: message
    };

    if (errors) {
        response.errors = errors;
    }

    res.status(statusCode).json(response);
};

// Database error handler
const handleDatabaseError = (error) => {
    console.error('Database Error:', {
        name: error.name,
        message: error.message,
        code: error.code,
        timestamp: new Date().toISOString()
    });

    // Return appropriate error response based on error type
    switch (error.name) {
        case 'MongoNetworkError':
            return new ErrorResponse('Database connection failed', 503);
        case 'MongoTimeoutError':
            return new ErrorResponse('Database operation timed out', 504);
        case 'ValidationError':
            return new ErrorResponse('Invalid data provided', 400);
        case 'CastError':
            return new ErrorResponse('Invalid ID format', 400);
        default:
            return new ErrorResponse('Database error occurred', 500);
    }
};

// Authentication error handler
const handleAuthError = (error) => {
    console.error('Authentication Error:', {
        name: error.name,
        message: error.message,
        timestamp: new Date().toISOString()
    });

    switch (error.name) {
        case 'JsonWebTokenError':
            return new ErrorResponse('Invalid authentication token', 401);
        case 'TokenExpiredError':
            return new ErrorResponse('Authentication token expired', 401);
        case 'NotBeforeError':
            return new ErrorResponse('Token not active yet', 401);
        default:
            return new ErrorResponse('Authentication failed', 401);
    }
};

// File upload error handler
const handleFileUploadError = (error) => {
    console.error('File Upload Error:', {
        code: error.code,
        message: error.message,
        field: error.field,
        timestamp: new Date().toISOString()
    });

    switch (error.code) {
        case 'LIMIT_FILE_SIZE':
            return new ErrorResponse('File size too large', 400);
        case 'LIMIT_FILE_COUNT':
            return new ErrorResponse('Too many files uploaded', 400);
        case 'LIMIT_UNEXPECTED_FILE':
            return new ErrorResponse('Unexpected file field', 400);
        case 'LIMIT_PART_COUNT':
            return new ErrorResponse('Too many parts in multipart data', 400);
        case 'LIMIT_FIELD_KEY':
            return new ErrorResponse('Field name too long', 400);
        case 'LIMIT_FIELD_VALUE':
            return new ErrorResponse('Field value too long', 400);
        case 'LIMIT_FIELD_COUNT':
            return new ErrorResponse('Too many fields', 400);
        default:
            return new ErrorResponse('File upload failed', 400);
    }
};

// Request validation error handler
const handleValidationError = (errors) => {
    const formattedErrors = {};
    
    if (Array.isArray(errors)) {
        errors.forEach(error => {
            if (error.param) {
                formattedErrors[error.param] = error.msg;
            }
        });
    } else if (typeof errors === 'object') {
        Object.keys(errors).forEach(key => {
            formattedErrors[key] = errors[key].message || errors[key];
        });
    }

    return {
        message: 'Validation failed',
        errors: formattedErrors
    };
};

// Rate limit error handler
const handleRateLimitError = (req, res) => {
    console.warn('Rate Limit Exceeded:', {
        ip: req.ip,
        url: req.originalUrl,
        method: req.method,
        userAgent: req.get('User-Agent'),
        timestamp: new Date().toISOString()
    });

    return sendError(res, 'Too many requests, please try again later', 429);
};

// Security error logger
const logSecurityEvent = (event, req, details = {}) => {
    console.warn('Security Event:', {
        event,
        ip: req.ip,
        url: req.originalUrl,
        method: req.method,
        userAgent: req.get('User-Agent'),
        user: req.user ? req.user.email : 'anonymous',
        timestamp: new Date().toISOString(),
        ...details
    });
};

module.exports = {
    errorHandler,
    notFound,
    asyncHandler,
    formatValidationErrors,
    sendResponse,
    sendSuccess,
    sendError,
    handleDatabaseError,
    handleAuthError,
    handleFileUploadError,
    handleValidationError,
    handleRateLimitError,
    logSecurityEvent
};
