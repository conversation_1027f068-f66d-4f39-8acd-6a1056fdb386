const mongoose = require('mongoose');
require('dotenv').config();

async function cleanupDatabases() {
    try {
        console.log('🔄 Starting Database Cleanup...\n');

        // Connect to MongoDB
        const mongoURI = 'mongodb://localhost:27017';
        await mongoose.connect(mongoURI);
        console.log('✅ Connected to MongoDB\n');

        // Get admin database to list all databases
        const adminDb = mongoose.connection.db.admin();
        const databases = await adminDb.listDatabases();

        console.log('📊 Available Databases:');
        databases.databases.forEach(db => {
            console.log(`   - ${db.name} (${(db.sizeOnDisk / 1024 / 1024).toFixed(2)} MB)`);
        });
        console.log('');

        // Check each database for collections
        for (const dbInfo of databases.databases) {
            const dbName = dbInfo.name;
            
            // Skip system databases
            if (['admin', 'local', 'config'].includes(dbName)) {
                console.log(`🔒 System Database: ${dbName} (keeping)`);
                continue;
            }

            // Check our main database
            if (dbName === 'bitstech_pos') {
                console.log(`✅ Main Database: ${dbName} (keeping)`);
                
                // Show collections in main database
                const db = mongoose.connection.useDb(dbName);
                const collections = await db.listCollections().toArray();
                console.log(`   Collections (${collections.length}):`);
                
                for (const collection of collections) {
                    const count = await db.collection(collection.name).countDocuments();
                    console.log(`     - ${collection.name}: ${count} documents`);
                }
                continue;
            }

            // Check other databases
            console.log(`🔍 Checking Database: ${dbName}`);
            const db = mongoose.connection.useDb(dbName);
            const collections = await db.listCollections().toArray();
            
            if (collections.length === 0) {
                console.log(`   ❌ Empty database - Safe to delete`);
            } else {
                console.log(`   ⚠️ Has ${collections.length} collections:`);
                for (const collection of collections) {
                    const count = await db.collection(collection.name).countDocuments();
                    console.log(`     - ${collection.name}: ${count} documents`);
                }
            }
        }

        console.log('\n🎯 Cleanup Recommendations:');
        console.log('✅ Keep: bitstech_pos (main POS data)');
        console.log('🔒 Keep: admin, local, config (MongoDB system)');
        console.log('❌ Can Delete: Empty databases or old versions');

        console.log('\n⚠️ Manual Deletion Required:');
        console.log('   Use MongoDB Compass to delete unwanted databases');
        console.log('   Right-click database → "Drop Database"');

        await mongoose.disconnect();
        console.log('\n✅ Database cleanup analysis completed!');
        process.exit(0);

    } catch (error) {
        console.error('❌ Error during cleanup:', error);
        process.exit(1);
    }
}

// Run cleanup analysis
cleanupDatabases();
