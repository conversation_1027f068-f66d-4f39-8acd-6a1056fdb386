const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const Product = require('../models/Product');
const Sale = require('../models/Sale');

const router = express.Router();

// @route   GET /api/inventory
// @desc    Get inventory overview
// @access  Private
router.get('/', protect, async (req, res) => {
    try {
        const products = await Product.find({ isActive: true })
            .populate('category', 'name color')
            .select('name sku inventory.quantity inventory.minStock price category')
            .sort({ 'inventory.quantity': 1 });

        const totalProducts = products.length;
        const lowStockProducts = products.filter(p => p.inventory.quantity <= p.inventory.minStock);
        const outOfStockProducts = products.filter(p => p.inventory.quantity === 0);
        const totalValue = products.reduce((sum, p) => sum + (p.inventory.quantity * p.price), 0);

        res.json({
            success: true,
            data: {
                products,
                summary: {
                    totalProducts,
                    lowStockCount: lowStockProducts.length,
                    outOfStockCount: outOfStockProducts.length,
                    totalValue
                }
            }
        });
    } catch (error) {
        console.error('Error fetching inventory:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching inventory data'
        });
    }
});

// @route   PUT /api/inventory/:id
// @desc    Update product inventory
// @access  Private (Admin, Manager)
router.put('/:id', protect, authorize('admin', 'manager'), async (req, res) => {
    try {
        const { quantity, minStock, reason = 'Manual adjustment' } = req.body;

        const product = await Product.findById(req.params.id);
        if (!product) {
            return res.status(404).json({
                success: false,
                message: 'Product not found'
            });
        }

        const oldQuantity = product.inventory.quantity;

        // Update inventory
        if (quantity !== undefined) {
            product.inventory.quantity = quantity;
        }
        if (minStock !== undefined) {
            product.inventory.minStock = minStock;
        }

        await product.save();

        // Log the inventory movement (you can create a separate InventoryMovement model)
        console.log(`Inventory updated for ${product.name}: ${oldQuantity} -> ${product.inventory.quantity} (${reason})`);

        res.json({
            success: true,
            message: 'Inventory updated successfully',
            data: {
                product: {
                    _id: product._id,
                    name: product.name,
                    sku: product.sku,
                    inventory: product.inventory
                },
                change: {
                    oldQuantity,
                    newQuantity: product.inventory.quantity,
                    difference: product.inventory.quantity - oldQuantity,
                    reason
                }
            }
        });
    } catch (error) {
        console.error('Error updating inventory:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating inventory'
        });
    }
});

// @route   GET /api/inventory/low-stock
// @desc    Get products with low stock
// @access  Private
router.get('/low-stock', protect, async (req, res) => {
    try {
        const products = await Product.find({
            isActive: true,
            $expr: { $lte: ['$inventory.quantity', '$inventory.minStock'] }
        })
        .populate('category', 'name color')
        .select('name sku inventory price category')
        .sort({ 'inventory.quantity': 1 });

        res.json({
            success: true,
            data: products,
            count: products.length
        });
    } catch (error) {
        console.error('Error fetching low stock products:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching low stock products'
        });
    }
});

// @route   GET /api/inventory/movements
// @desc    Get inventory movement history
// @access  Private
router.get('/movements', protect, async (req, res) => {
    try {
        const { startDate, endDate, productId } = req.query;

        // Build query for sales (outgoing movements)
        let salesQuery = {};
        if (startDate && endDate) {
            salesQuery.createdAt = {
                $gte: new Date(startDate),
                $lte: new Date(endDate)
            };
        }
        if (productId) {
            salesQuery['items.product'] = productId;
        }

        const sales = await Sale.find(salesQuery)
            .populate('items.product', 'name sku')
            .populate('cashier', 'firstName lastName')
            .sort({ createdAt: -1 })
            .limit(100);

        // Transform sales data to movement format
        const movements = [];
        sales.forEach(sale => {
            sale.items.forEach(item => {
                if (!productId || item.product._id.toString() === productId) {
                    movements.push({
                        type: 'sale',
                        product: item.product,
                        quantity: -item.quantity, // Negative for outgoing
                        unitPrice: item.unitPrice,
                        totalPrice: item.totalPrice,
                        reference: sale.saleNumber,
                        user: sale.cashier,
                        date: sale.createdAt,
                        notes: `Sale to ${sale.customer.name}`
                    });
                }
            });
        });

        // Sort by date (most recent first)
        movements.sort((a, b) => new Date(b.date) - new Date(a.date));

        res.json({
            success: true,
            data: movements,
            count: movements.length
        });
    } catch (error) {
        console.error('Error fetching inventory movements:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching inventory movements'
        });
    }
});

module.exports = router;
