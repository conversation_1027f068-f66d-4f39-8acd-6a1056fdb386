'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Eye, EyeOff, LogIn, ArrowLeft, Wifi, WifiOff, RefreshCw } from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import apiClient from '@/lib/api'

export default function LoginPage() {
  const { login, isLoading, error, clearError } = useAuth()
  const [showPassword, setShowPassword] = useState(false)
  const [language, setLanguage] = useState<'en' | 'mm'>('en')
  const [formData, setFormData] = useState({
    email: '',
    password: ''
  })
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [backendStatus, setBackendStatus] = useState<'checking' | 'online' | 'offline'>('checking')

  // Check backend status on component mount
  useEffect(() => {
    const checkBackendStatus = async () => {
      try {
        setBackendStatus('checking')

        // Try to make a simple API call to check backend availability
        const response = await apiClient.get('/auth/health')

        if (response.success) {
          if (response.message?.includes('Backend not available')) {
            setBackendStatus('offline')
          } else {
            setBackendStatus('online')
          }
        } else {
          setBackendStatus('offline')
        }
      } catch (error) {
        console.log('Backend health check failed:', error)
        setBackendStatus('offline')
      }
    }

    checkBackendStatus()
  }, [])

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {}

    if (!formData.email) {
      newErrors.email = language === 'en' ? 'Email is required' : 'အီးမေးလ် လိုအပ်ပါသည်'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = language === 'en' ? 'Email is invalid' : 'အီးမေးလ် မှားယွင်းနေပါသည်'
    }

    if (!formData.password) {
      newErrors.password = language === 'en' ? 'Password is required' : 'စကားဝှက် လိုအပ်ပါသည်'
    } else if (formData.password.length < 6) {
      newErrors.password = language === 'en' ? 'Password must be at least 6 characters' : 'စကားဝှက်သည် အနည်းဆုံး ၆ လုံး ရှိရမည်'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    clearError()

    try {
      await login(formData)
      // Navigation is handled by the auth context
    } catch (error) {
      // Error is handled by the auth context
      console.error('Login failed:', error)
    }
  }

  const text = {
    en: {
      title: 'Welcome Back',
      subtitle: 'Sign in to your BitsTech POS account',
      email: 'Email',
      password: 'Password',
      showPassword: 'Show password',
      hidePassword: 'Hide password',
      signIn: 'Sign In',
      signingIn: 'Signing In...',
      noAccount: "Don't have an account?",
      signUp: 'Sign up',
      backToHome: 'Back to Home',
      forgotPassword: 'Forgot your password?'
    },
    mm: {
      title: 'ပြန်လည်ကြိုဆိုပါသည်',
      subtitle: 'သင့် BitsTech POS အကောင့်သို့ ဝင်ရောက်ပါ',
      email: 'အီးမေးလ်',
      password: 'စကားဝှက်',
      showPassword: 'စကားဝှက် ပြရန်',
      hidePassword: 'စကားဝှက် ဖုံးရန်',
      signIn: 'ဝင်ရောက်ရန်',
      signingIn: 'ဝင်ရောက်နေသည်...',
      noAccount: 'အကောင့်မရှိသေးလား?',
      signUp: 'အကောင့်ဖွင့်ရန်',
      backToHome: 'ပင်မစာမျက်နှာသို့',
      forgotPassword: 'စကားဝှက် မေ့နေပါသလား?'
    }
  }

  const t = text[language]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Back to Home */}
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" className="text-gray-600 dark:text-gray-400">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t.backToHome}
            </Button>
          </Link>
        </div>

        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">BT</span>
              </div>
            </div>

            <CardTitle className="text-2xl text-center">{t.title}</CardTitle>
            <CardDescription className="text-center">
              {t.subtitle}
            </CardDescription>

            {/* Language Toggle & Backend Status */}
            <div className="flex justify-between items-center pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLanguage(language === 'en' ? 'mm' : 'en')}
                className="text-xs"
              >
                {language === 'en' ? 'မြန်မာ' : 'English'}
              </Button>

              {/* Backend Status Indicator */}
              <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${
                backendStatus === 'online'
                  ? 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
                  : backendStatus === 'offline'
                  ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
                  : 'bg-blue-100 text-blue-700 dark:bg-blue-900/20 dark:text-blue-400'
              }`}>
                {backendStatus === 'online' ? (
                  <Wifi className="h-3 w-3" />
                ) : backendStatus === 'offline' ? (
                  <WifiOff className="h-3 w-3" />
                ) : (
                  <RefreshCw className="h-3 w-3 animate-spin" />
                )}
                <span>
                  {backendStatus === 'online'
                    ? (language === 'mm' ? 'အွန်လိုင်း' : 'Online')
                    : backendStatus === 'offline'
                    ? (language === 'mm' ? 'အော့ဖ်လိုင်း' : 'Offline')
                    : (language === 'mm' ? 'စစ်ဆေးနေ' : 'Checking')
                  }
                </span>
              </div>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* Backend Status Message */}
              {backendStatus === 'offline' && (
                <div className="p-3 text-sm text-yellow-700 bg-yellow-50 dark:bg-yellow-900/20 dark:text-yellow-300 rounded-md border border-yellow-200 dark:border-yellow-800">
                  <div className="flex items-center space-x-2 mb-2">
                    <WifiOff className="h-4 w-4" />
                    <span className="font-medium">
                      {language === 'mm' ? 'အော့ဖ်လိုင်း မုဒ်' : 'Offline Mode'}
                    </span>
                  </div>
                  <p className="text-xs">
                    {language === 'mm'
                      ? 'Backend server မရရှိပါ။ အော့ဖ်လိုင်း authentication သုံးပြီး ဝင်ရောက်နိုင်ပါသည်။'
                      : 'Backend server not available. You can still login using offline authentication.'
                    }
                  </p>
                </div>
              )}

              {(errors.general || error) && (
                <div className="p-3 text-sm text-red-600 bg-red-50 dark:bg-red-900/20 rounded-md">
                  {errors.general || error}
                </div>
              )}

              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  {t.email}
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  {t.password}
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange}
                    className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                    aria-label={showPassword ? t.hidePassword : t.showPassword}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-500" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-500" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password}</p>
                )}
              </div>

              <div className="flex items-center justify-between">
                <Link
                  href="/auth/forgot-password"
                  className="text-sm text-blue-600 hover:text-blue-500"
                >
                  {t.forgotPassword}
                </Link>
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    {t.signingIn}
                  </>
                ) : (
                  <>
                    <LogIn className="mr-2 h-4 w-4" />
                    {t.signIn}
                  </>
                )}
              </Button>
            </form>

            {/* System Information */}
            {backendStatus === 'offline' && (
              <div className="mt-4 p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-md border border-yellow-200 dark:border-yellow-800">
                <p className="text-xs font-medium text-yellow-800 dark:text-yellow-200 mb-1">
                  {language === 'mm' ? 'အော့ဖ်လိုင်း မုဒ်' : 'Offline Mode'}
                </p>
                <p className="text-xs text-yellow-700 dark:text-yellow-300">
                  {language === 'mm'
                    ? 'အင်တာနက် မရှိသော်လည်း စနစ်ကို အသုံးပြုနိုင်ပါသည်။ အွန်လိုင်းပြန်ရောက်သောအခါ အချက်အလက်များ အလိုအလျောက် sync ဖြစ်ပါမည်။'
                    : 'System works offline. Data will automatically sync when connection is restored.'
                  }
                </p>
              </div>
            )}

            <div className="mt-6 text-center text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                {t.noAccount}{' '}
              </span>
              <Link
                href="/auth/register"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                {t.signUp}
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
