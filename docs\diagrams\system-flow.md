# System Flow Diagram

## Main System Flow

```mermaid
flowchart TD
    A[Login Page] --> B{Authentication}
    B -->|Success| C[Dashboard]
    B -->|Fail| A
    
    C --> D[POS Terminal]
    C --> E[Products Management]
    C --> F[Sales History]
    C --> G[Reports]
    C --> H[Inventory]
    C --> I[User Management]
    C --> J[Settings]
    
    D --> D1[Product Selection]
    D1 --> D2[Cart Management]
    D2 --> D3[Payment Processing]
    D3 --> D4[Receipt Generation]
    
    E --> E1[Add Product]
    E --> E2[Edit Product]
    E --> E3[Delete Product]
    E --> E4[Product Categories]
    
    F --> F1[Daily Sales]
    F --> F2[Transaction Details]
    F --> F3[Returns/Refunds]
    
    G --> G1[Sales Reports]
    G --> G2[Inventory Reports]
    G --> G3[Financial Reports]
    G --> G4[Staff Performance]
    
    H --> H1[Stock Levels]
    H --> H2[Low Stock Alerts]
    H --> H3[Purchase Orders]
    
    I --> I1[Add User]
    I --> I2[User Roles]
    I --> I3[Permissions]
    
    J --> J1[Company Settings]
    J --> J2[Tax Configuration]
    J --> J3[Currency Settings]
    J --> J4[Theme Settings]
    J --> J5[Language Settings]
```

## User Authentication Flow

```mermaid
flowchart LR
    A[User Login] --> B{Valid Credentials?}
    B -->|No| C[Show Error Message]
    C --> A
    B -->|Yes| D[Generate JWT Token]
    D --> E[Store Token]
    E --> F{Check User Role}
    
    F -->|Admin| G[Full Access Dashboard]
    F -->|Manager| H[Manager Dashboard]
    F -->|Cashier| I[POS Terminal Only]
    
    G --> J[All Features Available]
    H --> K[Limited Admin Features]
    I --> L[Sales Operations Only]
    
    J --> M[Session Management]
    K --> M
    L --> M
    
    M --> N{Token Valid?}
    N -->|Yes| O[Continue Session]
    N -->|No| P[Redirect to Login]
    P --> A
```

## POS Terminal Workflow

```mermaid
sequenceDiagram
    participant U as User/Cashier
    participant P as POS Terminal
    participant I as Inventory
    participant Pay as Payment
    participant R as Receipt
    
    U->>P: Start New Sale
    U->>P: Scan/Select Product
    P->>I: Check Stock Availability
    I-->>P: Stock Status
    P->>P: Add to Cart
    U->>P: Apply Discount (Optional)
    U->>P: Select Payment Method
    P->>Pay: Process Payment
    Pay-->>P: Payment Confirmation
    P->>I: Update Stock Levels
    P->>R: Generate Receipt
    R-->>U: Print/Email Receipt
    P->>P: Complete Transaction
```

## Product Management Flow

```mermaid
flowchart TD
    A[Products Page] --> B[View Products List]
    B --> C{Action Selection}
    
    C -->|Add New| D[Product Form]
    C -->|Edit| E[Edit Product Form]
    C -->|Delete| F[Confirm Deletion]
    C -->|View Details| G[Product Details]
    
    D --> D1[Enter Product Info]
    D1 --> D2[Upload Images]
    D2 --> D3[Set Categories]
    D3 --> D4[Configure Pricing]
    D4 --> D5[Save Product]
    D5 --> H[Update Inventory]
    
    E --> E1[Load Existing Data]
    E1 --> E2[Modify Information]
    E2 --> E3[Save Changes]
    E3 --> H
    
    F --> F1[Confirm Action]
    F1 --> F2[Remove from Database]
    F2 --> H
    
    G --> G1[Display Product Info]
    G1 --> G2[Show Stock Levels]
    G2 --> G3[View Sales History]
    
    H --> I[Refresh Products List]
    I --> B
```

## Sales Transaction Flow

```mermaid
stateDiagram-v2
    [*] --> Idle
    Idle --> ProductSelection : Start Sale
    ProductSelection --> CartManagement : Add Products
    CartManagement --> ProductSelection : Add More Items
    CartManagement --> PaymentProcessing : Proceed to Payment
    PaymentProcessing --> PaymentFailed : Payment Error
    PaymentFailed --> PaymentProcessing : Retry Payment
    PaymentProcessing --> ReceiptGeneration : Payment Success
    ReceiptGeneration --> TransactionComplete : Receipt Printed
    TransactionComplete --> Idle : New Sale
    
    CartManagement --> Idle : Cancel Sale
    PaymentFailed --> Idle : Cancel Transaction
```

## Inventory Management Flow

```mermaid
flowchart TD
    A[Inventory Dashboard] --> B[Stock Overview]
    B --> C{Stock Status}
    
    C -->|Low Stock| D[Low Stock Alerts]
    C -->|Normal| E[Regular Operations]
    C -->|Overstock| F[Overstock Management]
    
    D --> D1[Generate Purchase Order]
    D1 --> D2[Contact Suppliers]
    D2 --> D3[Receive Stock]
    D3 --> G[Update Inventory]
    
    E --> E1[Daily Stock Check]
    E1 --> E2[Process Sales Updates]
    E2 --> G
    
    F --> F1[Identify Slow Movers]
    F1 --> F2[Create Promotions]
    F2 --> F3[Adjust Pricing]
    F3 --> G
    
    G --> H[Generate Reports]
    H --> I[Stock Valuation]
    I --> J[Inventory Analytics]
    J --> A
```

## User Role Permissions

### Admin Role Flow
- Full system access
- User management capabilities
- System configuration rights
- Financial data access
- Backup and restore functions

### Manager Role Flow
- POS operations access
- Product management rights
- Sales reports viewing
- Staff performance monitoring
- Limited system settings

### Cashier Role Flow
- POS terminal access only
- Basic product lookup
- Customer service functions
- Daily sales summary
- No administrative access

## Multi-language Support Flow

```mermaid
flowchart LR
    A[User Login] --> B[Detect Language Preference]
    B --> C{Language Setting}
    
    C -->|Myanmar| D[Load MM Translations]
    C -->|English| E[Load EN Translations]
    C -->|Auto| F[Detect Browser Language]
    
    D --> G[Apply Myanmar UI]
    E --> H[Apply English UI]
    F --> I{Browser Language}
    
    I -->|MM| D
    I -->|EN| E
    I -->|Other| E
    
    G --> J[Format Numbers/Dates MM]
    H --> K[Format Numbers/Dates EN]
    
    J --> L[Display Localized Interface]
    K --> L
    
    L --> M[Language Switch Available]
    M --> N[User Changes Language]
    N --> O[Reload Interface]
    O --> C
```
