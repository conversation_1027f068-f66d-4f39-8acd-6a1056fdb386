'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Download,
  FileText,
  Table,
  X,
  Calendar,
  Filter,
  CheckCircle,
  Clock
} from 'lucide-react'

interface ExportModalProps {
  isOpen: boolean
  onClose: () => void
  reportType: string
  reportTitle: string
  language: 'en' | 'mm'
}

export function ExportModal({ isOpen, onClose, reportType, reportTitle, language }: ExportModalProps) {
  const [exportFormat, setExportFormat] = useState('pdf')
  const [dateRange, setDateRange] = useState('month')
  const [customStartDate, setCustomStartDate] = useState('')
  const [customEndDate, setCustomEndDate] = useState('')
  const [includeCharts, setIncludeCharts] = useState(true)
  const [includeDetails, setIncludeDetails] = useState(true)
  const [isExporting, setIsExporting] = useState(false)
  const [exportComplete, setExportComplete] = useState(false)

  if (!isOpen) return null

  const handleExport = async () => {
    setIsExporting(true)

    try {
      // Simulate export process
      await new Promise(resolve => setTimeout(resolve, 3000))

      // Create mock download
      const exportData = {
        reportType,
        format: exportFormat,
        dateRange,
        customStartDate,
        customEndDate,
        includeCharts,
        includeDetails,
        timestamp: new Date().toISOString()
      }

      if (exportFormat === 'pdf') {
        // Mock PDF generation
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/pdf' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${reportType}-report-${Date.now()}.pdf`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      } else if (exportFormat === 'excel') {
        // Mock Excel generation
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/vnd.ms-excel' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${reportType}-report-${Date.now()}.xlsx`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      } else {
        // Mock CSV generation
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'text/csv' })
        const url = URL.createObjectURL(blob)
        const a = document.createElement('a')
        a.href = url
        a.download = `${reportType}-report-${Date.now()}.csv`
        document.body.appendChild(a)
        a.click()
        document.body.removeChild(a)
        URL.revokeObjectURL(url)
      }

      setExportComplete(true)
      setTimeout(() => {
        setExportComplete(false)
        onClose()
      }, 2000)
    } catch (error) {
      console.error('Export failed:', error)
    } finally {
      setIsExporting(false)
    }
  }

  const getFormatIcon = (format: string) => {
    switch (format) {
      case 'pdf': return <FileText className="h-5 w-5 text-red-600" />
      case 'excel': return <Table className="h-5 w-5 text-green-600" />
      case 'csv': return <Table className="h-5 w-5 text-blue-600" />
      default: return <FileText className="h-5 w-5 text-gray-600" />
    }
  }

  const getFormatDescription = (format: string) => {
    switch (format) {
      case 'pdf':
        return language === 'mm'
          ? 'ပုံများ နှင့် ဇယားများ ပါဝင်သော အစီရင်ခံစာ'
          : 'Report with charts and formatted tables'
      case 'excel':
        return language === 'mm'
          ? 'ဒေတာ ခွဲခြမ်းစိတ်ဖြာမှု အတွက် Excel ဖိုင်'
          : 'Excel file for data analysis'
      case 'csv':
        return language === 'mm'
          ? 'ရိုးရှင်းသော ဒေတာ ထုတ်ယူမှု'
          : 'Simple data export format'
      default:
        return ''
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Download className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အစီရင်ခံစာ ထုတ်ယူရန်' : 'Export Report'}
              </CardTitle>
              <CardDescription>
                {reportTitle} - {language === 'mm' ? 'ဖော်မတ် နှင့် ရွေးချယ်မှုများ သတ်မှတ်ပါ' : 'Configure format and options'}
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Export Format Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              {language === 'mm' ? 'ထုတ်ယူမှု ဖော်မတ်' : 'Export Format'}
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {['pdf', 'excel', 'csv'].map((format) => (
                <div
                  key={format}
                  className={`p-3 border rounded-lg cursor-pointer transition-all ${
                    exportFormat === format
                      ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                  onClick={() => setExportFormat(format)}
                >
                  <div className="flex items-center gap-2 mb-2">
                    {getFormatIcon(format)}
                    <span className="font-medium capitalize">{format}</span>
                    {exportFormat === format && (
                      <CheckCircle className="h-4 w-4 text-blue-600 ml-auto" />
                    )}
                  </div>
                  <p className="text-xs text-gray-600">
                    {getFormatDescription(format)}
                  </p>
                </div>
              ))}
            </div>
          </div>

          {/* Date Range Selection */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              {language === 'mm' ? 'ရက်စွဲ အပိုင်းအခြား' : 'Date Range'}
            </Label>
            <Select value={dateRange} onValueChange={setDateRange}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="today">{language === 'mm' ? 'ယနေ့' : 'Today'}</SelectItem>
                <SelectItem value="week">{language === 'mm' ? 'ဒီအပတ်' : 'This Week'}</SelectItem>
                <SelectItem value="month">{language === 'mm' ? 'ဒီလ' : 'This Month'}</SelectItem>
                <SelectItem value="quarter">{language === 'mm' ? 'ဒီသုံးလ' : 'This Quarter'}</SelectItem>
                <SelectItem value="year">{language === 'mm' ? 'ဒီနှစ်' : 'This Year'}</SelectItem>
                <SelectItem value="custom">{language === 'mm' ? 'စိတ်ကြိုက်' : 'Custom Range'}</SelectItem>
              </SelectContent>
            </Select>

            {dateRange === 'custom' && (
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-xs text-gray-600">
                    {language === 'mm' ? 'စတင် ရက်စွဲ' : 'Start Date'}
                  </Label>
                  <Input
                    type="date"
                    value={customStartDate}
                    onChange={(e) => setCustomStartDate(e.target.value)}
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-600">
                    {language === 'mm' ? 'ပြီးဆုံး ရက်စွဲ' : 'End Date'}
                  </Label>
                  <Input
                    type="date"
                    value={customEndDate}
                    onChange={(e) => setCustomEndDate(e.target.value)}
                  />
                </div>
              </div>
            )}
          </div>

          {/* Export Options */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              {language === 'mm' ? 'ထုတ်ယူမှု ရွေးချယ်မှုများ' : 'Export Options'}
            </Label>
            <div className="space-y-3">
              <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-sm">
                    {language === 'mm' ? 'ပုံများ နှင့် ဇယားများ ပါဝင်ရန်' : 'Include Charts & Graphs'}
                  </h4>
                  <p className="text-xs text-gray-600">
                    {language === 'mm'
                      ? 'အမြင်အာရုံ ဒေတာ ပုံများ ပါဝင်မည်'
                      : 'Visual data representations will be included'
                    }
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={includeCharts}
                  onChange={(e) => setIncludeCharts(e.target.checked)}
                  className="rounded"
                  aria-label={language === 'mm' ? 'ပုံများ နှင့် ဇယားများ ပါဝင်ရန်' : 'Include Charts & Graphs'}
                />
              </div>

              <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div>
                  <h4 className="font-medium text-sm">
                    {language === 'mm' ? 'အသေးစိတ် ဒေတာ ပါဝင်ရန်' : 'Include Detailed Data'}
                  </h4>
                  <p className="text-xs text-gray-600">
                    {language === 'mm'
                      ? 'အသေးစိတ် စာရင်းများ နှင့် ဒေတာ ပါဝင်မည်'
                      : 'Detailed lists and raw data will be included'
                    }
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={includeDetails}
                  onChange={(e) => setIncludeDetails(e.target.checked)}
                  className="rounded"
                  aria-label={language === 'mm' ? 'အသေးစိတ် ဒေတာ ပါဝင်ရန်' : 'Include Detailed Data'}
                />
              </div>
            </div>
          </div>

          {/* Export Preview */}
          <div className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
            <h4 className="font-medium text-sm mb-2">
              {language === 'mm' ? 'ထုတ်ယူမှု အကြမ်းဖျင်း' : 'Export Preview'}
            </h4>
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span>{reportTitle}</span>
                <Badge variant="outline" className="text-xs">
                  {exportFormat.toUpperCase()}
                </Badge>
              </div>
              <div className="flex items-center gap-2">
                <Calendar className="h-4 w-4" />
                <span>
                  {dateRange === 'custom'
                    ? `${customStartDate} - ${customEndDate}`
                    : dateRange
                  }
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Filter className="h-4 w-4" />
                <span>
                  {language === 'mm' ? 'ရွေးချယ်မှုများ' : 'Options'}:
                  {includeCharts && ' Charts'}
                  {includeDetails && ' Details'}
                </span>
              </div>
            </div>
          </div>

          {/* Export Status */}
          {isExporting && (
            <div className="flex items-center gap-3 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <div className="animate-spin rounded-full h-5 w-5 border-2 border-blue-600 border-t-transparent"></div>
              <span className="text-blue-600 font-medium">
                {language === 'mm' ? 'ထုတ်ယူနေသည်...' : 'Exporting...'}
              </span>
            </div>
          )}

          {exportComplete && (
            <div className="flex items-center gap-3 p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
              <CheckCircle className="h-5 w-5 text-green-600" />
              <span className="text-green-600 font-medium">
                {language === 'mm' ? 'ထုတ်ယူမှု အောင်မြင်ပါသည်!' : 'Export completed successfully!'}
              </span>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <Button variant="outline" onClick={onClose} disabled={isExporting}>
              {language === 'mm' ? 'မလုပ်တော့' : 'Cancel'}
            </Button>
            <Button
              onClick={handleExport}
              disabled={isExporting || exportComplete}
              className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
            >
              {isExporting ? (
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  {language === 'mm' ? 'ထုတ်ယူနေသည်...' : 'Exporting...'}
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Download className="h-4 w-4" />
                  {language === 'mm' ? 'ထုတ်ယူရန်' : 'Export Report'}
                </div>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
