# Folder Structure Diagram

## Project Structure Overview

```mermaid
graph TD
    A[BitesTech POS] --> B[frontend/]
    A --> C[backend/]
    A --> D[shared/]
    A --> E[docs/]
    
    B --> B1[src/]
    B --> B2[public/]
    B --> B3[package.json]
    
    B1 --> B11[components/]
    B1 --> B12[pages/]
    B1 --> B13[hooks/]
    B1 --> B14[utils/]
    B1 --> B15[styles/]
    B1 --> B16[locales/]
    B1 --> B17[store/]
    
    B11 --> B111[ui/]
    B11 --> B112[pos/]
    B11 --> B113[dashboard/]
    B11 --> B114[layout/]
    
    B12 --> B121[dashboard/]
    B12 --> B122[pos/]
    B12 --> B123[products/]
    B12 --> B124[sales/]
    B12 --> B125[reports/]
    B12 --> B126[inventory/]
    B12 --> B127[users/]
    B12 --> B128[settings/]
    
    C --> C1[src/]
    C --> C2[package.json]
    
    C1 --> C11[controllers/]
    C1 --> C12[models/]
    C1 --> C13[routes/]
    C1 --> C14[middleware/]
    C1 --> C15[utils/]
    C1 --> C16[config/]
    
    D --> D1[types/]
    D --> D2[constants/]
    D --> D3[interfaces/]
```

## Frontend Component Structure

```mermaid
graph TD
    A[Frontend Components] --> B[UI Components]
    A --> C[Feature Components]
    A --> D[Layout Components]
    A --> E[Form Components]
    
    B --> B1[Button]
    B --> B2[Input]
    B --> B3[Modal]
    B --> B4[Card]
    B --> B5[Table]
    B --> B6[Loading]
    B --> B7[Alert]
    
    C --> C1[POS Components]
    C --> C2[Dashboard Components]
    C --> C3[Product Components]
    C --> C4[Report Components]
    
    C1 --> C11[ProductGrid]
    C1 --> C12[Cart]
    C1 --> C13[PaymentModal]
    C1 --> C14[Receipt]
    C1 --> C15[BarcodeScanner]
    
    C2 --> C21[StatsCard]
    C2 --> C22[SalesChart]
    C2 --> C23[RecentSales]
    C2 --> C24[QuickActions]
    
    D --> D1[Header]
    D --> D2[Sidebar]
    D --> D3[Footer]
    D --> D4[Layout]
    D --> D5[Navigation]
    
    E --> E1[ProductForm]
    E --> E2[UserForm]
    E --> E3[SettingsForm]
    E --> E4[LoginForm]
```

## Backend API Structure

```mermaid
graph TD
    A[Backend API] --> B[Controllers]
    A --> C[Models]
    A --> D[Routes]
    A --> E[Middleware]
    A --> F[Utils]
    
    B --> B1[authController]
    B --> B2[posController]
    B --> B3[productController]
    B --> B4[saleController]
    B --> B5[reportController]
    B --> B6[userController]
    B --> B7[settingController]
    
    C --> C1[User Model]
    C --> C2[Product Model]
    C --> C3[Sale Model]
    C --> C4[Category Model]
    C --> C5[Inventory Model]
    C --> C6[Setting Model]
    
    D --> D1[/api/auth]
    D --> D2[/api/pos]
    D --> D3[/api/products]
    D --> D4[/api/sales]
    D --> D5[/api/reports]
    D --> D6[/api/users]
    D --> D7[/api/settings]
    
    E --> E1[Authentication]
    E --> E2[Validation]
    E --> E3[Error Handler]
    E --> E4[Logger]
    E --> E5[CORS]
    
    F --> F1[Database Utils]
    F --> F2[Encryption]
    F --> F3[Email Service]
    F --> F4[File Upload]
```

## Database Schema Structure

```mermaid
erDiagram
    USER {
        string id PK
        string username
        string email
        string password_hash
        string role
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    PRODUCT {
        string id PK
        string name
        string description
        string barcode
        decimal price
        decimal cost
        string category_id FK
        integer stock_quantity
        string image_url
        boolean is_active
        datetime created_at
        datetime updated_at
    }
    
    CATEGORY {
        string id PK
        string name
        string description
        string parent_id FK
        boolean is_active
        datetime created_at
    }
    
    SALE {
        string id PK
        string user_id FK
        decimal total_amount
        decimal tax_amount
        decimal discount_amount
        string payment_method
        string status
        datetime sale_date
        datetime created_at
    }
    
    SALE_ITEM {
        string id PK
        string sale_id FK
        string product_id FK
        integer quantity
        decimal unit_price
        decimal total_price
        datetime created_at
    }
    
    INVENTORY {
        string id PK
        string product_id FK
        integer quantity_in_stock
        integer minimum_stock_level
        datetime last_updated
    }
    
    SETTING {
        string id PK
        string key
        string value
        string category
        datetime updated_at
    }
    
    USER ||--o{ SALE : creates
    PRODUCT ||--o{ SALE_ITEM : contains
    SALE ||--o{ SALE_ITEM : includes
    CATEGORY ||--o{ PRODUCT : categorizes
    PRODUCT ||--|| INVENTORY : tracks
    CATEGORY ||--o{ CATEGORY : parent_child
```

## File Organization Principles

### Frontend Organization
- **Components**: Reusable UI components organized by functionality
- **Pages**: Next.js file-based routing with nested layouts
- **Hooks**: Custom React hooks for shared logic
- **Utils**: Helper functions and utilities
- **Store**: State management with clear separation
- **Styles**: CSS modules and global styles
- **Locales**: Translation files organized by language

### Backend Organization
- **Controllers**: Business logic separated by domain
- **Models**: Database schemas with validation
- **Routes**: API endpoints with clear naming
- **Middleware**: Reusable middleware functions
- **Utils**: Helper functions and services
- **Config**: Environment-specific configurations

### Shared Code Organization
- **Types**: TypeScript definitions shared between frontend/backend
- **Constants**: Application-wide constants
- **Interfaces**: Common interface definitions
- **Validators**: Shared validation schemas

## Naming Conventions

### File Naming
- **Components**: PascalCase (e.g., `ProductCard.tsx`)
- **Pages**: kebab-case (e.g., `product-details.tsx`)
- **Utilities**: camelCase (e.g., `formatCurrency.ts`)
- **Constants**: UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)

### Directory Naming
- **Folders**: kebab-case (e.g., `user-management/`)
- **Feature folders**: Descriptive names (e.g., `pos-terminal/`)
- **Utility folders**: Functional names (e.g., `utils/`, `helpers/`)

### Code Organization
- **Index files**: Re-export components for clean imports
- **Barrel exports**: Centralized exports from directories
- **Feature grouping**: Related files grouped together
- **Separation of concerns**: Clear responsibility boundaries

This structure ensures:
- ✅ Scalable architecture
- ✅ Easy navigation
- ✅ Clear separation of concerns
- ✅ Maintainable codebase
- ✅ Team collaboration efficiency
