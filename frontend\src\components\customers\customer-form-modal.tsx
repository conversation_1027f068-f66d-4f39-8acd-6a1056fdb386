'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  X,
  Save,
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Gift,
  AlertCircle,
  Users
} from 'lucide-react'

interface CustomerFormData {
  name: string
  email: string
  phone: string
  address: string
  dateOfBirth: string
  gender: 'male' | 'female' | 'other' | ''
  customerType: 'regular' | 'vip' | 'wholesale' | 'new' | ''
  loyaltyPoints: string
  notes: string
  preferences: {
    language: 'en' | 'mm'
    currency: 'MMK' | 'USD'
    notifications: boolean
  }
}

interface CustomerFormModalProps {
  isOpen: boolean
  onClose: () => void
  onSave: (customerData: CustomerFormData) => void
  language: 'en' | 'mm'
  editCustomer?: any
  isEditing?: boolean
}

export function CustomerFormModal({
  isOpen,
  onClose,
  onSave,
  language,
  editCustomer,
  isEditing = false
}: CustomerFormModalProps) {
  const [formData, setFormData] = useState<CustomerFormData>({
    name: editCustomer?.name || '',
    email: editCustomer?.email || '',
    phone: editCustomer?.phone || '',
    address: editCustomer?.address || '',
    dateOfBirth: editCustomer?.dateOfBirth ? editCustomer.dateOfBirth.split('T')[0] : '',
    gender: editCustomer?.gender || '',
    customerType: editCustomer?.customerType || 'regular',
    loyaltyPoints: editCustomer?.loyaltyPoints?.toString() || '0',
    notes: editCustomer?.notes || '',
    preferences: {
      language: editCustomer?.preferences?.language || 'mm',
      currency: editCustomer?.preferences?.currency || 'MMK',
      notifications: editCustomer?.preferences?.notifications || true
    }
  })

  const [errors, setErrors] = useState<{ [key: string]: string }>({})
  const [saving, setSaving] = useState(false)

  if (!isOpen) return null

  const updateFormData = (field: keyof CustomerFormData, value: string | boolean | object) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const updatePreferences = (field: keyof CustomerFormData['preferences'], value: string | boolean) => {
    setFormData(prev => ({
      ...prev,
      preferences: {
        ...prev.preferences,
        [field]: value
      }
    }))
  }

  const validateForm = () => {
    const newErrors: { [key: string]: string } = {}

    if (!formData.name.trim()) {
      newErrors.name = language === 'mm' ? 'အမည် လိုအပ်သည်' : 'Name is required'
    }

    if (!formData.email.trim()) {
      newErrors.email = language === 'mm' ? 'အီးမေးလ် လိုအပ်သည်' : 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = language === 'mm' ? 'မှန်ကန်သော အီးမေးလ် ဖော်မတ် မဟုတ်ပါ' : 'Invalid email format'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = language === 'mm' ? 'ဖုန်းနံပါတ် လိုအပ်သည်' : 'Phone number is required'
    }

    if (formData.loyaltyPoints && isNaN(Number(formData.loyaltyPoints))) {
      newErrors.loyaltyPoints = language === 'mm' ? 'အမှတ် ကိန်းဂဏန်း ဖြစ်ရမည်' : 'Points must be a number'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!validateForm()) return

    try {
      setSaving(true)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call

      onSave(formData)
      onClose()
    } catch (error) {
      console.error('Error saving customer:', error)
    } finally {
      setSaving(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-pink-600" />
                {isEditing
                  ? (language === 'mm' ? 'ဖောက်သည် ပြင်ဆင်ရန်' : 'Edit Customer')
                  : (language === 'mm' ? 'ဖောက်သည် အသစ် ထည့်ရန်' : 'Add New Customer')
                }
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ဖောက်သည် အချက်အလက်များ နှင့် ရွေးချယ်မှုများ ဖြည့်စွက်ပါ'
                  : 'Fill in customer information and preferences'
                }
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အခြေခံ အချက်အလက်များ' : 'Basic Information'}
              </h3>

              <div>
                <Label htmlFor="name">
                  {language === 'mm' ? 'အမည်' : 'Full Name'} *
                </Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => updateFormData('name', e.target.value)}
                  placeholder={language === 'mm' ? 'ဥပမာ: မောင်မောင်' : 'e.g. John Smith'}
                  className={errors.name ? 'border-red-500' : ''}
                />
                {errors.name && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.name}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="email">
                  {language === 'mm' ? 'အီးမေးလ်' : 'Email Address'} *
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData('email', e.target.value)}
                  placeholder={language === 'mm' ? 'ဥပမာ: <EMAIL>' : 'e.g. <EMAIL>'}
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.email}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="phone">
                  {language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone Number'} *
                </Label>
                <Input
                  id="phone"
                  value={formData.phone}
                  onChange={(e) => updateFormData('phone', e.target.value)}
                  placeholder={language === 'mm' ? 'ဥပမာ: 09-123-456-789' : 'e.g. +95-9-123-456-789'}
                  className={errors.phone ? 'border-red-500' : ''}
                />
                {errors.phone && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.phone}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="dateOfBirth">
                  {language === 'mm' ? 'မွေးနေ့' : 'Date of Birth'}
                </Label>
                <Input
                  id="dateOfBirth"
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => updateFormData('dateOfBirth', e.target.value)}
                />
              </div>

              <div>
                <Label htmlFor="gender">
                  {language === 'mm' ? 'လိင်' : 'Gender'}
                </Label>
                <Select value={formData.gender} onValueChange={(value) => updateFormData('gender', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === 'mm' ? 'လိင် ရွေးချယ်ပါ' : 'Select gender'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="male">{language === 'mm' ? 'ယောက်ျား' : 'Male'}</SelectItem>
                    <SelectItem value="female">{language === 'mm' ? 'မ' : 'Female'}</SelectItem>
                    <SelectItem value="other">{language === 'mm' ? 'အခြား' : 'Other'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Gift className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'ဖောက်သည် အမျိုးအစား' : 'Customer Type & Loyalty'}
              </h3>

              <div>
                <Label htmlFor="customerType">
                  {language === 'mm' ? 'ဖောက်သည် အမျိုးအစား' : 'Customer Type'}
                </Label>
                <Select value={formData.customerType} onValueChange={(value) => updateFormData('customerType', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="regular">{language === 'mm' ? 'ပုံမှန်' : 'Regular'}</SelectItem>
                    <SelectItem value="vip">{language === 'mm' ? 'VIP' : 'VIP'}</SelectItem>
                    <SelectItem value="wholesale">{language === 'mm' ? 'လက်ကားရောင်း' : 'Wholesale'}</SelectItem>
                    <SelectItem value="new">{language === 'mm' ? 'အသစ်' : 'New'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="loyaltyPoints">
                  {language === 'mm' ? 'သစ္စာရှိမှု အမှတ်များ' : 'Loyalty Points'}
                </Label>
                <Input
                  id="loyaltyPoints"
                  type="number"
                  value={formData.loyaltyPoints}
                  onChange={(e) => updateFormData('loyaltyPoints', e.target.value)}
                  placeholder="0"
                  className={errors.loyaltyPoints ? 'border-red-500' : ''}
                />
                {errors.loyaltyPoints && (
                  <p className="text-sm text-red-600 mt-1 flex items-center gap-1">
                    <AlertCircle className="h-3 w-3" />
                    {errors.loyaltyPoints}
                  </p>
                )}
              </div>

              <div>
                <Label htmlFor="address">
                  {language === 'mm' ? 'လိပ်စာ' : 'Address'}
                </Label>
                <Textarea
                  id="address"
                  value={formData.address}
                  onChange={(e) => updateFormData('address', e.target.value)}
                  placeholder={language === 'mm' ? 'ဥပမာ: အမှတ် ၁၂၃၊ ပြည်လမ်း၊ ကမာရွတ်မြို့နယ်၊ ရန်ကုန်မြို့' : 'e.g. 123 Main Street, Downtown, Yangon'}
                  rows={3}
                />
              </div>

              <div>
                <Label htmlFor="notes">
                  {language === 'mm' ? 'မှတ်ချက်များ' : 'Notes'}
                </Label>
                <Textarea
                  id="notes"
                  value={formData.notes}
                  onChange={(e) => updateFormData('notes', e.target.value)}
                  placeholder={language === 'mm' ? 'မှတ်ချက်များ ရိုက်ထည့်ပါ' : 'Enter any additional notes'}
                  rows={2}
                />
              </div>
            </div>
          </div>

          {/* Preferences */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <MapPin className="h-5 w-5 text-green-600" />
              {language === 'mm' ? 'ရွေးချယ်မှုများ' : 'Preferences'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <Label htmlFor="language">
                  {language === 'mm' ? 'ဘာသာစကား' : 'Language'}
                </Label>
                <Select
                  value={formData.preferences.language}
                  onValueChange={(value: 'en' | 'mm') => updatePreferences('language', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="mm">{language === 'mm' ? 'မြန်မာ' : 'Myanmar'}</SelectItem>
                    <SelectItem value="en">{language === 'mm' ? 'အင်္ဂလိပ်' : 'English'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label htmlFor="currency">
                  {language === 'mm' ? 'ငွေကြေး' : 'Currency'}
                </Label>
                <Select
                  value={formData.preferences.currency}
                  onValueChange={(value: 'MMK' | 'USD') => updatePreferences('currency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="MMK">MMK (Myanmar Kyat)</SelectItem>
                    <SelectItem value="USD">USD (US Dollar)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                <div>
                  <Label className="font-medium">
                    {language === 'mm' ? 'အကြောင်းကြားမှုများ' : 'Notifications'}
                  </Label>
                  <p className="text-sm text-gray-600">
                    {language === 'mm'
                      ? 'အီးမေးလ် နှင့် SMS သတိပေးချက်များ'
                      : 'Email and SMS alerts'
                    }
                  </p>
                </div>
                <input
                  type="checkbox"
                  checked={formData.preferences.notifications}
                  onChange={(e) => updatePreferences('notifications', e.target.checked)}
                  className="rounded"
                  aria-label={language === 'mm' ? 'အကြောင်းကြားမှုများ' : 'Notifications'}
                />
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <Button variant="outline" onClick={onClose}>
              {language === 'mm' ? 'မလုပ်တော့' : 'Cancel'}
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-gradient-to-r from-pink-600 to-rose-600 hover:from-pink-700 hover:to-rose-700"
            >
              {saving ? (
                <div className="flex items-center gap-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                  {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <Save className="h-4 w-4" />
                  {language === 'mm' ? 'သိမ်းရန်' : 'Save Customer'}
                </div>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
