const express = require('express');
const router = express.Router();
const puppeteer = require('puppeteer');
const path = require('path');

// Generate PDF from invoice data
router.post('/generate-pdf', async (req, res) => {
  try {
    console.log('📄 Starting PDF generation...');
    
    const { invoiceData, template, customization, templateCustomizations, companySettings, language } = req.body;
    
    if (!invoiceData) {
      return res.status(400).json({
        success: false,
        message: 'Invoice data is required'
      });
    }

    // Launch puppeteer browser
    const browser = await puppeteer.launch({
      headless: 'new',
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-accelerated-2d-canvas',
        '--no-first-run',
        '--no-zygote',
        '--single-process',
        '--disable-gpu'
      ]
    });

    const page = await browser.newPage();

    // Set viewport for A4 size
    await page.setViewport({
      width: 794,
      height: 1123,
      deviceScaleFactor: 2
    });

    // Generate HTML content for the invoice
    const htmlContent = generateInvoiceHTML(invoiceData, template, customization, templateCustomizations, companySettings, language);

    // Set content and wait for images to load
    await page.setContent(htmlContent, {
      waitUntil: ['networkidle0', 'domcontentloaded']
    });

    // Generate PDF with A4 settings
    const pdfBuffer = await page.pdf({
      format: 'A4',
      width: '210mm',
      height: '297mm',
      margin: {
        top: '0mm',
        right: '0mm',
        bottom: '0mm',
        left: '0mm'
      },
      printBackground: true,
      preferCSSPageSize: true
    });

    await browser.close();

    // Set response headers for PDF download
    const filename = `Invoice-${invoiceData.saleNumber || 'Unknown'}-${new Date().toISOString().split('T')[0]}.pdf`;
    
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${filename}"`);
    res.setHeader('Content-Length', pdfBuffer.length);

    // Send PDF buffer
    res.send(pdfBuffer);

    console.log('✅ PDF generated successfully:', filename);

  } catch (error) {
    console.error('❌ PDF generation error:', error);
    res.status(500).json({
      success: false,
      message: 'Failed to generate PDF',
      error: error.message
    });
  }
});

// Function to generate HTML content for invoice
function generateInvoiceHTML(invoiceData, template, customization, templateCustomizations, companySettings, language) {
  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 2
    }).format(amount);
  };

  const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=120x120&margin=5&ecc=M&data=${encodeURIComponent(`Invoice: ${invoiceData.saleNumber}\nTotal: ${formatCurrency(invoiceData.total)} ${invoiceData.currency || 'MMK'}\nDate: ${invoiceData.timestamp.toLocaleDateString()}`)}`;

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="UTF-8">
        <title>Invoice ${invoiceData.saleNumber}</title>
        <style>
          @page {
            size: A4;
            margin: 0;
          }
          
          html, body {
            width: 794px !important;
            height: 1123px !important;
            margin: 0 !important;
            padding: 0 !important;
            font-family: Arial, sans-serif !important;
            font-size: 11px !important;
            line-height: 1.3 !important;
            background: white !important;
            color: black !important;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
            overflow: hidden !important;
          }
          
          .invoice-container {
            width: 794px !important;
            height: 1123px !important;
            margin: 0 !important;
            padding: 40px !important;
            box-sizing: border-box !important;
            background: white !important;
            color: black !important;
            overflow: hidden !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: flex-start !important;
          }
          
          /* Force all colors to be print-friendly */
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
          }
          
          /* QR Code sizing */
          img[src*="qrserver.com"] {
            width: 80px !important;
            height: 80px !important;
            max-width: 80px !important;
            max-height: 80px !important;
          }
          
          /* Ensure all images print properly */
          img {
            max-width: 100% !important;
            height: auto !important;
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
          }
          
          /* Template 1 Styles */
          .template-1 {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
          }
          
          .template-1 .header {
            background: rgba(255,255,255,0.1) !important;
            padding: 20px !important;
            border-radius: 10px !important;
            margin-bottom: 20px !important;
          }
          
          .template-1 .company-info {
            color: white !important;
          }
          
          .template-1 .invoice-details {
            background: rgba(255,255,255,0.1) !important;
            padding: 15px !important;
            border-radius: 8px !important;
          }
          
          .template-1 table {
            background: white !important;
            color: black !important;
            border-radius: 8px !important;
            overflow: hidden !important;
          }
          
          .template-1 th {
            background: #667eea !important;
            color: white !important;
            padding: 12px 8px !important;
            text-align: left !important;
          }
          
          .template-1 td {
            padding: 10px 8px !important;
            border-bottom: 1px solid #e5e7eb !important;
          }
          
          .template-1 .totals {
            background: rgba(255,255,255,0.1) !important;
            padding: 20px !important;
            border-radius: 8px !important;
            margin-top: 20px !important;
          }
          
          .template-1 .qr-section {
            background: rgba(255,255,255,0.1) !important;
            padding: 15px !important;
            border-radius: 8px !important;
            text-align: center !important;
          }
        </style>
      </head>
      <body>
        <div class="invoice-container template-1">
          <!-- Header -->
          <div class="header">
            <div style="display: flex; justify-content: space-between; align-items: center;">
              <div class="company-info">
                <h1 style="margin: 0; font-size: 24px; font-weight: bold;">${companySettings?.name || 'BitsTech'}</h1>
                <p style="margin: 5px 0; font-size: 12px;">${companySettings?.address || 'No 456, Technology Street, Yangon, Myanmar'}</p>
                <p style="margin: 5px 0; font-size: 12px;">📞 ${companySettings?.phone || '+95 9 ***********'}</p>
                <p style="margin: 5px 0; font-size: 12px;">✉️ ${companySettings?.email || '<EMAIL>'}</p>
              </div>
              <div class="invoice-details">
                <h2 style="margin: 0; font-size: 20px; font-weight: bold;">${language === 'mm' ? 'ငွေတောင်းခံလွှာ' : 'INVOICE'}</h2>
                <p style="margin: 5px 0;"><strong>${language === 'mm' ? 'နံပါတ်:' : 'Invoice #:'}</strong> ${invoiceData.saleNumber}</p>
                <p style="margin: 5px 0;"><strong>${language === 'mm' ? 'ရက်စွဲ:' : 'Date:'}</strong> ${new Date(invoiceData.timestamp).toLocaleDateString()}</p>
                <p style="margin: 5px 0;"><strong>${language === 'mm' ? 'ပေးရမည့်ရက်:' : 'Due Date:'}</strong> ${new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}</p>
              </div>
            </div>
          </div>
          
          <!-- Customer Info -->
          <div style="background: rgba(255,255,255,0.1); padding: 15px; border-radius: 8px; margin-bottom: 20px;">
            <h3 style="margin: 0 0 10px 0; font-size: 14px;">${language === 'mm' ? 'ဖောက်သည်အချက်အလက်' : 'Bill To:'}</h3>
            <p style="margin: 3px 0; font-size: 12px;"><strong>${language === 'mm' ? 'အမည်:' : 'Name:'}</strong> ${invoiceData.customer?.name || 'Walk-in Customer'}</p>
            <p style="margin: 3px 0; font-size: 12px;"><strong>${language === 'mm' ? 'ဖုန်း:' : 'Phone:'}</strong> ${invoiceData.customer?.phone || 'N/A'}</p>
            <p style="margin: 3px 0; font-size: 12px;"><strong>${language === 'mm' ? 'လိပ်စာ:' : 'Address:'}</strong> ${invoiceData.customer?.address || 'Yangon, Myanmar'}</p>
          </div>
          
          <!-- Items Table -->
          <div style="margin-bottom: 20px;">
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr>
                  <th style="text-align: left;">${language === 'mm' ? 'ပစ္စည်း' : 'Item'}</th>
                  <th style="text-align: center;">${language === 'mm' ? 'အရေအတွက်' : 'Qty'}</th>
                  <th style="text-align: right;">${language === 'mm' ? 'ယူနစ်ဈေး' : 'Unit Price'}</th>
                  <th style="text-align: right;">${language === 'mm' ? 'စုစုပေါင်း' : 'Total'}</th>
                </tr>
              </thead>
              <tbody>
                ${invoiceData.items?.map(item => `
                  <tr>
                    <td>${item.product.name}</td>
                    <td style="text-align: center;">${item.quantity}</td>
                    <td style="text-align: right;">${formatCurrency(item.product.price)}</td>
                    <td style="text-align: right; font-weight: bold;">${formatCurrency(item.totalPrice)}</td>
                  </tr>
                `).join('') || ''}
              </tbody>
            </table>
          </div>
          
          <!-- Totals -->
          <div class="totals">
            <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
              <span>${language === 'mm' ? 'စုစုပေါင်း:' : 'Subtotal:'}</span>
              <span>${formatCurrency(invoiceData.subtotal || 0)} ${invoiceData.currency || 'MMK'}</span>
            </div>
            ${invoiceData.discount ? `
              <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span>${language === 'mm' ? 'လျှော့စျေး:' : 'Discount:'}</span>
                <span>-${formatCurrency(invoiceData.discount)} ${invoiceData.currency || 'MMK'}</span>
              </div>
            ` : ''}
            ${invoiceData.tax ? `
              <div style="display: flex; justify-content: space-between; margin-bottom: 8px;">
                <span>${language === 'mm' ? 'အခွန်:' : 'Tax:'}</span>
                <span>${formatCurrency(invoiceData.tax)} ${invoiceData.currency || 'MMK'}</span>
              </div>
            ` : ''}
            <div style="display: flex; justify-content: space-between; font-weight: bold; font-size: 16px; border-top: 2px solid rgba(255,255,255,0.3); padding-top: 10px; margin-top: 10px;">
              <span>${language === 'mm' ? 'စုစုပေါင်း:' : 'Total:'}</span>
              <span>${formatCurrency(invoiceData.total || 0)} ${invoiceData.currency || 'MMK'}</span>
            </div>
          </div>
          
          <!-- QR Code and Footer -->
          <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 30px;">
            <div class="qr-section">
              <img src="${qrCodeUrl}" alt="QR Code" style="width: 80px; height: 80px;" />
              <p style="margin: 8px 0 0 0; font-size: 10px;">${language === 'mm' ? 'QR ကုဒ် ဖတ်ရန်' : 'Scan for details'}</p>
            </div>
            <div style="text-align: right;">
              <p style="margin: 0; font-size: 12px; font-weight: bold;">${language === 'mm' ? 'ကျေးဇူးတင်ပါသည်!' : 'Thank you for your business!'}</p>
              <p style="margin: 5px 0 0 0; font-size: 10px;">${language === 'mm' ? 'ပေးချေမှု နည်းလမ်း:' : 'Payment Method:'} ${invoiceData.paymentMethod || 'Cash'}</p>
            </div>
          </div>
        </div>
      </body>
    </html>
  `;
}

module.exports = router;
