// Central Currency Management System
// Synchronizes currency across all pages and components

import { useState, useEffect } from 'react'

export interface CurrencyConfig {
  code: string
  symbol: string
  name: string
  rate: number // Exchange rate to base currency (MMK)
  decimals: number
  position: 'before' | 'after' // Symbol position
}

export const SUPPORTED_CURRENCIES: Record<string, CurrencyConfig> = {
  MMK: {
    code: 'MMK',
    symbol: 'K',
    name: 'Myanmar Kyat',
    rate: 1,
    decimals: 0,
    position: 'after'
  },
  USD: {
    code: 'USD',
    symbol: '$',
    name: 'US Dollar',
    rate: 0.00048, // 1 MMK = 0.00048 USD (approximate)
    decimals: 2,
    position: 'before'
  },
  THB: {
    code: 'THB',
    symbol: '฿',
    name: 'Thai Baht',
    rate: 0.017, // 1 MMK = 0.017 THB (approximate)
    decimals: 2,
    position: 'before'
  }
}

// Currency storage keys
const CURRENCY_STORAGE_KEY = 'bitstech_currency_settings'
const DEFAULT_CURRENCY = 'MMK'

// Get current currency from localStorage or settings
export function getCurrentCurrency(): CurrencyConfig {
  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    return SUPPORTED_CURRENCIES[DEFAULT_CURRENCY]
  }

  try {
    // Try to get from localStorage first
    const stored = localStorage.getItem(CURRENCY_STORAGE_KEY)
    if (stored) {
      const parsed = JSON.parse(stored)
      if (parsed.code && SUPPORTED_CURRENCIES[parsed.code]) {
        return SUPPORTED_CURRENCIES[parsed.code]
      }
    }

    // Try to get from settings
    const settings = localStorage.getItem('bitstech_settings')
    if (settings) {
      const parsedSettings = JSON.parse(settings)
      if (parsedSettings.currency && SUPPORTED_CURRENCIES[parsedSettings.currency]) {
        return SUPPORTED_CURRENCIES[parsedSettings.currency]
      }
    }

    // Try to get from tax-currency settings
    const taxCurrencySettings = localStorage.getItem('bitstech_tax_currency_settings')
    if (taxCurrencySettings) {
      const parsed = JSON.parse(taxCurrencySettings)
      if (parsed.currency && SUPPORTED_CURRENCIES[parsed.currency]) {
        return SUPPORTED_CURRENCIES[parsed.currency]
      }
    }
  } catch (error) {
    console.warn('Error getting currency from storage:', error)
  }

  return SUPPORTED_CURRENCIES[DEFAULT_CURRENCY]
}

// Set current currency and sync across all storage locations
export function setCurrentCurrency(currencyCode: string): void {
  if (!SUPPORTED_CURRENCIES[currencyCode]) {
    console.warn(`Unsupported currency: ${currencyCode}`)
    return
  }

  // Check if we're in browser environment
  if (typeof window === 'undefined') {
    return
  }

  const currency = SUPPORTED_CURRENCIES[currencyCode]

  try {
    // Update main currency storage
    localStorage.setItem(CURRENCY_STORAGE_KEY, JSON.stringify(currency))

    // Sync with settings
    const settings = localStorage.getItem('bitstech_settings')
    if (settings) {
      const parsedSettings = JSON.parse(settings)
      parsedSettings.currency = currencyCode
      localStorage.setItem('bitstech_settings', JSON.stringify(parsedSettings))
    } else {
      localStorage.setItem('bitstech_settings', JSON.stringify({ currency: currencyCode }))
    }

    // Sync with tax-currency settings
    const taxCurrencySettings = localStorage.getItem('bitstech_tax_currency_settings')
    if (taxCurrencySettings) {
      const parsed = JSON.parse(taxCurrencySettings)
      parsed.currency = currencyCode
      localStorage.setItem('bitstech_tax_currency_settings', JSON.stringify(parsed))
    } else {
      localStorage.setItem('bitstech_tax_currency_settings', JSON.stringify({ currency: currencyCode }))
    }

    // Dispatch custom event for real-time updates
    window.dispatchEvent(new CustomEvent('currencyChanged', {
      detail: { currency: currency }
    }))

    console.log(`✅ Currency updated to ${currencyCode} and synced across all storage locations`)
  } catch (error) {
    console.error('Error setting currency:', error)
  }
}

// Format currency amount with proper symbol and decimals
export function formatCurrency(
  amount: number, 
  currencyCode?: string,
  options?: {
    showSymbol?: boolean
    showCode?: boolean
    compact?: boolean
  }
): string {
  const currency = currencyCode ? SUPPORTED_CURRENCIES[currencyCode] : getCurrentCurrency()
  const { showSymbol = true, showCode = false, compact = false } = options || {}

  if (!currency) {
    return amount.toString()
  }

  // Convert amount if different currency
  let convertedAmount = amount
  if (currencyCode && currencyCode !== 'MMK') {
    convertedAmount = amount * currency.rate
  }

  // Format with proper decimals
  const formatted = convertedAmount.toLocaleString('en-US', {
    minimumFractionDigits: currency.decimals,
    maximumFractionDigits: currency.decimals
  })

  // Compact formatting for large numbers
  if (compact && convertedAmount >= 1000000) {
    const millions = convertedAmount / 1000000
    const compactFormatted = millions.toFixed(1) + 'M'
    return showSymbol 
      ? (currency.position === 'before' ? `${currency.symbol}${compactFormatted}` : `${compactFormatted} ${currency.symbol}`)
      : compactFormatted
  }

  if (compact && convertedAmount >= 1000) {
    const thousands = convertedAmount / 1000
    const compactFormatted = thousands.toFixed(1) + 'K'
    return showSymbol 
      ? (currency.position === 'before' ? `${currency.symbol}${compactFormatted}` : `${compactFormatted} ${currency.symbol}`)
      : compactFormatted
  }

  // Build final string
  let result = formatted

  if (showSymbol) {
    result = currency.position === 'before' 
      ? `${currency.symbol}${formatted}`
      : `${formatted} ${currency.symbol}`
  }

  if (showCode) {
    result += ` ${currency.code}`
  }

  return result
}

// Convert amount between currencies
export function convertCurrency(
  amount: number,
  fromCurrency: string,
  toCurrency: string
): number {
  const from = SUPPORTED_CURRENCIES[fromCurrency]
  const to = SUPPORTED_CURRENCIES[toCurrency]

  if (!from || !to) {
    return amount
  }

  // Convert to MMK first, then to target currency
  const mmkAmount = amount / from.rate
  return mmkAmount * to.rate
}

// Get currency symbol only
export function getCurrencySymbol(currencyCode?: string): string {
  const currency = currencyCode ? SUPPORTED_CURRENCIES[currencyCode] : getCurrentCurrency()
  return currency?.symbol || 'K'
}

// Get currency code only
export function getCurrencyCode(): string {
  return getCurrentCurrency().code
}

// Initialize currency system
export function initializeCurrency(): void {
  // Set up currency change listener
  if (typeof window !== 'undefined') {
    window.addEventListener('currencyChanged', (event: any) => {
      console.log('Currency changed:', event.detail.currency)
    })
  }
}

// Hook for React components to use currency
export function useCurrency() {
  const [currency, setCurrency] = useState(getCurrentCurrency())

  useEffect(() => {
    const handleCurrencyChange = (event: any) => {
      setCurrency(event.detail.currency)
    }

    window.addEventListener('currencyChanged', handleCurrencyChange)
    return () => window.removeEventListener('currencyChanged', handleCurrencyChange)
  }, [])

  return {
    currency,
    formatCurrency: (amount: number, options?: any) => formatCurrency(amount, currency.code, options),
    setCurrency: setCurrentCurrency,
    convertCurrency
  }
}
