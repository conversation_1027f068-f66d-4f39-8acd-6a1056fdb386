'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import {
  Target,
  TrendingUp,
  TrendingDown,
  ArrowLeft,
  Download,
  DollarSign,
  Percent,
  Calculator,
  BarChart3,
  PieChart,
  AlertTriangle,
  CheckCircle,
  Package,
  ShoppingCart
} from 'lucide-react'

interface ProfitMetrics {
  totalRevenue: number
  totalCost: number
  grossProfit: number
  grossProfitMargin: number
  netProfit: number
  netProfitMargin: number
  operatingExpenses: number
  profitGrowth: number
}

interface CategoryProfit {
  category: string
  revenue: number
  cost: number
  profit: number
  margin: number
  trend: 'up' | 'down' | 'stable'
  trendPercentage: number
}

interface ProductProfit {
  _id: string
  name: string
  sku: string
  category: string
  revenue: number
  cost: number
  profit: number
  margin: number
  unitsSold: number
  profitPerUnit: number
}

export default function ProfitAnalysisPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [dateRange, setDateRange] = useState('month')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [loading, setLoading] = useState(true)

  const [profitMetrics, setProfitMetrics] = useState<ProfitMetrics>({
    totalRevenue: 342800000,
    totalCost: 264560000,
    grossProfit: 78240000,
    grossProfitMargin: 22.8,
    netProfit: 65340000,
    netProfitMargin: 19.1,
    operatingExpenses: 12900000,
    profitGrowth: 15.6
  })

  const [categoryProfits, setCategoryProfits] = useState<CategoryProfit[]>([
    {
      category: 'Laptops',
      revenue: 135000000,
      cost: 108000000,
      profit: 27000000,
      margin: 20.0,
      trend: 'up',
      trendPercentage: 12.5
    },
    {
      category: 'Monitors',
      revenue: 68500000,
      cost: 51375000,
      profit: 17125000,
      margin: 25.0,
      trend: 'up',
      trendPercentage: 8.3
    },
    {
      category: 'Accessories',
      revenue: 45200000,
      cost: 29380000,
      profit: 15820000,
      margin: 35.0,
      trend: 'stable',
      trendPercentage: 2.1
    },
    {
      category: 'Processors',
      revenue: 38600000,
      cost: 33341000,
      profit: 5259000,
      margin: 13.6,
      trend: 'down',
      trendPercentage: -5.2
    },
    {
      category: 'Memory',
      revenue: 32400000,
      cost: 23328000,
      profit: 9072000,
      margin: 28.0,
      trend: 'up',
      trendPercentage: 6.7
    },
    {
      category: 'Storage',
      revenue: 23100000,
      cost: 16170000,
      profit: 6930000,
      margin: 30.0,
      trend: 'up',
      trendPercentage: 9.4
    }
  ])

  const [topProfitableProducts, setTopProfitableProducts] = useState<ProductProfit[]>([
    {
      _id: '1',
      name: 'ASUS VivoBook 15',
      sku: 'LAP001',
      category: 'Laptops',
      revenue: 67500000,
      cost: 54000000,
      profit: 13500000,
      margin: 20.0,
      unitsSold: 45,
      profitPerUnit: 300000
    },
    {
      _id: '2',
      name: 'Samsung 27" Monitor',
      sku: 'MON001',
      category: 'Monitors',
      revenue: 17100000,
      cost: 12825000,
      profit: 4275000,
      margin: 25.0,
      unitsSold: 38,
      profitPerUnit: 112500
    },
    {
      _id: '3',
      name: 'Logitech MX Master 3S',
      sku: 'MOU001',
      category: 'Accessories',
      revenue: 8040000,
      cost: 5226000,
      profit: 2814000,
      margin: 35.0,
      unitsSold: 67,
      profitPerUnit: 42000
    },
    {
      _id: '4',
      name: 'Kingston 1TB SSD',
      sku: 'SSD001',
      category: 'Storage',
      revenue: 9600000,
      cost: 6720000,
      profit: 2880000,
      margin: 30.0,
      unitsSold: 32,
      profitPerUnit: 90000
    },
    {
      _id: '5',
      name: 'Corsair 32GB RAM',
      sku: 'RAM001',
      category: 'Memory',
      revenue: 7200000,
      cost: 5184000,
      profit: 2016000,
      margin: 28.0,
      unitsSold: 24,
      profitPerUnit: 84000
    }
  ])

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchProfitAnalysis()
    }
  }, [isAuthenticated, dateRange, categoryFilter])

  const fetchProfitAnalysis = async () => {
    try {
      setLoading(true)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Mock data would be fetched here based on filters
    } catch (error) {
      console.error('Error fetching profit analysis:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} MMK`
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />
      default: return <BarChart3 className="h-4 w-4 text-gray-600" />
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600'
      case 'down': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getMarginStatus = (margin: number) => {
    if (margin >= 30) return { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100', label: 'Excellent' }
    if (margin >= 20) return { icon: CheckCircle, color: 'text-blue-600', bg: 'bg-blue-100', label: 'Good' }
    if (margin >= 10) return { icon: AlertTriangle, color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Fair' }
    return { icon: AlertTriangle, color: 'text-red-600', bg: 'bg-red-100', label: 'Poor' }
  }

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/reports')}
            className="hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Reports'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-red-600 to-pink-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Target className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'အမြတ် ခွဲခြမ်းစိတ်ဖြာမှု' : 'Profit Analysis'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm' 
                      ? 'အမြတ်နှုန်း နှင့် ကုန်ကျစရိတ် ခွဲခြမ်းစိတ်ဖြာမှု'
                      : 'Detailed profit margins and cost analysis'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="w-40 bg-white/20 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="week">{language === 'mm' ? 'ဒီအပတ်' : 'This Week'}</SelectItem>
                    <SelectItem value="month">{language === 'mm' ? 'ဒီလ' : 'This Month'}</SelectItem>
                    <SelectItem value="quarter">{language === 'mm' ? 'ဒီသုံးလ' : 'This Quarter'}</SelectItem>
                  </SelectContent>
                </Select>
                <Button className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                  <Download className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ထုတ်ယူရန်' : 'Export'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Key Profit Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း အမြတ်' : 'Gross Profit'}
                  </p>
                  <p className="text-2xl font-bold">{formatCurrency(profitMetrics.grossProfit)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <TrendingUp className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600">+{profitMetrics.profitGrowth}%</span>
                  </div>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း အမြတ်နှုန်း' : 'Gross Margin'}
                  </p>
                  <p className="text-2xl font-bold">{profitMetrics.grossProfitMargin}%</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'mm' ? 'ဝင်ငွေ၏ ရာခိုင်နှုန်း' : 'Of total revenue'}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                  <Percent className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'သန့် အမြတ်' : 'Net Profit'}
                  </p>
                  <p className="text-2xl font-bold">{formatCurrency(profitMetrics.netProfit)}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'mm' ? 'ကုန်ကျစရိတ် နုတ်ပြီး' : 'After expenses'}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-xl">
                  <Target className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'သန့် အမြတ်နှုန်း' : 'Net Margin'}
                  </p>
                  <p className="text-2xl font-bold">{profitMetrics.netProfitMargin}%</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'mm' ? 'နောက်ဆုံး အမြတ်နှုန်း' : 'Final margin'}
                  </p>
                </div>
                <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-xl">
                  <Calculator className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Category Profit Analysis */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <PieChart className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အမျိုးအစား အမြတ် ခွဲခြမ်းစိတ်ဖြာမှု' : 'Category Profit Analysis'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'ကုန်ပစ္စည်း အမျိုးအစားများ၏ အမြတ်နှုန်း'
                  : 'Profit margins by product category'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categoryProfits.map((category) => {
                  const marginStatus = getMarginStatus(category.margin)
                  const MarginIcon = marginStatus.icon
                  
                  return (
                    <div key={category.category} className="space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{category.category}</span>
                          <Badge className={`${marginStatus.bg} ${marginStatus.color} text-xs`}>
                            <MarginIcon className="h-3 w-3 mr-1" />
                            {marginStatus.label}
                          </Badge>
                        </div>
                        <div className="flex items-center gap-2">
                          {getTrendIcon(category.trend)}
                          <span className={`text-sm font-semibold ${getTrendColor(category.trend)}`}>
                            {category.margin}%
                          </span>
                        </div>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-600 h-2 rounded-full transition-all duration-500" 
                          style={{ width: `${Math.min(category.margin, 40)}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-sm text-gray-600">
                        <span>{language === 'mm' ? 'အမြတ်' : 'Profit'}: {formatCurrency(category.profit)}</span>
                        <span className={getTrendColor(category.trend)}>
                          {category.trend === 'up' ? '+' : category.trend === 'down' ? '' : ''}{category.trendPercentage.toFixed(1)}%
                        </span>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Top Profitable Products */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-green-600" />
                {language === 'mm' ? 'အမြတ်အများဆုံး ကုန်ပစ္စည်းများ' : 'Most Profitable Products'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'အမြတ်အများဆုံး ရရှိသော ကုန်ပစ္စည်းများ'
                  : 'Products generating highest profits'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topProfitableProducts.map((product, index) => {
                  const marginStatus = getMarginStatus(product.margin)
                  
                  return (
                    <div key={product._id} className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                      <div className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                          {index + 1}
                        </div>
                        <div>
                          <h4 className="font-medium">{product.name}</h4>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-gray-600">{product.sku}</span>
                            <Badge className={`${marginStatus.bg} ${marginStatus.color} text-xs`}>
                              {product.margin}%
                            </Badge>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="font-semibold text-green-600">{formatCurrency(product.profit)}</p>
                        <p className="text-xs text-gray-600">{formatCurrency(product.profitPerUnit)}/unit</p>
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Profit Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-purple-600" />
              {language === 'mm' ? 'အသေးစိတ် အမြတ် ခွဲခြမ်းစိတ်ဖြာမှု' : 'Detailed Profit Breakdown'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topProfitableProducts.map((product) => {
                const marginStatus = getMarginStatus(product.margin)
                const MarginIcon = marginStatus.icon
                
                return (
                  <div key={product._id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow">
                    <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                      {/* Product Info */}
                      <div className="lg:col-span-2">
                        <div className="flex items-center gap-3">
                          <div>
                            <h4 className="font-semibold">{product.name}</h4>
                            <p className="text-sm text-gray-600">{product.sku} • {product.category}</p>
                            <Badge className={`${marginStatus.bg} ${marginStatus.color} text-xs mt-1`}>
                              <MarginIcon className="h-3 w-3 mr-1" />
                              {marginStatus.label} ({product.margin}%)
                            </Badge>
                          </div>
                        </div>
                      </div>

                      {/* Financial Metrics */}
                      <div className="text-center">
                        <p className="text-lg font-bold">{formatCurrency(product.revenue)}</p>
                        <p className="text-xs text-gray-600">{language === 'mm' ? 'ဝင်ငွေ' : 'Revenue'}</p>
                      </div>

                      <div className="text-center">
                        <p className="text-lg font-bold">{formatCurrency(product.cost)}</p>
                        <p className="text-xs text-gray-600">{language === 'mm' ? 'ကုန်ကျစရိတ်' : 'Cost'}</p>
                      </div>

                      <div className="text-center">
                        <p className="text-lg font-bold text-green-600">{formatCurrency(product.profit)}</p>
                        <p className="text-xs text-gray-600">{language === 'mm' ? 'အမြတ်' : 'Profit'}</p>
                      </div>

                      {/* Units & Profit per Unit */}
                      <div className="text-center">
                        <p className="text-lg font-bold">{product.unitsSold}</p>
                        <p className="text-xs text-gray-600">{language === 'mm' ? 'ရောင်းချ' : 'Units Sold'}</p>
                        <p className="text-sm font-medium text-green-600 mt-1">
                          {formatCurrency(product.profitPerUnit)}/unit
                        </p>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Profit Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Calculator className="h-5 w-5 text-orange-600" />
              {language === 'mm' ? 'အမြတ် အနှစ်ချုပ်' : 'Profit Summary'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="text-center p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <DollarSign className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                <h3 className="font-semibold text-lg">
                  {language === 'mm' ? 'စုစုပေါင်း ဝင်ငွေ' : 'Total Revenue'}
                </h3>
                <p className="text-2xl font-bold text-blue-600">{formatCurrency(profitMetrics.totalRevenue)}</p>
              </div>

              <div className="text-center p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <ShoppingCart className="h-8 w-8 text-red-600 mx-auto mb-2" />
                <h3 className="font-semibold text-lg">
                  {language === 'mm' ? 'စုစုပေါင်း ကုန်ကျစရိတ်' : 'Total Cost'}
                </h3>
                <p className="text-2xl font-bold text-red-600">{formatCurrency(profitMetrics.totalCost)}</p>
              </div>

              <div className="text-center p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <Target className="h-8 w-8 text-green-600 mx-auto mb-2" />
                <h3 className="font-semibold text-lg">
                  {language === 'mm' ? 'သန့် အမြတ်' : 'Net Profit'}
                </h3>
                <p className="text-2xl font-bold text-green-600">{formatCurrency(profitMetrics.netProfit)}</p>
                <p className="text-sm text-gray-600 mt-1">{profitMetrics.netProfitMargin}% margin</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
