'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import { useSettings, useTranslation } from '@/contexts/settings-context'
import apiClient from '@/lib/api'
import {
  Users,
  ArrowLeft,
  Save,
  Check,
  Plus,
  Edit,
  Trash2,
  Shield,
  UserCheck,
  UserX,
  Crown,
  Key,
  Mail,
  Phone,
  Calendar,
  Activity
} from 'lucide-react'

interface User {
  id: string
  name: string
  email: string
  phone: string
  role: 'admin' | 'manager' | 'cashier'
  status: 'active' | 'inactive'
  lastLogin: string
  createdAt: string
  permissions: string[]
}

interface Role {
  id: string
  name: string
  nameLocal: string
  description: string
  color: string
  permissions: string[]
}

export default function UserManagementPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const { t } = useTranslation()
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [showAddUser, setShowAddUser] = useState(false)
  const [editingUser, setEditingUser] = useState<User | null>(null)
  const [showEditUser, setShowEditUser] = useState(false)

  const [users, setUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const roles: Role[] = [
    {
      id: 'admin',
      name: 'Administrator',
      nameLocal: 'စီမံခန့်ခွဲသူ',
      description: 'Full system access and control',
      color: 'bg-red-500',
      permissions: ['all']
    },
    {
      id: 'manager',
      name: 'Manager',
      nameLocal: 'မန်နေဂျာ',
      description: 'Manage products, sales, and reports',
      color: 'bg-blue-500',
      permissions: ['products', 'sales', 'inventory', 'reports', 'purchase-orders']
    },
    {
      id: 'cashier',
      name: 'Cashier',
      nameLocal: 'ငွေကောင်တာ',
      description: 'Handle sales and POS operations',
      color: 'bg-green-500',
      permissions: ['pos', 'sales']
    }
  ]

  const [newUser, setNewUser] = useState({
    name: '',
    email: '',
    phone: '',
    role: 'cashier' as const,
    password: ''
  })

  // Load users data
  const loadUsers = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.getUsers()

      if (response.success) {
        // Transform API data to match UI interface
        const transformedUsers = response.data.map((user: any) => ({
          id: user.id,
          name: user.fullName || `${user.firstName} ${user.lastName}`,
          email: user.email,
          phone: user.phone || '',
          role: user.role,
          status: user.isActive ? 'active' : 'inactive',
          lastLogin: user.lastLogin || 'Never',
          createdAt: user.createdAt,
          permissions: user.permissions || []
        }))
        setUsers(transformedUsers)
      } else {
        setError(response.message || 'Failed to load users')
      }
    } catch (error) {
      console.error('Error loading users:', error)
      setError('Failed to load users')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    } else if (isAuthenticated) {
      loadUsers()
    }
  }, [isAuthenticated, isLoading, router])

  const getRoleInfo = (roleId: string) => {
    return roles.find(role => role.id === roleId) || roles[2]
  }

  const toggleUserStatus = async (userId: string) => {
    try {
      const user = users.find(u => u.id === userId)
      if (!user) return

      const newStatus = user.status === 'active' ? false : true
      const response = await apiClient.updateUserStatus(userId, newStatus)

      if (response.success) {
        setUsers(prev => prev.map(u =>
          u.id === userId
            ? { ...u, status: newStatus ? 'active' : 'inactive' }
            : u
        ))
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)
      } else {
        throw new Error(response.message || 'Failed to update user status')
      }
    } catch (error) {
      console.error('Error updating user status:', error)
      alert(language === 'mm'
        ? 'အသုံးပြုသူ အခြေအနေ ပြောင်းလဲမှု မအောင်မြင်ပါ'
        : 'Failed to update user status'
      )
    }
  }

  const deleteUser = async (userId: string) => {
    const confirmMessage = language === 'mm'
      ? 'ဤအသုံးပြုသူကို ဖျက်ရန် သေချာပါသလား?'
      : 'Are you sure you want to delete this user?'

    if (confirm(confirmMessage)) {
      try {
        const response = await apiClient.deleteUser(userId)

        if (response.success) {
          setUsers(prev => prev.filter(user => user.id !== userId))
          setSaved(true)
          setTimeout(() => setSaved(false), 3000)
        } else {
          throw new Error(response.message || 'Failed to delete user')
        }
      } catch (error) {
        console.error('Error deleting user:', error)
        alert(language === 'mm'
          ? 'အသုံးပြုသူ ဖျက်ခြင်း မအောင်မြင်ပါ'
          : 'Failed to delete user'
        )
      }
    }
  }

  const addUser = async () => {
    if (!newUser.name || !newUser.email || !newUser.password) {
      alert(language === 'mm'
        ? 'လိုအပ်သော အချက်အလက်များ ဖြည့်စွက်ပါ'
        : 'Please fill in all required fields'
      )
      return
    }

    try {
      setSaving(true)
      const [firstName, ...lastNameParts] = newUser.name.split(' ')
      const lastName = lastNameParts.join(' ') || ''

      const userData = {
        firstName,
        lastName,
        email: newUser.email,
        phone: newUser.phone,
        role: newUser.role,
        username: newUser.email.split('@')[0],
        password: newUser.password,
        permissions: getRoleInfo(newUser.role).permissions
      }

      const response = await apiClient.createUser(userData)

      if (response.success) {
        // Transform and add to local state
        const newUserData: User = {
          id: response.data.id,
          name: `${response.data.firstName} ${response.data.lastName}`,
          email: response.data.email,
          phone: response.data.phone || '',
          role: response.data.role,
          status: response.data.isActive ? 'active' : 'inactive',
          lastLogin: response.data.lastLogin || 'Never',
          createdAt: response.data.createdAt,
          permissions: response.data.permissions || []
        }

        setUsers(prev => [...prev, newUserData])
        setNewUser({ name: '', email: '', phone: '', role: 'cashier', password: '' })
        setShowAddUser(false)
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)
      } else {
        throw new Error(response.message || 'Failed to create user')
      }
    } catch (error) {
      console.error('Error creating user:', error)
      alert(language === 'mm'
        ? 'အသုံးပြုသူ ဖန်တီးမှု မအောင်မြင်ပါ'
        : 'Failed to create user'
      )
    } finally {
      setSaving(false)
    }
  }

  const editUser = (userData: User) => {
    setEditingUser(userData)
    setShowEditUser(true)
  }

  const updateUser = async () => {
    if (!editingUser) return

    setSaving(true)
    try {
      const [firstName, ...lastNameParts] = editingUser.name.split(' ')
      const lastName = lastNameParts.join(' ') || ''

      const userData = {
        firstName,
        lastName,
        email: editingUser.email,
        phone: editingUser.phone,
        role: editingUser.role,
        isActive: editingUser.status === 'active',
        permissions: getRoleInfo(editingUser.role).permissions
      }

      const response = await apiClient.updateUser(editingUser.id, userData)

      if (response.success) {
        // Transform and update local state
        const updatedUser: User = {
          ...editingUser,
          name: `${response.data.firstName} ${response.data.lastName}`,
          email: response.data.email,
          phone: response.data.phone || '',
          role: response.data.role,
          status: response.data.isActive ? 'active' : 'inactive',
          permissions: response.data.permissions || []
        }

        setUsers(prev => prev.map(u =>
          u.id === editingUser.id ? updatedUser : u
        ))
        setEditingUser(null)
        setShowEditUser(false)
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)
      } else {
        throw new Error(response.message || 'Failed to update user')
      }
    } catch (error) {
      console.error('Error updating user:', error)
      alert(language === 'mm'
        ? 'အသုံးပြုသူ ပြင်ဆင်မှု မအောင်မြင်ပါ'
        : 'Failed to update user'
      )
    } finally {
      setSaving(false)
    }
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 1500))
      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error('Error saving users:', error)
    } finally {
      setSaving(false)
    }
  }



  if (!isAuthenticated) {
    return null
  }

  if (error) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <div className="text-red-500 text-6xl mb-4">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
              {language === 'mm' ? 'အမှား ဖြစ်ပွားခဲ့သည်' : 'Error Occurred'}
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-4">{error}</p>
            <Button onClick={loadUsers} className="bg-blue-600 hover:bg-blue-700">
              {language === 'mm' ? 'ပြန်လည် ကြိုးစားရန်' : 'Try Again'}
            </Button>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-orange-50 dark:hover:bg-orange-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-orange-600 to-red-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Users className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'အသုံးပြုသူ စီမံခန့်ခွဲမှု' : 'User Management'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm'
                      ? 'အသုံးပြုသူ အကောင့်များ၊ အခန်းကဏ္ဍများ နှင့် ခွင့်ပြုချက်များ စီမံခန့်ခွဲပါ'
                      : 'Manage user accounts, roles, and permissions'
                    }
                  </p>
                </div>
              </div>
              <Button
                onClick={() => setShowAddUser(true)}
                className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              >
                <Plus className="h-4 w-4 mr-2" />
                {language === 'mm' ? 'အသုံးပြုသူ ထည့်ရန်' : 'Add User'}
              </Button>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          {/* User Statistics */}
          <div className="lg:col-span-4 grid grid-cols-1 md:grid-cols-4 gap-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                    <Users className="h-6 w-6 text-blue-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {language === 'mm' ? 'စုစုပေါင်း အသုံးပြုသူများ' : 'Total Users'}
                    </p>
                    <p className="text-2xl font-bold">{users.length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                    <UserCheck className="h-6 w-6 text-green-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {language === 'mm' ? 'တက်ကြွ အသုံးပြုသူများ' : 'Active Users'}
                    </p>
                    <p className="text-2xl font-bold">{users.filter(u => u.status === 'active').length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-red-100 dark:bg-red-900/20 rounded-xl">
                    <Crown className="h-6 w-6 text-red-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {language === 'mm' ? 'စီမံခန့်ခွဲသူများ' : 'Administrators'}
                    </p>
                    <p className="text-2xl font-bold">{users.filter(u => u.role === 'admin').length}</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-4">
                  <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-xl">
                    <Activity className="h-6 w-6 text-purple-600" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {language === 'mm' ? 'ယနေ့ လော့ဂ်အင်' : 'Today Logins'}
                    </p>
                    <p className="text-2xl font-bold">3</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Users List */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အသုံးပြုသူ စာရင်း' : 'Users List'}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users.map((userData) => {
                  const roleInfo = getRoleInfo(userData.role)
                  return (
                    <div key={userData.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                      <div className="flex items-center gap-4">
                        <div className={`w-12 h-12 rounded-full ${roleInfo.color} flex items-center justify-center text-white font-bold`}>
                          {userData.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <div className="flex items-center gap-2">
                            <h3 className="font-medium">{userData.name}</h3>
                            <Badge
                              variant="secondary"
                              className={`text-xs ${userData.status === 'active' ? 'bg-green-100 text-green-700' : 'bg-red-100 text-red-700'}`}
                            >
                              {userData.status === 'active'
                                ? (language === 'mm' ? 'တက်ကြွ' : 'Active')
                                : (language === 'mm' ? 'ပိတ်ထား' : 'Inactive')
                              }
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {language === 'mm' ? roleInfo.nameLocal : roleInfo.name}
                            </Badge>
                          </div>
                          <div className="flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400 mt-1">
                            <span className="flex items-center gap-1">
                              <Mail className="h-3 w-3" />
                              {userData.email}
                            </span>
                            <span className="flex items-center gap-1">
                              <Phone className="h-3 w-3" />
                              {userData.phone}
                            </span>
                            <span className="flex items-center gap-1">
                              <Calendar className="h-3 w-3" />
                              {language === 'mm' ? 'နောက်ဆုံး လော့ဂ်အင်' : 'Last login'}: {userData.lastLogin}
                            </span>
                          </div>
                        </div>
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleUserStatus(userData.id)}
                          className={userData.status === 'active' ? 'text-red-600 hover:text-red-700' : 'text-green-600 hover:text-green-700'}
                        >
                          {userData.status === 'active' ? <UserX className="h-4 w-4" /> : <UserCheck className="h-4 w-4" />}
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => editUser(userData)}
                          className="text-blue-600 hover:text-blue-700"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        {userData.role !== 'admin' && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => deleteUser(userData.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>

          {/* Roles & Permissions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Key className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'အခန်းကဏ္ဍများ' : 'Roles'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {roles.map((role) => (
                <div key={role.id} className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                  <div className="flex items-center gap-3 mb-2">
                    <div className={`w-4 h-4 rounded-full ${role.color}`}></div>
                    <h4 className="font-medium">
                      {language === 'mm' ? role.nameLocal : role.name}
                    </h4>
                  </div>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                    {role.description}
                  </p>
                  <div className="flex flex-wrap gap-1">
                    {role.permissions.map((permission) => (
                      <Badge key={permission} variant="outline" className="text-xs">
                        {permission}
                      </Badge>
                    ))}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        </div>

        {/* Add User Modal */}
        {showAddUser && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md mx-4">
              <CardHeader>
                <CardTitle>
                  {language === 'mm' ? 'အသုံးပြုသူ အသစ် ထည့်ရန်' : 'Add New User'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'အမည်' : 'Name'}</Label>
                  <Input
                    value={newUser.name}
                    onChange={(e) => setNewUser(prev => ({ ...prev, name: e.target.value }))}
                    placeholder="Enter full name"
                  />
                </div>

                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'အီးမေးလ်' : 'Email'}</Label>
                  <Input
                    type="email"
                    value={newUser.email}
                    onChange={(e) => setNewUser(prev => ({ ...prev, email: e.target.value }))}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone'}</Label>
                  <Input
                    value={newUser.phone}
                    onChange={(e) => setNewUser(prev => ({ ...prev, phone: e.target.value }))}
                    placeholder="+95 9 xxx xxx xxx"
                  />
                </div>

                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'အခန်းကဏ္ဍ' : 'Role'}</Label>
                  <Select
                    value={newUser.role}
                    onValueChange={(value) => setNewUser(prev => ({ ...prev, role: value as any }))}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder={language === 'mm' ? 'အခန်းကဏ္ဍ ရွေးချယ်ပါ' : 'Select role'} />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map((role) => (
                        <SelectItem key={role.id} value={role.id}>
                          {language === 'mm' ? role.nameLocal : role.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'စကားဝှက်' : 'Password'}</Label>
                  <Input
                    type="password"
                    value={newUser.password}
                    onChange={(e) => setNewUser(prev => ({ ...prev, password: e.target.value }))}
                    placeholder="Enter password"
                  />
                </div>

                <div className="flex gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => setShowAddUser(false)}
                    className="flex-1"
                  >
                    {language === 'mm' ? 'မလုပ်တော့' : 'Cancel'}
                  </Button>
                  <Button
                    onClick={addUser}
                    disabled={!newUser.name || !newUser.email || !newUser.password}
                    className="flex-1 bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700"
                  >
                    {language === 'mm' ? 'ထည့်ရန်' : 'Add User'}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Edit User Modal */}
        {showEditUser && editingUser && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <Card className="w-full max-w-md mx-4">
              <CardHeader>
                <CardTitle>
                  {language === 'mm' ? 'အသုံးပြုသူ ပြင်ဆင်ရန်' : 'Edit User'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'အမည်' : 'Name'}</Label>
                  <Input
                    value={editingUser.name}
                    onChange={(e) => setEditingUser(prev => prev ? ({ ...prev, name: e.target.value }) : null)}
                    placeholder="Enter full name"
                  />
                </div>

                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'အီးမေးလ်' : 'Email'}</Label>
                  <Input
                    type="email"
                    value={editingUser.email}
                    onChange={(e) => setEditingUser(prev => prev ? ({ ...prev, email: e.target.value }) : null)}
                    placeholder="<EMAIL>"
                  />
                </div>

                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone'}</Label>
                  <Input
                    value={editingUser.phone}
                    onChange={(e) => setEditingUser(prev => prev ? ({ ...prev, phone: e.target.value }) : null)}
                    placeholder="+95 9 xxx xxx xxx"
                  />
                </div>

                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'အခန်းကဏ္ဍ' : 'Role'}</Label>
                  <Select
                    value={editingUser.role}
                    onValueChange={(value) => setEditingUser(prev => prev ? ({ ...prev, role: value as any }) : null)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {roles.map((role) => (
                        <SelectItem key={role.id} value={role.id}>
                          {language === 'mm' ? role.nameLocal : role.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>{language === 'mm' ? 'အခြေအနေ' : 'Status'}</Label>
                  <Select
                    value={editingUser.status}
                    onValueChange={(value) => setEditingUser(prev => prev ? ({ ...prev, status: value as any }) : null)}
                  >
                    <SelectTrigger className="w-full">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="active">
                        {language === 'mm' ? 'တက်ကြွ' : 'Active'}
                      </SelectItem>
                      <SelectItem value="inactive">
                        {language === 'mm' ? 'ပိတ်ထား' : 'Inactive'}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="flex gap-2 pt-4">
                  <Button
                    variant="outline"
                    onClick={() => {
                      setShowEditUser(false)
                      setEditingUser(null)
                    }}
                    className="flex-1"
                  >
                    {language === 'mm' ? 'မလုပ်တော့' : 'Cancel'}
                  </Button>
                  <Button
                    onClick={updateUser}
                    className="flex-1 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    disabled={!editingUser.name || !editingUser.email || saving}
                  >
                    {saving ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                        {language === 'mm' ? 'ပြင်ဆင်နေသည်...' : 'Updating...'}
                      </div>
                    ) : (
                      language === 'mm' ? 'ပြင်ဆင်ရန်' : 'Update User'
                    )}
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-gradient-to-r from-orange-600 to-red-600 hover:from-orange-700 hover:to-red-700 text-white px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
              </div>
            ) : saved ? (
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းပြီး' : 'Saved'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းရန်' : 'Save Changes'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
