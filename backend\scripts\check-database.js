const mongoose = require('mongoose');
require('dotenv').config();

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos';

async function checkDatabase() {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI);
        console.log('✅ Connected to MongoDB');

        // Get database instance
        const db = mongoose.connection.db;
        
        console.log('📊 Database Information:');
        console.log(`   Database Name: ${db.databaseName}`);
        
        // List all collections
        console.log('📂 Collections:');
        const collections = await db.listCollections().toArray();
        
        for (const collection of collections) {
            const count = await db.collection(collection.name).countDocuments();
            console.log(`   - ${collection.name}: ${count} documents`);
        }

        // Check specific collections
        console.log('\n📋 Detailed Collection Data:');
        
        // Categories
        const categories = await db.collection('categories').find({}).toArray();
        console.log(`\n📂 Categories (${categories.length}):`);
        categories.forEach(cat => console.log(`   - ${cat.name} (${cat.color})`));
        
        // Users
        const users = await db.collection('users').find({}).toArray();
        console.log(`\n👥 Users (${users.length}):`);
        users.forEach(user => console.log(`   - ${user.username} (${user.email}) - ${user.role}`));
        
        // Products
        const products = await db.collection('products').find({}).toArray();
        console.log(`\n📦 Products (${products.length}):`);
        products.forEach(product => console.log(`   - ${product.name} - ${product.price.toLocaleString()} ${product.currency || 'MMK'}`));
        
        // Settings
        const settings = await db.collection('settings').findOne();
        console.log(`\n⚙️ Settings:`);
        if (settings) {
            console.log(`   - Store: ${settings.storeName}`);
            console.log(`   - Currency: ${settings.currency}`);
            console.log(`   - Tax Rate: ${settings.taxRate}%`);
        }
        
        // Exchange Rates
        const exchangeRates = await db.collection('exchangerates').find({}).toArray();
        console.log(`\n💱 Exchange Rates (${exchangeRates.length}):`);
        exchangeRates.forEach(rate => console.log(`   - ${rate.currency}: ${rate.rate} (${rate.symbol})`));

        console.log('\n🔄 Disconnecting from MongoDB...');
        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');

        console.log('\n🎉 Database check completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error checking database:', error);
        process.exit(1);
    }
}

// Run the check
checkDatabase();
