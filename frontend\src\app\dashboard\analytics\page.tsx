'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import { useTheme } from '@/contexts/theme-context'
import { useCurrency } from '@/contexts/currency-context'
import { apiClient } from '@/lib/api'
import {
  TrendingUp,
  DollarSign,
  Users,
  Package,
  Target,
  BarChart3,
  ArrowUp,
  ArrowDown,
  Star,
  AlertTriangle,
  CheckCircle,
  Clock,
  RefreshCw,
  Plus,
  ShoppingCart
} from 'lucide-react'

interface KPIData {
  title: string
  value: string
  change: number
  changeType: 'increase' | 'decrease'
  target?: string
  status: 'good' | 'warning' | 'danger'
  icon: any
  color: string
}

interface ForecastData {
  period: string
  revenue: number
  orders: number
  customers: number
  confidence: number
}

interface AnalyticsData {
  salesTrend: { date: string; sales: number; orders: number }[]
  topProducts: { name: string; sales: number; growth: number }[]
  customerSegments: { segment: string; count: number; revenue: number }[]
  forecast: ForecastData[]
}

export default function AdvancedAnalyticsPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const { language } = useTheme()
  const { formatCurrency, currentCurrency, setCurrency } = useCurrency()
  const router = useRouter()
  const [dataLoading, setDataLoading] = useState(false)
  const [timeRange, setTimeRange] = useState('30d')
  // Initialize with mock data immediately to prevent loading spinner
  const [kpiData, setKpiData] = useState<KPIData[]>(() => {
    // Generate initial mock data with language support
    return [
      {
        title: language === 'mm' ? 'ဝင်ငွေတိုးတက်မှု' : 'Revenue Growth',
        value: language === 'mm' ? 'ရယူနေသည်...' : 'Loading...',
        change: 0,
        changeType: 'increase' as const,
        target: language === 'mm' ? 'ရယူနေသည်...' : 'Loading...',
        status: 'good' as const,
        icon: TrendingUp,
        color: 'text-green-600'
      },
      {
        title: language === 'mm' ? 'ဖောက်သည်ရယူမှု' : 'Customer Acquisition',
        value: language === 'mm' ? 'ရယူနေသည်...' : 'Loading...',
        change: 0,
        changeType: 'increase' as const,
        target: language === 'mm' ? 'ရယူနေသည်...' : 'Loading...',
        status: 'good' as const,
        icon: Users,
        color: 'text-blue-600'
      },
      {
        title: language === 'mm' ? 'ပျမ်းမျှအော်ဒါတန်ဖိုး' : 'Average Order Value',
        value: language === 'mm' ? 'ရယူနေသည်...' : 'Loading...',
        change: 0,
        changeType: 'increase' as const,
        target: language === 'mm' ? 'ရယူနေသည်...' : 'Loading...',
        status: 'good' as const,
        icon: ShoppingCart,
        color: 'text-purple-600'
      },
      {
        title: language === 'mm' ? 'ပြောင်းလဲမှုနှုန်း' : 'Conversion Rate',
        value: language === 'mm' ? 'ရယူနေသည်...' : 'Loading...',
        change: 0,
        changeType: 'increase' as const,
        target: language === 'mm' ? 'ရယူနေသည်...' : 'Loading...',
        status: 'good' as const,
        icon: Target,
        color: 'text-orange-600'
      }
    ]
  })
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>({
    salesTrend: [],
    topProducts: [],
    customerSegments: [],
    forecast: []
  })

  const [isCurrencyRefresh, setIsCurrencyRefresh] = useState(false)

  // Memoize functions to prevent re-renders
  const formatPercentage = React.useCallback((value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }, [])

  const getStatusIcon = React.useCallback((status: string) => {
    switch (status) {
      case 'good': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'danger': return <AlertTriangle className="h-4 w-4 text-red-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }, [])

  const getStatusColor = React.useCallback((status: string) => {
    switch (status) {
      case 'good': return 'bg-green-100 text-green-800 border-green-200'
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'danger': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }, [])

  const generateMockKPIs = React.useCallback((): KPIData[] => {
    // Check if system was reset - if so, return empty KPIs
    const resetFlag = typeof window !== 'undefined' ? localStorage.getItem('bitstech_system_reset') : null
    if (resetFlag) {
      // Return zero data if reset
      return [
        {
          title: language === 'mm' ? 'ဝင်ငွေတိုးတက်မှု' : 'Revenue Growth',
          value: '0%',
          change: 0,
          changeType: 'increase' as const,
          target: '0%',
          status: 'warning' as const,
          icon: TrendingUp,
          color: 'text-green-600'
        },
        {
          title: language === 'mm' ? 'ဖောက်သည်ရယူမှု' : 'Customer Acquisition',
          value: '0',
          change: 0,
          changeType: 'increase' as const,
          target: '0',
          status: 'warning' as const,
          icon: Users,
          color: 'text-blue-600'
        },
        {
          title: language === 'mm' ? 'ပျမ်းမျှအော်ဒါတန်ဖိုး' : 'Average Order Value',
          value: formatCurrency(0),
          change: 0,
          changeType: 'increase' as const,
          target: formatCurrency(0),
          status: 'warning' as const,
          icon: DollarSign,
          color: 'text-yellow-600'
        },
        {
          title: language === 'mm' ? 'ကုန်ပစ္စည်းလည်ပတ်မှု' : 'Inventory Turnover',
          value: '0x',
          change: 0,
          changeType: 'increase' as const,
          target: '0x',
          status: 'warning' as const,
          icon: Package,
          color: 'text-purple-600'
        },
        {
          title: language === 'mm' ? 'ဖောက်သည်စိတ်ကျေနပ်မှု' : 'Customer Satisfaction',
          value: '0/5',
          change: 0,
          changeType: 'increase' as const,
          target: '0/5',
          status: 'warning' as const,
          icon: Star,
          color: 'text-orange-600'
        },
        {
          title: language === 'mm' ? 'ပြောင်းလဲမှုနှုန်း' : 'Conversion Rate',
          value: '0%',
          change: 0,
          changeType: 'increase' as const,
          target: '0%',
          status: 'warning' as const,
          icon: Target,
          color: 'text-red-600'
        }
      ]
    }

    // Return realistic mock data if not reset
    return [
      {
        title: language === 'mm' ? 'ဝင်ငွေတိုးတက်မှု' : 'Revenue Growth',
        value: '15.8%',
        change: 15.8,
        changeType: 'increase' as const,
        target: '12%',
        status: 'good' as const,
        icon: TrendingUp,
        color: 'text-green-600'
      },
      {
        title: language === 'mm' ? 'ဖောက်သည်ရယူမှု' : 'Customer Acquisition',
        value: '234',
        change: 12.5,
        changeType: 'increase' as const,
        target: '200',
        status: 'good' as const,
        icon: Users,
        color: 'text-blue-600'
      },
      {
        title: language === 'mm' ? 'ပျမ်းမျှအော်ဒါတန်ဖိုး' : 'Average Order Value',
        value: formatCurrency(125000),
        change: -3.2,
        changeType: 'decrease' as const,
        target: formatCurrency(130000),
        status: 'warning' as const,
        icon: DollarSign,
        color: 'text-yellow-600'
      },
      {
        title: language === 'mm' ? 'ကုန်ပစ္စည်းလည်ပတ်မှု' : 'Inventory Turnover',
        value: '8.5x',
        change: 5.7,
        changeType: 'increase' as const,
        target: '8x',
        status: 'good' as const,
        icon: Package,
        color: 'text-purple-600'
      },
      {
        title: language === 'mm' ? 'ဖောက်သည်စိတ်ကျေနပ်မှု' : 'Customer Satisfaction',
        value: '4.7/5',
        change: 2.1,
        changeType: 'increase' as const,
        target: '4.5/5',
        status: 'good' as const,
        icon: Star,
        color: 'text-orange-600'
      },
      {
        title: language === 'mm' ? 'ပြောင်းလဲမှုနှုန်း' : 'Conversion Rate',
        value: '3.8%',
        change: -0.5,
        changeType: 'decrease' as const,
        target: '4%',
        status: 'warning' as const,
        icon: Target,
        color: 'text-red-600'
      }
    ]
  }, [formatCurrency, currentCurrency, language])

  // Memoize KPI cards with currency-aware data
  const kpiCards = React.useMemo(() => {
    // Use mock data if no real data available, but make it currency-aware
    const currentKpiData = kpiData && kpiData.length > 0 ? kpiData : generateMockKPIs()

    if (!currentKpiData || !Array.isArray(currentKpiData) || currentKpiData.length === 0) {
      return (
        <div className="col-span-full text-center py-8">
          <div className="w-16 h-16 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
            <BarChart3 className="h-8 w-8 text-gray-400" />
          </div>
          <p className="text-gray-600 dark:text-gray-400">No KPI data available</p>
        </div>
      )
    }

    return currentKpiData.map((kpi, index) => {
      // Safety checks for KPI data
      if (!kpi || typeof kpi !== 'object') {
        return null
      }

      // Safely get Icon component
      let IconComponent = BarChart3
      if (kpi.icon && typeof kpi.icon === 'function') {
        IconComponent = kpi.icon
      }

      const title = kpi.title || 'Unknown Metric'
      const value = kpi.value || '0'
      const change = typeof kpi.change === 'number' ? kpi.change : 0
      const changeType = kpi.changeType || 'increase'
      const status = kpi.status || 'warning'
      const color = kpi.color || 'text-gray-600'
      const target = kpi.target

      return (
        <Card key={`kpi-${index}-${title}`} className="hover:shadow-lg transition-shadow">
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <div className={`p-3 rounded-xl bg-gray-100 dark:bg-gray-800`}>
                <IconComponent className={`h-6 w-6 ${color}`} />
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(status)}
                <Badge className={getStatusColor(status)}>
                  {status === 'good' ? (language === 'mm' ? 'ကောင်းမွန်' : 'Good') :
                   status === 'warning' ? (language === 'mm' ? 'သတိပေး' : 'Warning') :
                   (language === 'mm' ? 'အန္တရာယ်' : 'Danger')}
                </Badge>
              </div>
            </div>

            <div className="space-y-2">
              <h3 className="font-medium text-gray-600 dark:text-gray-400">
                {title}
              </h3>
              <p className="text-2xl font-bold">{value}</p>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-1">
                  {changeType === 'increase' ? (
                    <ArrowUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <ArrowDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className={`text-sm font-medium ${
                    changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {formatPercentage(change)}
                  </span>
                </div>

                {target && (
                  <div className="text-sm text-gray-500">
                    {language === 'mm' ? 'ပန်းတိုင်' : 'Target'}: {target}
                  </div>
                )}
              </div>
            </div>
          </CardContent>
        </Card>
      )
    }).filter(Boolean)
  }, [kpiData, currentCurrency, formatPercentage, getStatusIcon, getStatusColor, generateMockKPIs])

  // Define fetchAnalyticsData before useEffect hooks
  const fetchAnalyticsData = React.useCallback(async () => {
    try {
      // Only show loading if it's not a currency refresh
      if (!isCurrencyRefresh) {
        setDataLoading(true)
      }

      // Load real analytics data from API
      try {
        const [dashboardResponse, salesResponse] = await Promise.all([
          apiClient.getDashboardStats(),
          apiClient.getSalesReport({
            startDate: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
            endDate: new Date().toISOString().split('T')[0],
            groupBy: 'day'
          })
        ])

        if (dashboardResponse.success) {
          // Convert real data to KPI format
          const realKPIs: KPIData[] = [
            {
              title: language === 'mm' ? 'ဝင်ငွေတိုးတက်မှု' : 'Revenue Growth',
              value: formatCurrency(dashboardResponse.data.todaySales?.amount || 0),
              change: dashboardResponse.data.todaySales?.change || 0,
              changeType: (dashboardResponse.data.todaySales?.change || 0) >= 0 ? 'increase' : 'decrease',
              target: formatCurrency(2000000),
              status: (dashboardResponse.data.todaySales?.amount || 0) > 1500000 ? 'good' : 'warning',
              icon: TrendingUp,
              color: 'text-green-600'
            },
            {
              title: language === 'mm' ? 'စုစုပေါင်းကုန်ပစ္စည်းများ' : 'Total Products',
              value: `${dashboardResponse.data.totalProducts?.count || 0}`,
              change: dashboardResponse.data.totalProducts?.change || 0,
              changeType: (dashboardResponse.data.totalProducts?.change || 0) >= 0 ? 'increase' : 'decrease',
              target: '1000',
              status: (dashboardResponse.data.totalProducts?.count || 0) > 500 ? 'good' : 'warning',
              icon: Package,
              color: 'text-blue-600'
            },
            {
              title: language === 'mm' ? 'စတော့နည်းသောပစ္စည်းများ' : 'Low Stock Items',
              value: `${dashboardResponse.data.lowStockItems?.count || 0}`,
              change: -(dashboardResponse.data.lowStockItems?.count || 0),
              changeType: 'decrease',
              target: '0',
              status: (dashboardResponse.data.lowStockItems?.count || 0) < 5 ? 'good' : 'warning',
              icon: AlertTriangle,
              color: 'text-orange-600'
            },
            {
              title: language === 'mm' ? 'နေ့စဉ်ရောင်းချမှုများ' : 'Daily Transactions',
              value: `${dashboardResponse.data.todaySales?.transactions || 0}`,
              change: 5.2,
              changeType: 'increase',
              target: '100',
              status: (dashboardResponse.data.todaySales?.transactions || 0) > 50 ? 'good' : 'warning',
              icon: ShoppingCart,
              color: 'text-purple-600'
            }
          ]

          // Convert real data to analytics format
          const realAnalytics: AnalyticsData = {
            salesTrend: dashboardResponse.data.salesTrend || [],
            topProducts: dashboardResponse.data.topProducts || [],
            customerSegments: [],
            forecast: []
          }

          setKpiData(realKPIs)
          setAnalyticsData(realAnalytics)
          console.log('📊 Real analytics data loaded successfully')
        } else {
          throw new Error('Failed to load analytics data from API')
        }
      } catch (apiError) {
        console.error('Analytics API failed - Real database required:', apiError)
        // Force real database only - no mock data fallback
        setKpiData([])
        setAnalyticsData({
          salesTrend: [],
          topProducts: [],
          customerSegments: [],
          forecast: []
        })
      }
    } catch (error) {
      console.error('Error fetching analytics data:', error)
      // Use mock data on any error
      const mockKPIs = generateMockKPIs()
      const mockAnalytics = generateMockAnalytics()
      setKpiData(mockKPIs)
      setAnalyticsData(mockAnalytics)
    } finally {
      setDataLoading(false)
      setIsCurrencyRefresh(false)
    }
  }, [formatCurrency, isCurrencyRefresh])

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Listen for currency changes and refresh data immediately (without reloading)
  useEffect(() => {
    const handleCurrencyChange = () => {
      console.log('📊 Analytics currency change detected, refreshing KPI data immediately')
      // Immediate currency refresh without loading state
      setIsCurrencyRefresh(true)

      // Update KPI data immediately with new currency formatting
      setKpiData(prevData => prevData.map(kpi => ({
        ...kpi,
        value: kpi.value.includes('Loading') || kpi.value.includes('ရယူနေသည်') ? kpi.value :
               kpi.title.includes('Revenue') || kpi.title.includes('ဝင်ငွေ') ? formatCurrency(parseFloat(kpi.value.replace(/[^\d.-]/g, '')) || 0) :
               kpi.title.includes('Order Value') || kpi.title.includes('အော်ဒါတန်ဖိုး') ? formatCurrency(parseFloat(kpi.value.replace(/[^\d.-]/g, '')) || 0) :
               kpi.value,
        target: kpi.target && (kpi.target.includes('Loading') || kpi.target.includes('ရယူနေသည်')) ? kpi.target :
                kpi.title.includes('Revenue') || kpi.title.includes('ဝင်ငွေ') ? formatCurrency(parseFloat((kpi.target || '0').replace(/[^\d.-]/g, '')) || 0) :
                kpi.title.includes('Order Value') || kpi.title.includes('အော်ဒါတန်ဖိုး') ? formatCurrency(parseFloat((kpi.target || '0').replace(/[^\d.-]/g, '')) || 0) :
                kpi.target || ''
      })))

      // Don't fetch fresh data immediately - use debouncing
      // fetchAnalyticsData() // Commented out to prevent excessive API calls
    }

    const handleGlobalCurrencySync = (event: CustomEvent) => {
      console.log('📊 Analytics global currency sync detected:', event.detail)
      handleCurrencyChange()
    }

    // Listen for currency change events
    window.addEventListener('currency-changed', handleCurrencyChange)
    window.addEventListener('currencyChanged', handleCurrencyChange)
    window.addEventListener('global-currency-sync', handleGlobalCurrencySync as EventListener)
    window.addEventListener('currency-display-update', handleCurrencyChange)

    return () => {
      window.removeEventListener('currency-changed', handleCurrencyChange)
      window.removeEventListener('currencyChanged', handleCurrencyChange)
      window.removeEventListener('global-currency-sync', handleGlobalCurrencySync as EventListener)
      window.removeEventListener('currency-display-update', handleCurrencyChange)
    }
  }, [formatCurrency]) // Remove fetchAnalyticsData to prevent excessive re-renders

  useEffect(() => {
    if (isAuthenticated) {
      fetchAnalyticsData()
    }
  }, [isAuthenticated, timeRange]) // Remove fetchAnalyticsData from dependencies

  // Separate effect for language changes
  useEffect(() => {
    if (isAuthenticated) {
      console.log('🌐 Language changed to:', language)
      // Regenerate KPI data with new language
      const mockKPIs = generateMockKPIs()
      setKpiData(mockKPIs)
    }
  }, [language, isAuthenticated, generateMockKPIs])

  // Separate effect for currency changes - immediate update without loading
  useEffect(() => {
    console.log('🔄 Currency changed to:', currentCurrency)
    // Immediate currency refresh without loading state
    setIsCurrencyRefresh(true)

    // Update KPI data immediately with new currency formatting
    setKpiData(prevData => prevData.map(kpi => ({
      ...kpi,
      value: kpi.value.includes('Loading') || kpi.value.includes('ရယူနေသည်') ? kpi.value :
             kpi.title.includes('Revenue') || kpi.title.includes('ဝင်ငွေ') ? formatCurrency(parseFloat(kpi.value.replace(/[^\d.-]/g, '')) || 0) :
             kpi.title.includes('Order Value') || kpi.title.includes('အော်ဒါတန်ဖိုး') ? formatCurrency(parseFloat(kpi.value.replace(/[^\d.-]/g, '')) || 0) :
             kpi.value,
      target: kpi.target && (kpi.target.includes('Loading') || kpi.target.includes('ရယူနေသည်')) ? kpi.target :
              kpi.title.includes('Revenue') || kpi.title.includes('ဝင်ငွေ') ? formatCurrency(parseFloat((kpi.target || '0').replace(/[^\d.-]/g, '')) || 0) :
              kpi.title.includes('Order Value') || kpi.title.includes('အော်ဒါတန်ဖိုး') ? formatCurrency(parseFloat((kpi.target || '0').replace(/[^\d.-]/g, '')) || 0) :
              kpi.target || ''
    })))

    // Don't fetch fresh data on currency change - just update formatting
    // fetchAnalyticsData() // Removed to prevent excessive API calls
    setIsCurrencyRefresh(false)
  }, [currentCurrency, formatCurrency]) // Remove fetchAnalyticsData from dependencies





  const generateMockAnalytics = React.useCallback((): AnalyticsData => {
    // Check if system was reset - if so, return empty analytics
    const resetFlag = typeof window !== 'undefined' ? localStorage.getItem('bitstech_system_reset') : null
    if (resetFlag) {
      // Return zero data if reset
      return {
        salesTrend: [
          { date: '2024-01-01', sales: 0, orders: 0 },
          { date: '2024-01-02', sales: 0, orders: 0 },
          { date: '2024-01-03', sales: 0, orders: 0 },
          { date: '2024-01-04', sales: 0, orders: 0 },
          { date: '2024-01-05', sales: 0, orders: 0 },
          { date: '2024-01-06', sales: 0, orders: 0 },
          { date: '2024-01-07', sales: 0, orders: 0 }
        ],
        topProducts: [],
        customerSegments: [
          { segment: 'VIP', count: 0, revenue: 0 },
          { segment: 'Regular', count: 0, revenue: 0 },
          { segment: 'Wholesale', count: 0, revenue: 0 },
          { segment: 'New', count: 0, revenue: 0 }
        ],
        forecast: [
          { period: 'Next Week', revenue: 0, orders: 0, customers: 0, confidence: 0 },
          { period: 'Next Month', revenue: 0, orders: 0, customers: 0, confidence: 0 },
          { period: 'Next Quarter', revenue: 0, orders: 0, customers: 0, confidence: 0 }
        ]
      }
    }

    // Return realistic mock data if not reset
    return {
      salesTrend: [
        { date: '2024-01-01', sales: 1200000, orders: 45 },
        { date: '2024-01-02', sales: 1350000, orders: 52 },
        { date: '2024-01-03', sales: 980000, orders: 38 },
        { date: '2024-01-04', sales: 1450000, orders: 58 },
        { date: '2024-01-05', sales: 1680000, orders: 62 },
        { date: '2024-01-06', sales: 1520000, orders: 55 },
        { date: '2024-01-07', sales: 1750000, orders: 68 }
      ],
      topProducts: [
        { name: 'ASUS VivoBook 15', sales: 2500000, growth: 18.5 },
        { name: 'Samsung 27" Monitor', sales: 1800000, growth: 12.3 },
        { name: 'HP LaserJet Pro', sales: 1200000, growth: -5.2 },
        { name: 'Kingston 1TB SSD', sales: 950000, growth: 25.7 },
        { name: 'Logitech Wireless Mouse', sales: 680000, growth: 8.9 }
      ],
      customerSegments: [
        { segment: 'VIP', count: 45, revenue: 8500000 },
        { segment: 'Regular', count: 234, revenue: 12300000 },
        { segment: 'Wholesale', count: 12, revenue: 15600000 },
        { segment: 'New', count: 89, revenue: 2100000 }
      ],
      forecast: [
        { period: 'Next Week', revenue: 8500000, orders: 320, customers: 85, confidence: 92 },
        { period: 'Next Month', revenue: 35000000, orders: 1280, customers: 340, confidence: 87 },
        { period: 'Next Quarter', revenue: 105000000, orders: 3840, customers: 1020, confidence: 78 }
      ]
    }
  }, [])



  // Only show loading spinner for authentication, not for data loading


  if (!isAuthenticated) {
    return null
  }

  // Show empty state if no data
  if (!kpiData || kpiData.length === 0) {
    return (
      <MainLayout language={language}>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  Advanced Analytics
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  KPIs, forecasting, and business intelligence insights
                </p>
              </div>
            </div>

            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  No Analytics Data
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  Analytics data will appear once you have sales data
                </p>
                <Button onClick={() => router.push('/pos')} className="bg-blue-600 hover:bg-blue-700">
                  <Plus className="h-4 w-4 mr-2" />
                  Start Selling
                </Button>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-indigo-600 to-purple-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <BarChart3 className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'အဆင့်မြင့်ခွဲခြမ်းစိတ်ဖြာမှု' : 'Advanced Analytics'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm' ? 'KPI များ၊ ခန့်မှန်းချက်များနှင့် စီးပွားရေးဉာဏ်ရည်ထိုးထွင်းမြင်မှုများ' : 'KPIs, forecasting, and business intelligence insights'}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Select value={timeRange} onValueChange={setTimeRange}>
                  <SelectTrigger className="w-40 bg-white/20 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7d">{language === 'mm' ? 'လွန်ခဲ့သော ၇ ရက်' : 'Last 7 days'}</SelectItem>
                    <SelectItem value="30d">{language === 'mm' ? 'လွန်ခဲ့သော ၃၀ ရက်' : 'Last 30 days'}</SelectItem>
                    <SelectItem value="90d">{language === 'mm' ? 'လွန်ခဲ့သော ၉၀ ရက်' : 'Last 90 days'}</SelectItem>
                    <SelectItem value="1y">{language === 'mm' ? 'လွန်ခဲ့သောနှစ်' : 'Last year'}</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  onClick={fetchAnalyticsData}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ပြန်လည်ရယူရန်' : 'Refresh'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* KPI Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {kpiCards}
        </div>

        {/* Analytics Charts */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sales Trend Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'ရောင်းအား လမ်းကြောင်း' : 'Sales Trend'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' ? 'နေ့စဉ် ရောင်းအား ခွဲခြမ်းစိတ်ဖြာမှု' : 'Daily sales analysis'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                {analyticsData?.salesTrend && analyticsData.salesTrend.length > 0 ? (
                  <div className="w-full h-full flex items-end justify-between gap-2 px-4">
                    {analyticsData.salesTrend.map((day: any, index: number) => {
                      const maxValue = Math.max(...analyticsData.salesTrend.map((d: any) => d.revenue))
                      const height = Math.max(10, (day.revenue / maxValue) * 220)
                      return (
                        <div key={index} className="flex flex-col items-center gap-2 flex-1">
                          <div
                            className="bg-gradient-to-t from-blue-600 to-blue-400 rounded-t-md w-full transition-all duration-300 hover:from-blue-700 hover:to-blue-500 cursor-pointer"
                            style={{ height: `${height}px` }}
                            title={`${day.date}: ${formatCurrency(day.revenue)}`}
                          ></div>
                          <span className="text-xs text-gray-500 font-medium transform -rotate-45 origin-center">
                            {new Date(day.date).toLocaleDateString('en-US', { month: 'short', day: 'numeric' })}
                          </span>
                        </div>
                      )
                    })}
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>{language === 'mm' ? 'ရောင်းအား ဒေတာ မရှိသေးပါ' : 'No sales data available'}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Customer Segments Pie Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-green-600" />
                {language === 'mm' ? 'ဖောက်သည် အုပ်စုများ' : 'Customer Segments'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' ? 'ဖောက်သည် အမျိုးအစားအလိုက် ခွဲခြမ်းစိတ်ဖြာမှု' : 'Revenue by customer type'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64">
                {analyticsData?.customerSegments && analyticsData.customerSegments.length > 0 ? (
                  <div className="flex items-center justify-center h-full">
                    <div className="relative w-48 h-48">
                      {/* Simple Pie Chart Representation */}
                      <div className="w-full h-full rounded-full border-8 border-gray-200 relative overflow-hidden">
                        {analyticsData.customerSegments.map((segment: any, index: number) => {
                          const colors = ['border-blue-500', 'border-green-500', 'border-purple-500', 'border-orange-500']
                          const totalRevenue = analyticsData.customerSegments.reduce((sum: number, s: any) => sum + s.revenue, 0)
                          const percentage = (segment.revenue / totalRevenue) * 100
                          return (
                            <div
                              key={segment.segment}
                              className={`absolute inset-0 rounded-full border-8 ${colors[index % colors.length]}`}
                              style={{
                                clipPath: `polygon(50% 50%, 50% 0%, ${50 + percentage * 0.5}% 0%, 50% 50%)`
                              }}
                              title={`${segment.segment}: ${percentage.toFixed(1)}%`}
                            />
                          )
                        })}
                      </div>

                      {/* Legend */}
                      <div className="absolute -right-24 top-0 space-y-2">
                        {analyticsData.customerSegments.map((segment: any, index: number) => {
                          const colors = ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500']
                          const totalRevenue = analyticsData.customerSegments.reduce((sum: number, s: any) => sum + s.revenue, 0)
                          const percentage = (segment.revenue / totalRevenue) * 100
                          return (
                            <div key={segment.segment} className="flex items-center gap-2 text-sm">
                              <div className={`w-3 h-3 rounded-full ${colors[index % colors.length]}`}></div>
                              <span className="text-gray-700 dark:text-gray-300">
                                {segment.segment} ({percentage.toFixed(1)}%)
                              </span>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="flex items-center justify-center h-full text-gray-500">
                    <div className="text-center">
                      <Users className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>{language === 'mm' ? 'ဖောက်သည် ဒေတာ မရှိသေးပါ' : 'No customer data available'}</p>
                    </div>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Top Products and Forecast */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Products */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'အရောင်းရဆုံး ကုန်ပစ္စည်းများ' : 'Top Products'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' ? 'အရောင်းရဆုံး ကုန်ပစ္စည်း ၁၀ ခု' : 'Best selling products'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData?.topProducts && analyticsData.topProducts.length > 0 ? (
                  analyticsData.topProducts.slice(0, 5).map((product: any, index: number) => (
                    <div key={product.name} className="flex items-center justify-between">
                      <div className="flex items-center gap-3">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-bold ${
                          index === 0 ? 'bg-yellow-500' :
                          index === 1 ? 'bg-gray-400' :
                          index === 2 ? 'bg-orange-500' :
                          'bg-blue-500'
                        }`}>
                          {index + 1}
                        </div>
                        <div>
                          <p className="font-medium text-gray-900 dark:text-white">{product.name}</p>
                          <p className="text-sm text-gray-500">{formatCurrency(product.sales)}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className={`text-sm font-medium ${
                          product.growth > 0 ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {product.growth > 0 ? '+' : ''}{product.growth.toFixed(1)}%
                        </p>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>{language === 'mm' ? 'ကုန်ပစ္စည်း ဒေတာ မရှိသေးပါ' : 'No product data available'}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Forecast */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5 text-orange-600" />
                {language === 'mm' ? 'ခန့်မှန်းချက်များ' : 'Forecasts'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' ? 'အနာဂတ် ရောင်းအား ခန့်မှန်းချက်များ' : 'Future sales predictions'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {analyticsData?.forecast && analyticsData.forecast.length > 0 ? (
                  analyticsData.forecast.map((forecast: any, index: number) => (
                    <div key={forecast.period} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-gray-900 dark:text-white">{forecast.period}</h4>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500" />
                          <span className="text-sm text-gray-500">{forecast.confidence}%</span>
                        </div>
                      </div>
                      <div className="grid grid-cols-3 gap-4 text-sm">
                        <div>
                          <p className="text-gray-500">{language === 'mm' ? 'ရောင်းအား' : 'Revenue'}</p>
                          <p className="font-medium">{formatCurrency(forecast.revenue)}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">{language === 'mm' ? 'အမှာစာများ' : 'Orders'}</p>
                          <p className="font-medium">{forecast.orders}</p>
                        </div>
                        <div>
                          <p className="text-gray-500">{language === 'mm' ? 'ဖောက်သည်များ' : 'Customers'}</p>
                          <p className="font-medium">{forecast.customers}</p>
                        </div>
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Target className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>{language === 'mm' ? 'ခန့်မှန်းချက် ဒေတာ မရှိသေးပါ' : 'No forecast data available'}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}