'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  Users,
  Plus,
  Search,
  Filter,
  MoreVertical,
  Edit,
  Trash2,
  Shield,
  ShieldCheck,
  UserCheck,
  UserX,
  Mail,
  Phone,
  Calendar,
  Activity,
  Eye,
  Download
} from 'lucide-react'

interface User {
  _id: string
  name: string
  email: string
  phone: string
  role: 'admin' | 'manager' | 'cashier'
  status: 'active' | 'inactive' | 'suspended'
  avatar?: string
  createdAt: string
  lastLogin?: string
  permissions: string[]
  department?: string
  salary?: number
  address?: string
  emergencyContact?: string
}

export default function UsersPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [users, setUsers] = useState<User[]>([])
  const [filteredUsers, setFilteredUsers] = useState<User[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [roleFilter, setRoleFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchUsers()
    }
  }, [isAuthenticated])

  useEffect(() => {
    filterUsers()
  }, [users, searchQuery, roleFilter, statusFilter])

  // Listen for currency changes and refresh data
  useEffect(() => {
    const handleCurrencyChange = () => {
      console.log('📊 Users currency change detected, refreshing data')
      // No need to refetch data, just trigger re-render with new currency formatting
      // The formatCurrency function will handle the new currency
    }

    // Listen for currency change events
    window.addEventListener('currency-changed', handleCurrencyChange)
    window.addEventListener('currencyChanged', handleCurrencyChange)

    return () => {
      window.removeEventListener('currency-changed', handleCurrencyChange)
      window.removeEventListener('currencyChanged', handleCurrencyChange)
    }
  }, [])

  const fetchUsers = async () => {
    try {
      setLoading(true)

      // Fetch users from real API
      const response = await apiClient.getUsers({
        page: 1,
        limit: 100,
        search: searchQuery,
        role: roleFilter === 'all' ? undefined : roleFilter,
        status: statusFilter === 'all' ? undefined : statusFilter
      })

      if (response.success && response.data) {
        // Ensure response.data is an array
        const usersData = Array.isArray(response.data) ? response.data : []
        setUsers(usersData)
        console.log('✅ Users loaded:', usersData.length, 'users')
      } else {
        console.error('Failed to fetch users:', response.error)
        setUsers([])
      }
    } catch (error) {
      console.error('Error fetching users:', error)
      setUsers([])
    } finally {
      setLoading(false)
    }
  }

  const filterUsers = () => {
    // Ensure users is an array before filtering
    if (!Array.isArray(users)) {
      setFilteredUsers([])
      return
    }

    let filtered = users

    if (searchQuery) {
      filtered = filtered.filter(user =>
        (user.name || (user as any).fullName || `${(user as any).firstName} ${(user as any).lastName}`)?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.phone?.includes(searchQuery)
      )
    }

    if (roleFilter !== 'all') {
      filtered = filtered.filter(user => user.role === roleFilter)
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(user => user.status === statusFilter)
    }

    setFilteredUsers(filtered)
  }

  const getRoleIcon = (role: string) => {
    switch (role) {
      case 'admin': return <ShieldCheck className="h-4 w-4 text-red-600" />
      case 'manager': return <Shield className="h-4 w-4 text-blue-600" />
      case 'cashier': return <UserCheck className="h-4 w-4 text-green-600" />
      default: return <Users className="h-4 w-4 text-gray-600" />
    }
  }

  const getRoleBadge = (role: string) => {
    switch (role) {
      case 'admin':
        return <Badge className="bg-red-100 text-red-800">Administrator</Badge>
      case 'manager':
        return <Badge className="bg-blue-100 text-blue-800">Manager</Badge>
      case 'cashier':
        return <Badge className="bg-green-100 text-green-800">Cashier</Badge>
      default:
        return <Badge variant="outline">{role}</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800">Inactive</Badge>
      case 'suspended':
        return <Badge className="bg-red-100 text-red-800">Suspended</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  // Remove local formatCurrency function - using imported one

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatLastLogin = (dateString?: string) => {
    if (!dateString) return 'Never'
    
    const now = new Date()
    const loginDate = new Date(dateString)
    const diffInHours = Math.floor((now.getTime() - loginDate.getTime()) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Just now'
    if (diffInHours < 24) return `${diffInHours}h ago`
    if (diffInHours < 168) return `${Math.floor(diffInHours / 24)}d ago`
    return formatDate(dateString)
  }

  const getUserStats = () => {
    // Ensure users is an array before processing
    if (!Array.isArray(users) || users.length === 0) {
      return { total: 0, active: 0, admins: 0, managers: 0, cashiers: 0 }
    }

    const total = users.length
    const active = users.filter(u => u.status === 'active').length
    const admins = users.filter(u => u.role === 'admin').length
    const managers = users.filter(u => u.role === 'manager').length
    const cashiers = users.filter(u => u.role === 'cashier').length

    return { total, active, admins, managers, cashiers }
  }



  if (!isAuthenticated) {
    return null
  }

  // Check if user has permission to access users page
  const userRole = user?.role
  const hasUserAccess = userRole === 'admin' || userRole === 'manager' || userRole === 'supervisor'

  if (!hasUserAccess) {
    return (
      <MainLayout language={user?.preferences?.language as 'en' | 'mm' || 'en'}>
        <div className="flex items-center justify-center min-h-[60vh]">
          <Card className="w-full max-w-md">
            <CardContent className="p-8 text-center">
              <div className="w-16 h-16 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
                <ShieldCheck className="h-8 w-8 text-red-600" />
              </div>
              <h3 className="text-lg font-semibold mb-2">
                {user?.preferences?.language === 'mm' ? 'ဝင်ရောက်ခွင့် မရှိပါ' : 'Access Denied'}
              </h3>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {user?.preferences?.language === 'mm'
                  ? 'သင့်တွင် အသုံးပြုသူ စီမံခန့်ခွဲမှု စာမျက်နှာကို ဝင်ရောက်ခွင့် မရှိပါ။'
                  : 'You do not have permission to access the user management page.'
                }
              </p>
              <Button onClick={() => window.history.back()} variant="outline">
                {user?.preferences?.language === 'mm' ? 'ပြန်သွားရန်' : 'Go Back'}
              </Button>
            </CardContent>
          </Card>
        </div>
      </MainLayout>
    )
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'
  const stats = getUserStats()

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-600 to-blue-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Users className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'အသုံးပြုသူ စီမံခန့်ခွဲမှု' : 'User Management'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm' 
                      ? 'အသုံးပြုသူများ၊ အခွင့်အရေးများ နှင့် ဝင်ရောက်ခွင့်များ စီမံခန့်ခွဲရန်'
                      : 'Manage users, roles, and permissions'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => setShowAddModal(true)}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'အသုံးပြုသူ အသစ်' : 'Add User'}
                </Button>
                <Button className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                  <Download className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ထုတ်ယူရန်' : 'Export'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* User Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း အသုံးပြုသူများ' : 'Total Users'}
                  </p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'လက်ရှိ အသုံးပြုနေသူများ' : 'Active Users'}
                  </p>
                  <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                  <Activity className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စီမံခန့်ခွဲသူများ' : 'Administrators'}
                  </p>
                  <p className="text-2xl font-bold text-red-600">{stats.admins}</p>
                </div>
                <div className="p-3 bg-red-100 dark:bg-red-900/20 rounded-xl">
                  <ShieldCheck className="h-6 w-6 text-red-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'မန်နေဂျာများ' : 'Managers'}
                  </p>
                  <p className="text-2xl font-bold text-blue-600">{stats.managers}</p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                  <Shield className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'ငွေကောက်သူများ' : 'Cashiers'}
                  </p>
                  <p className="text-2xl font-bold text-green-600">{stats.cashiers}</p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                  <UserCheck className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={language === 'mm' ? 'အမည်၊ အီးမေးလ် သို့မဟုတ် ဖုန်းနံပါတ် ရှာရန်...' : 'Search by name, email, or phone...'}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-4">
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder={language === 'mm' ? 'အခန်းကဏ္ဍ' : 'Role'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Roles'}</SelectItem>
                    <SelectItem value="admin">{language === 'mm' ? 'စီမံခန့်ခွဲသူ' : 'Administrator'}</SelectItem>
                    <SelectItem value="manager">{language === 'mm' ? 'မန်နေဂျာ' : 'Manager'}</SelectItem>
                    <SelectItem value="cashier">{language === 'mm' ? 'ငွေကောက်သူ' : 'Cashier'}</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder={language === 'mm' ? 'အခြေအနေ' : 'Status'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Status'}</SelectItem>
                    <SelectItem value="active">{language === 'mm' ? 'လက်ရှိ အသုံးပြုနေ' : 'Active'}</SelectItem>
                    <SelectItem value="inactive">{language === 'mm' ? 'အသုံးမပြုတော့' : 'Inactive'}</SelectItem>
                    <SelectItem value="suspended">{language === 'mm' ? 'ရပ်ဆိုင်းထား' : 'Suspended'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Users List */}
        <div className="space-y-4">
          {loading ? (
            // Loading State
            <div className="space-y-4">
              {[...Array(3)].map((_, index) => (
                <Card key={index} className="animate-pulse">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-full"></div>
                        <div className="space-y-2">
                          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-32"></div>
                          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-48"></div>
                          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-24"></div>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                        <div className="h-8 w-16 bg-gray-200 dark:bg-gray-700 rounded"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredUsers.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {language === 'mm' ? 'အသုံးပြုသူ မတွေ့ရပါ' : 'No Users Found'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {language === 'mm'
                    ? 'လက်ရှိ စစ်ထုတ်မှု အတိုင်း အသုံးပြုသူ မတွေ့ရပါ'
                    : 'No users match your current filters'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredUsers.map((user) => (
              <Card key={user._id} className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                        {user.name?.charAt(0)?.toUpperCase() || (user as any).firstName?.charAt(0)?.toUpperCase() || 'U'}
                      </div>
                      
                      <div>
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-lg">{user.name || (user as any).fullName || `${(user as any).firstName} ${(user as any).lastName}`}</h3>
                          {getRoleIcon(user.role)}
                          {getRoleBadge(user.role)}
                          {getStatusBadge(user.status)}
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-1">
                            <Mail className="h-4 w-4" />
                            <span>{user.email}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Phone className="h-4 w-4" />
                            <span>{user.phone}</span>
                          </div>
                          <div className="flex items-center gap-1">
                            <Calendar className="h-4 w-4" />
                            <span>{language === 'mm' ? 'နောက်ဆုံး ဝင်ရောက်' : 'Last login'}: {formatLastLogin(user.lastLogin)}</span>
                          </div>
                        </div>
                        
                        <div className="flex items-center gap-4 text-sm text-gray-500 mt-2">
                          <span>{language === 'mm' ? 'ဌာနခွဲ' : 'Department'}: {user.department}</span>
                          <span>{language === 'mm' ? 'လစာ' : 'Salary'}: {formatCurrency(user.salary || 0)}</span>
                          <span>{language === 'mm' ? 'ပါဝင်ချိန်' : 'Joined'}: {formatDate(user.createdAt)}</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/users/${user._id}`)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        {language === 'mm' ? 'ကြည့်ရန်' : 'View'}
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/users/${user._id}/edit`)}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        {language === 'mm' ? 'ပြင်ဆင်' : 'Edit'}
                      </Button>
                      
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700 hover:bg-red-50"
                      >
                        <Trash2 className="h-4 w-4 mr-2" />
                        {language === 'mm' ? 'ဖျက်ရန်' : 'Delete'}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </MainLayout>
  )
}
