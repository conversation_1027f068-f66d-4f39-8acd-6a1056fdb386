const mongoose = require('mongoose');

const supplierSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Supplier name is required'],
        trim: true,
        maxlength: [100, 'Supplier name cannot exceed 100 characters']
    },
    code: {
        type: String,
        required: [true, 'Supplier code is required'],
        unique: true,
        trim: true,
        uppercase: true,
        maxlength: [20, 'Supplier code cannot exceed 20 characters']
    },
    contactPerson: {
        name: {
            type: String,
            required: [true, 'Contact person name is required'],
            trim: true
        },
        title: {
            type: String,
            trim: true
        },
        email: {
            type: String,
            trim: true,
            lowercase: true,
            match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email']
        },
        phone: {
            type: String,
            trim: true
        },
        mobile: {
            type: String,
            trim: true
        }
    },
    company: {
        address: {
            street: String,
            city: String,
            state: String,
            country: String,
            zipCode: String
        },
        phone: String,
        fax: String,
        website: String,
        taxId: String,
        registrationNumber: String
    },
    paymentTerms: {
        type: String,
        enum: ['cash', 'net_15', 'net_30', 'net_45', 'net_60', 'net_90', 'custom'],
        default: 'net_30'
    },
    customPaymentTerms: {
        type: String,
        trim: true
    },
    creditLimit: {
        type: Number,
        default: 0,
        min: [0, 'Credit limit cannot be negative']
    },
    currency: {
        type: String,
        enum: ['MMK', 'USD', 'THB', 'EUR', 'SGD'],
        default: 'MMK'
    },
    categories: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Category'
    }],
    products: [{
        product: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Product'
        },
        supplierSku: String,
        supplierPrice: Number,
        minimumOrderQuantity: {
            type: Number,
            default: 1
        },
        leadTime: {
            type: Number, // in days
            default: 7
        }
    }],
    rating: {
        type: Number,
        min: 1,
        max: 5,
        default: 3
    },
    notes: {
        type: String,
        maxlength: [1000, 'Notes cannot exceed 1000 characters']
    },
    isActive: {
        type: Boolean,
        default: true
    },
    lastOrderDate: {
        type: Date
    },
    totalOrders: {
        type: Number,
        default: 0
    },
    totalOrderValue: {
        type: Number,
        default: 0
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Created by user is required']
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes
supplierSchema.index({ code: 1 });
supplierSchema.index({ name: 1 });
supplierSchema.index({ isActive: 1 });
supplierSchema.index({ 'contactPerson.email': 1 });

// Virtual for full contact info
supplierSchema.virtual('fullContactInfo').get(function() {
    return {
        name: this.contactPerson.name,
        title: this.contactPerson.title,
        email: this.contactPerson.email,
        phone: this.contactPerson.phone || this.contactPerson.mobile,
        company: this.name
    };
});

// Virtual for address string
supplierSchema.virtual('fullAddress').get(function() {
    const addr = this.company.address;
    if (!addr) return '';

    const parts = [addr.street, addr.city, addr.state, addr.country, addr.zipCode].filter(Boolean);
    return parts.join(', ');
});

// Pre-save middleware
supplierSchema.pre('save', function(next) {
    if (this.isNew) {
        // Generate supplier code if not provided
        if (!this.code) {
            const nameCode = this.name.substring(0, 3).toUpperCase();
            const timestamp = Date.now().toString().slice(-4);
            this.code = `SUP-${nameCode}-${timestamp}`;
        }
    }
    next();
});

// Static methods
supplierSchema.statics.findActive = function() {
    return this.find({ isActive: true }).sort({ name: 1 });
};

supplierSchema.statics.findByCategory = function(categoryId) {
    return this.find({
        categories: categoryId,
        isActive: true
    }).sort({ name: 1 });
};

// Instance methods
supplierSchema.methods.addProduct = function(productId, supplierData = {}) {
    const existingProduct = this.products.find(p => p.product.toString() === productId.toString());

    if (existingProduct) {
        // Update existing product
        Object.assign(existingProduct, supplierData);
    } else {
        // Add new product
        this.products.push({
            product: productId,
            ...supplierData
        });
    }

    return this.save();
};

supplierSchema.methods.removeProduct = function(productId) {
    this.products = this.products.filter(p => p.product.toString() !== productId.toString());
    return this.save();
};

supplierSchema.methods.updateOrderStats = function(orderValue) {
    this.totalOrders += 1;
    this.totalOrderValue += orderValue;
    this.lastOrderDate = new Date();
    return this.save();
};

module.exports = mongoose.model('Supplier', supplierSchema);
