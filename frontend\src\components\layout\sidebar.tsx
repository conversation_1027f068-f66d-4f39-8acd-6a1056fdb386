'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { cn } from '@/lib/utils'
import { Button } from '@/components/ui/button'
import {
  LayoutDashboard,
  ShoppingCart,
  Package,
  TrendingUp,
  BarChart3,
  Users,
  Settings,
  ChevronLeft,
  ChevronRight,
  Store,
  Package2,
  Target,
  Smartphone,
  TestTube,
  FileText,
  Receipt,
  Truck
} from 'lucide-react'

interface SidebarProps {
  language?: 'en' | 'mm'
  isOpen?: boolean
  onClose?: () => void
}

export function Sidebar({ language = 'en', isOpen = false, onClose }: SidebarProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [companyName, setCompanyName] = useState('BitsTech')
  const [companySubtitle, setCompanySubtitle] = useState('POS System')
  const pathname = usePathname()

  // Load company info on mount
  useEffect(() => {
    loadCompanyInfo()

    // Listen for storage changes to update company name in real-time
    const handleStorageChange = () => {
      loadCompanyInfo()
    }

    window.addEventListener('storage', handleStorageChange)

    // Also listen for custom events from the same tab
    window.addEventListener('companyInfoUpdated', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('companyInfoUpdated', handleStorageChange)
    }
  }, [])

  const loadCompanyInfo = () => {
    try {
      const stored = localStorage.getItem('bitstech_company_info')
      if (stored) {
        const parsed = JSON.parse(stored)
        setCompanyName(parsed.name || 'BitsTech')
        setCompanySubtitle(parsed.subtitle || 'POS System')
      }
    } catch (error) {
      console.error('Error loading company info:', error)
    }
  }

  // Generate logo letter from company name
  const getLogoLetter = (name: string) => {
    return name.charAt(0).toUpperCase()
  }



  const menuItems = [
    {
      title: language === 'en' ? 'Dashboard' : 'ဒက်ရှ်ဘုတ်',
      href: '/dashboard',
      icon: LayoutDashboard,
      active: pathname === '/dashboard'
    },
    {
      title: language === 'en' ? 'Analytics' : 'ခွဲခြမ်းစိတ်ဖြာမှု',
      href: '/dashboard/analytics',
      icon: BarChart3,
      active: pathname === '/dashboard/analytics'
    },
    {
      title: language === 'en' ? 'KPIs' : 'KPI များ',
      href: '/dashboard/kpis',
      icon: Target,
      active: pathname === '/dashboard/kpis'
    },
    {
      title: language === 'en' ? 'Forecasting' : 'ခန့်မှန်းချက်',
      href: '/dashboard/forecasting',
      icon: TrendingUp,
      active: pathname === '/dashboard/forecasting'
    },
    {
      title: language === 'en' ? 'Products' : 'ကုန်ပစ္စည်းများ',
      href: '/products',
      icon: Package,
      active: pathname.startsWith('/products')
    },
    {
      title: language === 'en' ? 'Sales' : 'ရောင်းအား',
      href: '/sales',
      icon: ShoppingCart,
      active: pathname.startsWith('/sales')
    },
    {
      title: language === 'en' ? 'Invoice' : 'ဖိုင်ဝယ်စ်',
      href: '/invoice',
      icon: Receipt,
      active: pathname.startsWith('/invoice')
    },
    {
      title: language === 'en' ? 'Inventory' : 'စတော့',
      href: '/inventory',
      icon: Package2,
      active: pathname.startsWith('/inventory')
    },
    {
      title: language === 'en' ? 'Purchase Orders' : 'ဝယ်ယူမှု အမှာစာများ',
      href: '/purchase-orders',
      icon: FileText,
      active: pathname.startsWith('/purchase-orders')
    },
    {
      title: language === 'en' ? 'Reports' : 'အစီရင်ခံစာများ',
      href: '/reports',
      icon: BarChart3,
      active: pathname.startsWith('/reports')
    },
    {
      title: language === 'en' ? 'Customers' : 'ဖောက်သည်များ',
      href: '/customers',
      icon: Users,
      active: pathname.startsWith('/customers')
    },
    {
      title: language === 'en' ? 'Suppliers' : 'ပေးသွင်းသူများ',
      href: '/suppliers',
      icon: Truck,
      active: pathname.startsWith('/suppliers')
    },
    {
      title: language === 'en' ? 'Users' : 'အသုံးပြုသူများ',
      href: '/users',
      icon: Users,
      active: pathname.startsWith('/users')
    },
    {
      title: language === 'en' ? 'Settings' : 'ဆက်တင်များ',
      href: '/settings',
      icon: Settings,
      active: pathname.startsWith('/settings')
    },
    {
      title: language === 'en' ? 'System Testing' : 'စနစ် စမ်းသပ်မှု',
      href: '/testing',
      icon: TestTube,
      active: pathname.startsWith('/testing')
    }
  ]

  return (
    <div className={cn(
      "fixed left-0 top-16 z-40 h-[calc(100vh-4rem)] bg-background border-r border-border transition-all duration-300",
      "lg:translate-x-0",
      isOpen ? "translate-x-0" : "-translate-x-full lg:translate-x-0",
      isCollapsed ? "w-16" : "w-64"
    )}>
      <div className="flex flex-col h-full relative">
        {/* Animated Background Pattern */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute inset-0 bg-gradient-to-br from-blue-500/20 via-purple-500/20 to-emerald-500/20 animate-pulse"></div>
        </div>
        {/* Header */}
        <div className="relative z-10 p-4 border-b border-indigo-100 dark:border-gray-700">
          {!isCollapsed && (
            <div className="flex items-center space-x-3 group">
              <div className="relative">
                {/* Logo Container */}
                <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg shadow-indigo-500/25 transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                  <span className="text-white font-bold text-lg">{getLogoLetter(companyName)}</span>
                </div>
                {/* Glow Effect */}
                <div className="absolute -inset-2 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-2xl opacity-20 blur-lg animate-pulse"></div>
              </div>
              <div className="flex flex-col">
                <span className="text-xl font-bold bg-gradient-to-r from-indigo-600 via-purple-600 to-pink-600 dark:from-indigo-400 dark:via-purple-400 dark:to-pink-400 bg-clip-text text-transparent">
                  {companyName}
                </span>
                <span className="text-xs text-indigo-500 dark:text-indigo-400 font-medium">
                  {companySubtitle}
                </span>
              </div>
            </div>
          )}

          {isCollapsed && (
            <div className="flex justify-center">
              <div className="relative group">
                <div className="w-10 h-10 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-2xl flex items-center justify-center shadow-lg shadow-indigo-500/25 transform transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                  <span className="text-white font-bold text-lg">{getLogoLetter(companyName)}</span>
                </div>
                <div className="absolute -inset-2 bg-gradient-to-br from-indigo-500 via-purple-500 to-pink-500 rounded-2xl opacity-20 blur-lg animate-pulse"></div>
              </div>
            </div>
          )}

          {/* Control Buttons */}
          <div className="absolute top-4 right-4 flex items-center gap-1">
            {/* Mobile Close Button */}
            {onClose && (
              <Button
                variant="ghost"
                size="icon"
                onClick={onClose}
                className="lg:hidden h-8 w-8 text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-200 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 rounded-xl transition-all duration-200"
              >
                <ChevronLeft className="h-4 w-4" />
              </Button>
            )}

            {/* Collapse Button */}
            <Button
              variant="ghost"
              size="icon"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="hidden lg:flex h-8 w-8 text-indigo-600 hover:text-indigo-800 dark:text-indigo-400 dark:hover:text-indigo-200 hover:bg-indigo-100 dark:hover:bg-indigo-900/30 rounded-xl transition-all duration-200 hover:scale-110"
            >
              {isCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
            </Button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="relative z-10 flex-1 p-2 space-y-1 overflow-y-auto">
          {menuItems.map((item, index) => {
            const Icon = item.icon
            return (
              <Link key={item.href} href={item.href}>
                <div
                  className={cn(
                    "group relative flex items-center rounded-xl transition-all duration-300 transform hover:scale-105",
                    "border border-transparent hover:border-slate-600/50",
                    item.active
                      ? "bg-gradient-to-r from-emerald-500/20 to-blue-500/20 text-emerald-400 border-emerald-500/30 shadow-lg shadow-emerald-500/10"
                      : "text-slate-400 hover:text-white hover:bg-slate-700/30",
                    isCollapsed ? "justify-center p-2 mx-1" : "space-x-3 px-3 py-2.5 mx-1"
                  )}
                  onClick={onClose}
                >
                  {/* Icon Container */}
                  <div className={cn(
                    "relative flex items-center justify-center rounded-lg transition-all duration-300",
                    item.active
                      ? "bg-gradient-to-br from-emerald-500 to-blue-600 shadow-lg"
                      : "bg-slate-700/50 group-hover:bg-slate-600/50",
                    isCollapsed ? "w-8 h-8" : "w-7 h-7"
                  )}>
                    <Icon className={cn(
                      "transition-all duration-300",
                      item.active ? "text-white" : "text-slate-300 group-hover:text-white",
                      isCollapsed ? "h-4 w-4" : "h-3.5 w-3.5"
                    )} />

                    {/* Glow Effect for Active Item */}
                    {item.active && (
                      <div className="absolute -inset-1 bg-gradient-to-br from-emerald-500 to-blue-600 rounded-lg opacity-30 blur animate-pulse"></div>
                    )}
                  </div>

                  {/* Text Label */}
                  {!isCollapsed && (
                    <span className={cn(
                      "text-sm font-medium transition-all duration-300 truncate",
                      item.active ? "text-emerald-300" : "text-black dark:text-white"
                    )}>
                      {item.title}
                    </span>
                  )}

                  {/* Active Indicator */}
                  {item.active && (
                    <div className="absolute right-0 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-gradient-to-b from-emerald-400 to-blue-500 rounded-l-full"></div>
                  )}

                  {/* Tooltip for Collapsed State */}
                  {isCollapsed && (
                    <div className="absolute left-full ml-2 px-2 py-1 bg-gray-800 dark:bg-slate-800 text-white text-xs rounded-md opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-50 shadow-lg">
                      {item.title}
                    </div>
                  )}
                </div>
              </Link>
            )
          })}
        </nav>

        {/* Footer */}
        <div className="relative z-10 p-4 border-t border-indigo-100 dark:border-gray-700">
          {!isCollapsed && (
            <div className="text-center space-y-2">
              <div className="flex items-center justify-center space-x-2">
                <div className="relative">
                  <div className="w-3 h-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-pulse"></div>
                  <div className="absolute inset-0 w-3 h-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-ping opacity-75"></div>
                </div>
                <p className="text-xs font-bold text-indigo-700 dark:text-indigo-300">{companyName} {companySubtitle}</p>
              </div>
              <div className="bg-gradient-to-r from-indigo-50 to-purple-50 dark:from-indigo-900/20 dark:to-purple-900/20 rounded-xl p-2">
                <p className="text-xs text-indigo-600 dark:text-indigo-400 font-medium">
                  {language === 'en' ? 'For Myanmar Business' : 'မြန်မာစီးပွားရေးအတွက်'}
                </p>
              </div>
            </div>
          )}
          {isCollapsed && (
            <div className="flex justify-center">
              <div className="relative">
                <div className="w-3 h-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-pulse"></div>
                <div className="absolute inset-0 w-3 h-3 bg-gradient-to-r from-indigo-500 to-purple-500 rounded-full animate-ping opacity-75"></div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}
