// Settings Sync Utility for Real-time Cross-Tab Synchronization

export interface SettingsEvent {
  type: 'currency' | 'theme' | 'language' | 'exchange-rates' | 'tax' | 'user'
  data: any
  timestamp: number
  tabId: string
}

class SettingsSync {
  private tabId: string
  private listeners: Map<string, Function[]> = new Map()
  private isClient: boolean

  constructor() {
    this.isClient = typeof window !== 'undefined'
    this.tabId = this.isClient ? `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}` : ''

    // Only initialize sync on client side and after component mount
    if (this.isClient && typeof document !== 'undefined') {
      // Delay initialization to ensure DOM is ready
      setTimeout(() => {
        this.initializeSync()
      }, 0)
    }
  }

  private initializeSync() {
    // Listen for storage changes from other tabs
    window.addEventListener('storage', (event) => {
      if (event.key === 'bitstech_settings_sync' && event.newValue) {
        try {
          const settingsEvent: SettingsEvent = JSON.parse(event.newValue)
          
          // Don't process events from the same tab
          if (settingsEvent.tabId === this.tabId) return
          
          // Emit to local listeners
          this.emit(settingsEvent.type, settingsEvent.data)
        } catch (error) {
          console.error('Failed to parse settings sync event:', error)
        }
      }
    })

    // Listen for custom events within the same tab
    window.addEventListener('settings-sync', ((event: CustomEvent) => {
      const { type, data } = event.detail
      this.broadcast(type, data)
    }) as EventListener)
  }

  // Broadcast settings change to other tabs
  broadcast(type: SettingsEvent['type'], data: any) {
    if (!this.isClient) return

    const event: SettingsEvent = {
      type,
      data,
      timestamp: Date.now(),
      tabId: this.tabId
    }

    try {
      localStorage.setItem('bitstech_settings_sync', JSON.stringify(event))

      // Remove the sync event after a short delay to prevent accumulation
      setTimeout(() => {
        const currentEvent = localStorage.getItem('bitstech_settings_sync')
        if (currentEvent) {
          const parsed = JSON.parse(currentEvent)
          if (parsed.timestamp === event.timestamp) {
            localStorage.removeItem('bitstech_settings_sync')
          }
        }
      }, 100)
    } catch (error) {
      console.error('Failed to broadcast settings sync:', error)
    }
  }

  // Listen for specific setting changes
  on(type: SettingsEvent['type'], callback: Function) {
    if (!this.listeners.has(type)) {
      this.listeners.set(type, [])
    }
    this.listeners.get(type)!.push(callback)
  }

  // Remove listener
  off(type: SettingsEvent['type'], callback: Function) {
    const listeners = this.listeners.get(type)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
  }

  // Emit event to local listeners
  private emit(type: SettingsEvent['type'], data: any) {
    const listeners = this.listeners.get(type)
    if (listeners) {
      listeners.forEach(callback => {
        try {
          callback(data)
        } catch (error) {
          console.error('Settings sync listener error:', error)
        }
      })
    }
  }

  // Sync specific settings
  syncCurrency(currency: string) {
    this.broadcast('currency', { currency })
  }

  syncTheme(theme: string) {
    this.broadcast('theme', { theme })
  }

  syncLanguage(language: string) {
    this.broadcast('language', { language })
  }

  syncExchangeRates(rates: any) {
    this.broadcast('exchange-rates', { rates })
  }

  syncTaxSettings(settings: any) {
    this.broadcast('tax', { settings })
  }

  syncUserSettings(settings: any) {
    this.broadcast('user', { settings })
  }

  // Get current tab ID
  getTabId() {
    return this.tabId
  }

  // Check if settings are in sync across tabs
  async checkSync(): Promise<boolean> {
    if (!this.isClient) return true

    try {
      const syncData = localStorage.getItem('bitstech_settings_sync')
      if (!syncData) return true

      const event: SettingsEvent = JSON.parse(syncData)
      const timeDiff = Date.now() - event.timestamp

      // Consider sync valid if last event was within 5 seconds
      return timeDiff < 5000
    } catch (error) {
      return true
    }
  }

  // Force sync all settings from localStorage
  forceSyncFromStorage() {
    if (!this.isClient) return

    try {
      // Currency
      const currency = localStorage.getItem('bitstech_current_currency')
      if (currency) {
        this.emit('currency', { currency })
      }

      // Theme
      const theme = localStorage.getItem('bitstech_theme')
      if (theme) {
        this.emit('theme', { theme })
      }

      // Language
      const language = localStorage.getItem('bitstech_language')
      if (language) {
        this.emit('language', { language })
      }

      // Exchange rates
      const rates = localStorage.getItem('bitstech_exchange_rates')
      if (rates) {
        this.emit('exchange-rates', { rates: JSON.parse(rates) })
      }

      // Tax settings
      const taxSettings = localStorage.getItem('bitstech_tax_settings')
      if (taxSettings) {
        this.emit('tax', { settings: JSON.parse(taxSettings) })
      }
    } catch (error) {
      console.error('Failed to force sync from storage:', error)
    }
  }
}

// Create singleton instance only on client side
let settingsSyncInstance: SettingsSync | null = null

export function getSettingsSync(): SettingsSync | null {
  if (typeof window !== 'undefined') {
    if (!settingsSyncInstance) {
      settingsSyncInstance = new SettingsSync()
    }
    return settingsSyncInstance
  }
  return null
}

// Safe export function that doesn't instantiate during SSR
export function getSettingsSyncSafe(): SettingsSync | null {
  return typeof window !== 'undefined' ? getSettingsSync() : null
}

// Export for backward compatibility - use function to avoid const reassignment
export function createSettingsSync(): SettingsSync | null {
  return getSettingsSyncSafe()
}

// Helper function to trigger sync events
export function triggerSettingsSync(type: SettingsEvent['type'], data: any): void {
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('settings-sync', {
      detail: { type, data }
    }))
  }
}

// Validation helpers
export function validateCurrency(currency: string): boolean {
  return ['MMK', 'USD', 'THB'].includes(currency)
}

export function validateTheme(theme: string): boolean {
  return ['light', 'dark', 'system'].includes(theme)
}

export function validateLanguage(language: string): boolean {
  return ['en', 'mm', 'th'].includes(language)
}

export function validateExchangeRates(rates: any): boolean {
  if (!rates || typeof rates !== 'object') return false

  const requiredCurrencies = ['MMK', 'USD', 'THB']
  return requiredCurrencies.every(currency => {
    const rate = rates[currency]
    return rate && typeof rate.rate === 'number' && rate.rate > 0
  })
}

export function validateTaxSettings(settings: any): boolean {
  if (!settings || typeof settings !== 'object') return false

  return (
    typeof settings.defaultRate === 'number' &&
    settings.defaultRate >= 0 &&
    settings.defaultRate <= 100 &&
    typeof settings.taxName === 'string' &&
    settings.taxName.trim().length > 0
  )
}

export default getSettingsSyncSafe
