'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import { useTheme } from '@/contexts/theme-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  DollarSign,
  ShoppingCart,
  Users,
  Package,
  Calendar,
  Download,
  FileText,
  PieChart,
  Activity,
  Target,
  ArrowUpRight,
  ArrowDownRight,
  Eye,
  Filter,
  RefreshCw
} from 'lucide-react'

interface SalesMetrics {
  totalRevenue: number
  totalSales: number
  totalCustomers: number
  totalProducts: number
  avgOrderValue: number
  profitMargin: number
  growthRate: number
  topSellingProduct: string
}

interface ReportCard {
  id: string
  title: string
  titleLocal: string
  description: string
  descriptionLocal: string
  icon: any
  color: string
  bgColor: string
  route: string
  metrics?: string
}

export default function ReportsPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const { language } = useTheme()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [dateRange, setDateRange] = useState('today')
  const [loading, setLoading] = useState(true)
  const [exporting, setExporting] = useState(false)

  const [salesMetrics, setSalesMetrics] = useState<SalesMetrics>({
    totalRevenue: 0,
    totalSales: 0,
    totalCustomers: 0,
    totalProducts: 0,
    avgOrderValue: 0,
    profitMargin: 0,
    growthRate: 0,
    topSellingProduct: ''
  })

  // Generate report cards with real data
  const reportCards: ReportCard[] = [
    {
      id: 'sales-analytics',
      title: 'Sales Analytics',
      titleLocal: 'ရောင်းအား ခွဲခြမ်းစိတ်ဖြာမှု',
      description: 'Detailed sales performance and trends analysis',
      descriptionLocal: 'ရောင်းအား စွမ်းဆောင်ရည် နှင့် လမ်းကြောင်း ခွဲခြမ်းစိတ်ဖြာမှု',
      icon: BarChart3,
      color: 'text-blue-600',
      bgColor: 'bg-blue-100 dark:bg-blue-900/20',
      route: '/reports/sales-analytics',
      metrics: `↗ ${salesMetrics.growthRate}% vs last period`
    },
    {
      id: 'revenue-tracking',
      title: 'Revenue Tracking',
      titleLocal: 'ဝင်ငွေ ခြေရာခံမှု',
      description: 'Revenue trends and financial performance',
      descriptionLocal: 'ဝင်ငွေ လမ်းကြောင်း နှင့် ငွေကြေး စွမ်းဆောင်ရည်',
      icon: TrendingUp,
      color: 'text-green-600',
      bgColor: 'bg-green-100 dark:bg-green-900/20',
      route: '/reports/revenue-tracking',
      metrics: `${formatCurrency(salesMetrics.totalRevenue)} today`
    },
    {
      id: 'product-performance',
      title: 'Product Performance',
      titleLocal: 'ကုန်ပစ္စည်း စွမ်းဆောင်ရည်',
      description: 'Best and worst performing products analysis',
      descriptionLocal: 'အကောင်းဆုံး နှင့် အဆိုးဆုံး ရောင်းအား ကုန်ပစ္စည်းများ',
      icon: Package,
      color: 'text-purple-600',
      bgColor: 'bg-purple-100 dark:bg-purple-900/20',
      route: '/reports/product-performance',
      metrics: `${salesMetrics.totalProducts} products tracked`
    },
    {
      id: 'customer-analytics',
      title: 'Customer Analytics',
      titleLocal: 'ဖောက်သည် ခွဲခြမ်းစိတ်ဖြာမှု',
      description: 'Customer behavior and purchase patterns',
      descriptionLocal: 'ဖောက်သည် အပြုအမူ နှင့် ဝယ်ယူမှု ပုံစံများ',
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-100 dark:bg-orange-900/20',
      route: '/reports/customer-analytics',
      metrics: `${salesMetrics.totalCustomers} active customers`
    },
    {
      id: 'profit-analysis',
      title: 'Profit Analysis',
      titleLocal: 'အမြတ် ခွဲခြမ်းစိတ်ဖြာမှု',
      description: 'Detailed profit margins and cost analysis',
      descriptionLocal: 'အမြတ်နှုန်း နှင့် ကုန်ကျစရိတ် ခွဲခြမ်းစိတ်ဖြာမှု',
      icon: Target,
      color: 'text-red-600',
      bgColor: 'bg-red-100 dark:bg-red-900/20',
      route: '/reports/profit-analysis',
      metrics: `${salesMetrics.profitMargin}% margin`
    },
    {
      id: 'inventory-reports',
      title: 'Inventory Reports',
      titleLocal: 'စာရင်းဝင် အစီရင်ခံစာများ',
      description: 'Stock levels, turnover, and inventory insights',
      descriptionLocal: 'စတော့ အဆင့်များ၊ လည်ပတ်မှု နှင့် စာရင်းဝင် အသိအမြင်များ',
      icon: Activity,
      color: 'text-teal-600',
      bgColor: 'bg-teal-100 dark:bg-teal-900/20',
      route: '/reports/inventory-reports',
      metrics: '95% stock accuracy'
    }
  ]

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchSalesMetrics()
    }
  }, [isAuthenticated, dateRange])

  // Listen for currency changes and refresh data
  useEffect(() => {
    const handleCurrencyChange = () => {
      console.log('📊 Reports currency change detected, refreshing data')
      // No need to refetch data, just trigger re-render with new currency formatting
      // The formatCurrency function will handle the new currency
    }

    // Listen for currency change events
    window.addEventListener('currency-changed', handleCurrencyChange)
    window.addEventListener('currencyChanged', handleCurrencyChange)
    window.addEventListener('global-currency-sync', handleCurrencyChange)

    return () => {
      window.removeEventListener('currency-changed', handleCurrencyChange)
      window.removeEventListener('currencyChanged', handleCurrencyChange)
      window.removeEventListener('global-currency-sync', handleCurrencyChange)
    }
  }, [])

  // Export function
  const handleExportReport = async (format: 'pdf' | 'excel' = 'pdf') => {
    try {
      setExporting(true)

      // Get current date range data
      // Use fallback since API method doesn't exist
      const response = { success: false, error: 'Export functionality not implemented yet' }

      if (response.success) {
        // Create download link
        const blob = new Blob([(response as any).data], {
          type: format === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        link.download = `sales-report-${dateRange}-${new Date().toISOString().split('T')[0]}.${format}`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        console.log(`✅ Report exported as ${format.toUpperCase()}`)
      } else {
        // Fallback: Generate simple report
        const reportData = {
          title: language === 'mm' ? 'ရောင်းအား အစီရင်ခံစာ' : 'Sales Report',
          dateRange,
          metrics: salesMetrics,
          generatedAt: new Date().toISOString()
        }

        const jsonBlob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' })
        const url = window.URL.createObjectURL(jsonBlob)
        const link = document.createElement('a')
        link.href = url
        link.download = `sales-report-${dateRange}-${new Date().toISOString().split('T')[0]}.json`
        document.body.appendChild(link)
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)

        console.log('✅ Report exported as JSON (fallback)')
      }
    } catch (error) {
      console.error('Error exporting report:', error)
      alert(language === 'mm' ? 'အစီရင်ခံစာ ထုတ်ယူရာတွင် အမှားရှိသည်' : 'Error exporting report')
    } finally {
      setExporting(false)
    }
  }

  const fetchSalesMetrics = async () => {
    try {
      setLoading(true)
      console.log('🔄 Loading real sales metrics from API...')

      // Try to fetch real sales metrics from multiple API sources
      try {
        // Get dashboard stats first
        const [dashboardResponse, salesResponse] = await Promise.all([
          apiClient.getDashboardStats(),
          apiClient.getSalesMetrics({ dateRange })
        ])

        let metrics: SalesMetrics = {
          totalRevenue: 0,
          totalSales: 0,
          totalCustomers: 0,
          totalProducts: 0,
          avgOrderValue: 0,
          profitMargin: 0,
          growthRate: 0,
          topSellingProduct: language === 'mm' ? 'မရှိပါ' : 'None'
        }

        // Use dashboard data if available
        if (dashboardResponse.success && dashboardResponse.data) {
          const data = dashboardResponse.data
          metrics = {
            totalRevenue: data.todaySales?.amount || 0,
            totalSales: data.todaySales?.transactions || 0,
            totalCustomers: data.totalCustomers || 0,
            totalProducts: data.totalProducts?.count || 0,
            avgOrderValue: data.todaySales?.amount && data.todaySales?.transactions
              ? Math.round(data.todaySales.amount / data.todaySales.transactions)
              : 0,
            profitMargin: data.profitMargin || 0,
            growthRate: data.todaySales?.change || 0,
            topSellingProduct: data.topProducts?.[0]?.name || (language === 'mm' ? 'မရှိပါ' : 'None')
          }
        }

        // Override with sales metrics if available
        if (salesResponse.success && salesResponse.data) {
          const salesData = salesResponse.data
          metrics = {
            ...metrics,
            totalRevenue: salesData.totalRevenue || metrics.totalRevenue,
            totalSales: salesData.totalSales || metrics.totalSales,
            totalCustomers: salesData.totalCustomers || metrics.totalCustomers,
            totalProducts: salesData.totalProducts || metrics.totalProducts,
            avgOrderValue: salesData.avgOrderValue || metrics.avgOrderValue,
            profitMargin: salesData.profitMargin || metrics.profitMargin,
            growthRate: salesData.growthRate || metrics.growthRate,
            topSellingProduct: salesData.topSellingProduct || metrics.topSellingProduct
          }
        }

        setSalesMetrics(metrics)
        console.log('✅ Real sales metrics loaded successfully:', metrics)

      } catch (apiError) {
        console.warn('API not available, using empty data:', apiError)
        setSalesMetrics({
          totalRevenue: 0,
          totalSales: 0,
          totalCustomers: 0,
          totalProducts: 0,
          avgOrderValue: 0,
          profitMargin: 0,
          growthRate: 0,
          topSellingProduct: language === 'mm' ? 'မရှိပါ' : 'None'
        })
      }
    } catch (error) {
      console.error('Error fetching sales metrics:', error)
      setSalesMetrics({
        totalRevenue: 0,
        totalSales: 0,
        totalCustomers: 0,
        totalProducts: 0,
        avgOrderValue: 0,
        profitMargin: 0,
        growthRate: 0,
        topSellingProduct: language === 'mm' ? 'မရှိပါ' : 'None'
      })
    } finally {
      setLoading(false)
    }
  }



  const getDateRangeText = () => {
    switch (dateRange) {
      case 'today': return language === 'mm' ? 'ယနေ့' : 'Today'
      case 'week': return language === 'mm' ? 'ဒီအပတ်' : 'This Week'
      case 'month': return language === 'mm' ? 'ဒီလ' : 'This Month'
      default: return language === 'mm' ? 'ယနေ့' : 'Today'
    }
  }



  if (!isAuthenticated) {
    return null
  }

  // Show empty state if no data (after reset)
  if (salesMetrics.totalRevenue === 0 && salesMetrics.totalSales === 0) {
    return (
      <MainLayout language={language}>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {language === 'mm' ? 'အစီရင်ခံစာများ နှင့် ခွဲခြမ်းစိတ်ဖြာမှု' : 'Reports & Analytics'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  {language === 'mm'
                    ? 'ရောင်းအား၊ ဝင်ငွေ နှင့် လုပ်ငန်း စွမ်းဆောင်ရည် ခွဲခြမ်းစိတ်ဖြာမှု'
                    : 'Sales, revenue, and business performance analytics'
                  }
                </p>
              </div>
            </div>

            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <BarChart3 className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {language === 'mm' ? 'အစီရင်ခံစာ ဒေတာ မရှိပါ' : 'No Report Data'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {language === 'mm'
                    ? 'ရောင်းအား ဒေတာ ရှိမှ အစီရင်ခံစာများ ပေါ်လာမည်'
                    : 'Reports will appear once you have sales data'
                  }
                </p>
                <Button onClick={() => router.push('/pos')} className="bg-blue-600 hover:bg-blue-700">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ရောင်းချ စတင်ရန်' : 'Start Selling'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 to-purple-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <BarChart3 className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'အစီရင်ခံစာများ နှင့် ခွဲခြမ်းစိတ်ဖြာမှု' : 'Reports & Analytics'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm'
                      ? 'ရောင်းအား၊ ဝင်ငွေ နှင့် လုပ်ငန်း စွမ်းဆောင်ရည် ခွဲခြမ်းစိတ်ဖြာမှု'
                      : 'Sales, revenue, and business performance analytics'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="w-40 bg-white/20 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="today">{language === 'mm' ? 'ယနေ့' : 'Today'}</SelectItem>
                    <SelectItem value="week">{language === 'mm' ? 'ဒီအပတ်' : 'This Week'}</SelectItem>
                    <SelectItem value="month">{language === 'mm' ? 'ဒီလ' : 'This Month'}</SelectItem>
                  </SelectContent>
                </Select>
                <Button
                  onClick={fetchSalesMetrics}
                  disabled={loading}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                  {language === 'mm' ? 'ပြန်လည်ရယူ' : 'Refresh'}
                </Button>
                <Button
                  onClick={() => handleExportReport('pdf')}
                  disabled={exporting}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <Download className={`h-4 w-4 mr-2 ${exporting ? 'animate-spin' : ''}`} />
                  {exporting ? (language === 'mm' ? 'ထုတ်နေသည်...' : 'Exporting...') : (language === 'mm' ? 'ထုတ်ယူရန်' : 'Export')}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း ဝင်ငွေ' : 'Total Revenue'}
                  </p>
                  <p className="text-2xl font-bold">{formatCurrency(salesMetrics.totalRevenue)}</p>
                  <div className="flex items-center gap-1 mt-1">
                    <ArrowUpRight className="h-3 w-3 text-green-600" />
                    <span className="text-xs text-green-600">+{salesMetrics.growthRate}%</span>
                  </div>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                  <DollarSign className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း ရောင်းချမှု' : 'Total Sales'}
                  </p>
                  <p className="text-2xl font-bold">{salesMetrics.totalSales}</p>
                  <p className="text-xs text-gray-500 mt-1">{getDateRangeText()}</p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                  <ShoppingCart className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'ပျမ်းမျှ အမှာစာ တန်ဖိုး' : 'Avg Order Value'}
                  </p>
                  <p className="text-2xl font-bold">{formatCurrency(salesMetrics.avgOrderValue)}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'mm' ? 'တစ်ခုချင်းစီ' : 'Per transaction'}
                  </p>
                </div>
                <div className="p-3 bg-purple-100 dark:bg-purple-900/20 rounded-xl">
                  <Target className="h-6 w-6 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'အမြတ်နှုန်း' : 'Profit Margin'}
                  </p>
                  <p className="text-2xl font-bold">{salesMetrics.profitMargin}%</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'mm' ? 'စုစုပေါင်း အမြတ်' : 'Overall margin'}
                  </p>
                </div>
                <div className="p-3 bg-orange-100 dark:bg-orange-900/20 rounded-xl">
                  <TrendingUp className="h-6 w-6 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Categories */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {reportCards.map((report) => {
            const Icon = report.icon
            return (
              <Card key={report.id} className="hover:shadow-lg transition-all duration-300 cursor-pointer group">
                <CardContent className="p-6">
                  <div className="flex items-start justify-between mb-4">
                    <div className={`p-3 ${report.bgColor} rounded-xl group-hover:scale-110 transition-transform`}>
                      <Icon className={`h-6 w-6 ${report.color}`} />
                    </div>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => router.push(report.route)}
                      className="opacity-0 group-hover:opacity-100 transition-opacity"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>

                  <h3 className="font-semibold text-lg mb-2">
                    {language === 'mm' ? report.titleLocal : report.title}
                  </h3>

                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                    {language === 'mm' ? report.descriptionLocal : report.description}
                  </p>

                  {report.metrics && (
                    <div className="flex items-center justify-between">
                      <Badge variant="secondary" className="text-xs">
                        {report.metrics}
                      </Badge>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(report.route)}
                        className={`${report.color} hover:bg-gray-100 dark:hover:bg-gray-800`}
                      >
                        <ArrowUpRight className="h-4 w-4" />
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-blue-600" />
              {language === 'mm' ? 'အမြန် လုပ်ဆောင်ချက်များ' : 'Quick Actions'}
            </CardTitle>
            <CardDescription>
              {language === 'mm'
                ? 'အသုံးများသော အစီရင်ခံစာများ နှင့် ထုတ်ယူမှု ရွေးချယ်မှုများ'
                : 'Frequently used reports and export options'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2"
                onClick={() => router.push('/reports/daily-summary')}
              >
                <Calendar className="h-6 w-6 text-blue-600" />
                <span className="font-medium">
                  {language === 'mm' ? 'နေ့စဉ် အနှစ်ချုပ်' : 'Daily Summary'}
                </span>
                <span className="text-xs text-gray-500">
                  {language === 'mm' ? 'ယနေ့ ရောင်းအား အစီရင်ခံစာ' : 'Today\'s sales report'}
                </span>
              </Button>

              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2"
                onClick={() => router.push('/reports/top-products')}
              >
                <Package className="h-6 w-6 text-green-600" />
                <span className="font-medium">
                  {language === 'mm' ? 'ထိပ်တန်း ကုန်ပစ္စည်းများ' : 'Top Products'}
                </span>
                <span className="text-xs text-gray-500">
                  {language === 'mm' ? 'အရောင်းရဆုံး ကုန်ပစ္စည်းများ' : 'Best selling products'}
                </span>
              </Button>

              <Button
                variant="outline"
                className="h-auto p-4 flex flex-col items-center gap-2"
                onClick={() => handleExportReport('pdf')}
                disabled={exporting}
              >
                <Download className={`h-6 w-6 text-purple-600 ${exporting ? 'animate-spin' : ''}`} />
                <span className="font-medium">
                  {language === 'mm' ? 'အစီရင်ခံစာ ထုတ်ယူရန်' : 'Export Reports'}
                </span>
                <span className="text-xs text-gray-500">
                  {language === 'mm' ? 'PDF/Excel ဖော်မတ်များ' : 'PDF/Excel formats'}
                </span>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
