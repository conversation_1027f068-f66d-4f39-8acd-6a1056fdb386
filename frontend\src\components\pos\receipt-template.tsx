'use client'

import React from 'react'
import { Card, CardContent } from '@/components/ui/card'

interface ReceiptItem {
  productName: string
  sku: string
  quantity: number
  unitPrice: number
  totalPrice: number
  discount?: number
  tax?: number
}

interface ReceiptData {
  saleNumber: string
  date: string
  time: string
  cashier: {
    firstName: string
    lastName: string
    username: string
  }
  customer?: {
    name?: string
    email?: string
    phone?: string
    address?: string
  }
  items: ReceiptItem[]
  subtotal: number
  totalDiscount: number
  totalTax: number
  totalAmount: number
  currency: string
  paymentMethod: string
  amountPaid: number
  changeAmount: number
  notes?: string
}

interface ReceiptTemplateProps {
  data: ReceiptData
  language?: 'en' | 'mm'
  companyInfo?: {
    name: string
    address: string
    phone: string
    email: string
    website?: string
    taxId?: string
  }
}

export function ReceiptTemplate({
  data,
  language = 'en',
  companyInfo = {
    name: 'BitsTech POS',
    address: 'Yangon, Myanmar',
    phone: '+95-9-***********',
    email: '<EMAIL>',
    website: 'www.bitstech.com',
    taxId: 'TAX-*********'
  }
}: ReceiptTemplateProps) {

  const formatPrice = (price: number) => {
    return `${price.toLocaleString()} ${data.currency}`
  }

  const text = {
    en: {
      receipt: 'RECEIPT',
      saleNo: 'Sale No',
      date: 'Date',
      time: 'Time',
      cashier: 'Cashier',
      customer: 'Customer',
      item: 'Item',
      qty: 'Qty',
      price: 'Price',
      total: 'Total',
      subtotal: 'Subtotal',
      discount: 'Discount',
      tax: 'Tax',
      grandTotal: 'Grand Total',
      payment: 'Payment Method',
      paid: 'Amount Paid',
      change: 'Change',
      notes: 'Notes',
      thankYou: 'Thank you for your business!',
      visitAgain: 'Please visit us again',
      poweredBy: 'Powered by BitsTech POS'
    },
    mm: {
      receipt: 'ဘောင်ချာ',
      saleNo: 'ရောင်းအား နံပါတ်',
      date: 'ရက်စွဲ',
      time: 'အချိန်',
      cashier: 'ငွေကောင်တာ',
      customer: 'ဝယ်သူ',
      item: 'ပစ္စည်း',
      qty: 'အရေအတွက်',
      price: 'ဈေး',
      total: 'စုစုပေါင်း',
      subtotal: 'ခွဲစုစုပေါင်း',
      discount: 'လျှော့စျေး',
      tax: 'အခွန်',
      grandTotal: 'စုစုပေါင်း',
      payment: 'ငွေချေမှု',
      paid: 'ပေးငွေ',
      change: 'ပြန်ငွေ',
      notes: 'မှတ်ချက်',
      thankYou: 'ဝယ်ယူအားပေးမှုအတွက် ကျေးဇူးတင်ပါသည်!',
      visitAgain: 'နောက်တစ်ကြိမ် လာရောက်ဝယ်ယူပါ',
      poweredBy: 'BitsTech POS မှ ပံ့ပိုးထားသည်'
    }
  }

  const t = text[language]

  return (
    <div className="receipt-template bg-white text-black p-6 max-w-sm mx-auto font-mono text-sm">
      {/* Header */}
      <div className="text-center border-b-2 border-dashed border-gray-400 pb-4 mb-4">
        <h1 className="text-lg font-bold mb-2">{companyInfo.name}</h1>
        <div className="text-xs space-y-1">
          <div>{companyInfo.address}</div>
          <div>{companyInfo.phone}</div>
          <div>{companyInfo.email}</div>
          {companyInfo.website && <div>{companyInfo.website}</div>}
          {companyInfo.taxId && <div>Tax ID: {companyInfo.taxId}</div>}
        </div>
      </div>

      {/* Receipt Title */}
      <div className="text-center mb-4">
        <h2 className="text-xl font-bold">{t.receipt}</h2>
      </div>

      {/* Sale Information */}
      <div className="mb-4 space-y-1">
        <div className="flex justify-between">
          <span>{t.saleNo}:</span>
          <span className="font-semibold">{data.saleNumber}</span>
        </div>
        <div className="flex justify-between">
          <span>{t.date}:</span>
          <span>{data.date}</span>
        </div>
        <div className="flex justify-between">
          <span>{t.time}:</span>
          <span>{data.time}</span>
        </div>
        <div className="flex justify-between">
          <span>{t.cashier}:</span>
          <span>{data.cashier.firstName} {data.cashier.lastName}</span>
        </div>
        {data.customer?.name && (
          <div className="flex justify-between">
            <span>{t.customer}:</span>
            <span>{data.customer.name}</span>
          </div>
        )}
      </div>

      {/* Items */}
      <div className="border-t border-b border-dashed border-gray-400 py-2 mb-4">
        <div className="grid grid-cols-12 gap-1 text-xs font-semibold mb-2">
          <div className="col-span-6">{t.item}</div>
          <div className="col-span-2 text-center">{t.qty}</div>
          <div className="col-span-2 text-right">{t.price}</div>
          <div className="col-span-2 text-right">{t.total}</div>
        </div>

        {data.items.map((item, index) => (
          <div key={index} className="mb-2">
            <div className="grid grid-cols-12 gap-1 text-xs">
              <div className="col-span-6">
                <div className="font-medium">{item.productName}</div>
                <div className="text-gray-600">{item.sku}</div>
              </div>
              <div className="col-span-2 text-center">{item.quantity}</div>
              <div className="col-span-2 text-right">{formatPrice(item.unitPrice)}</div>
              <div className="col-span-2 text-right font-medium">{formatPrice(item.totalPrice)}</div>
            </div>
            {item.discount && item.discount > 0 && (
              <div className="text-xs text-red-600 text-right">
                -{formatPrice(item.discount)} {t.discount}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Totals */}
      <div className="space-y-1 mb-4">
        <div className="flex justify-between">
          <span>{t.subtotal}:</span>
          <span>{formatPrice(data.subtotal)}</span>
        </div>

        {data.totalDiscount > 0 && (
          <div className="flex justify-between text-red-600">
            <span>{t.discount}:</span>
            <span>-{formatPrice(data.totalDiscount)}</span>
          </div>
        )}

        {data.totalTax > 0 && (
          <div className="flex justify-between">
            <span>{t.tax}:</span>
            <span>{formatPrice(data.totalTax)}</span>
          </div>
        )}

        <div className="border-t border-dashed border-gray-400 pt-2">
          <div className="flex justify-between text-lg font-bold">
            <span>{t.grandTotal}:</span>
            <span>{formatPrice(data.totalAmount)}</span>
          </div>
        </div>
      </div>

      {/* Payment Information */}
      <div className="border-t border-dashed border-gray-400 pt-2 mb-4 space-y-1">
        <div className="flex justify-between">
          <span>{t.payment}:</span>
          <span className="uppercase">{data.paymentMethod.replace('_', ' ')}</span>
        </div>
        <div className="flex justify-between">
          <span>{t.paid}:</span>
          <span>{formatPrice(data.amountPaid)}</span>
        </div>
        {data.changeAmount > 0 && (
          <div className="flex justify-between font-semibold">
            <span>{t.change}:</span>
            <span>{formatPrice(data.changeAmount)}</span>
          </div>
        )}
      </div>

      {/* Notes */}
      {data.notes && (
        <div className="mb-4">
          <div className="text-xs font-semibold mb-1">{t.notes}:</div>
          <div className="text-xs">{data.notes}</div>
        </div>
      )}

      {/* Footer */}
      <div className="text-center border-t border-dashed border-gray-400 pt-4 space-y-2">
        <div className="text-sm font-semibold">{t.thankYou}</div>
        <div className="text-xs">{t.visitAgain}</div>
        <div className="text-xs text-gray-600 mt-4">{t.poweredBy}</div>
      </div>

      {/* Print Styles */}
      <style jsx>{`
        @media print {
          .receipt-template {
            width: 80mm;
            margin: 0;
            padding: 10px;
            font-size: 12px;
            line-height: 1.2;
          }

          .receipt-template * {
            color: black !important;
            background: white !important;
          }

          @page {
            size: 80mm auto;
            margin: 0;
          }
        }
      `}</style>
    </div>
  )
}

export default ReceiptTemplate
