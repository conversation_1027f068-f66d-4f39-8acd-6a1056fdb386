'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
// import { Switch } from '@/components/ui/switch' // Component not available
import { useAuth } from '@/contexts/auth-context'
import apiClient from '@/lib/api'
import {
  ArrowLeft,
  Save,
  Building2,
  User,
  CreditCard,
  Package
} from 'lucide-react'

interface SupplierFormData {
  name: string
  code: string
  contactPerson: {
    name: string
    title: string
    email: string
    phone: string
    mobile: string
  }
  company: {
    address: {
      street: string
      city: string
      state: string
      country: string
      zipCode: string
    }
    phone: string
    website: string
  }
  paymentTerms: string
  creditLimit: number
  currency: string
  categories: string[]
  rating: number
  isActive: boolean
}

export default function EditSupplierPage() {
  const { user, isAuthenticated } = useAuth()
  const router = useRouter()
  const params = useParams()
  const supplierId = params.id as string

  const [formData, setFormData] = useState<SupplierFormData>({
    name: '',
    code: '',
    contactPerson: {
      name: '',
      title: '',
      email: '',
      phone: '',
      mobile: ''
    },
    company: {
      address: {
        street: '',
        city: '',
        state: '',
        country: '',
        zipCode: ''
      },
      phone: '',
      website: ''
    },
    paymentTerms: 'net_30',
    creditLimit: 0,
    currency: 'USD',
    categories: [],
    rating: 5,
    isActive: true
  })

  const [categories, setCategories] = useState<Array<{_id: string, name: string}>>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const t = {
    en: {
      editSupplier: 'Edit Supplier',
      back: 'Back to Supplier',
      save: 'Save Changes',
      saving: 'Saving...',
      basicInfo: 'Basic Information',
      contactInfo: 'Contact Information',
      businessInfo: 'Business Information',
      supplierName: 'Supplier Name',
      supplierCode: 'Supplier Code',
      contactPerson: 'Contact Person',
      title: 'Title',
      email: 'Email',
      phone: 'Phone',
      mobile: 'Mobile',
      address: 'Address',
      street: 'Street',
      city: 'City',
      state: 'State',
      country: 'Country',
      zipCode: 'ZIP Code',
      website: 'Website',
      paymentTerms: 'Payment Terms',
      creditLimit: 'Credit Limit',
      currency: 'Currency',
      categories: 'Categories',
      rating: 'Rating',
      status: 'Status',
      active: 'Active',
      required: 'Required'
    },
    mm: {
      editSupplier: 'ပေးသွင်းသူ ပြင်ဆင်ရန်',
      back: 'ပေးသွင်းသူ သို့ ပြန်သွားရန်',
      save: 'ပြောင်းလဲမှုများ သိမ်းဆည်းရန်',
      saving: 'သိမ်းဆည်းနေသည်...',
      basicInfo: 'အခြေခံ အချက်အလက်များ',
      contactInfo: 'ဆက်သွယ်ရေး အချက်အလက်များ',
      businessInfo: 'လုပ်ငန်း အချက်အလက်များ',
      supplierName: 'ပေးသွင်းသူ အမည်',
      supplierCode: 'ပေးသွင်းသူ ကုဒ်',
      contactPerson: 'ဆက်သွယ်ရမည့် ပုဂ္ဂိုလ်',
      title: 'ရာထူး',
      email: 'အီးမေးလ်',
      phone: 'ဖုန်းနံပါတ်',
      mobile: 'မိုဘိုင်း',
      address: 'လိပ်စာ',
      street: 'လမ်း',
      city: 'မြို့',
      state: 'ပြည်နယ်',
      country: 'နိုင်ငံ',
      zipCode: 'စာတိုက်သင်္ကေတ',
      website: 'ဝက်ဘ်ဆိုက်',
      paymentTerms: 'ငွေပေးချေမှု စည်းကမ်းများ',
      creditLimit: 'ခရက်ဒစ် ကန့်သတ်ချက်',
      currency: 'ငွေကြေး',
      categories: 'အမျိုးအစားများ',
      rating: 'အဆင့်သတ်မှတ်ချက်',
      status: 'အခြေအနေ',
      active: 'အသုံးပြုနေသော',
      required: 'လိုအပ်သည်'
    }
  }

  const text = t[language]

  useEffect(() => {
    if (!isAuthenticated) {
      router.push('/auth/login')
      return
    }

    if (supplierId) {
      fetchSupplierData()
      fetchCategories()
    }
  }, [isAuthenticated, supplierId])

  const fetchSupplierData = async () => {
    try {
      setLoading(true)
      // Use fallback since API method doesn't exist
      const response = { success: false, error: 'Supplier details API not implemented yet' }

      if (response.success && (response as any).data) {
        const supplier = (response as any).data
        setFormData({
          name: supplier.name || '',
          code: supplier.code || '',
          contactPerson: {
            name: supplier.contactPerson?.name || '',
            title: supplier.contactPerson?.title || '',
            email: supplier.contactPerson?.email || '',
            phone: supplier.contactPerson?.phone || '',
            mobile: supplier.contactPerson?.mobile || ''
          },
          company: {
            address: {
              street: supplier.company?.address?.street || '',
              city: supplier.company?.address?.city || '',
              state: supplier.company?.address?.state || '',
              country: supplier.company?.address?.country || '',
              zipCode: supplier.company?.address?.zipCode || ''
            },
            phone: supplier.company?.phone || '',
            website: supplier.company?.website || ''
          },
          paymentTerms: supplier.paymentTerms || 'net_30',
          creditLimit: supplier.creditLimit || 0,
          currency: supplier.currency || 'USD',
          categories: supplier.categories?.map((cat: any) => cat._id) || [],
          rating: supplier.rating || 5,
          isActive: supplier.isActive !== false
        })
      }
    } catch (error) {
      console.error('Error loading supplier:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await apiClient.getCategories()
      if (response.success && response.data) {
        setCategories(response.data)
      }
    } catch (error) {
      console.error('Error loading categories:', error)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    const keys = field.split('.')
    if (keys.length === 1) {
      setFormData(prev => ({ ...prev, [field]: value }))
    } else if (keys.length === 2) {
      setFormData(prev => ({
        ...prev,
        [keys[0]]: { ...(prev[keys[0] as keyof SupplierFormData] as any), [keys[1]]: value }
      }))
    } else if (keys.length === 3) {
      setFormData(prev => ({
        ...prev,
        [keys[0]]: {
          ...(prev[keys[0] as keyof SupplierFormData] as any),
          [keys[1]]: { ...(prev[keys[0] as keyof SupplierFormData] as any)[keys[1]], [keys[2]]: value }
        }
      }))
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)

      // Validation
      if (!formData.name.trim()) {
        alert(text.required + ': ' + text.supplierName)
        return
      }

      if (!formData.contactPerson.name.trim()) {
        alert(text.required + ': ' + text.contactPerson)
        return
      }

      const response = await apiClient.updateSupplier(supplierId, formData)

      if (response.success) {
        alert(language === 'mm' ? 'ပေးသွင်းသူ အချက်အလက်များ ပြင်ဆင်ပြီးပါပြီ်' : 'Supplier updated successfully!')
        router.push(`/suppliers/${supplierId}`)
      } else {
        alert('Failed to update supplier: ' + (response.error || 'Unknown error'))
      }
    } catch (error) {
      console.error('Error updating supplier:', error)
      alert('Error updating supplier. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  if (!isAuthenticated) {
    return null
  }

  if (loading) {
    return (
      <MainLayout language={language}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Building2 className="h-12 w-12 mx-auto mb-4 animate-spin text-blue-600" />
            <p className="text-gray-600 dark:text-gray-400">
              {language === 'mm' ? 'ပေးသွင်းသူ အချက်အလက်များ ရယူနေသည်...' : 'Loading supplier data...'}
            </p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push(`/suppliers/${supplierId}`)}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {text.back}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {text.editSupplier}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {formData.name} - {formData.code}
              </p>
            </div>
          </div>
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-blue-600 hover:bg-blue-700"
          >
            {saving ? (
              <>
                <Building2 className="h-4 w-4 mr-2 animate-spin" />
                {text.saving}
              </>
            ) : (
              <>
                <Save className="h-4 w-4 mr-2" />
                {text.save}
              </>
            )}
          </Button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Building2 className="h-5 w-5 text-blue-600" />
                {text.basicInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="name">{text.supplierName} *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={text.supplierName}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="code">{text.supplierCode} *</Label>
                <Input
                  id="code"
                  value={formData.code}
                  onChange={(e) => handleInputChange('code', e.target.value)}
                  placeholder={text.supplierCode}
                />
              </div>
              <div className="space-y-2">
                <Label>{text.categories}</Label>
                <Select 
                  value={formData.categories[0] || ''} 
                  onValueChange={(value) => handleInputChange('categories', [value])}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={text.categories} />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category._id} value={category._id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="flex items-center justify-between">
                <Label htmlFor="isActive">{text.active}</Label>
                <input
                  type="checkbox"
                  id="isActive"
                  checked={formData.isActive}
                  onChange={(e) => handleInputChange('isActive', e.target.checked)}
                  className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                  title={text.active}
                  aria-label={text.active}
                />
              </div>
            </CardContent>
          </Card>

          {/* Contact Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-green-600" />
                {text.contactInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="contactName">{text.contactPerson} *</Label>
                <Input
                  id="contactName"
                  value={formData.contactPerson.name}
                  onChange={(e) => handleInputChange('contactPerson.name', e.target.value)}
                  placeholder={text.contactPerson}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactTitle">{text.title}</Label>
                <Input
                  id="contactTitle"
                  value={formData.contactPerson.title}
                  onChange={(e) => handleInputChange('contactPerson.title', e.target.value)}
                  placeholder={text.title}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="contactEmail">{text.email}</Label>
                <Input
                  id="contactEmail"
                  type="email"
                  value={formData.contactPerson.email}
                  onChange={(e) => handleInputChange('contactPerson.email', e.target.value)}
                  placeholder={text.email}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="contactPhone">{text.phone}</Label>
                  <Input
                    id="contactPhone"
                    value={formData.contactPerson.phone}
                    onChange={(e) => handleInputChange('contactPerson.phone', e.target.value)}
                    placeholder={text.phone}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="contactMobile">{text.mobile}</Label>
                  <Input
                    id="contactMobile"
                    value={formData.contactPerson.mobile}
                    onChange={(e) => handleInputChange('contactPerson.mobile', e.target.value)}
                    placeholder={text.mobile}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Business Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCard className="h-5 w-5 text-purple-600" />
                {text.businessInfo}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label>{text.paymentTerms}</Label>
                <Select 
                  value={formData.paymentTerms} 
                  onValueChange={(value) => handleInputChange('paymentTerms', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="cash">Cash</SelectItem>
                    <SelectItem value="net_15">Net 15 Days</SelectItem>
                    <SelectItem value="net_30">Net 30 Days</SelectItem>
                    <SelectItem value="net_45">Net 45 Days</SelectItem>
                    <SelectItem value="net_60">Net 60 Days</SelectItem>
                    <SelectItem value="net_90">Net 90 Days</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="creditLimit">{text.creditLimit}</Label>
                <Input
                  id="creditLimit"
                  type="number"
                  value={formData.creditLimit}
                  onChange={(e) => handleInputChange('creditLimit', parseFloat(e.target.value) || 0)}
                  placeholder="0"
                />
              </div>
              <div className="space-y-2">
                <Label>{text.currency}</Label>
                <Select 
                  value={formData.currency} 
                  onValueChange={(value) => handleInputChange('currency', value)}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="USD">USD</SelectItem>
                    <SelectItem value="MMK">MMK</SelectItem>
                    <SelectItem value="THB">THB</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="rating">{text.rating}</Label>
                <Select 
                  value={formData.rating.toString()} 
                  onValueChange={(value) => handleInputChange('rating', parseInt(value))}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">1 Star</SelectItem>
                    <SelectItem value="2">2 Stars</SelectItem>
                    <SelectItem value="3">3 Stars</SelectItem>
                    <SelectItem value="4">4 Stars</SelectItem>
                    <SelectItem value="5">5 Stars</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Address Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-orange-600" />
                {text.address}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="street">{text.street}</Label>
                <Input
                  id="street"
                  value={formData.company.address.street}
                  onChange={(e) => handleInputChange('company.address.street', e.target.value)}
                  placeholder={text.street}
                />
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="city">{text.city}</Label>
                  <Input
                    id="city"
                    value={formData.company.address.city}
                    onChange={(e) => handleInputChange('company.address.city', e.target.value)}
                    placeholder={text.city}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="state">{text.state}</Label>
                  <Input
                    id="state"
                    value={formData.company.address.state}
                    onChange={(e) => handleInputChange('company.address.state', e.target.value)}
                    placeholder={text.state}
                  />
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="country">{text.country}</Label>
                  <Input
                    id="country"
                    value={formData.company.address.country}
                    onChange={(e) => handleInputChange('company.address.country', e.target.value)}
                    placeholder={text.country}
                  />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="zipCode">{text.zipCode}</Label>
                  <Input
                    id="zipCode"
                    value={formData.company.address.zipCode}
                    onChange={(e) => handleInputChange('company.address.zipCode', e.target.value)}
                    placeholder={text.zipCode}
                  />
                </div>
              </div>
              <div className="space-y-2">
                <Label htmlFor="website">{text.website}</Label>
                <Input
                  id="website"
                  value={formData.company.website}
                  onChange={(e) => handleInputChange('company.website', e.target.value)}
                  placeholder="https://example.com"
                />
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
