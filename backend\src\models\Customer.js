const mongoose = require('mongoose');

const customerSchema = new mongoose.Schema({
    name: {
        type: String,
        required: [true, 'Customer name is required'],
        trim: true,
        maxlength: [100, 'Name cannot exceed 100 characters']
    },
    email: {
        type: String,
        trim: true,
        lowercase: true,
        match: [/^\w+([.-]?\w+)*@\w+([.-]?\w+)*(\.\w{2,3})+$/, 'Please enter a valid email'],
        sparse: true // Allows multiple null values but unique non-null values
    },
    phone: {
        type: String,
        required: [true, 'Phone number is required'],
        trim: true,
        unique: true,
        match: [/^[\+]?[0-9\-\s\(\)]+$/, 'Please enter a valid phone number']
    },
    address: {
        street: {
            type: String,
            trim: true
        },
        city: {
            type: String,
            trim: true,
            default: 'Yangon'
        },
        state: {
            type: String,
            trim: true
        },
        country: {
            type: String,
            trim: true,
            default: 'Myanmar'
        },
        postalCode: {
            type: String,
            trim: true
        },
        full: {
            type: String,
            trim: true
        }
    },
    customerType: {
        type: String,
        enum: ['regular', 'vip', 'wholesale', 'new'],
        default: 'regular'
    },
    loyaltyPoints: {
        type: Number,
        default: 0,
        min: [0, 'Loyalty points cannot be negative']
    },
    totalSpent: {
        type: Number,
        default: 0,
        min: [0, 'Total spent cannot be negative']
    },
    totalOrders: {
        type: Number,
        default: 0,
        min: [0, 'Total orders cannot be negative']
    },
    averageOrderValue: {
        type: Number,
        default: 0,
        min: [0, 'Average order value cannot be negative']
    },
    lastPurchaseDate: {
        type: Date
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'blocked'],
        default: 'active'
    },
    notes: {
        type: String,
        maxlength: [1000, 'Notes cannot exceed 1000 characters']
    },
    tags: [{
        type: String,
        trim: true
    }],
    preferences: {
        language: {
            type: String,
            enum: ['en', 'mm'],
            default: 'en'
        },
        currency: {
            type: String,
            enum: ['MMK', 'USD', 'THB'],
            default: 'MMK'
        },
        communicationMethod: {
            type: String,
            enum: ['email', 'phone', 'sms', 'none'],
            default: 'phone'
        },
        newsletter: {
            type: Boolean,
            default: false
        }
    },
    socialMedia: {
        facebook: {
            type: String,
            trim: true
        },
        instagram: {
            type: String,
            trim: true
        },
        twitter: {
            type: String,
            trim: true
        }
    },
    businessInfo: {
        companyName: {
            type: String,
            trim: true
        },
        taxId: {
            type: String,
            trim: true
        },
        businessType: {
            type: String,
            trim: true
        }
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes for better query performance
customerSchema.index({ email: 1 });
customerSchema.index({ phone: 1 });
customerSchema.index({ customerType: 1 });
customerSchema.index({ status: 1 });
customerSchema.index({ createdAt: -1 });
customerSchema.index({ totalSpent: -1 });
customerSchema.index({ loyaltyPoints: -1 });

// Text index for search functionality
customerSchema.index({
    name: 'text',
    email: 'text',
    phone: 'text',
    'address.full': 'text',
    'businessInfo.companyName': 'text'
});

// Virtual for full name display
customerSchema.virtual('displayName').get(function() {
    return this.businessInfo?.companyName || this.name;
});

// Virtual for loyalty level
customerSchema.virtual('loyaltyLevel').get(function() {
    if (this.loyaltyPoints >= 1000) return 'platinum';
    if (this.loyaltyPoints >= 500) return 'gold';
    if (this.loyaltyPoints >= 100) return 'silver';
    return 'bronze';
});

// Virtual for customer lifetime value
customerSchema.virtual('lifetimeValue').get(function() {
    return this.totalSpent + (this.loyaltyPoints * 10); // Assuming 1 point = 10 MMK
});

// Pre-save middleware to update computed fields
customerSchema.pre('save', function(next) {
    // Update full address
    if (this.address) {
        const addressParts = [
            this.address.street,
            this.address.city,
            this.address.state,
            this.address.country
        ].filter(Boolean);
        this.address.full = addressParts.join(', ');
    }

    // Update average order value
    if (this.totalOrders > 0) {
        this.averageOrderValue = this.totalSpent / this.totalOrders;
    }

    next();
});

// Instance method to add loyalty points
customerSchema.methods.addLoyaltyPoints = function(points) {
    this.loyaltyPoints += points;
    return this.save();
};

// Instance method to update purchase stats
customerSchema.methods.updatePurchaseStats = function(orderAmount) {
    this.totalSpent += orderAmount;
    this.totalOrders += 1;
    this.lastPurchaseDate = new Date();
    this.averageOrderValue = this.totalSpent / this.totalOrders;
    
    // Add loyalty points (1 point per 1000 MMK spent)
    const pointsToAdd = Math.floor(orderAmount / 1000);
    this.loyaltyPoints += pointsToAdd;
    
    return this.save();
};

// Static method to get customer statistics
customerSchema.statics.getStatistics = async function() {
    const stats = await this.aggregate([
        {
            $group: {
                _id: null,
                totalCustomers: { $sum: 1 },
                activeCustomers: {
                    $sum: { $cond: [{ $eq: ['$status', 'active'] }, 1, 0] }
                },
                totalSpent: { $sum: '$totalSpent' },
                totalLoyaltyPoints: { $sum: '$loyaltyPoints' },
                averageOrderValue: { $avg: '$averageOrderValue' }
            }
        }
    ]);

    const customerTypes = await this.aggregate([
        {
            $group: {
                _id: '$customerType',
                count: { $sum: 1 }
            }
        }
    ]);

    return {
        summary: stats[0] || {
            totalCustomers: 0,
            activeCustomers: 0,
            totalSpent: 0,
            totalLoyaltyPoints: 0,
            averageOrderValue: 0
        },
        customerTypes
    };
};

// Static method to find customers by loyalty level
customerSchema.statics.findByLoyaltyLevel = function(level) {
    let pointsRange = {};
    
    switch (level) {
        case 'platinum':
            pointsRange = { $gte: 1000 };
            break;
        case 'gold':
            pointsRange = { $gte: 500, $lt: 1000 };
            break;
        case 'silver':
            pointsRange = { $gte: 100, $lt: 500 };
            break;
        case 'bronze':
            pointsRange = { $lt: 100 };
            break;
        default:
            return this.find({});
    }
    
    return this.find({ loyaltyPoints: pointsRange });
};

module.exports = mongoose.model('Customer', customerSchema);
