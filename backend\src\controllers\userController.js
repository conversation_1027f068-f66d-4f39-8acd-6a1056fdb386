const User = require('../models/User');
const asyncHandler = require('../utils/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get all users
// @route   GET /api/users
// @access  Private (Admin, Manager)
const getUsers = asyncHandler(async (req, res, next) => {
    const { page = 1, limit = 10, role, status, search } = req.query;
    
    // Build query
    let query = {};
    
    if (role) {
        query.role = role;
    }
    
    if (status) {
        query.isActive = status === 'active';
    }
    
    if (search) {
        query.$or = [
            { firstName: { $regex: search, $options: 'i' } },
            { lastName: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { username: { $regex: search, $options: 'i' } }
        ];
    }
    
    // Execute query with pagination
    const users = await User.find(query)
        .select('-password')
        .sort({ createdAt: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);
    
    const total = await User.countDocuments(query);
    
    res.status(200).json({
        success: true,
        data: users,
        pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
        }
    });
});

// @desc    Get single user
// @route   GET /api/users/:id
// @access  Private (Admin, Manager)
const getUser = asyncHandler(async (req, res, next) => {
    const user = await User.findById(req.params.id).select('-password');
    
    if (!user) {
        return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
    }
    
    res.status(200).json({
        success: true,
        data: user
    });
});

// @desc    Create new user
// @route   POST /api/users
// @access  Private (Admin, Manager)
const createUser = asyncHandler(async (req, res, next) => {
    const {
        username,
        email,
        password,
        firstName,
        lastName,
        role,
        phone,
        address
    } = req.body;
    
    // Check if user already exists
    const existingUser = await User.findOne({
        $or: [{ email }, { username }]
    });
    
    if (existingUser) {
        return next(new ErrorResponse('User with this email or username already exists', 400));
    }
    
    // Create user
    const user = await User.create({
        username,
        email,
        password,
        firstName,
        lastName,
        role: role || 'cashier',
        phone,
        address,
        createdBy: req.user.id
    });
    
    // Remove password from response
    user.password = undefined;
    
    res.status(201).json({
        success: true,
        data: user,
        message: 'User created successfully'
    });
});

// @desc    Update user
// @route   PUT /api/users/:id
// @access  Private (Admin, Manager)
const updateUser = asyncHandler(async (req, res, next) => {
    const {
        username,
        email,
        firstName,
        lastName,
        role,
        phone,
        address,
        preferences
    } = req.body;
    
    let user = await User.findById(req.params.id);
    
    if (!user) {
        return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
    }
    
    // Check if email/username is already taken by another user
    if (email || username) {
        const existingUser = await User.findOne({
            _id: { $ne: req.params.id },
            $or: [
                ...(email ? [{ email }] : []),
                ...(username ? [{ username }] : [])
            ]
        });
        
        if (existingUser) {
            return next(new ErrorResponse('Email or username already taken by another user', 400));
        }
    }
    
    // Update fields
    const updateData = {};
    if (username !== undefined) updateData.username = username;
    if (email !== undefined) updateData.email = email;
    if (firstName !== undefined) updateData.firstName = firstName;
    if (lastName !== undefined) updateData.lastName = lastName;
    if (role !== undefined) updateData.role = role;
    if (phone !== undefined) updateData.phone = phone;
    if (address !== undefined) updateData.address = address;
    if (preferences !== undefined) updateData.preferences = preferences;
    
    updateData.updatedBy = req.user.id;
    
    user = await User.findByIdAndUpdate(req.params.id, updateData, {
        new: true,
        runValidators: true
    }).select('-password');
    
    res.status(200).json({
        success: true,
        data: user,
        message: 'User updated successfully'
    });
});

// @desc    Delete user
// @route   DELETE /api/users/:id
// @access  Private (Admin)
const deleteUser = asyncHandler(async (req, res, next) => {
    const user = await User.findById(req.params.id);
    
    if (!user) {
        return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
    }
    
    // Prevent deleting the last admin
    if (user.role === 'admin') {
        const adminCount = await User.countDocuments({ role: 'admin', isActive: true });
        if (adminCount <= 1) {
            return next(new ErrorResponse('Cannot delete the last active admin user', 400));
        }
    }
    
    // Prevent users from deleting themselves
    if (user._id.toString() === req.user.id) {
        return next(new ErrorResponse('You cannot delete your own account', 400));
    }
    
    await User.findByIdAndDelete(req.params.id);
    
    res.status(200).json({
        success: true,
        message: 'User deleted successfully'
    });
});

// @desc    Update user status (activate/deactivate)
// @route   PUT /api/users/:id/status
// @access  Private (Admin, Manager)
const updateUserStatus = asyncHandler(async (req, res, next) => {
    const { isActive } = req.body;
    
    const user = await User.findById(req.params.id);
    
    if (!user) {
        return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
    }
    
    // Prevent deactivating the last admin
    if (user.role === 'admin' && isActive === false) {
        const activeAdminCount = await User.countDocuments({ 
            role: 'admin', 
            isActive: true,
            _id: { $ne: req.params.id }
        });
        if (activeAdminCount === 0) {
            return next(new ErrorResponse('Cannot deactivate the last active admin user', 400));
        }
    }
    
    // Prevent users from deactivating themselves
    if (user._id.toString() === req.user.id && isActive === false) {
        return next(new ErrorResponse('You cannot deactivate your own account', 400));
    }
    
    user.isActive = isActive;
    user.updatedBy = req.user.id;
    await user.save();
    
    res.status(200).json({
        success: true,
        data: user,
        message: `User ${isActive ? 'activated' : 'deactivated'} successfully`
    });
});

// @desc    Change user password
// @route   PUT /api/users/:id/password
// @access  Private (Admin)
const changeUserPassword = asyncHandler(async (req, res, next) => {
    const { newPassword } = req.body;
    
    if (!newPassword || newPassword.length < 6) {
        return next(new ErrorResponse('Password must be at least 6 characters long', 400));
    }
    
    const user = await User.findById(req.params.id);
    
    if (!user) {
        return next(new ErrorResponse(`User not found with id of ${req.params.id}`, 404));
    }
    
    user.password = newPassword;
    user.updatedBy = req.user.id;
    await user.save();
    
    res.status(200).json({
        success: true,
        message: 'Password updated successfully'
    });
});

// @desc    Get user statistics
// @route   GET /api/users/stats
// @access  Private (Admin, Manager)
const getUserStats = asyncHandler(async (req, res, next) => {
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ isActive: true });
    const inactiveUsers = await User.countDocuments({ isActive: false });
    
    const usersByRole = await User.aggregate([
        {
            $group: {
                _id: '$role',
                count: { $sum: 1 }
            }
        }
    ]);
    
    const recentUsers = await User.find()
        .select('-password')
        .sort({ createdAt: -1 })
        .limit(5);
    
    res.status(200).json({
        success: true,
        data: {
            totalUsers,
            activeUsers,
            inactiveUsers,
            usersByRole,
            recentUsers
        }
    });
});

module.exports = {
    getUsers,
    getUser,
    createUser,
    updateUser,
    deleteUser,
    updateUserStatus,
    changeUserPassword,
    getUserStats
};
