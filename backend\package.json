{"name": "bitstech-pos-backend", "version": "1.0.0", "description": "Backend API for BitsTech POS System", "main": "src/server.js", "scripts": {"dev": "nodemon src/server.js", "start": "node src/server.js", "build": "echo 'Backend build completed'", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "format": "prettier --write src/", "db:migrate": "node src/scripts/migrate.js", "db:seed": "node src/scripts/seed.js", "db:seed-products": "node src/scripts/seedProducts.js", "db:seed-settings": "node src/scripts/seedSettings.js", "db:seed-all": "npm run db:seed && npm run db:seed-products && npm run db:seed-settings", "db:reset": "node src/scripts/reset.js", "backup": "node src/scripts/backup.js"}, "keywords": ["pos", "api", "express", "mongodb", "nodejs", "myanmar"], "author": "BitesTech Development Team", "license": "MIT", "dependencies": {"axios": "^1.9.0", "bcryptjs": "^2.4.3", "compression": "^1.7.4", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.0.1", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "moment": "^2.29.4", "mongoose": "^8.0.3", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.10.1", "puppeteer": "^24.10.1", "rate-limiter-flexible": "^2.4.2", "sharp": "^0.32.6", "uuid": "^9.0.1", "ws": "^8.18.2"}, "devDependencies": {"@types/jest": "^29.5.8", "eslint": "^8.55.0", "jest": "^29.7.0", "nodemon": "^3.0.2", "prettier": "^3.1.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}