const mongoose = require('mongoose');
const Supplier = require('../src/models/Supplier');
const User = require('../src/models/User');

// Database connection
const connectDB = async () => {
  try {
    await mongoose.connect('mongodb://localhost:27017/bitstech_pos');
    console.log('✅ MongoDB Connected');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

const seedSuppliers = async () => {
  try {
    console.log('🌱 Starting supplier seeding...');

    // Get admin user for createdBy field
    const adminUser = await User.findOne({ role: 'admin' });
    if (!adminUser) {
      console.error('❌ Admin user not found. Please seed users first.');
      return;
    }

    // Clear existing suppliers
    await Supplier.deleteMany({});
    console.log('🗑️  Cleared existing suppliers');

    // Sample suppliers data
    const suppliersData = [
      {
        name: 'TechWorld Myanmar',
        code: 'SUP001',
        contactPerson: {
          name: '<PERSON>g <PERSON>ng <PERSON>',
          email: '<EMAIL>',
          phone: '+95-9-*********'
        },
        company: {
          address: {
            street: 'No. 123, Pyay Road, Kamayut Township',
            city: 'Yangon',
            state: 'Yangon Region',
            zipCode: '11041',
            country: 'Myanmar'
          },
          phone: '+95-1-234567',
          website: 'https://techworld.mm',
          taxId: 'TW-001-2024'
        },
        paymentTerms: 'net_30',
        currency: 'MMK',
        notes: 'Primary supplier for computer hardware and accessories',
        isActive: true,
        rating: 5,
        createdBy: adminUser._id
      },
      {
        name: 'Digital Solutions Ltd',
        code: 'SUP002',
        contactPerson: {
          name: 'Ma Thida Win',
          email: '<EMAIL>',
          phone: '+95-9-*********'
        },
        company: {
          address: {
            street: 'Building 45, Hlaing Township',
            city: 'Yangon',
            state: 'Yangon Region',
            zipCode: '11051',
            country: 'Myanmar'
          },
          phone: '+95-1-345678',
          website: 'https://digitalsolutions.com',
          taxId: 'DS-002-2024'
        },
        paymentTerms: 'net_15',
        currency: 'MMK',
        notes: 'Specializes in laptops and mobile devices',
        isActive: true,
        rating: 4,
        createdBy: adminUser._id
      },
      {
        name: 'Myanmar IT Hub',
        code: 'SUP003',
        contactPerson: {
          name: 'Ko Zaw Min',
          email: '<EMAIL>',
          phone: '+95-9-*********'
        },
        company: {
          address: {
            street: 'IT Park, Thanlyin Township',
            city: 'Yangon',
            state: 'Yangon Region',
            zipCode: '11131',
            country: 'Myanmar'
          },
          phone: '+95-1-456789',
          website: 'https://myanmarithub.com',
          taxId: 'MIH-003-2024'
        },
        paymentTerms: 'net_45',
        currency: 'MMK',
        notes: 'Software and networking equipment supplier',
        isActive: true,
        rating: 4,
        createdBy: adminUser._id
      },
      {
        name: 'Global Tech Imports',
        code: 'SUP004',
        contactPerson: {
          name: 'Mr. John Smith',
          email: '<EMAIL>',
          phone: '+65-8888-9999'
        },
        company: {
          address: {
            street: '123 Orchard Road',
            city: 'Singapore',
            state: 'Singapore',
            zipCode: '238858',
            country: 'Singapore'
          },
          phone: '+65-6666-7777',
          website: 'https://globaltechimports.com',
          taxId: 'GTI-SG-2024'
        },
        paymentTerms: 'net_30',
        currency: 'USD',
        notes: 'International supplier for high-end electronics',
        isActive: true,
        rating: 5,
        createdBy: adminUser._id
      },
      {
        name: 'Local Electronics Store',
        code: 'SUP005',
        contactPerson: {
          name: 'Ma Khin Myo',
          email: '<EMAIL>',
          phone: '+95-9-*********'
        },
        company: {
          address: {
            street: 'No. 67, Bogyoke Aung San Road',
            city: 'Mandalay',
            state: 'Mandalay Region',
            zipCode: '05011',
            country: 'Myanmar'
          },
          phone: '+95-2-123456',
          taxId: 'LES-005-2024'
        },
        paymentTerms: 'cash',
        currency: 'MMK',
        notes: 'Local supplier for basic electronics and accessories',
        isActive: true,
        rating: 3,
        createdBy: adminUser._id
      }
    ];

    // Insert suppliers
    const suppliers = await Supplier.insertMany(suppliersData);
    console.log(`✅ Created ${suppliers.length} suppliers`);

    // Display created suppliers
    console.log('\n📋 Created Suppliers:');
    suppliers.forEach((supplier, index) => {
      console.log(`${index + 1}. ${supplier.name} (${supplier.code})`);
      console.log(`   Contact: ${supplier.contactPerson.name} - ${supplier.contactPerson.email}`);
      console.log(`   Payment Terms: ${supplier.paymentTerms}`);
      console.log(`   Currency: ${supplier.currency}`);
      console.log(`   Active: ${supplier.isActive}`);
      console.log(`   Rating: ${supplier.rating}/5`);
      console.log('');
    });

    console.log('🎉 Supplier seeding completed successfully!');

  } catch (error) {
    console.error('❌ Error seeding suppliers:', error);
  }
};

const runSeeder = async () => {
  await connectDB();
  await seedSuppliers();
  await mongoose.connection.close();
  console.log('📊 Database connection closed');
  process.exit(0);
};

// Run seeder
runSeeder();
