const express = require('express');
const router = express.Router();
const asyncHandler = require('../middleware/asyncHandler');
const { protect, authorize } = require('../middleware/auth');
const Sale = require('../models/Sale');
const Product = require('../models/Product');
const ErrorResponse = require('../utils/errorResponse');
const aiService = require('../services/aiService');

// @desc    Get comprehensive forecasting data
// @route   GET /api/forecasting
// @access  Private
router.get('/', protect, asyncHandler(async (req, res) => {
    const {
        period = 'next_month',
        includeProductForecasts = 'true',
        includeMarketTrends = 'true'
    } = req.query;

    console.log('📊 Generating comprehensive forecasting data for period:', period);

    try {
        // Generate sales forecasts
        const salesForecasts = await generateSalesForecasts(period);

        // Generate product-specific forecasts if requested
        let productForecasts = [];
        if (includeProductForecasts === 'true') {
            productForecasts = await generateProductForecasts();
        }

        // Generate market trends if requested
        let marketTrends = [];
        if (includeMarketTrends === 'true') {
            marketTrends = await generateMarketTrends();
        }

        // Generate forecast periods for UI
        const forecastPeriods = generateForecastPeriods(period);

        res.status(200).json({
            success: true,
            data: {
                period,
                salesForecasts,
                productForecasts,
                marketTrends,
                forecastPeriods,
                generatedAt: new Date().toISOString()
            }
        });

    } catch (error) {
        console.error('❌ Comprehensive forecasting error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
}));

// @desc    Get sales forecast
// @route   GET /api/forecasting/sales
// @access  Private
router.get('/sales', protect, asyncHandler(async (req, res) => {
    const { period = 'next_week', confidence = 85 } = req.query;

    // Get historical sales data (last 90 days for better prediction)
    const historicalDays = 90;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - historicalDays);

    const historicalSales = await Sale.find({
        createdAt: { $gte: startDate },
        status: 'completed'
    }).sort({ createdAt: 1 });

    // Calculate daily averages
    const dailyStats = {};
    historicalSales.forEach(sale => {
        const dateKey = sale.createdAt.toISOString().split('T')[0];
        if (!dailyStats[dateKey]) {
            dailyStats[dateKey] = {
                revenue: 0,
                orders: 0,
                customers: new Set(),
                items: 0
            };
        }
        dailyStats[dateKey].revenue += sale.totalAmount;
        dailyStats[dateKey].orders += 1;
        dailyStats[dateKey].customers.add(sale.customer?.name || 'walk-in');
        dailyStats[dateKey].items += sale.totalItems;
    });

    // Convert to arrays for calculation
    const dailyRevenues = Object.values(dailyStats).map(day => day.revenue);
    const dailyOrders = Object.values(dailyStats).map(day => day.orders);
    const dailyCustomers = Object.values(dailyStats).map(day => day.customers.size);

    // Simple moving average calculation
    const avgDailyRevenue = dailyRevenues.reduce((sum, val) => sum + val, 0) / dailyRevenues.length || 0;
    const avgDailyOrders = dailyOrders.reduce((sum, val) => sum + val, 0) / dailyOrders.length || 0;
    const avgDailyCustomers = dailyCustomers.reduce((sum, val) => sum + val, 0) / dailyCustomers.length || 0;

    // Calculate growth trends (last 30 days vs previous 30 days)
    const recentRevenues = dailyRevenues.slice(-30);
    const previousRevenues = dailyRevenues.slice(-60, -30);
    
    const recentAvg = recentRevenues.reduce((sum, val) => sum + val, 0) / recentRevenues.length || 0;
    const previousAvg = previousRevenues.reduce((sum, val) => sum + val, 0) / previousRevenues.length || 0;
    
    const growthRate = previousAvg > 0 ? ((recentAvg - previousAvg) / previousAvg) * 100 : 0;

    // Generate forecasts based on period
    let forecastDays, startForecast, endForecast;
    
    switch (period) {
        case 'next_week':
            forecastDays = 7;
            startForecast = new Date();
            startForecast.setDate(startForecast.getDate() + 1);
            endForecast = new Date(startForecast);
            endForecast.setDate(endForecast.getDate() + 6);
            break;
        case 'next_month':
            forecastDays = 30;
            startForecast = new Date();
            startForecast.setDate(startForecast.getDate() + 1);
            endForecast = new Date(startForecast);
            endForecast.setDate(endForecast.getDate() + 29);
            break;
        case 'next_quarter':
            forecastDays = 90;
            startForecast = new Date();
            startForecast.setDate(startForecast.getDate() + 1);
            endForecast = new Date(startForecast);
            endForecast.setDate(endForecast.getDate() + 89);
            break;
        default:
            forecastDays = 7;
            startForecast = new Date();
            startForecast.setDate(startForecast.getDate() + 1);
            endForecast = new Date(startForecast);
            endForecast.setDate(endForecast.getDate() + 6);
    }

    // Apply growth trend to predictions
    const trendMultiplier = 1 + (growthRate / 100);
    const predictedDailyRevenue = avgDailyRevenue * trendMultiplier;
    const predictedDailyOrders = avgDailyOrders * trendMultiplier;
    const predictedDailyCustomers = avgDailyCustomers * trendMultiplier;

    // Calculate confidence intervals (simple approach)
    const confidenceMultiplier = confidence / 100;
    const variance = 0.15; // 15% variance assumption
    
    const forecast = {
        period,
        startDate: startForecast.toISOString().split('T')[0],
        endDate: endForecast.toISOString().split('T')[0],
        confidence: parseInt(confidence),
        revenue: {
            predicted: Math.round(predictedDailyRevenue * forecastDays),
            min: Math.round(predictedDailyRevenue * forecastDays * (1 - variance)),
            max: Math.round(predictedDailyRevenue * forecastDays * (1 + variance)),
            growth: parseFloat(growthRate.toFixed(1))
        },
        orders: {
            predicted: Math.round(predictedDailyOrders * forecastDays),
            min: Math.round(predictedDailyOrders * forecastDays * (1 - variance)),
            max: Math.round(predictedDailyOrders * forecastDays * (1 + variance)),
            growth: parseFloat(growthRate.toFixed(1))
        },
        customers: {
            predicted: Math.round(predictedDailyCustomers * forecastDays),
            min: Math.round(predictedDailyCustomers * forecastDays * (1 - variance)),
            max: Math.round(predictedDailyCustomers * forecastDays * (1 + variance)),
            growth: parseFloat(growthRate.toFixed(1))
        },
        factors: [
            'Historical sales data',
            'Seasonal trends',
            'Growth patterns',
            'Market conditions'
        ]
    };

    res.status(200).json({
        success: true,
        data: forecast
    });
}));

// @desc    Get product demand forecast
// @route   GET /api/forecasting/products
// @access  Private
router.get('/products', protect, asyncHandler(async (req, res) => {
    const { limit = 10, period = 'next_week' } = req.query;

    // Get historical sales data for product analysis
    const historicalDays = 60;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - historicalDays);

    const sales = await Sale.find({
        createdAt: { $gte: startDate },
        status: 'completed'
    });

    // Aggregate product sales data
    const productStats = {};
    sales.forEach(sale => {
        sale.items.forEach(item => {
            const productId = item.product.toString();
            if (!productStats[productId]) {
                productStats[productId] = {
                    productId,
                    productName: item.productName,
                    productSku: item.productSku,
                    totalQuantity: 0,
                    salesCount: 0,
                    revenue: 0,
                    dailySales: {}
                };
            }

            const dateKey = sale.createdAt.toISOString().split('T')[0];
            if (!productStats[productId].dailySales[dateKey]) {
                productStats[productId].dailySales[dateKey] = 0;
            }

            productStats[productId].totalQuantity += item.quantity;
            productStats[productId].salesCount += 1;
            productStats[productId].revenue += item.totalPrice;
            productStats[productId].dailySales[dateKey] += item.quantity;
        });
    });

    // Calculate forecasts for each product
    const productForecasts = Object.values(productStats)
        .map(product => {
            const dailySalesArray = Object.values(product.dailySales);
            const avgDailySales = dailySalesArray.reduce((sum, val) => sum + val, 0) / historicalDays;
            
            // Simple trend calculation
            const recentSales = dailySalesArray.slice(-14); // Last 2 weeks
            const previousSales = dailySalesArray.slice(-28, -14); // Previous 2 weeks
            
            const recentAvg = recentSales.reduce((sum, val) => sum + val, 0) / recentSales.length || 0;
            const previousAvg = previousSales.reduce((sum, val) => sum + val, 0) / previousSales.length || 0;
            
            const trend = previousAvg > 0 ? ((recentAvg - previousAvg) / previousAvg) * 100 : 0;
            
            let forecastDays = 7;
            if (period === 'next_month') forecastDays = 30;
            if (period === 'next_quarter') forecastDays = 90;

            const trendMultiplier = 1 + (trend / 100);
            const predictedQuantity = Math.round(avgDailySales * trendMultiplier * forecastDays);

            return {
                productId: product.productId,
                productName: product.productName,
                productSku: product.productSku,
                currentStock: 0, // Will be updated from Product model
                predictedDemand: predictedQuantity,
                confidence: Math.min(95, Math.max(60, 85 + (product.salesCount / 10))), // Confidence based on sales frequency
                trend: trend > 5 ? 'increasing' : trend < -5 ? 'decreasing' : 'stable',
                trendPercentage: parseFloat(trend.toFixed(1)),
                category: 'General' // Will be updated from Product model
            };
        })
        .sort((a, b) => b.predictedDemand - a.predictedDemand)
        .slice(0, parseInt(limit));

    // Get current stock levels from Product model
    const productIds = productForecasts.map(pf => pf.productId);
    const products = await Product.find({ _id: { $in: productIds } })
        .populate('category', 'name');

    // Update forecasts with current stock and category info
    productForecasts.forEach(forecast => {
        const product = products.find(p => p._id.toString() === forecast.productId);
        if (product) {
            forecast.currentStock = product.inventory?.quantity || 0;
            forecast.category = product.category?.name || 'General';
            forecast.reorderSuggestion = forecast.currentStock < forecast.predictedDemand;
        }
    });

    res.status(200).json({
        success: true,
        data: productForecasts
    });
}));

// @desc    Get market trends analysis
// @route   GET /api/forecasting/trends
// @access  Private
router.get('/trends', protect, asyncHandler(async (req, res) => {
    const { period = '90d' } = req.query;

    const days = parseInt(period.replace('d', '')) || 90;
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - days);

    const sales = await Sale.find({
        createdAt: { $gte: startDate },
        status: 'completed'
    }).sort({ createdAt: 1 });

    // Analyze trends by week
    const weeklyData = {};
    sales.forEach(sale => {
        const weekStart = new Date(sale.createdAt);
        weekStart.setDate(weekStart.getDate() - weekStart.getDay());
        const weekKey = weekStart.toISOString().split('T')[0];

        if (!weeklyData[weekKey]) {
            weeklyData[weekKey] = {
                week: weekKey,
                revenue: 0,
                orders: 0,
                customers: new Set(),
                avgOrderValue: 0
            };
        }

        weeklyData[weekKey].revenue += sale.totalAmount;
        weeklyData[weekKey].orders += 1;
        weeklyData[weekKey].customers.add(sale.customer?.name || 'walk-in');
    });

    // Calculate average order values
    Object.values(weeklyData).forEach(week => {
        week.avgOrderValue = week.orders > 0 ? week.revenue / week.orders : 0;
        week.customers = week.customers.size;
    });

    const trends = Object.values(weeklyData).sort((a, b) => a.week.localeCompare(b.week));

    // Calculate overall trends
    const recentWeeks = trends.slice(-4); // Last 4 weeks
    const previousWeeks = trends.slice(-8, -4); // Previous 4 weeks

    const recentAvgRevenue = recentWeeks.reduce((sum, week) => sum + week.revenue, 0) / recentWeeks.length || 0;
    const previousAvgRevenue = previousWeeks.reduce((sum, week) => sum + week.revenue, 0) / previousWeeks.length || 0;

    const revenueTrend = previousAvgRevenue > 0 ? ((recentAvgRevenue - previousAvgRevenue) / previousAvgRevenue) * 100 : 0;

    res.status(200).json({
        success: true,
        data: {
            trends,
            summary: {
                revenueTrend: parseFloat(revenueTrend.toFixed(1)),
                trendDirection: revenueTrend > 5 ? 'increasing' : revenueTrend < -5 ? 'decreasing' : 'stable',
                totalWeeks: trends.length,
                avgWeeklyRevenue: trends.reduce((sum, week) => sum + week.revenue, 0) / trends.length || 0
            }
        }
    });
}));

// Helper functions for comprehensive forecasting
async function generateSalesForecasts(period) {
    const forecasts = [];
    const currentDate = new Date();

    // Determine forecast range based on period
    let days = 30; // default to next month
    switch (period) {
        case 'next_week':
            days = 7;
            break;
        case 'next_month':
            days = 30;
            break;
        case 'next_quarter':
            days = 90;
            break;
        case 'next_year':
            days = 365;
            break;
    }

    // Get historical sales data for better predictions
    const historicalSales = await getHistoricalSalesData();
    const avgDailySales = historicalSales.avgDailySales || 500000;

    // Generate forecasts for each day
    for (let i = 1; i <= Math.min(days, 30); i++) { // Limit to 30 days for performance
        const forecastDate = new Date(currentDate);
        forecastDate.setDate(currentDate.getDate() + i);

        // Use AI service for prediction
        const predictionData = {
            dayOfWeek: forecastDate.getDay(),
            month: forecastDate.getMonth() + 1,
            weather: Math.random() * 0.4 + 0.6, // Random weather factor
            promotions: Math.random() * 0.3, // Random promotion factor
            holidays: isHoliday(forecastDate) ? 1 : 0,
            previousSales: avgDailySales,
            seasonality: getSeasonalityFactor(forecastDate)
        };

        const prediction = await aiService.predictSales(predictionData);

        forecasts.push({
            date: forecastDate.toISOString().split('T')[0],
            predicted_sales: prediction.success ? prediction.prediction : avgDailySales,
            confidence: prediction.confidence || 0.75,
            factors: {
                dayOfWeek: predictionData.dayOfWeek,
                seasonality: predictionData.seasonality,
                weather: predictionData.weather,
                promotions: predictionData.promotions,
                holidays: predictionData.holidays
            }
        });
    }

    return forecasts;
}

async function generateProductForecasts() {
    try {
        const products = await Product.find({ isActive: true }).populate('category').limit(10);
        const forecasts = [];

        for (const product of products) {
            // Get product sales history
            const productSales = await getProductSalesHistory(product._id);

            // Generate inventory optimization
            const inventoryData = {
                currentStock: product.inventory.quantity,
                salesVelocity: productSales.avgDailySales || 1,
                leadTime: 7, // Default 7 days lead time
                seasonality: 0.7,
                promotions: 0.2,
                cost: product.cost
            };

            const optimization = await aiService.optimizeInventory(inventoryData);

            forecasts.push({
                product_id: product._id,
                product_name: product.name,
                current_stock: product.inventory.quantity,
                predicted_demand: Math.round(productSales.avgDailySales * 30),
                recommended_stock: optimization.success ? optimization.optimalStock : product.inventory.quantity + 10,
                reorder_point: Math.round(productSales.avgDailySales * 7),
                confidence: optimization.confidence || 0.8,
                category: product.category?.name || 'General',
                price: product.price,
                profit_margin: ((product.price - product.cost) / product.price * 100).toFixed(2)
            });
        }

        return forecasts;
    } catch (error) {
        console.error('Error generating product forecasts:', error);
        return [];
    }
}

async function generateMarketTrends() {
    const trends = [
        {
            category: 'Laptops',
            trend: 'increasing',
            growth_rate: 15.5,
            confidence: 0.85,
            description: 'Strong demand for gaming and professional laptops',
            recommendation: 'Increase laptop inventory by 20%'
        },
        {
            category: 'Components',
            trend: 'stable',
            growth_rate: 5.2,
            confidence: 0.78,
            description: 'Steady demand for computer components',
            recommendation: 'Maintain current stock levels'
        },
        {
            category: 'Accessories',
            trend: 'increasing',
            growth_rate: 22.3,
            confidence: 0.82,
            description: 'High demand for gaming accessories and peripherals',
            recommendation: 'Focus on gaming accessories and RGB products'
        },
        {
            category: 'Monitors',
            trend: 'increasing',
            growth_rate: 18.7,
            confidence: 0.79,
            description: 'Growing demand for high-refresh gaming monitors',
            recommendation: 'Stock more 144Hz+ gaming monitors'
        },
        {
            category: 'Storage',
            trend: 'stable',
            growth_rate: 8.1,
            confidence: 0.75,
            description: 'Consistent demand for SSDs and external storage',
            recommendation: 'Maintain balanced SSD/HDD inventory'
        }
    ];

    return trends;
}

function generateForecastPeriods(selectedPeriod) {
    const periods = [
        {
            id: 'next_week',
            name: 'Next Week',
            days: 7,
            selected: selectedPeriod === 'next_week'
        },
        {
            id: 'next_month',
            name: 'Next Month',
            days: 30,
            selected: selectedPeriod === 'next_month'
        },
        {
            id: 'next_quarter',
            name: 'Next Quarter',
            days: 90,
            selected: selectedPeriod === 'next_quarter'
        },
        {
            id: 'next_year',
            name: 'Next Year',
            days: 365,
            selected: selectedPeriod === 'next_year'
        }
    ];

    return periods;
}

async function getHistoricalSalesData() {
    try {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const sales = await Sale.find({
            createdAt: { $gte: thirtyDaysAgo }
        });

        const totalSales = sales.reduce((sum, sale) => sum + (sale.totalAmount || sale.total || 0), 0);
        const avgDailySales = totalSales / 30;

        return {
            totalSales,
            avgDailySales,
            salesCount: sales.length
        };
    } catch (error) {
        console.error('Error getting historical sales:', error);
        return { avgDailySales: 500000 };
    }
}

async function getProductSalesHistory(productId) {
    try {
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);

        const sales = await Sale.find({
            'items.product': productId,
            createdAt: { $gte: thirtyDaysAgo }
        });

        let totalQuantity = 0;
        sales.forEach(sale => {
            sale.items.forEach(item => {
                if (item.product.toString() === productId.toString()) {
                    totalQuantity += item.quantity;
                }
            });
        });

        return {
            totalQuantity,
            avgDailySales: totalQuantity / 30
        };
    } catch (error) {
        console.error('Error getting product sales history:', error);
        return { avgDailySales: 1 };
    }
}

function isHoliday(date) {
    // Myanmar holidays (simplified)
    const holidays = [
        '01-01', // New Year
        '01-04', // Independence Day
        '02-12', // Union Day
        '03-02', // Peasants Day
        '03-27', // Armed Forces Day
        '04-13', '04-14', '04-15', '04-16', // Thingyan
        '05-01', // Labour Day
        '07-19', // Martyrs Day
        '12-25'  // Christmas
    ];

    const monthDay = String(date.getMonth() + 1).padStart(2, '0') + '-' +
                    String(date.getDate()).padStart(2, '0');

    return holidays.includes(monthDay);
}

function getSeasonalityFactor(date) {
    const month = date.getMonth() + 1;

    // Higher sales during festival seasons and year-end
    if ([12, 1, 4, 10].includes(month)) {
        return 0.8; // High season
    } else if ([6, 7, 8].includes(month)) {
        return 0.5; // Low season (rainy season)
    } else {
        return 0.7; // Normal season
    }
}

module.exports = router;
