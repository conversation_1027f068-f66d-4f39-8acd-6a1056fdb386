'use client'

import React, { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { useAuth } from '@/contexts/auth-context'
import { useSettings } from '@/contexts/settings-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  Package,
  Users,
  BarChart3,
  DollarSign,
  ShoppingCart,
  AlertTriangle,
  Plus,
  Eye,
  RefreshCw,
  TrendingUp,
  TrendingDown,
  Star,
  Target,
  Activity
} from 'lucide-react'

export default function DashboardPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const { formatCurrency } = useCurrency()
  const router = useRouter()

  // Dashboard data state
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [inventoryAlerts, setInventoryAlerts] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [lastUpdated, setLastUpdated] = useState<Date>(new Date())

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Load dashboard data
  const loadDashboardData = async (showRefreshing = false) => {
    if (showRefreshing) setRefreshing(true)
    else setLoading(true)

    try {
      const [statsResponse, alertsResponse] = await Promise.all([
        apiClient.getDashboardStats(),
        apiClient.getInventoryAlerts()
      ])

      if (statsResponse.success) {
        setDashboardData(statsResponse.data)
      }

      if (alertsResponse.success) {
        setInventoryAlerts(alertsResponse.data)
      }

      setLastUpdated(new Date())
      console.log('📊 Dashboard data loaded successfully')
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // Real-time WebSocket connection for dashboard updates
  useEffect(() => {
    if (!isAuthenticated) return

    // Handle sales updates
    const handleSalesUpdate = (event: CustomEvent) => {
      const data = event.detail
      console.log('💰 Sales update received:', data)

      setDashboardData((prev: any) => prev ? {
        ...prev,
        todaySales: {
          ...prev.todaySales,
          amount: data.todaySales.amount,
          transactions: data.todaySales.transactions,
          change: data.todaySales.change
        },
        recentSales: data.recentSales || prev.recentSales
      } : prev)
      setLastUpdated(new Date())
    }

    // Handle currency changes - refresh dashboard data
    const handleCurrencyChange = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      console.log('🔄 Dashboard currency changed to:', newCurrency)
      // Refresh dashboard data to reflect new currency
      loadDashboardData(true)
    }

    // Handle inventory updates
    const handleInventoryUpdate = (event: CustomEvent) => {
      const data = event.detail
      console.log('📦 Inventory update received:', data)

      setDashboardData((prev: any) => prev ? {
        ...prev,
        totalProducts: {
          ...prev.totalProducts,
          count: data.totalProducts
        },
        lowStockItems: {
          count: data.lowStockCount,
          items: data.lowStockItems || prev.lowStockItems.items
        }
      } : prev)

      if (data.inventoryAlerts) {
        setInventoryAlerts(data.inventoryAlerts)
      }
      setLastUpdated(new Date())
    }

    // Handle full dashboard updates
    const handleFullDashboardUpdate = (event: CustomEvent) => {
      const data = event.detail
      console.log('🔄 Full dashboard update received:', data)

      setDashboardData(data.dashboardData)
      if (data.inventoryAlerts) {
        setInventoryAlerts(data.inventoryAlerts)
      }
      setLastUpdated(new Date())
    }

    // Add event listeners
    window.addEventListener('ws-sales-update', handleSalesUpdate as EventListener)
    window.addEventListener('ws-dashboard-update', handleInventoryUpdate as EventListener)
    window.addEventListener('ws-full-dashboard-update', handleFullDashboardUpdate as EventListener)
    window.addEventListener('global-currency-sync', handleCurrencyChange as EventListener)

    // Initialize WebSocket connection
    const initWebSocket = async () => {
      try {
        const { default: wsClient, subscribeToDashboardUpdates } = await import('@/lib/websocket')
        subscribeToDashboardUpdates()
        console.log('✅ Dashboard WebSocket subscribed')
      } catch (error) {
        console.warn('WebSocket not available:', error)
      }
    }

    initWebSocket()

    // Cleanup function
    return () => {
      window.removeEventListener('ws-sales-update', handleSalesUpdate as EventListener)
      window.removeEventListener('ws-dashboard-update', handleInventoryUpdate as EventListener)
      window.removeEventListener('ws-full-dashboard-update', handleFullDashboardUpdate as EventListener)
      window.removeEventListener('global-currency-sync', handleCurrencyChange as EventListener)
    }
  }, [isAuthenticated])

  // Real-time settings synchronization
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'bitstech_dashboard_stats' && event.newValue) {
        try {
          const newDashboardData = JSON.parse(event.newValue)
          setDashboardData(newDashboardData)
          console.log('🔄 Dashboard data synced from storage')
        } catch (error) {
          console.error('Error syncing dashboard data:', error)
        }
      }

      if (event.key === 'bitstech_inventory_alerts' && event.newValue) {
        try {
          const newAlerts = JSON.parse(event.newValue)
          setInventoryAlerts(newAlerts)
          console.log('⚠️ Inventory alerts synced from storage')
        } catch (error) {
          console.error('Error syncing inventory alerts:', error)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  // Load data on component mount
  useEffect(() => {
    if (isAuthenticated) {
      loadDashboardData()
    }
  }, [isAuthenticated])

  // Auto-refresh every 30 minutes (reduced frequency to prevent excessive API calls)
  useEffect(() => {
    if (!isAuthenticated) return

    const interval = setInterval(() => {
      loadDashboardData(true)
    }, 30 * 60 * 1000) // 30 minutes instead of 10

    return () => clearInterval(interval)
  }, [isAuthenticated])



  if (!isAuthenticated) {
    return null
  }

  // Generate stats from real data with language support
  const stats = dashboardData ? [
    {
      title: language === 'mm' ? 'ယနေ့ ရောင်းအား' : 'Today\'s Sales',
      value: formatCurrency(dashboardData.todaySales?.amount || 0),
      change: `${dashboardData.todaySales?.change > 0 ? '+' : ''}${dashboardData.todaySales?.change}%`,
      changeType: dashboardData.todaySales?.change >= 0 ? 'positive' as const : 'negative' as const,
      icon: DollarSign,
      color: 'text-green-600'
    },
    {
      title: language === 'mm' ? 'စုစုပေါင်း ကုန်ပစ္စည်းများ' : 'Total Products',
      value: dashboardData.totalProducts?.count?.toLocaleString() || '0',
      change: `${dashboardData.totalProducts?.change > 0 ? '+' : ''}${dashboardData.totalProducts?.change}`,
      changeType: dashboardData.totalProducts?.change >= 0 ? 'positive' as const : 'negative' as const,
      icon: Package,
      color: 'text-blue-600'
    },
    {
      title: language === 'mm' ? 'ငွေလွှဲမှုများ' : 'Transactions',
      value: dashboardData.todaySales?.transactions?.toString() || '0',
      change: '+23%',
      changeType: 'positive' as const,
      icon: ShoppingCart,
      color: 'text-purple-600'
    },
    {
      title: language === 'mm' ? 'စတော့နည်းသော ပစ္စည်းများ' : 'Low Stock Items',
      value: dashboardData.lowStockItems?.count?.toString() || '0',
      change: '-3',
      changeType: 'negative' as const,
      icon: AlertTriangle,
      color: 'text-orange-600'
    }
  ] : []

  const quickActions = [
    {
      title: language === 'mm' ? 'ရောင်းချမှုအသစ်' : 'New Sale',
      description: language === 'mm' ? 'ငွေလွှဲမှုအသစ် စတင်ရန်' : 'Start a new transaction',
      icon: Plus,
      href: '/pos',
      color: 'bg-blue-600 hover:bg-blue-700'
    },
    {
      title: language === 'mm' ? 'ကုန်ပစ္စည်း ထည့်ရန်' : 'Add Product',
      description: language === 'mm' ? 'စတော့သို့ ကုန်ပစ္စည်းအသစ် ထည့်ရန်' : 'Add new product to inventory',
      icon: Package,
      href: '/products/new',
      color: 'bg-green-600 hover:bg-green-700'
    },
    {
      title: language === 'mm' ? 'အစီရင်ခံစာများ ကြည့်ရန်' : 'View Reports',
      description: language === 'mm' ? 'ရောင်းအားနှင့် စတော့ အစီရင်ခံစာများ စစ်ဆေးရန်' : 'Check sales and inventory reports',
      icon: BarChart3,
      href: '/reports',
      color: 'bg-purple-600 hover:bg-purple-700'
    },
    {
      title: language === 'mm' ? 'အသုံးပြုသူများ စီမံရန်' : 'Manage Users',
      description: language === 'mm' ? 'အသုံးပြုသူ အကောင့်များ ထည့်ရန် သို့မဟုတ် ပြင်ဆင်ရန်' : 'Add or edit user accounts',
      icon: Users,
      href: '/users',
      color: 'bg-indigo-600 hover:bg-indigo-700'
    }
  ]

  // Use real recent sales data
  const recentSales = dashboardData?.recentSales || []

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {language === 'mm' ? 'ကြိုဆိုပါသည်' : 'Welcome back'}, {user?.firstName}!
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {language === 'mm'
                ? 'ယနေ့ သင့်လုပ်ငန်း အခြေအနေ ခြုံငုံကြည့်ရှုပါ'
                : 'Here\'s what\'s happening with your business today'
              }
            </p>
          </div>
          <div className="text-right">
            <div className="flex items-center gap-2 mb-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => loadDashboardData(true)}
                disabled={refreshing}
                className="flex items-center gap-2"
              >
                <RefreshCw className={`h-4 w-4 ${refreshing ? 'animate-spin' : ''}`} />
                {language === 'mm' ? 'ပြန်လည်ရယူ' : 'Refresh'}
              </Button>
            </div>
            <p className="text-sm text-gray-500 dark:text-gray-400">
              {user?.role === 'admin' && (language === 'mm' ? 'အုပ်ချုပ်သူ' : 'Administrator')}
              {user?.role === 'manager' && (language === 'mm' ? 'မန်နေဂျာ' : 'Manager')}
              {user?.role === 'cashier' && (language === 'mm' ? 'ငွေကောင်တာ' : 'Cashier')}
            </p>
            <p className="text-xs text-gray-400">
              {language === 'mm' ? 'နောက်ဆုံး အပ်ဒိတ်' : 'Last updated'}: {lastUpdated.toLocaleTimeString()}
            </p>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {stats.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card key={stat.title}>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    {stat.title}
                  </CardTitle>
                  <Icon className={`h-4 w-4 ${stat.color}`} />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {stat.value}
                  </div>
                  <p className={`text-xs ${
                    stat.changeType === 'positive' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {stat.change} from yesterday
                  </p>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Quick Actions */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            {language === 'mm' ? 'လျင်မြန်သော လုပ်ဆောင်ချက်များ' : 'Quick Actions'}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {quickActions.map((action, index) => {
              const Icon = action.icon
              return (
                <Button
                  key={action.title}
                  variant="outline"
                  className={`h-auto p-6 flex flex-col items-center space-y-2 ${action.color} text-white border-0`}
                  onClick={() => router.push(action.href)}
                >
                  <Icon className="h-8 w-8" />
                  <div className="text-center">
                    <div className="font-medium">
                      {action.title}
                    </div>
                    <div className="text-xs opacity-90">
                      {action.description}
                    </div>
                  </div>
                </Button>
              )
            })}
          </div>
        </div>

        {/* Charts Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Sales Chart */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'ရောင်းအား လမ်းကြောင်း' : 'Sales Trend'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' ? 'လွန်ခဲ့သော ၇ ရက်' : 'Last 7 days'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="h-64 flex items-center justify-center">
                {dashboardData?.salesChart ? (
                  <div className="w-full h-full flex items-end justify-between gap-2 px-4">
                    {dashboardData.salesChart.map((day: any, index: number) => (
                      <div key={index} className="flex flex-col items-center gap-2 flex-1">
                        <div
                          className="bg-blue-600 rounded-t-md w-full transition-all duration-300 hover:bg-blue-700"
                          style={{
                            height: `${Math.max(10, (day.amount / Math.max(...dashboardData.salesChart.map((d: any) => d.amount))) * 200)}px`
                          }}
                          title={`${day.day}: ${formatCurrency(day.amount)}`}
                        ></div>
                        <span className="text-xs text-gray-500 font-medium">{day.day}</span>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-gray-500">
                    <BarChart3 className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>{language === 'mm' ? 'ဒေတာ မရှိသေးပါ' : 'No data available'}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Category Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-green-600" />
                {language === 'mm' ? 'အမျိုးအစား ခွဲဝေမှု' : 'Category Distribution'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' ? 'ရောင်းအား အမျိုးအစားအလိုက်' : 'Sales by category'}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {dashboardData?.categoryStats ? dashboardData.categoryStats.map((category: any, index: number) => {
                  const colors = ['bg-blue-600', 'bg-green-600', 'bg-purple-600', 'bg-orange-600', 'bg-red-600']
                  const percentage = (category.sales / dashboardData.totalSales) * 100
                  return (
                    <div key={category.name} className="space-y-2">
                      <div className="flex justify-between items-center">
                        <span className="text-sm font-medium">{category.name}</span>
                        <span className="text-sm text-gray-500">{percentage.toFixed(1)}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${colors[index % colors.length]} transition-all duration-500`}
                          style={{ width: `${percentage}%` }}
                        ></div>
                      </div>
                      <div className="flex justify-between text-xs text-gray-500">
                        <span>{formatCurrency(category.sales)}</span>
                        <span>{category.products} {language === 'mm' ? 'ပစ္စည်း' : 'products'}</span>
                      </div>
                    </div>
                  )
                }) : (
                  <div className="text-center text-gray-500 py-8">
                    <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
                    <p>{language === 'mm' ? 'အမျိုးအစား ဒေတာ မရှိသေးပါ' : 'No category data available'}</p>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Sales */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>
                {language === 'mm' ? 'လတ်တလော ရောင်းချမှုများ' : 'Recent Sales'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ယနေ့ နောက်ဆုံး ငွေလွှဲမှုများ'
                  : 'Latest transactions from today'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentSales.length > 0 ? recentSales.map((sale: any) => (
                  <div key={sale.id} className="flex items-center justify-between">
                    <div>
                      <p className="font-medium">{sale.customer}</p>
                      <p className="text-sm text-gray-500">#{sale.id} • {sale.time}</p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(sale.amount)}</p>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => router.push(`/sales/${sale.id}`)}
                        title={language === 'mm' ? 'ရောင်းချမှု ကြည့်ရန်' : 'View Sale Details'}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                )) : (
                  <div className="text-center py-4 text-gray-500">
                    {language === 'mm' ? 'ရောင်းချမှု မရှိသေးပါ' : 'No recent sales'}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>
                {language === 'mm' ? 'စတော့ သတိပေးချက်များ' : 'Inventory Alerts'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'အာရုံစိုက်ရမည့် ပစ္စည်းများ'
                  : 'Items that need your attention'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {inventoryAlerts.length > 0 ? inventoryAlerts.map((alert: any) => (
                  <div
                    key={alert.id}
                    className={`flex items-center justify-between p-3 rounded-lg ${
                      alert.severity === 'high'
                        ? 'bg-red-50 dark:bg-red-900/20'
                        : 'bg-orange-50 dark:bg-orange-900/20'
                    }`}
                  >
                    <div className="flex items-center space-x-3">
                      <AlertTriangle className={`h-5 w-5 ${
                        alert.severity === 'high' ? 'text-red-600' : 'text-orange-600'
                      }`} />
                      <div>
                        <p className="font-medium">{alert.productName}</p>
                        <p className="text-sm text-gray-500">
                          {alert.currentStock === 0
                            ? (language === 'mm' ? 'စတော့ ကုန်သွားပြီ' : 'Out of stock')
                            : `${language === 'mm' ? 'လက်ကျန်' : 'Only'} ${alert.currentStock} ${language === 'mm' ? 'ခု သာ ကျန်တော့သည်' : 'remaining'}`
                          }
                        </p>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => router.push(`/purchase-orders/new?product=${alert.productId}`)}
                      title={language === 'mm' ? 'ပစ္စည်း အော်ဒါ လုပ်ရန်' : 'Create Purchase Order'}
                    >
                      {language === 'mm' ? 'အော်ဒါ' : 'Order'}
                    </Button>
                  </div>
                )) : (
                  <div className="text-center py-4 text-gray-500">
                    {language === 'mm' ? 'သတိပေးချက် မရှိပါ' : 'No alerts'}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
