'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import apiClient from '@/lib/api'

interface AppearanceSettings {
  theme: 'light' | 'dark' | 'system'
  primaryColor: string
  secondaryColor: string
  accentColor: string
  borderRadius: string
  fontSize: string
  fontFamily: string
  sidebarStyle: 'default' | 'compact' | 'minimal'
  headerStyle: 'default' | 'compact' | 'minimal'
  animationsEnabled: boolean
  reducedMotion: boolean
  highContrast: boolean
  colorBlindFriendly: boolean
}

interface AppearanceContextType {
  settings: AppearanceSettings
  updateSettings: (newSettings: Partial<AppearanceSettings>) => Promise<boolean>
  applySettings: () => void
  resetToDefaults: () => void
}

const defaultSettings: AppearanceSettings = {
  theme: 'system',
  primaryColor: '#3B82F6',
  secondaryColor: '#6B7280',
  accentColor: '#10B981',
  borderRadius: 'medium',
  fontSize: 'medium',
  fontFamily: 'Inter',
  sidebarStyle: 'default',
  headerStyle: 'default',
  animationsEnabled: true,
  reducedMotion: false,
  highContrast: false,
  colorBlindFriendly: false
}

const AppearanceContext = createContext<AppearanceContextType | undefined>(undefined)

export function AppearanceProvider({ children }: { children: ReactNode }) {
  const [settings, setSettings] = useState<AppearanceSettings>(defaultSettings)

  useEffect(() => {
    loadSettings()
  }, [])

  useEffect(() => {
    applySettings()
  }, [settings])

  const loadSettings = async () => {
    try {
      // Use fallback since API method doesn't exist
      const response = { success: false, data: {} }
      if (response.success && response.data) {
        setSettings({ ...defaultSettings, ...response.data })
      } else {
        // Load from localStorage as fallback
        const stored = localStorage.getItem('bitstech_appearance_settings')
        if (stored) {
          try {
            const parsed = JSON.parse(stored)
            setSettings({ ...defaultSettings, ...parsed })
          } catch (error) {
            console.error('Error parsing stored appearance settings:', error)
          }
        }
      }
    } catch (error) {
      console.error('Error loading appearance settings:', error)
    }
  }

  const updateSettings = async (newSettings: Partial<AppearanceSettings>): Promise<boolean> => {
    try {
      const updatedSettings = { ...settings, ...newSettings }
      
      // Use fallback since API method doesn't exist
      const response = { success: false, error: 'Appearance settings API not implemented yet' }

      if (response.success) {
        setSettings(updatedSettings)
        localStorage.setItem('bitstech_appearance_settings', JSON.stringify(updatedSettings))
        return true
      }
      return false
    } catch (error) {
      console.error('Error updating appearance settings:', error)
      // Still update locally even if API fails
      const updatedSettings = { ...settings, ...newSettings }
      setSettings(updatedSettings)
      localStorage.setItem('bitstech_appearance_settings', JSON.stringify(updatedSettings))
      return true
    }
  }

  const applySettings = () => {
    const root = document.documentElement

    // Apply CSS custom properties
    root.style.setProperty('--primary-color', settings.primaryColor)
    root.style.setProperty('--secondary-color', settings.secondaryColor)
    root.style.setProperty('--accent-color', settings.accentColor)

    // Apply border radius
    const borderRadiusMap = {
      sharp: '0px',
      medium: '0.375rem',
      rounded: '0.75rem'
    }
    root.style.setProperty('--border-radius', borderRadiusMap[settings.borderRadius as keyof typeof borderRadiusMap] || '0.375rem')

    // Apply font size
    const fontSizeMap = {
      small: '14px',
      medium: '16px',
      large: '18px'
    }
    root.style.setProperty('--base-font-size', fontSizeMap[settings.fontSize as keyof typeof fontSizeMap] || '16px')

    // Apply font family
    root.style.setProperty('--font-family', settings.fontFamily)

    // Apply animations
    if (!settings.animationsEnabled || settings.reducedMotion) {
      root.style.setProperty('--animation-duration', '0s')
      root.style.setProperty('--transition-duration', '0s')
    } else {
      root.style.setProperty('--animation-duration', '0.3s')
      root.style.setProperty('--transition-duration', '0.15s')
    }

    // Apply high contrast
    if (settings.highContrast) {
      root.classList.add('high-contrast')
    } else {
      root.classList.remove('high-contrast')
    }

    // Apply color blind friendly mode
    if (settings.colorBlindFriendly) {
      root.classList.add('color-blind-friendly')
    } else {
      root.classList.remove('color-blind-friendly')
    }

    // Apply sidebar style
    root.setAttribute('data-sidebar-style', settings.sidebarStyle)
    root.setAttribute('data-header-style', settings.headerStyle)

    // Update body classes for layout styles
    document.body.className = document.body.className
      .replace(/sidebar-\w+/g, '')
      .replace(/header-\w+/g, '')
      .replace(/font-\w+/g, '')

    document.body.classList.add(
      `sidebar-${settings.sidebarStyle}`,
      `header-${settings.headerStyle}`,
      `font-${settings.fontSize}`
    )
  }

  const resetToDefaults = () => {
    setSettings(defaultSettings)
    localStorage.removeItem('bitstech_appearance_settings')
  }

  const value: AppearanceContextType = {
    settings,
    updateSettings,
    applySettings,
    resetToDefaults
  }

  return (
    <AppearanceContext.Provider value={value}>
      {children}
    </AppearanceContext.Provider>
  )
}

export function useAppearance() {
  const context = useContext(AppearanceContext)
  if (context === undefined) {
    throw new Error('useAppearance must be used within an AppearanceProvider')
  }
  return context
}

export type { AppearanceSettings }
