'use client'

import React, { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import { useSettings } from '@/contexts/settings-context'
import { useCurrency } from '@/contexts/currency-context'
import { apiClient } from '@/lib/api'
import {
  TrendingUp,
  Brain,
  Calendar,
  DollarSign,
  Users,
  ShoppingCart,
  Package,
  Activity,
  BarChart3,
  LineChart,
  Target,
  Zap,
  AlertCircle,
  CheckCircle,
  Clock,
  RefreshCw,
  Download,
  Settings,
  Plus
} from 'lucide-react'

interface ForecastPeriod {
  id: string
  period: string
  periodLocal: string
  startDate: string
  endDate: string
  confidence: number
  revenue: {
    predicted: number
    min: number
    max: number
    growth: number
  }
  orders: {
    predicted: number
    min: number
    max: number
    growth: number
  }
  customers: {
    predicted: number
    min: number
    max: number
    growth: number
  }
  factors: string[]
  factorsLocal: string[]
}

interface ProductForecast {
  productId: string
  productName: string
  category: string
  currentStock: number
  predictedDemand: number
  recommendedOrder: number
  confidence: number
  trend: 'increasing' | 'decreasing' | 'stable'
  seasonality: 'high' | 'medium' | 'low'
}

interface MarketTrend {
  id: string
  trend: string
  trendLocal: string
  impact: 'positive' | 'negative' | 'neutral'
  confidence: number
  description: string
  descriptionLocal: string
  timeframe: string
}

export default function ForecastingDashboardPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [forecastPeriods, setForecastPeriods] = useState<ForecastPeriod[]>([])
  const [productForecasts, setProductForecasts] = useState<ProductForecast[]>([])
  const [marketTrends, setMarketTrends] = useState<MarketTrend[]>([])
  const [selectedPeriod, setSelectedPeriod] = useState('next_month')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchForecastingData()
    }
  }, [isAuthenticated, selectedPeriod])

  // Listen for currency changes and refresh data
  useEffect(() => {
    const handleCurrencyChange = () => {
      console.log('📊 Forecasting currency change detected, refreshing data')
      // No need to refetch data, just trigger re-render with new currency formatting
      // The formatCurrency function will handle the new currency
    }

    // Listen for currency change events
    window.addEventListener('currency-changed', handleCurrencyChange)
    window.addEventListener('currencyChanged', handleCurrencyChange)

    return () => {
      window.removeEventListener('currency-changed', handleCurrencyChange)
      window.removeEventListener('currencyChanged', handleCurrencyChange)
    }
  }, [])

  const fetchForecastingData = async () => {
    try {
      setLoading(true)

      // Fetch real forecasting data from API
      const response = await apiClient.getForecastingData({
        period: selectedPeriod,
        includeProductForecasts: true,
        includeMarketTrends: true
      })

      if (response.success && response.data) {
        setForecastPeriods(response.data.salesForecast || [])
        setProductForecasts(response.data.productForecasts || [])
        setMarketTrends(response.data.marketTrends || [])
        console.log('✅ Real forecasting data loaded successfully')
      } else {
        console.error('Failed to fetch forecasting data:', response.error)
        setForecastPeriods([])
        setProductForecasts([])
        setMarketTrends([])
      }


    } catch (error) {
      console.error('Error fetching forecasting data:', error)
    } finally {
      setLoading(false)
    }
  }



  const formatPercentage = (value: number) => {
    return `${value > 0 ? '+' : ''}${value.toFixed(1)}%`
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'decreasing': return <TrendingUp className="h-4 w-4 text-red-600 rotate-180" />
      case 'stable': return <Activity className="h-4 w-4 text-blue-600" />
      default: return <Activity className="h-4 w-4 text-gray-600" />
    }
  }

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'positive': return 'bg-green-100 text-green-800 border-green-200'
      case 'negative': return 'bg-red-100 text-red-800 border-red-200'
      case 'neutral': return 'bg-gray-100 text-gray-800 border-gray-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 90) return 'text-green-600'
    if (confidence >= 80) return 'text-blue-600'
    if (confidence >= 70) return 'text-yellow-600'
    return 'text-red-600'
  }

  const selectedForecast = forecastPeriods.find(p => p.id === selectedPeriod)



  if (!isAuthenticated) {
    return null
  }

  // Show empty state if no data (after reset)
  if (!forecastPeriods || forecastPeriods.length === 0) {
    return (
      <MainLayout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {language === 'mm' ? 'ရောင်းအား ခန့်မှန်းချက်' : 'Sales Forecasting'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  {language === 'mm'
                    ? 'AI အခြေခံ ရောင်းအား ခန့်မှန်းချက်များ နှင့် စျေးကွက် လမ်းကြောင်းများ'
                    : 'AI-powered sales predictions and market trend analysis'
                  }
                </p>
              </div>
            </div>

            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Brain className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {language === 'mm' ? 'ခန့်မှန်းချက် ဒေတာ မရှိပါ' : 'No Forecasting Data'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {language === 'mm'
                    ? 'ရောင်းအား ဒေတာ ရှိမှ ခန့်မှန်းချက်များ ပေါ်လာမည်'
                    : 'Forecasting data will appear once you have sales data'
                  }
                </p>
                <Button onClick={() => router.push('/pos')} className="bg-purple-600 hover:bg-purple-700">
                  <Plus className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ရောင်းချ စတင်ရန်' : 'Start Selling'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-600 to-pink-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Brain className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'ရောင်းအား ခန့်မှန်းချက်' : 'Sales Forecasting'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm'
                      ? 'AI အခြေခံ ရောင်းအား ခန့်မှန်းချက်များ နှင့် စျေးကွက် လမ်းကြောင်းများ'
                      : 'AI-powered sales predictions and market trend analysis'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                  <SelectTrigger className="w-40 bg-white/20 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {forecastPeriods.map((period) => (
                      <SelectItem key={period.id} value={period.id}>
                        {language === 'mm' ? period.periodLocal : period.period}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  onClick={fetchForecastingData}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ပြန်လည်ရယူ' : 'Refresh'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Main Forecast */}
        {selectedForecast && (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="flex items-center gap-2">
                    <Calendar className="h-5 w-5 text-purple-600" />
                    {language === 'mm' ? selectedForecast.periodLocal : selectedForecast.period}
                    {language === 'mm' ? ' ခန့်မှန်းချက်' : ' Forecast'}
                  </CardTitle>
                  <CardDescription>
                    {new Date(selectedForecast.startDate).toLocaleDateString()} - {new Date(selectedForecast.endDate).toLocaleDateString()}
                  </CardDescription>
                </div>
                <Badge className="bg-purple-100 text-purple-800">
                  <Brain className="h-3 w-3 mr-1" />
                  {selectedForecast.confidence}% {language === 'mm' ? 'ယုံကြည်မှု' : 'Confidence'}
                </Badge>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <DollarSign className="h-5 w-5 text-green-600" />
                    <h3 className="font-semibold">{language === 'mm' ? 'ဝင်ငွေ' : 'Revenue'}</h3>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-green-600">
                      {formatCurrency(selectedForecast.revenue.predicted)}
                    </p>
                    <p className="text-sm text-gray-600">
                      {formatCurrency(selectedForecast.revenue.min)} - {formatCurrency(selectedForecast.revenue.max)}
                    </p>
                    <p className="text-sm text-green-600 font-medium">
                      {formatPercentage(selectedForecast.revenue.growth)} {language === 'mm' ? 'တိုးတက်မှု' : 'growth'}
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <ShoppingCart className="h-5 w-5 text-blue-600" />
                    <h3 className="font-semibold">{language === 'mm' ? 'အမှာစာများ' : 'Orders'}</h3>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-blue-600">
                      {selectedForecast.orders.predicted.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600">
                      {selectedForecast.orders.min.toLocaleString()} - {selectedForecast.orders.max.toLocaleString()}
                    </p>
                    <p className="text-sm text-blue-600 font-medium">
                      {formatPercentage(selectedForecast.orders.growth)} {language === 'mm' ? 'တိုးတက်မှု' : 'growth'}
                    </p>
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Users className="h-5 w-5 text-purple-600" />
                    <h3 className="font-semibold">{language === 'mm' ? 'ဖောက်သည်များ' : 'Customers'}</h3>
                  </div>
                  <div>
                    <p className="text-2xl font-bold text-purple-600">
                      {selectedForecast.customers.predicted.toLocaleString()}
                    </p>
                    <p className="text-sm text-gray-600">
                      {selectedForecast.customers.min.toLocaleString()} - {selectedForecast.customers.max.toLocaleString()}
                    </p>
                    <p className="text-sm text-purple-600 font-medium">
                      {formatPercentage(selectedForecast.customers.growth)} {language === 'mm' ? 'တိုးတက်မှု' : 'growth'}
                    </p>
                  </div>
                </div>
              </div>

              <div className="mt-6 pt-6 border-t border-gray-200">
                <h4 className="font-medium mb-3">
                  {language === 'mm' ? 'ခန့်မှန်းချက် အခြေခံများ' : 'Forecast Factors'}
                </h4>
                <div className="flex flex-wrap gap-2">
                  {(language === 'mm' ? selectedForecast.factorsLocal : selectedForecast.factors).map((factor, index) => (
                    <Badge key={index} variant="outline" className="text-xs">
                      {factor}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Product Demand Forecast */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Package className="h-5 w-5 text-orange-600" />
                {language === 'mm' ? 'ကုန်ပစ္စည်း လိုအပ်ချက် ခန့်မှန်းချက်' : 'Product Demand Forecast'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ကုန်ပစ္စည်းများ၏ ခန့်မှန်းချက် လိုအပ်ချက် နှင့် အကြံပြုချက်များ'
                  : 'Predicted demand and recommendations for products'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {productForecasts.map((product) => (
                  <div key={product.productId} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h4 className="font-semibold">{product.productName}</h4>
                        <p className="text-sm text-gray-600">{product.category}</p>
                      </div>
                      <div className="flex items-center gap-2">
                        {getTrendIcon(product.trend)}
                        <Badge className={`${getConfidenceColor(product.confidence)} bg-gray-100`}>
                          {product.confidence}%
                        </Badge>
                      </div>
                    </div>

                    <div className="grid grid-cols-3 gap-4 text-sm">
                      <div>
                        <p className="text-gray-600">{language === 'mm' ? 'လက်ရှိ စတော့' : 'Current Stock'}</p>
                        <p className="font-semibold">{product.currentStock}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">{language === 'mm' ? 'ခန့်မှန်း လိုအပ်ချက်' : 'Predicted Demand'}</p>
                        <p className="font-semibold text-blue-600">{product.predictedDemand}</p>
                      </div>
                      <div>
                        <p className="text-gray-600">{language === 'mm' ? 'အကြံပြု မှာယူမှု' : 'Recommended Order'}</p>
                        <p className={`font-semibold ${product.recommendedOrder > 0 ? 'text-orange-600' : 'text-green-600'}`}>
                          {product.recommendedOrder > 0 ? product.recommendedOrder : (language === 'mm' ? 'မလိုအပ်' : 'None needed')}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Market Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <LineChart className="h-5 w-5 text-indigo-600" />
                {language === 'mm' ? 'စျေးကွက် လမ်းကြောင်းများ' : 'Market Trends'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'လုပ်ငန်းအပေါ် သက်ရောက်မှုရှိသော စျေးကွက် လမ်းကြောင်းများ'
                  : 'Market trends affecting your business'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {marketTrends.map((trend, index) => (
                  <div key={(trend as any).category || (trend as any).id || index} className="p-4 border border-gray-200 rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h4 className="font-semibold">
                          {language === 'mm' ? trend.trendLocal : trend.trend}
                        </h4>
                        <p className="text-sm text-gray-600 mt-1">
                          {language === 'mm' ? trend.descriptionLocal : trend.description}
                        </p>
                      </div>
                      <div className="flex flex-col items-end gap-2 ml-3">
                        <Badge className={getImpactColor(trend.impact)}>
                          {trend.impact === 'positive' ? (language === 'mm' ? 'အပြုသဘော' : 'Positive') :
                           trend.impact === 'negative' ? (language === 'mm' ? 'အနုတ်သဘော' : 'Negative') :
                           (language === 'mm' ? 'ကြားနေ' : 'Neutral')}
                        </Badge>
                        <span className={`text-sm font-medium ${getConfidenceColor(trend.confidence)}`}>
                          {trend.confidence}% {language === 'mm' ? 'ယုံကြည်မှု' : 'confidence'}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 text-sm text-gray-500">
                      <Clock className="h-4 w-4" />
                      <span>{trend.timeframe}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </MainLayout>
  )
}
