const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
    getExchangeRates,
    updateExchangeRates,
    getExchangeRateHistory,
    resetExchangeRates,
    fetchLiveRates
} = require('../controllers/exchangeRateController');

const router = express.Router();

// Public route to get current exchange rates
router.get('/', getExchangeRates);

// Protected routes
router.use(protect);

// Admin and Manager routes
router.put('/', authorize('admin', 'manager'), updateExchangeRates);
router.get('/history', authorize('admin', 'manager'), getExchangeRateHistory);
router.post('/fetch-live', authorize('admin', 'manager'), fetchLiveRates);

// Admin only routes
router.post('/reset', authorize('admin'), resetExchangeRates);

module.exports = router;
