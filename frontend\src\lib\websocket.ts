// WebSocket client for real-time updates
class WebSocketClient {
  private ws: WebSocket | null = null
  private wsConnected: boolean = false
  private wsReconnectAttempts: number = 0
  private maxReconnectAttempts: number = 5
  private reconnectTimeout: NodeJS.Timeout | null = null
  private subscriptions: Set<string> = new Set()

  constructor() {
    // Only initialize in browser environment
    if (typeof window !== 'undefined' && typeof WebSocket !== 'undefined') {
      // Enable WebSocket by default for real-time database mode
      const enableWebSocket = true

      if (enableWebSocket) {
        // Delay initialization to avoid SSR issues
        setTimeout(() => {
          this.initializeWebSocket()
        }, 1000)
      } else {
        console.log('🔌 WebSocket disabled')
      }
    }
  }

  // Initialize WebSocket connection
  private initializeWebSocket() {
    if (typeof window === 'undefined' || typeof WebSocket === 'undefined') {
      console.warn('WebSocket not available in this environment')
      return
    }

    try {
      const wsURL = 'ws://localhost:5001/ws'
      console.log('🔌 Attempting WebSocket connection to:', wsURL)

      this.ws = new WebSocket(wsURL)

      this.ws.onopen = () => {
        console.log('✅ WebSocket connected successfully')
        this.wsConnected = true
        this.wsReconnectAttempts = 0

        // Authenticate if token is available
        try {
          const token = localStorage.getItem('token') || localStorage.getItem('bitstech_auth_token')
          if (token) {
            this.send({
              type: 'authenticate',
              token
            })
          } else {
            // Send guest connection without authentication
            this.send({
              type: 'guest_connect'
            })
          }
        } catch (error) {
          console.warn('Authentication attempt failed:', error)
          // Continue without authentication
          this.send({
            type: 'guest_connect'
          })
        }

        // Re-subscribe to all channels
        this.subscriptions.forEach(channel => {
          this.send({
            type: 'subscribe',
            channel
          })
        })
      }

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          this.handleMessage(data)
        } catch (error) {
          console.error('Error parsing WebSocket message:', error)
        }
      }

      this.ws.onclose = (event) => {
        console.log('🔌 WebSocket disconnected. Code:', event.code, 'Reason:', event.reason)
        this.wsConnected = false

        // Only attempt reconnect if it wasn't a manual close
        if (event.code !== 1000) {
          this.attemptReconnect()
        }
      }

      this.ws.onerror = (error) => {
        console.warn('⚠️ WebSocket connection error (this is normal in development):', error)
        this.wsConnected = false
        // Don't attempt reconnect on connection refused (server not running)
        if (error.type === 'error') {
          console.log('💡 Tip: Start the backend server to enable real-time features')
        }
      }
    } catch (error) {
      console.warn('WebSocket initialization failed:', error)
      this.attemptReconnect()
    }
  }

  // Attempt to reconnect with exponential backoff
  private attemptReconnect() {
    // Always attempt reconnect for real-time database mode
    const enableWebSocket = true

    if (!enableWebSocket) {
      console.log('🔌 WebSocket reconnection skipped (disabled)')
      return
    }

    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }

    if (this.wsReconnectAttempts < this.maxReconnectAttempts) {
      this.wsReconnectAttempts++
      const delay = Math.min(Math.pow(2, this.wsReconnectAttempts) * 1000, 30000) // Max 30 seconds

      console.log(`🔄 Attempting WebSocket reconnection (${this.wsReconnectAttempts}/${this.maxReconnectAttempts}) in ${delay}ms`)

      this.reconnectTimeout = setTimeout(() => {
        this.initializeWebSocket()
      }, delay)
    } else {
      console.warn('⚠️ Max WebSocket reconnection attempts reached. Stopping reconnection attempts.')
      console.log('💡 To enable WebSocket: localStorage.setItem("enable_websocket", "true") and refresh')
    }
  }

  // Handle incoming messages
  private handleMessage(data: any) {
    console.log('📡 WebSocket message received:', data)
    
    // Emit custom events for different message types
    switch (data.type) {
      case 'welcome':
        console.log('🎉 WebSocket welcome:', data.message)
        break
        
      case 'authenticated':
        console.log('🔐 WebSocket authenticated:', data.message)
        break
        
      case 'subscribed':
        console.log('📡 Subscribed to channel:', data.channel)
        break
        
      case 'inventory-update':
        window.dispatchEvent(new CustomEvent('ws-inventory-update', { detail: data }))
        break
        
      case 'product-sold':
        window.dispatchEvent(new CustomEvent('ws-product-sold', { detail: data }))
        break
        
      case 'low-stock-alert':
        window.dispatchEvent(new CustomEvent('ws-low-stock-alert', { detail: data }))
        break
        
      case 'sales-update':
        window.dispatchEvent(new CustomEvent('ws-sales-update', { detail: data }))
        break
        
      case 'dashboard-update':
        window.dispatchEvent(new CustomEvent('ws-dashboard-update', { detail: data }))
        break
        
      case 'full-dashboard-update':
        window.dispatchEvent(new CustomEvent('ws-full-dashboard-update', { detail: data }))
        break
        
      case 'invoice-update':
        window.dispatchEvent(new CustomEvent('ws-invoice-update', { detail: data }))
        break
        
      case 'company-settings-update':
        window.dispatchEvent(new CustomEvent('ws-company-settings-update', { detail: data }))
        break
        
      case 'customization-update':
        window.dispatchEvent(new CustomEvent('ws-customization-update', { detail: data }))
        break
        
      case 'error':
        console.warn('⚠️ WebSocket error (non-critical):', data.error)
        // Don't throw errors for authentication failures in development
        if (data.error && !data.error.includes('Authentication')) {
          console.error('❌ WebSocket error:', data.error)
        }
        break

      case 'authentication_failed':
        console.warn('🔐 WebSocket authentication failed - continuing as guest')
        break

      case 'guest_connected':
        console.log('👤 WebSocket connected as guest')
        break
        
      case 'pong':
        console.log('🏓 WebSocket pong received')
        break
        
      default:
        console.log('📨 Unknown WebSocket message type:', data.type)
        window.dispatchEvent(new CustomEvent('ws-general-update', { detail: data }))
    }
  }

  // Send message via WebSocket
  send(message: any): boolean {
    if (this.ws && this.wsConnected && this.ws.readyState === WebSocket.OPEN) {
      try {
        this.ws.send(JSON.stringify(message))
        return true
      } catch (error) {
        console.error('Error sending WebSocket message:', error)
        return false
      }
    }
    console.warn('WebSocket not connected, message not sent:', message)
    return false
  }

  // Subscribe to a channel
  subscribe(channel: string): boolean {
    this.subscriptions.add(channel)
    return this.send({
      type: 'subscribe',
      channel
    })
  }

  // Unsubscribe from a channel
  unsubscribe(channel: string): boolean {
    this.subscriptions.delete(channel)
    return this.send({
      type: 'unsubscribe',
      channel
    })
  }

  // Send ping to keep connection alive
  ping(): boolean {
    return this.send({
      type: 'ping',
      timestamp: Date.now()
    })
  }

  // Get connection status
  isConnected(): boolean {
    return this.wsConnected && this.ws?.readyState === WebSocket.OPEN
  }

  // Get subscribed channels
  getSubscriptions(): string[] {
    return Array.from(this.subscriptions)
  }

  // Close connection
  close() {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
    
    this.subscriptions.clear()
    this.wsConnected = false
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
  }

  // Reconnect manually
  reconnect() {
    this.close()
    this.wsReconnectAttempts = 0
    this.initializeWebSocket()
  }
}

// Create singleton instance only in browser environment
let wsClient: WebSocketClient | null = null

if (typeof window !== 'undefined' && typeof WebSocket !== 'undefined') {
  wsClient = new WebSocketClient()
}

// Export singleton instance
export default wsClient

// Export class for testing
export { WebSocketClient }

// Helper functions for common operations
export const subscribeToInventoryUpdates = () => {
  return wsClient?.subscribe('inventory-updates') || false
}

export const subscribeToDashboardUpdates = () => {
  return wsClient?.subscribe('dashboard-updates') || false
}

export const subscribeToInvoiceUpdates = () => {
  return wsClient?.subscribe('invoice-updates') || false
}

export const unsubscribeFromInventoryUpdates = () => {
  return wsClient?.unsubscribe('inventory-updates') || false
}

export const unsubscribeFromDashboardUpdates = () => {
  return wsClient?.unsubscribe('dashboard-updates') || false
}

export const unsubscribeFromInvoiceUpdates = () => {
  return wsClient?.unsubscribe('invoice-updates') || false
}

// Auto-ping to keep connection alive
if (typeof window !== 'undefined' && typeof WebSocket !== 'undefined' && wsClient) {
  setInterval(() => {
    try {
      if (wsClient && wsClient.isConnected()) {
        wsClient.ping()
      }
    } catch (error) {
      console.warn('Ping failed:', error)
    }
  }, 30000) // Ping every 30 seconds
}
