const mongoose = require('mongoose');

// Database configurations
const OLD_DB = 'mongodb://localhost:27017/bitestech_pos';
const NEW_DB = 'mongodb://localhost:27017/bitstech_pos';

async function migrateDatabase() {
    console.log('🔄 Starting database migration...');
    console.log(`📤 Source: bitestech_pos`);
    console.log(`📥 Target: bitstech_pos`);

    try {
        // Connect to old database
        console.log('\n📊 Connecting to source database...');
        const oldConnection = mongoose.createConnection();
        await oldConnection.openUri(OLD_DB);
        console.log('✅ Connected to source database');

        // Connect to new database
        console.log('\n📊 Connecting to target database...');
        const newConnection = mongoose.createConnection();
        await newConnection.openUri(NEW_DB);
        console.log('✅ Connected to target database');

        // Clear target database
        console.log('\n🗑️  Clearing target database...');
        const existingCollections = await newConnection.db.listCollections().toArray();
        for (const col of existingCollections) {
            await newConnection.db.collection(col.name).drop();
            console.log(`   🗑️  Dropped collection: ${col.name}`);
        }
        console.log('✅ Target database cleared');

        // Get all collections from old database
        const collections = await oldConnection.db.listCollections().toArray();
        console.log(`\n📂 Found ${collections.length} collections to migrate:`);
        collections.forEach(col => console.log(`   - ${col.name}`));

        // Migrate each collection
        for (const collection of collections) {
            const collectionName = collection.name;
            console.log(`\n🔄 Migrating collection: ${collectionName}`);

            // Get data from old collection
            const data = await oldConnection.db.collection(collectionName).find({}).toArray();
            console.log(`   📊 Found ${data.length} documents`);

            if (data.length > 0) {
                // Insert data into new collection
                await newConnection.db.collection(collectionName).insertMany(data);
                console.log(`   ✅ Migrated ${data.length} documents`);
            } else {
                console.log(`   ⚠️  No documents to migrate`);
            }
        }

        // Verify migration
        console.log('\n🔍 Verifying migration...');
        const newCollections = await newConnection.db.listCollections().toArray();

        for (const collection of newCollections) {
            const count = await newConnection.db.collection(collection.name).countDocuments();
            console.log(`   ✅ ${collection.name}: ${count} documents`);
        }

        // Close connections
        await oldConnection.close();
        await newConnection.close();

        console.log('\n🎉 Database migration completed successfully!');
        console.log('\n📋 Summary:');
        console.log(`   - Source database: bitestech_pos`);
        console.log(`   - Target database: bitstech_pos`);
        console.log(`   - Collections migrated: ${collections.length}`);
        console.log(`   - Status: ✅ Success`);

        console.log('\n⚠️  Next steps:');
        console.log('   1. Verify data in MongoDB Compass');
        console.log('   2. Update application to use new database');
        console.log('   3. Test all functionality');
        console.log('   4. Remove old database when confirmed');

    } catch (error) {
        console.error('\n❌ Migration failed:', error.message);
        console.error('Stack trace:', error.stack);
        process.exit(1);
    }
}

// Run migration
migrateDatabase();
