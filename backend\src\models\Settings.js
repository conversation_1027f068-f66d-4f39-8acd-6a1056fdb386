const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
    // General Settings
    storeName: {
        type: String,
        required: true,
        default: 'BitsTech POS'
    },
    storeEmail: {
        type: String,
        required: true,
        default: '<EMAIL>'
    },
    storePhone: {
        type: String,
        default: '+95-9-***********'
    },
    storeAddress: {
        type: String,
        default: 'Yangon, Myanmar'
    },
    
    // Tax Settings
    defaultTaxRate: {
        type: Number,
        default: 0,
        min: 0,
        max: 100
    },
    taxName: {
        type: String,
        default: 'Tax'
    },
    taxNumber: {
        type: String,
        default: ''
    },
    
    // Currency Settings
    primaryCurrency: {
        type: String,
        enum: ['MMK', 'USD', 'THB'],
        default: 'MMK'
    },
    currencyPosition: {
        type: String,
        enum: ['before', 'after'],
        default: 'after'
    },
    decimalPlaces: {
        type: Number,
        default: 2,
        min: 0,
        max: 4
    },
    thousandSeparator: {
        type: String,
        default: ','
    },
    decimalSeparator: {
        type: String,
        default: '.'
    },
    
    // Language Settings
    defaultLanguage: {
        type: String,
        enum: ['en', 'mm', 'th'],
        default: 'en'
    },
    
    // Theme Settings
    defaultTheme: {
        type: String,
        enum: ['light', 'dark', 'system'],
        default: 'light'
    },
    defaultColorScheme: {
        type: String,
        enum: ['blue', 'green', 'purple', 'orange', 'pink', 'indigo'],
        default: 'blue'
    },
    
    // Receipt Settings
    receiptHeader: {
        type: String,
        default: 'Thank you for your purchase!'
    },
    receiptFooter: {
        type: String,
        default: 'Please come again!'
    },
    showLogo: {
        type: Boolean,
        default: true
    },
    logoUrl: {
        type: String,
        default: ''
    },
    
    // Inventory Settings
    lowStockThreshold: {
        type: Number,
        default: 10,
        min: 0
    },
    enableLowStockAlerts: {
        type: Boolean,
        default: true
    },
    autoReorderEnabled: {
        type: Boolean,
        default: false
    },
    autoReorderQuantity: {
        type: Number,
        default: 50,
        min: 1
    },
    
    // POS Settings
    enableBarcode: {
        type: Boolean,
        default: true
    },
    enableCustomerDisplay: {
        type: Boolean,
        default: true
    },
    enableReceiptPrinting: {
        type: Boolean,
        default: true
    },
    defaultPaymentMethod: {
        type: String,
        enum: ['cash', 'card', 'kbz_pay', 'wave_money', 'nug_pay', 'thailand_bank'],
        default: 'cash'
    },
    
    // Notification Settings
    emailNotifications: {
        type: Boolean,
        default: true
    },
    smsNotifications: {
        type: Boolean,
        default: false
    },
    pushNotifications: {
        type: Boolean,
        default: true
    },
    
    // Security Settings
    sessionTimeout: {
        type: Number,
        default: 30, // minutes
        min: 5,
        max: 480
    },
    requirePasswordChange: {
        type: Boolean,
        default: false
    },
    passwordExpiryDays: {
        type: Number,
        default: 90,
        min: 30,
        max: 365
    },
    
    // Backup Settings
    autoBackupEnabled: {
        type: Boolean,
        default: true
    },
    backupFrequency: {
        type: String,
        enum: ['daily', 'weekly', 'monthly'],
        default: 'daily'
    },
    
    // System Settings
    maintenanceMode: {
        type: Boolean,
        default: false
    },
    debugMode: {
        type: Boolean,
        default: false
    },
    
    // Metadata
    lastUpdated: {
        type: Date,
        default: Date.now
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    }
}, {
    timestamps: true
});

// Static method to get current settings
settingsSchema.statics.getCurrentSettings = async function() {
    let settings = await this.findOne().sort({ updatedAt: -1 });
    
    if (!settings) {
        // Create default settings if none exist
        settings = new this({});
        await settings.save();
    }
    
    return settings;
};

// Instance method to update settings
settingsSchema.methods.updateSettings = async function(newSettings, userId) {
    Object.assign(this, newSettings);
    this.lastUpdated = new Date();
    this.updatedBy = userId;
    return await this.save();
};

module.exports = mongoose.model('Settings', settingsSchema);
