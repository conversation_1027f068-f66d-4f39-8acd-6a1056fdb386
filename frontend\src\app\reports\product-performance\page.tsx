'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import {
  Package,
  TrendingUp,
  TrendingDown,
  ArrowLeft,
  Download,
  Star,
  AlertTriangle,
  DollarSign,
  ShoppingCart,
  BarChart3,
  Target,
  Award,
  ThumbsDown
} from 'lucide-react'

interface ProductPerformance {
  _id: string
  name: string
  sku: string
  category: string
  totalSold: number
  totalRevenue: number
  avgPrice: number
  profitMargin: number
  stockLevel: number
  reorderPoint: number
  trend: 'up' | 'down' | 'stable'
  trendPercentage: number
  rank: number
}

export default function ProductPerformancePage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [dateRange, setDateRange] = useState('month')
  const [sortBy, setSortBy] = useState('revenue')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [loading, setLoading] = useState(true)

  const [products, setProducts] = useState<ProductPerformance[]>([])
  const [categories, setCategories] = useState<string[]>([])

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchProductPerformance()
    }
  }, [isAuthenticated, dateRange, sortBy, categoryFilter])

  const fetchProductPerformance = async () => {
    try {
      setLoading(true)
      
      // Mock product performance data
      const mockProducts: ProductPerformance[] = [
        {
          _id: '1',
          name: 'ASUS VivoBook 15',
          sku: 'LAP001',
          category: 'Laptops',
          totalSold: 45,
          totalRevenue: 67500000,
          avgPrice: 1500000,
          profitMargin: 18.5,
          stockLevel: 12,
          reorderPoint: 10,
          trend: 'up',
          trendPercentage: 23.4,
          rank: 1
        },
        {
          _id: '2',
          name: 'Samsung 27" Monitor',
          sku: 'MON001',
          category: 'Monitors',
          totalSold: 38,
          totalRevenue: 17100000,
          avgPrice: 450000,
          profitMargin: 22.1,
          stockLevel: 8,
          reorderPoint: 5,
          trend: 'up',
          trendPercentage: 15.2,
          rank: 2
        },
        {
          _id: '3',
          name: 'Logitech MX Master 3S',
          sku: 'MOU001',
          category: 'Accessories',
          totalSold: 67,
          totalRevenue: 8040000,
          avgPrice: 120000,
          profitMargin: 35.8,
          stockLevel: 25,
          reorderPoint: 15,
          trend: 'up',
          trendPercentage: 8.7,
          rank: 3
        },
        {
          _id: '4',
          name: 'Intel Core i7-13700K',
          sku: 'CPU001',
          category: 'Processors',
          totalSold: 18,
          totalRevenue: 7650000,
          avgPrice: 425000,
          profitMargin: 12.3,
          stockLevel: 6,
          reorderPoint: 8,
          trend: 'down',
          trendPercentage: -5.2,
          rank: 4
        },
        {
          _id: '5',
          name: 'Kingston 32GB RAM',
          sku: 'RAM001',
          category: 'Memory',
          totalSold: 29,
          totalRevenue: 5800000,
          avgPrice: 200000,
          profitMargin: 28.4,
          stockLevel: 15,
          reorderPoint: 10,
          trend: 'stable',
          trendPercentage: 1.2,
          rank: 5
        },
        {
          _id: '6',
          name: 'HP LaserJet Pro',
          sku: 'PRI001',
          category: 'Printers',
          totalSold: 8,
          totalRevenue: 2400000,
          avgPrice: 300000,
          profitMargin: 15.6,
          stockLevel: 3,
          reorderPoint: 5,
          trend: 'down',
          trendPercentage: -12.8,
          rank: 6
        }
      ]

      const uniqueCategories = [...new Set(mockProducts.map(p => p.category))]
      setCategories(uniqueCategories)

      // Apply filters and sorting
      let filteredProducts = mockProducts
      
      if (categoryFilter !== 'all') {
        filteredProducts = filteredProducts.filter(p => p.category === categoryFilter)
      }

      // Sort products
      filteredProducts.sort((a, b) => {
        switch (sortBy) {
          case 'revenue': return b.totalRevenue - a.totalRevenue
          case 'quantity': return b.totalSold - a.totalSold
          case 'profit': return b.profitMargin - a.profitMargin
          case 'trend': return b.trendPercentage - a.trendPercentage
          default: return b.totalRevenue - a.totalRevenue
        }
      })

      setProducts(filteredProducts)
    } catch (error) {
      console.error('Error fetching product performance:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} MMK`
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'down': return <TrendingDown className="h-4 w-4 text-red-600" />
      default: return <BarChart3 className="h-4 w-4 text-gray-600" />
    }
  }

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case 'up': return 'text-green-600'
      case 'down': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getRankBadge = (rank: number) => {
    if (rank === 1) return <Badge className="bg-yellow-100 text-yellow-800"><Award className="h-3 w-3 mr-1" />Top</Badge>
    if (rank <= 3) return <Badge className="bg-green-100 text-green-800">Top 3</Badge>
    if (rank <= 5) return <Badge className="bg-blue-100 text-blue-800">Top 5</Badge>
    return <Badge variant="outline">#{rank}</Badge>
  }

  const getStockStatus = (stockLevel: number, reorderPoint: number) => {
    if (stockLevel <= reorderPoint) {
      return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />Low Stock</Badge>
    }
    if (stockLevel <= reorderPoint * 1.5) {
      return <Badge className="bg-yellow-100 text-yellow-800">Medium</Badge>
    }
    return <Badge className="bg-green-100 text-green-800">Good</Badge>
  }

  const getTopPerformers = () => products.slice(0, 3)
  const getWorstPerformers = () => products.slice(-3).reverse()

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'
  const topPerformers = getTopPerformers()
  const worstPerformers = getWorstPerformers()

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/reports')}
            className="hover:bg-purple-50 dark:hover:bg-purple-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Reports'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-600 to-pink-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Package className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'ကုန်ပစ္စည်း စွမ်းဆောင်ရည်' : 'Product Performance'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm' 
                      ? 'အကောင်းဆုံး နှင့် အဆိုးဆုံး ရောင်းအား ကုန်ပစ္စည်းများ ခွဲခြမ်းစိတ်ဖြာမှု'
                      : 'Best and worst performing products analysis'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="w-40 bg-white/20 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="week">{language === 'mm' ? 'ဒီအပတ်' : 'This Week'}</SelectItem>
                    <SelectItem value="month">{language === 'mm' ? 'ဒီလ' : 'This Month'}</SelectItem>
                    <SelectItem value="quarter">{language === 'mm' ? 'ဒီသုံးလ' : 'This Quarter'}</SelectItem>
                  </SelectContent>
                </Select>
                <Button className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                  <Download className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ထုတ်ယူရန်' : 'Export'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">
                  {language === 'mm' ? 'အမျိုးအစား' : 'Category'}
                </label>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === 'mm' ? 'အမျိုးအစား ရွေးချယ်ပါ' : 'Select category'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Categories'}</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">
                  {language === 'mm' ? 'အစီအစဉ်' : 'Sort By'}
                </label>
                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="revenue">{language === 'mm' ? 'ဝင်ငွေ' : 'Revenue'}</SelectItem>
                    <SelectItem value="quantity">{language === 'mm' ? 'အရေအတွက်' : 'Quantity Sold'}</SelectItem>
                    <SelectItem value="profit">{language === 'mm' ? 'အမြတ်နှုန်း' : 'Profit Margin'}</SelectItem>
                    <SelectItem value="trend">{language === 'mm' ? 'လမ်းကြောင်း' : 'Trend'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-600" />
                {language === 'mm' ? 'ထိပ်တန်း ကုန်ပစ္စည်းများ' : 'Top Performers'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'အရောင်းရဆုံး ကုန်ပစ္စည်းများ'
                  : 'Best selling products'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topPerformers.map((product, index) => (
                  <div key={product._id} className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {index + 1}
                      </div>
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-gray-600">{product.sku}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-green-600">{formatCurrency(product.totalRevenue)}</p>
                      <p className="text-sm text-gray-600">{product.totalSold} sold</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Worst Performers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ThumbsDown className="h-5 w-5 text-red-600" />
                {language === 'mm' ? 'အားနည်း ကုန်ပစ္စည်းများ' : 'Needs Attention'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'အရောင်းနည်း ကုန်ပစ္စည်းများ'
                  : 'Products that need attention'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {worstPerformers.map((product, index) => (
                  <div key={product._id} className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center text-sm font-bold">
                        {products.length - index}
                      </div>
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-gray-600">{product.sku}</p>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-red-600">{formatCurrency(product.totalRevenue)}</p>
                      <p className="text-sm text-gray-600">{product.totalSold} sold</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Product List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-blue-600" />
              {language === 'mm' ? 'အသေးစိတ် စွမ်းဆောင်ရည်' : 'Detailed Performance'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {products.map((product) => (
                <div key={product._id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow">
                  <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                    {/* Product Info */}
                    <div className="lg:col-span-2">
                      <div className="flex items-center gap-3">
                        <div>
                          <h4 className="font-semibold">{product.name}</h4>
                          <p className="text-sm text-gray-600">{product.sku} • {product.category}</p>
                          <div className="flex items-center gap-2 mt-1">
                            {getRankBadge(product.rank)}
                            {getStockStatus(product.stockLevel, product.reorderPoint)}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Sales Metrics */}
                    <div className="text-center">
                      <p className="text-lg font-bold">{product.totalSold}</p>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'ရောင်းချ' : 'Sold'}</p>
                    </div>

                    <div className="text-center">
                      <p className="text-lg font-bold">{formatCurrency(product.totalRevenue)}</p>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'ဝင်ငွေ' : 'Revenue'}</p>
                    </div>

                    <div className="text-center">
                      <p className="text-lg font-bold">{product.profitMargin}%</p>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'အမြတ်နှုန်း' : 'Profit'}</p>
                    </div>

                    {/* Trend */}
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1">
                        {getTrendIcon(product.trend)}
                        <span className={`font-semibold ${getTrendColor(product.trend)}`}>
                          {product.trend === 'up' ? '+' : product.trend === 'down' ? '' : ''}{product.trendPercentage.toFixed(1)}%
                        </span>
                      </div>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'လမ်းကြောင်း' : 'Trend'}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
