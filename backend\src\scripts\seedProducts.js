const mongoose = require('mongoose');
const Category = require('../models/Category');
const Product = require('../models/Product');
require('dotenv').config();

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGODB_URI);
        console.log('MongoDB connected for seeding products...');
    } catch (error) {
        console.error('Database connection error:', error);
        process.exit(1);
    }
};

const seedCategories = async () => {
    try {
        // Clear existing categories
        await Category.deleteMany({});
        console.log('Cleared existing categories');

        const categories = [
            {
                name: 'Laptops',
                description: 'Laptops and notebook computers',
                slug: 'laptops',
                color: '#FF6B6B',
                icon: 'laptop',
                isActive: true,
                sortOrder: 1
            },
            {
                name: 'Monitors',
                description: 'Computer monitors and displays',
                slug: 'monitors',
                color: '#4ECDC4',
                icon: 'monitor',
                isActive: true,
                sortOrder: 2
            },
            {
                name: 'Accessories',
                description: 'Computer accessories and peripherals',
                slug: 'accessories',
                color: '#45B7D1',
                icon: 'mouse',
                isActive: true,
                sortOrder: 3
            },
            {
                name: 'Storage',
                description: 'Storage devices and memory',
                slug: 'storage',
                color: '#96CEB4',
                icon: 'hard-drive',
                isActive: true,
                sortOrder: 4
            },
            {
                name: 'Components',
                description: 'Computer components and parts',
                slug: 'components',
                color: '#FFEAA7',
                icon: 'cpu',
                isActive: true,
                sortOrder: 5
            },
            {
                name: 'Networking',
                description: 'Network equipment and cables',
                slug: 'networking',
                color: '#DDA0DD',
                icon: 'wifi',
                isActive: true,
                sortOrder: 6
            }
        ];

        const createdCategories = await Category.insertMany(categories);
        console.log(`Created ${createdCategories.length} categories`);
        return createdCategories;
    } catch (error) {
        console.error('Error seeding categories:', error);
        throw error;
    }
};

const seedProducts = async (categories) => {
    try {
        // Clear existing products
        await Product.deleteMany({});
        console.log('Cleared existing products');

        const products = [
            // Laptops
            {
                name: 'Dell XPS 13 Laptop',
                description: '13.3" FHD InfinityEdge Display, Intel Core i7, 16GB RAM, 512GB SSD',
                sku: 'LAP-DELL-XPS13-001',
                barcode: '1234567890123',
                category: categories.find(c => c.slug === 'laptops')._id,
                price: 1850000,
                cost: 1650000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 15,
                    minQuantity: 3,
                    maxQuantity: 50,
                    unit: 'piece'
                },
                images: [
                    {
                        url: 'https://images.unsplash.com/photo-1496181133206-80ce9b88a853?w=400&h=300&fit=crop',
                        alt: 'Dell XPS 13 Laptop',
                        isPrimary: true
                    }
                ],
                isActive: true,
                isFeatured: true,
                tags: ['laptop', 'dell', 'ultrabook', 'i7']
            },
            {
                name: 'HP Pavilion Gaming Laptop',
                description: '15.6" FHD IPS, AMD Ryzen 5, 8GB RAM, 512GB SSD, GTX 1650',
                sku: 'LAP-HP-PAV15-001',
                category: categories.find(c => c.slug === 'laptops')._id,
                price: 1250000,
                cost: 1100000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 8,
                    minQuantity: 2,
                    maxQuantity: 30,
                    unit: 'piece'
                },
                images: [
                    {
                        url: 'https://images.unsplash.com/photo-1588872657578-7efd1f1555ed?w=400&h=300&fit=crop',
                        alt: 'HP Pavilion Gaming Laptop',
                        isPrimary: true
                    }
                ],
                isActive: true,
                isFeatured: true,
                tags: ['laptop', 'hp', 'gaming', 'ryzen']
            },
            {
                name: 'ASUS VivoBook 14',
                description: '14" FHD Display, Intel Core i5, 8GB RAM, 256GB SSD',
                sku: 'LAP-ASUS-VB14-001',
                category: categories.find(c => c.slug === 'laptops')._id,
                price: 950000,
                cost: 850000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 12,
                    minQuantity: 3,
                    maxQuantity: 40,
                    unit: 'piece'
                },
                images: [
                    {
                        url: 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=300&fit=crop',
                        alt: 'ASUS VivoBook 14',
                        isPrimary: true
                    }
                ],
                isActive: true,
                isFeatured: false,
                tags: ['laptop', 'asus', 'vivobook', 'i5']
            },

            // Monitors
            {
                name: 'Samsung 27" 4K Monitor',
                description: '27" UHD 4K Display, IPS Panel, USB-C, Height Adjustable',
                sku: 'MON-SAM-27-4K-001',
                barcode: '2345678901234',
                category: categories.find(c => c.slug === 'monitors')._id,
                price: 450000,
                cost: 380000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 12,
                    minQuantity: 2,
                    maxQuantity: 30,
                    unit: 'piece'
                },
                images: [
                    {
                        url: 'https://images.unsplash.com/photo-1527443224154-c4a3942d3acf?w=400&h=300&fit=crop',
                        alt: 'Samsung 27" 4K Monitor',
                        isPrimary: true
                    }
                ],
                isActive: true,
                isFeatured: true,
                tags: ['monitor', 'samsung', '4k', 'ips']
            },
            {
                name: 'LG UltraWide 34" Monitor',
                description: '34" UltraWide QHD, Curved Display, HDR10, USB-C',
                sku: 'MON-LG-34UW-001',
                category: categories.find(c => c.slug === 'monitors')._id,
                price: 650000,
                cost: 550000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 6,
                    minQuantity: 1,
                    maxQuantity: 20,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['monitor', 'lg', 'ultrawide', 'curved']
            },

            // Accessories
            {
                name: 'Logitech MX Master 3 Mouse',
                description: 'Advanced wireless mouse with precision scroll wheel',
                sku: 'ACC-LOG-MX3-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 125000,
                cost: 95000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 25,
                    minQuantity: 5,
                    maxQuantity: 100,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['mouse', 'logitech', 'wireless', 'precision']
            },
            {
                name: 'Corsair K95 RGB Keyboard',
                description: 'Mechanical gaming keyboard with RGB backlighting',
                sku: 'ACC-COR-K95-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 185000,
                cost: 145000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 18,
                    minQuantity: 3,
                    maxQuantity: 60,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['keyboard', 'corsair', 'mechanical', 'rgb']
            },
            {
                name: 'Webcam HD 1080p',
                description: 'Full HD webcam with auto-focus and noise reduction',
                sku: 'ACC-WEB-HD-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 65000,
                cost: 45000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 20,
                    minQuantity: 5,
                    maxQuantity: 80,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['webcam', 'hd', 'video', 'conference']
            },

            // Storage
            {
                name: 'Samsung 1TB NVMe SSD',
                description: 'High-speed NVMe SSD with 3,500 MB/s read speed',
                sku: 'STO-SAM-1TB-001',
                category: categories.find(c => c.slug === 'storage')._id,
                price: 165000,
                cost: 135000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 30,
                    minQuantity: 8,
                    maxQuantity: 150,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['ssd', 'samsung', 'nvme', 'storage']
            },
            {
                name: 'WD 2TB External HDD',
                description: 'Portable external hard drive with USB 3.0',
                sku: 'STO-WD-2TB-001',
                category: categories.find(c => c.slug === 'storage')._id,
                price: 95000,
                cost: 75000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 22,
                    minQuantity: 5,
                    maxQuantity: 100,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['hdd', 'wd', 'external', 'portable']
            },

            // Components
            {
                name: 'NVIDIA RTX 4060 Graphics Card',
                description: 'High-performance graphics card for gaming and content creation',
                sku: 'COM-NV-RTX4060-001',
                category: categories.find(c => c.slug === 'components')._id,
                price: 485000,
                cost: 425000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 5,
                    minQuantity: 1,
                    maxQuantity: 20,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['gpu', 'nvidia', 'rtx', 'gaming']
            },
            {
                name: 'Intel Core i7-13700K CPU',
                description: '13th Gen Intel Core processor with 16 cores',
                sku: 'COM-INT-I7-13700K-001',
                category: categories.find(c => c.slug === 'components')._id,
                price: 425000,
                cost: 375000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 8,
                    minQuantity: 2,
                    maxQuantity: 30,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['cpu', 'intel', 'i7', 'processor']
            },

            // Networking
            {
                name: 'TP-Link WiFi 6 Router',
                description: 'AX3000 Dual Band Gigabit WiFi 6 Router',
                sku: 'NET-TPL-AX3000-001',
                category: categories.find(c => c.slug === 'networking')._id,
                price: 125000,
                cost: 95000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 15,
                    minQuantity: 3,
                    maxQuantity: 50,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['router', 'tp-link', 'wifi6', 'networking']
            },
            {
                name: 'Cat6 Ethernet Cable 5m',
                description: 'High-speed Cat6 ethernet cable for reliable connections',
                sku: 'NET-CAB-CAT6-5M-001',
                category: categories.find(c => c.slug === 'networking')._id,
                price: 8500,
                cost: 6000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 50,
                    minQuantity: 10,
                    maxQuantity: 200,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['cable', 'ethernet', 'cat6', 'networking']
            },

            // Additional Laptops
            {
                name: 'MacBook Air M2',
                description: '13.6" Liquid Retina Display, Apple M2 chip, 8GB RAM, 256GB SSD',
                sku: 'LAP-APPLE-MBA-M2-001',
                category: categories.find(c => c.slug === 'laptops')._id,
                price: 2200000,
                cost: 1950000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 5,
                    minQuantity: 1,
                    maxQuantity: 20,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['laptop', 'apple', 'macbook', 'm2']
            },
            {
                name: 'Lenovo ThinkPad E15',
                description: '15.6" FHD, Intel Core i5, 8GB RAM, 512GB SSD, Business Laptop',
                sku: 'LAP-LEN-TP-E15-001',
                category: categories.find(c => c.slug === 'laptops')._id,
                price: 1150000,
                cost: 1000000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 10,
                    minQuantity: 2,
                    maxQuantity: 35,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['laptop', 'lenovo', 'thinkpad', 'business']
            },
            {
                name: 'Acer Aspire 5',
                description: '15.6" FHD, AMD Ryzen 7, 16GB RAM, 512GB SSD',
                sku: 'LAP-ACER-ASP5-001',
                category: categories.find(c => c.slug === 'laptops')._id,
                price: 1050000,
                cost: 920000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 7,
                    minQuantity: 2,
                    maxQuantity: 25,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['laptop', 'acer', 'aspire', 'ryzen']
            },
            {
                name: 'MSI Gaming Laptop GF63',
                description: '15.6" FHD 144Hz, Intel i5, 8GB RAM, 512GB SSD, GTX 1650',
                sku: 'LAP-MSI-GF63-001',
                category: categories.find(c => c.slug === 'laptops')._id,
                price: 1350000,
                cost: 1180000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 6,
                    minQuantity: 1,
                    maxQuantity: 20,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['laptop', 'msi', 'gaming', 'gtx']
            },

            // Additional Monitors
            {
                name: 'ASUS 24" Gaming Monitor',
                description: '24" FHD 144Hz, 1ms Response Time, FreeSync',
                sku: 'MON-ASUS-24G-001',
                category: categories.find(c => c.slug === 'monitors')._id,
                price: 280000,
                cost: 230000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 15,
                    minQuantity: 3,
                    maxQuantity: 50,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['monitor', 'asus', 'gaming', '144hz']
            },
            {
                name: 'Dell 32" 4K Monitor',
                description: '32" UHD 4K IPS, USB-C Hub, Height Adjustable',
                sku: 'MON-DELL-32-4K-001',
                category: categories.find(c => c.slug === 'monitors')._id,
                price: 750000,
                cost: 650000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 4,
                    minQuantity: 1,
                    maxQuantity: 15,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['monitor', 'dell', '4k', 'usb-c']
            },
            {
                name: 'AOC 27" Curved Monitor',
                description: '27" FHD Curved VA Panel, 75Hz, HDMI/VGA',
                sku: 'MON-AOC-27C-001',
                category: categories.find(c => c.slug === 'monitors')._id,
                price: 320000,
                cost: 270000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 8,
                    minQuantity: 2,
                    maxQuantity: 30,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['monitor', 'aoc', 'curved', '75hz']
            },

            // Additional Accessories
            {
                name: 'Razer DeathAdder V3 Mouse',
                description: 'Ergonomic gaming mouse with 30K DPI sensor',
                sku: 'ACC-RAZ-DA-V3-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 85000,
                cost: 65000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 25,
                    minQuantity: 5,
                    maxQuantity: 80,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['mouse', 'razer', 'gaming', 'ergonomic']
            },
            {
                name: 'SteelSeries Arctis 7 Headset',
                description: 'Wireless gaming headset with DTS Headphone:X 2.0',
                sku: 'ACC-SS-ARC7-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 195000,
                cost: 155000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 12,
                    minQuantity: 3,
                    maxQuantity: 40,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['headset', 'steelseries', 'wireless', 'gaming']
            },
            {
                name: 'Webcam HD 1080p',
                description: 'USB webcam with auto-focus and built-in microphone',
                sku: 'ACC-WEB-HD-002',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 45000,
                cost: 35000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 30,
                    minQuantity: 8,
                    maxQuantity: 100,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['webcam', 'hd', 'usb', 'microphone']
            },
            {
                name: 'USB-C to HDMI Adapter',
                description: '4K@60Hz USB-C to HDMI adapter cable',
                sku: 'ACC-USB-HDMI-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 25000,
                cost: 18000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 40,
                    minQuantity: 10,
                    maxQuantity: 150,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['adapter', 'usb-c', 'hdmi', '4k']
            },

            // Additional Storage
            {
                name: 'Samsung 1TB NVMe SSD',
                description: '980 PRO PCIe 4.0 NVMe M.2 SSD with heatsink',
                sku: 'STO-SAM-1TB-NVME-001',
                category: categories.find(c => c.slug === 'storage')._id,
                price: 185000,
                cost: 155000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 18,
                    minQuantity: 4,
                    maxQuantity: 60,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['ssd', 'samsung', 'nvme', 'pcie4']
            },
            {
                name: 'Seagate 4TB External HDD',
                description: 'Backup Plus Portable 4TB USB 3.0 External Hard Drive',
                sku: 'STO-SEA-4TB-EXT-001',
                category: categories.find(c => c.slug === 'storage')._id,
                price: 145000,
                cost: 120000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 12,
                    minQuantity: 3,
                    maxQuantity: 40,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['hdd', 'seagate', 'external', '4tb']
            },
            {
                name: 'Kingston 32GB USB Flash Drive',
                description: 'DataTraveler 100 G3 USB 3.0 Flash Drive',
                sku: 'STO-KIN-32GB-USB-001',
                category: categories.find(c => c.slug === 'storage')._id,
                price: 18000,
                cost: 14000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 45,
                    minQuantity: 15,
                    maxQuantity: 150,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['usb', 'kingston', 'flash-drive', '32gb']
            },

            // Additional Components
            {
                name: 'AMD Ryzen 7 5800X CPU',
                description: '8-Core, 16-Thread Desktop Processor with Wraith Prism Cooler',
                sku: 'COM-AMD-R7-5800X-001',
                category: categories.find(c => c.slug === 'components')._id,
                price: 385000,
                cost: 340000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 6,
                    minQuantity: 2,
                    maxQuantity: 25,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['cpu', 'amd', 'ryzen7', 'processor']
            },
            {
                name: 'NVIDIA RTX 4060 Graphics Card',
                description: 'GeForce RTX 4060 8GB GDDR6 Gaming Graphics Card',
                sku: 'COM-NV-RTX4060-002',
                category: categories.find(c => c.slug === 'components')._id,
                price: 485000,
                cost: 425000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 4,
                    minQuantity: 1,
                    maxQuantity: 15,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['gpu', 'nvidia', 'rtx4060', 'graphics']
            },
            {
                name: 'Corsair 16GB DDR4 RAM',
                description: 'Vengeance LPX 16GB (2x8GB) DDR4 3200MHz C16',
                sku: 'COM-COR-16GB-DDR4-001',
                category: categories.find(c => c.slug === 'components')._id,
                price: 95000,
                cost: 78000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 20,
                    minQuantity: 5,
                    maxQuantity: 80,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['ram', 'corsair', 'ddr4', '16gb']
            },
            {
                name: 'ASUS B550 Motherboard',
                description: 'PRIME B550M-A WiFi Micro ATX AM4 Motherboard',
                sku: 'COM-ASUS-B550-001',
                category: categories.find(c => c.slug === 'components')._id,
                price: 165000,
                cost: 140000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 8,
                    minQuantity: 2,
                    maxQuantity: 30,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['motherboard', 'asus', 'b550', 'am4']
            },

            // Additional Networking
            {
                name: 'D-Link 8-Port Gigabit Switch',
                description: 'DGS-1008A 8-Port Gigabit Desktop Switch',
                sku: 'NET-DL-8PORT-001',
                category: categories.find(c => c.slug === 'networking')._id,
                price: 45000,
                cost: 35000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 15,
                    minQuantity: 3,
                    maxQuantity: 50,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['switch', 'd-link', 'gigabit', '8-port']
            },
            {
                name: 'USB WiFi Adapter AC600',
                description: 'Dual Band 600Mbps USB 3.0 WiFi Adapter',
                sku: 'NET-USB-WIFI-AC600-001',
                category: categories.find(c => c.slug === 'networking')._id,
                price: 28000,
                cost: 22000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 35,
                    minQuantity: 10,
                    maxQuantity: 120,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['wifi', 'usb', 'adapter', 'ac600']
            },

            // More Accessories
            {
                name: 'Laptop Cooling Pad',
                description: 'RGB LED Laptop Cooler with 6 Fans and 2 USB Ports',
                sku: 'ACC-COOL-PAD-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 35000,
                cost: 28000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 20,
                    minQuantity: 5,
                    maxQuantity: 70,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['cooling', 'laptop', 'rgb', 'fan']
            },
            {
                name: 'Wireless Charging Pad',
                description: '15W Fast Wireless Charger for Smartphones',
                sku: 'ACC-WIRELESS-CHARGE-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 22000,
                cost: 17000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 25,
                    minQuantity: 8,
                    maxQuantity: 100,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: false,
                tags: ['wireless', 'charger', 'smartphone', '15w']
            },
            {
                name: 'Bluetooth Speaker Portable',
                description: 'Waterproof Bluetooth 5.0 Speaker with 12H Battery',
                sku: 'ACC-BT-SPEAKER-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 55000,
                cost: 42000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 18,
                    minQuantity: 4,
                    maxQuantity: 60,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['bluetooth', 'speaker', 'waterproof', 'portable']
            },

            // Sold Out Items
            {
                name: 'Gaming Chair RGB',
                description: 'Ergonomic Gaming Chair with RGB Lighting and Lumbar Support',
                sku: 'ACC-CHAIR-RGB-001',
                category: categories.find(c => c.slug === 'accessories')._id,
                price: 285000,
                cost: 235000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 0,
                    minQuantity: 1,
                    maxQuantity: 20,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['chair', 'gaming', 'rgb', 'ergonomic']
            },
            {
                name: 'MacBook Pro 16" M2',
                description: '16.2" Liquid Retina XDR, Apple M2 Pro, 16GB RAM, 512GB SSD',
                sku: 'LAP-APPLE-MBP16-M2-001',
                category: categories.find(c => c.slug === 'laptops')._id,
                price: 3850000,
                cost: 3450000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 0,
                    minQuantity: 1,
                    maxQuantity: 10,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['laptop', 'apple', 'macbook-pro', 'm2-pro']
            },
            {
                name: 'RTX 4090 Graphics Card',
                description: 'NVIDIA GeForce RTX 4090 24GB GDDR6X Gaming GPU',
                sku: 'COM-NV-RTX4090-001',
                category: categories.find(c => c.slug === 'components')._id,
                price: 2850000,
                cost: 2550000,
                currency: 'MMK',
                taxRate: 5,
                inventory: {
                    quantity: 0,
                    minQuantity: 1,
                    maxQuantity: 5,
                    unit: 'piece'
                },
                isActive: true,
                isFeatured: true,
                tags: ['gpu', 'nvidia', 'rtx4090', 'flagship']
            }
        ];

        const createdProducts = await Product.insertMany(products);
        console.log(`Created ${createdProducts.length} products`);
        return createdProducts;
    } catch (error) {
        console.error('Error seeding products:', error);
        throw error;
    }
};

const seedData = async () => {
    try {
        await connectDB();

        console.log('Starting to seed categories and products...');

        const categories = await seedCategories();
        const products = await seedProducts(categories);

        console.log('✅ Successfully seeded database with sample data!');
        console.log(`📁 Categories: ${categories.length}`);
        console.log(`📦 Products: ${products.length}`);

        process.exit(0);
    } catch (error) {
        console.error('❌ Error seeding database:', error);
        process.exit(1);
    }
};

// Run the seeding
seedData();
