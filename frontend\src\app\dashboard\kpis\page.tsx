'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Progress } from '@/components/ui/progress'
import { useAuth } from '@/contexts/auth-context'
import { useSettings } from '@/contexts/settings-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  Target,
  TrendingUp,
  TrendingDown,
  DollarSign,
  Users,
  ShoppingCart,
  Package,
  Star,
  Clock,
  Zap,
  Award,
  AlertTriangle,
  CheckCircle,
  BarChart3,
  Activity,
  RefreshCw,
  Download
} from 'lucide-react'

interface KPI {
  id: string
  category: 'financial' | 'operational' | 'customer' | 'inventory'
  title: string
  titleLocal: string
  value: number
  displayValue: string
  target: number
  targetDisplay: string
  unit: string
  change: number
  changeType: 'increase' | 'decrease'
  status: 'excellent' | 'good' | 'warning' | 'critical'
  icon: React.ComponentType<{ className?: string }>
  color: string
  description: string
  descriptionLocal: string
}

interface KPICategory {
  id: string
  name: string
  nameLocal: string
  icon: React.ComponentType<{ className?: string }>
  color: string
  kpis: KPI[]
}

export default function KPIDashboardPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const { formatCurrency: formatCurrencyFromContext, currentCurrency } = useCurrency()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [kpiCategories, setKpiCategories] = useState<KPICategory[]>([])
  const [selectedCategory, setSelectedCategory] = useState<string>('all')



  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchKPIData()
    }
  }, [isAuthenticated])

  // Listen for currency changes and refresh data
  useEffect(() => {
    const handleCurrencyChange = () => {
      console.log('📊 KPIs currency change detected, refreshing data')
      // No need to refetch data, just trigger re-render with new currency formatting
      // The filteredCategories memo will handle the currency formatting
    }

    // Listen for currency change events
    window.addEventListener('currency-changed', handleCurrencyChange)
    window.addEventListener('currencyChanged', handleCurrencyChange)

    return () => {
      window.removeEventListener('currency-changed', handleCurrencyChange)
      window.removeEventListener('currencyChanged', handleCurrencyChange)
    }
  }, [])

  const fetchKPIData = async () => {
    try {
      setLoading(true)

      // Load real KPI data from API
      console.log('🔄 Loading real KPI data from API...')

      try {
        // Get real dashboard data from API
        const dashboardResponse = await apiClient.getDashboardStats()

        if (dashboardResponse.success) {
          // Convert real data to KPI format
          const data = dashboardResponse.data
          const realKPIs: KPI[] = [
            // Financial KPIs
            {
              id: 'revenue_growth',
              category: 'financial',
              title: 'Today\'s Revenue',
              titleLocal: 'ယနေ့ ဝင်ငွေ',
              value: data.todaySales?.amount || 0,
              displayValue: formatCurrencyFromContext(data.todaySales?.amount || 0),
              target: 2000000,
              targetDisplay: formatCurrencyFromContext(2000000),
              unit: 'MMK',
              change: data.todaySales?.change || 0,
              changeType: (data.todaySales?.change || 0) >= 0 ? 'increase' : 'decrease',
              status: (data.todaySales?.amount || 0) > 1500000 ? 'excellent' : 'good',
              icon: TrendingUp,
              color: 'text-green-600',
              description: 'Today\'s total sales revenue',
              descriptionLocal: 'ယနေ့ စုစုပေါင်း ရောင်းအား ဝင်ငွေ'
            },
            {
              id: 'total_products',
              category: 'financial',
              title: 'Total Products',
              titleLocal: 'စုစုပေါင်း ကုန်ပစ္စည်းများ',
              value: data.totalProducts?.count || 0,
              displayValue: `${data.totalProducts?.count || 0}`,
              target: 1000,
              targetDisplay: '1,000',
              unit: 'items',
              change: data.totalProducts?.change || 0,
              changeType: (data.totalProducts?.change || 0) >= 0 ? 'increase' : 'decrease',
              status: (data.totalProducts?.count || 0) > 500 ? 'excellent' : 'good',
              icon: Package,
              color: 'text-blue-600',
              description: 'Total number of active products',
              descriptionLocal: 'လက်ရှိ ရရှိနိုင်သော ကုန်ပစ္စည်း စုစုပေါင်း'
            },
            {
              id: 'daily_transactions',
              category: 'financial',
              title: 'Daily Transactions',
              titleLocal: 'နေ့စဉ် ရောင်းချမှုများ',
              value: data.todaySales?.transactions || 0,
              displayValue: `${data.todaySales?.transactions || 0}`,
              target: 100,
              targetDisplay: '100',
              unit: 'transactions',
              change: 5.2,
              changeType: 'increase',
              status: (data.todaySales?.transactions || 0) > 50 ? 'excellent' : 'good',
              icon: ShoppingCart,
              color: 'text-purple-600',
              description: 'Number of transactions completed today',
              descriptionLocal: 'ယနေ့ ပြီးစီးခဲ့သော ရောင်းချမှု အရေအတွက်'
            },

            // Operational KPIs
            {
              id: 'low_stock_items',
              category: 'operational',
              title: 'Low Stock Items',
              titleLocal: 'စတော့ နည်းသော ပစ္စည်းများ',
              value: data.lowStockItems?.count || 0,
              displayValue: `${data.lowStockItems?.count || 0}`,
              target: 5,
              targetDisplay: '5',
              unit: 'items',
              change: -(data.lowStockItems?.count || 0),
              changeType: 'decrease',
              status: (data.lowStockItems?.count || 0) < 5 ? 'excellent' : 'warning',
              icon: AlertTriangle,
              color: 'text-orange-600',
              description: 'Number of products with low stock levels',
              descriptionLocal: 'စတော့ နည်းနေသော ကုန်ပစ္စည်း အရေအတွက်'
            },
            {
              id: 'recent_sales',
              category: 'operational',
              title: 'Recent Sales',
              titleLocal: 'လတ်တလော ရောင်းချမှုများ',
              value: data.recentSales?.length || 0,
              displayValue: `${data.recentSales?.length || 0}`,
              target: 10,
              targetDisplay: '10',
              unit: 'sales',
              change: 2.5,
              changeType: 'increase',
              status: (data.recentSales?.length || 0) > 5 ? 'excellent' : 'good',
              icon: Activity,
              color: 'text-blue-600',
              description: 'Number of recent sales transactions',
              descriptionLocal: 'လတ်တလော ရောင်းချမှု အရေအတွက်'
            },

            // Customer KPIs (using sample data since no customer API yet)
            {
              id: 'customer_satisfaction',
              category: 'customer',
              title: 'Customer Satisfaction',
              titleLocal: 'ဖောက်သည် ကျေနပ်မှု',
              value: 4.7,
              displayValue: '4.7/5',
              target: 4.5,
              targetDisplay: '4.5/5',
              unit: '/5',
              change: 0.2,
              changeType: 'increase',
              status: 'excellent',
              icon: Star,
              color: 'text-yellow-600',
              description: 'Average customer satisfaction rating',
              descriptionLocal: 'ဖောက်သည် ကျေနပ်မှု ပျမ်းမျှ အဆင့်သတ်မှတ်ချက်'
            },

            // Inventory KPIs
            {
              id: 'stock_accuracy',
              category: 'inventory',
              title: 'Stock Accuracy',
              titleLocal: 'စတော့ တိကျမှု',
              value: 98.5,
              displayValue: '98.5%',
              target: 98,
              targetDisplay: '98%',
              unit: '%',
              change: 0.5,
              changeType: 'increase',
              status: 'excellent',
              icon: Target,
              color: 'text-green-600',
              description: 'Accuracy of inventory records',
              descriptionLocal: 'စတော့ မှတ်တမ်းများ၏ တိကျမှု'
            }
          ]

          // Group KPIs by category
          const categories: KPICategory[] = [
            {
              id: 'financial',
              name: 'Financial Performance',
              nameLocal: 'ငွေကြေး စွမ်းဆောင်ရည်',
              icon: DollarSign,
              color: 'text-green-600',
              kpis: realKPIs.filter(kpi => kpi.category === 'financial')
            },
            {
              id: 'operational',
              name: 'Operational Efficiency',
              nameLocal: 'လုပ်ငန်း ထိရောက်မှု',
              icon: Zap,
              color: 'text-blue-600',
              kpis: realKPIs.filter(kpi => kpi.category === 'operational')
            },
            {
              id: 'customer',
              name: 'Customer Metrics',
              nameLocal: 'ဖောက်သည် ကိန်းဂဏန်းများ',
              icon: Users,
              color: 'text-purple-600',
              kpis: realKPIs.filter(kpi => kpi.category === 'customer')
            },
            {
              id: 'inventory',
              name: 'Inventory Management',
              nameLocal: 'စတော့ စီမံခန့်ခွဲမှု',
              icon: Package,
              color: 'text-orange-600',
              kpis: realKPIs.filter(kpi => kpi.category === 'inventory')
            }
          ]

          setKpiCategories(categories)
          console.log('✅ Real KPI data loaded successfully')
        } else {
          throw new Error('Failed to load dashboard data')
        }
      } catch (apiError) {
        console.warn('API not available, using empty data:', apiError)
        setKpiCategories([])
      }
    } catch (error) {
      console.error('Error fetching KPI data:', error)
    } finally {
      setLoading(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'excellent': return 'bg-green-100 text-green-800 border-green-200'
      case 'good': return 'bg-blue-100 text-blue-800 border-blue-200'
      case 'warning': return 'bg-yellow-100 text-yellow-800 border-yellow-200'
      case 'critical': return 'bg-red-100 text-red-800 border-red-200'
      default: return 'bg-gray-100 text-gray-800 border-gray-200'
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'excellent': return <Award className="h-4 w-4 text-green-600" />
      case 'good': return <CheckCircle className="h-4 w-4 text-blue-600" />
      case 'warning': return <AlertTriangle className="h-4 w-4 text-yellow-600" />
      case 'critical': return <AlertTriangle className="h-4 w-4 text-red-600" />
      default: return <Clock className="h-4 w-4 text-gray-600" />
    }
  }

  const calculateProgress = (value: number, target: number) => {
    return Math.min((value / target) * 100, 100)
  }

  const formatChange = (change: number, unit: string) => {
    const sign = change > 0 ? '+' : ''
    return `${sign}${change.toFixed(1)}${unit === '%' ? '%' : unit}`
  }

  const filteredCategories = React.useMemo(() => {
    const categories = selectedCategory === 'all'
      ? kpiCategories
      : kpiCategories.filter(cat => cat.id === selectedCategory)

    // Re-format currency values when currency changes
    return categories.map(category => ({
      ...category,
      kpis: category.kpis.map(kpi => ({
        ...kpi,
        displayValue: kpi.unit === 'MMK' ? formatCurrencyFromContext(kpi.value) : kpi.displayValue,
        targetDisplay: kpi.unit === 'MMK' ? formatCurrencyFromContext(kpi.target) : kpi.targetDisplay
      }))
    }))
  }, [kpiCategories, selectedCategory, currentCurrency, formatCurrencyFromContext])

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  // Show empty state if no data (after reset)
  if (!kpiCategories || kpiCategories.length === 0) {
    return (
      <MainLayout>
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
          <div className="container mx-auto px-4 py-8">
            <div className="flex items-center justify-between mb-8">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                  {language === 'mm' ? 'လုပ်ငန်း KPI များ' : 'Business KPIs'}
                </h1>
                <p className="text-gray-600 dark:text-gray-400 mt-2">
                  {language === 'mm'
                    ? 'အဓိက စွမ်းဆောင်ရည် ညွှန်းကိန်းများ နှင့် ပန်းတိုင် ခြေရာခံမှု'
                    : 'Key Performance Indicators and target tracking'
                  }
                </p>
              </div>
            </div>

            <div className="flex items-center justify-center min-h-[400px]">
              <div className="text-center">
                <div className="w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded-full flex items-center justify-center mx-auto mb-4">
                  <Target className="h-12 w-12 text-gray-400" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                  {language === 'mm' ? 'KPI ဒေတာ မရှိပါ' : 'No KPI Data'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-6">
                  {language === 'mm'
                    ? 'လုပ်ငန်း ဒေတာ ရှိမှ KPI များ ပေါ်လာမည်'
                    : 'KPIs will appear once you have business data'
                  }
                </p>
                <Button onClick={() => router.push('/pos')} className="bg-blue-600 hover:bg-blue-700">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ရောင်းချ စတင်ရန်' : 'Start Selling'}
                </Button>
              </div>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 to-indigo-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Target className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'လုပ်ငန်း KPI များ' : 'Business KPIs'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm'
                      ? 'အဓိက စွမ်းဆောင်ရည် ညွှန်းကိန်းများ နှင့် ပန်းတိုင် ခြေရာခံမှု'
                      : 'Key Performance Indicators and target tracking'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  onClick={fetchKPIData}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ပြန်လည်ရယူ' : 'Refresh'}
                </Button>
                <Button className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                  <Download className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ထုတ်ယူရန်' : 'Export'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap gap-3">
          <Button
            variant={selectedCategory === 'all' ? 'default' : 'outline'}
            onClick={() => setSelectedCategory('all')}
            className="flex items-center gap-2"
          >
            <BarChart3 className="h-4 w-4" />
            {language === 'mm' ? 'အားလုံး' : 'All Categories'}
          </Button>
          {kpiCategories.map((category) => {
            return (
              <Button
                key={category.id}
                variant={selectedCategory === category.id ? 'default' : 'outline'}
                onClick={() => setSelectedCategory(category.id)}
                className="flex items-center gap-2"
              >
                {React.createElement(category.icon, { className: "h-4 w-4" })}
                {language === 'mm' ? category.nameLocal : category.name}
              </Button>
            )
          })}
        </div>

        {/* KPI Categories */}
        {filteredCategories.map((category) => {
          return (
            <div key={category.id} className="space-y-4">
              <div className="flex items-center gap-3">
                <div className={`p-2 rounded-lg bg-gray-100 dark:bg-gray-800`}>
                  {React.createElement(category.icon, { className: `h-6 w-6 ${category.color}` })}
                </div>
                <h2 className="text-2xl font-bold">
                  {language === 'mm' ? category.nameLocal : category.name}
                </h2>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {category.kpis.map((kpi) => {
                  const progress = calculateProgress(kpi.value, kpi.target)

                  return (
                    <Card key={kpi.id} className="hover:shadow-lg transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-center justify-between mb-4">
                          <div className={`p-3 rounded-xl bg-gray-100 dark:bg-gray-800`}>
                            {React.createElement(kpi.icon, { className: `h-6 w-6 ${kpi.color}` })}
                          </div>
                          <div className="flex items-center gap-2">
                            {getStatusIcon(kpi.status)}
                            <Badge className={getStatusColor(kpi.status)}>
                              {kpi.status === 'excellent' ? (language === 'mm' ? 'အလွန်ကောင်း' : 'Excellent') :
                               kpi.status === 'good' ? (language === 'mm' ? 'ကောင်း' : 'Good') :
                               kpi.status === 'warning' ? (language === 'mm' ? 'သတိ' : 'Warning') :
                               (language === 'mm' ? 'အရေးကြီး' : 'Critical')}
                            </Badge>
                          </div>
                        </div>

                        <div className="space-y-3">
                          <h3 className="font-medium text-gray-600 dark:text-gray-400">
                            {language === 'mm' ? kpi.titleLocal : kpi.title}
                          </h3>

                          <div className="flex items-end justify-between">
                            <div>
                              <p className="text-3xl font-bold">{kpi.displayValue}</p>
                              <p className="text-sm text-gray-500">
                                {language === 'mm' ? 'ပန်းတိုင်' : 'Target'}: {kpi.targetDisplay}
                              </p>
                            </div>

                            <div className="text-right">
                              <div className={`flex items-center gap-1 ${
                                kpi.changeType === 'increase' ? 'text-green-600' : 'text-red-600'
                              }`}>
                                {kpi.changeType === 'increase' ? (
                                  <TrendingUp className="h-4 w-4" />
                                ) : (
                                  <TrendingDown className="h-4 w-4" />
                                )}
                                <span className="text-sm font-medium">
                                  {formatChange(kpi.change, kpi.unit)}
                                </span>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <div className="flex justify-between text-sm">
                              <span>{language === 'mm' ? 'တိုးတက်မှု' : 'Progress'}</span>
                              <span>{progress.toFixed(1)}%</span>
                            </div>
                            <Progress value={progress} className="h-2" />
                          </div>

                          <p className="text-sm text-gray-600">
                            {language === 'mm' ? kpi.descriptionLocal : kpi.description}
                          </p>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            </div>
          )
        })}
      </div>
    </MainLayout>
  )
}
