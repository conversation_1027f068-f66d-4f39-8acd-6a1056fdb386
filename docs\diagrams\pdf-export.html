<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitesTech POS System - Architecture & Diagrams</title>
    <script src="https://cdn.jsdelivr.net/npm/mermaid/dist/mermaid.min.js"></script>
    <style>
        @page {
            size: A4;
            margin: 2cm;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 100%;
            margin: 0;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            border-bottom: 3px solid #2563eb;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #1e40af;
            font-size: 2.5em;
            margin: 0;
        }
        
        .header p {
            color: #6b7280;
            font-size: 1.2em;
            margin: 10px 0;
        }
        
        .section {
            margin-bottom: 40px;
            page-break-inside: avoid;
        }
        
        .section h2 {
            color: #1e40af;
            border-left: 4px solid #3b82f6;
            padding-left: 15px;
            font-size: 1.8em;
            margin-bottom: 20px;
        }
        
        .section h3 {
            color: #374151;
            font-size: 1.4em;
            margin-top: 25px;
            margin-bottom: 15px;
        }
        
        .diagram-container {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        
        .mermaid {
            background: white;
            border-radius: 4px;
            padding: 15px;
        }
        
        .description {
            background: #eff6ff;
            border-left: 4px solid #3b82f6;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 4px 4px 0;
        }
        
        .tech-stack {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .tech-item {
            background: #f1f5f9;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #10b981;
        }
        
        .tech-item h4 {
            color: #065f46;
            margin: 0 0 10px 0;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: #fef3c7;
            padding: 12px;
            border-radius: 6px;
            border-left: 3px solid #f59e0b;
        }
        
        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 20px;
            border-top: 2px solid #e5e7eb;
            color: #6b7280;
        }
        
        .page-break {
            page-break-before: always;
        }
        
        @media print {
            .section {
                page-break-inside: avoid;
            }
            
            .diagram-container {
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>BitesTech POS System</h1>
        <p>Architecture & System Diagrams Documentation</p>
        <p><strong>Version 1.0</strong> | <em>Complete System Overview</em></p>
    </div>

    <div class="section">
        <h2>📊 System Architecture Overview</h2>
        <div class="description">
            <p><strong>ဒီ diagram က</strong> POS System ရဲ့ overall architecture ကို ပြသထားပါတယ်။ Frontend, Backend, နှင့် Database layers တွေရဲ့ relationships နှင့် data flow ကို မြင်နိုင်ပါတယ်။</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph TB
    subgraph "Frontend (React + Next.js)"
        A[Mobile/Tablet Interface] --> B[Desktop Interface]
        B --> C[Responsive Components]
        C --> D[Theme System]
        D --> E[Multi-language]
    end
    
    subgraph "Backend (Node.js + Express)"
        F[Authentication API] --> G[POS API]
        G --> H[Inventory API]
        H --> I[Reports API]
        I --> J[User Management API]
    end
    
    subgraph "Database"
        K[(MongoDB/PostgreSQL)]
        L[Products Collection]
        M[Sales Collection]
        N[Users Collection]
        O[Settings Collection]
    end
    
    A --> F
    B --> F
    F --> K
    G --> L
    H --> M
    I --> N
    J --> O
            </div>
        </div>
        
        <div class="tech-stack">
            <div class="tech-item">
                <h4>Frontend Technologies</h4>
                <ul>
                    <li>React 18 + Next.js 14</li>
                    <li>TypeScript</li>
                    <li>Tailwind CSS + shadcn/ui</li>
                    <li>Zustand (State Management)</li>
                </ul>
            </div>
            <div class="tech-item">
                <h4>Backend Technologies</h4>
                <ul>
                    <li>Node.js + Express.js</li>
                    <li>MongoDB/PostgreSQL</li>
                    <li>JWT Authentication</li>
                    <li>RESTful API</li>
                </ul>
            </div>
            <div class="tech-item">
                <h4>DevOps & Tools</h4>
                <ul>
                    <li>Docker Containerization</li>
                    <li>Git Version Control</li>
                    <li>ESLint + Prettier</li>
                    <li>Jest Testing</li>
                </ul>
            </div>
        </div>
    </div>

    <div class="section page-break">
        <h2>🔄 System Flow & User Workflows</h2>
        <div class="description">
            <p><strong>ဒီ section မှာ</strong> user authentication, POS terminal operations, နှင့် main system workflows တွေကို ပြသထားပါတယ်။</p>
        </div>
        
        <h3>Main System Navigation Flow</h3>
        <div class="diagram-container">
            <div class="mermaid">
flowchart TD
    A[Login Page] --> B{Authentication}
    B -->|Success| C[Dashboard]
    B -->|Fail| A
    
    C --> D[POS Terminal]
    C --> E[Products Management]
    C --> F[Sales History]
    C --> G[Reports]
    C --> H[Inventory]
    C --> I[User Management]
    C --> J[Settings]
    
    D --> D1[Product Selection]
    D1 --> D2[Cart Management]
    D2 --> D3[Payment Processing]
    D3 --> D4[Receipt Generation]
    
    E --> E1[Add Product]
    E --> E2[Edit Product]
    E --> E3[Delete Product]
    E --> E4[Product Categories]
            </div>
        </div>
        
        <h3>POS Terminal Workflow</h3>
        <div class="diagram-container">
            <div class="mermaid">
sequenceDiagram
    participant U as User/Cashier
    participant P as POS Terminal
    participant I as Inventory
    participant Pay as Payment
    participant R as Receipt
    
    U->>P: Start New Sale
    U->>P: Scan/Select Product
    P->>I: Check Stock Availability
    I-->>P: Stock Status
    P->>P: Add to Cart
    U->>P: Apply Discount (Optional)
    U->>P: Select Payment Method
    P->>Pay: Process Payment
    Pay-->>P: Payment Confirmation
    P->>I: Update Stock Levels
    P->>R: Generate Receipt
    R-->>U: Print/Email Receipt
    P->>P: Complete Transaction
            </div>
        </div>
    </div>

    <div class="section page-break">
        <h2>📁 Project Structure & Organization</h2>
        <div class="description">
            <p><strong>ဒီ diagram က</strong> complete project folder structure နှင့် code organization ကို ပြသထားပါတယ်။</p>
        </div>
        
        <div class="diagram-container">
            <div class="mermaid">
graph TD
    A[BitesTech POS] --> B[frontend/]
    A --> C[backend/]
    A --> D[shared/]
    A --> E[docs/]
    
    B --> B1[src/]
    B --> B2[public/]
    B --> B3[package.json]
    
    B1 --> B11[components/]
    B1 --> B12[pages/]
    B1 --> B13[hooks/]
    B1 --> B14[utils/]
    B1 --> B15[styles/]
    B1 --> B16[locales/]
    B1 --> B17[store/]
    
    C --> C1[src/]
    C --> C2[package.json]
    
    C1 --> C11[controllers/]
    C1 --> C12[models/]
    C1 --> C13[routes/]
    C1 --> C14[middleware/]
    C1 --> C15[utils/]
    C1 --> C16[config/]
            </div>
        </div>
    </div>

    <div class="section">
        <h2>🎯 Key Features & Capabilities</h2>
        <div class="features-grid">
            <div class="feature-item">
                <h4>🔐 Authentication</h4>
                <p>JWT-based secure login with role-based access control</p>
            </div>
            <div class="feature-item">
                <h4>📱 Multi-Platform</h4>
                <p>Phone, tablet, desktop responsive design</p>
            </div>
            <div class="feature-item">
                <h4>🌐 Multi-Language</h4>
                <p>Myanmar & English language support</p>
            </div>
            <div class="feature-item">
                <h4>💰 Multi-Currency</h4>
                <p>MMK, Thai Baht, USD currency support</p>
            </div>
            <div class="feature-item">
                <h4>🎨 Customizable</h4>
                <p>Dark/Light themes with custom colors</p>
            </div>
            <div class="feature-item">
                <h4>📊 Analytics</h4>
                <p>Comprehensive reports and business intelligence</p>
            </div>
        </div>
    </div>

    <div class="footer">
        <p><strong>BitesTech POS System</strong> - Empowering businesses with modern point-of-sale technology</p>
        <p>Built with ❤️ for Myanmar businesses | Documentation Version 1.0</p>
        <p>© 2024 BitesTech. All rights reserved.</p>
    </div>

    <script>
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#1e40af',
                primaryBorderColor: '#2563eb',
                lineColor: '#6b7280',
                secondaryColor: '#f1f5f9',
                tertiaryColor: '#eff6ff'
            }
        });
    </script>
</body>
</html>
