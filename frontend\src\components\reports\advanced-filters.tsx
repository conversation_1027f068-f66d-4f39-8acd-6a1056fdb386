'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Filter,
  Calendar,
  X,
  RefreshCw,
  CheckCircle,
  Clock,
  TrendingUp,
  Users,
  Package,
  DollarSign
} from 'lucide-react'

interface AdvancedFiltersProps {
  isOpen: boolean
  onClose: () => void
  onApply: (filters: FilterOptions) => void
  language: 'en' | 'mm'
  reportType: string
}

interface FilterOptions {
  dateRange: string
  customStartDate: string
  customEndDate: string
  categories: string[]
  priceRange: {
    min: number
    max: number
  }
  customerSegments: string[]
  productStatus: string[]
  sortBy: string
  sortOrder: 'asc' | 'desc'
  groupBy: string
}

export function AdvancedFilters({ isOpen, onClose, onApply, language, reportType }: AdvancedFiltersProps) {
  const [filters, setFilters] = useState<FilterOptions>({
    dateRange: 'month',
    customStartDate: '',
    customEndDate: '',
    categories: [],
    priceRange: { min: 0, max: 10000000 },
    customerSegments: [],
    productStatus: [],
    sortBy: 'date',
    sortOrder: 'desc',
    groupBy: 'none'
  })

  const [activeFilters, setActiveFilters] = useState<string[]>([])

  if (!isOpen) return null

  const categories = [
    'Laptops', 'Monitors', 'Accessories', 'Processors', 'Memory', 'Storage', 'Printers'
  ]

  const customerSegments = [
    { id: 'vip', name: 'VIP Customers', nameLocal: 'VIP ဖောက်သည်များ' },
    { id: 'loyal', name: 'Loyal Customers', nameLocal: 'သစ္စာရှိ ဖောက်သည်များ' },
    { id: 'regular', name: 'Regular Customers', nameLocal: 'ပုံမှန် ဖောက်သည်များ' },
    { id: 'new', name: 'New Customers', nameLocal: 'ဖောက်သည် အသစ်များ' }
  ]

  const productStatuses = [
    { id: 'good', name: 'Good Stock', nameLocal: 'ကောင်းမွန်သော စတော့' },
    { id: 'low', name: 'Low Stock', nameLocal: 'နည်းသော စတော့' },
    { id: 'out', name: 'Out of Stock', nameLocal: 'စတော့ ကုန်' },
    { id: 'overstock', name: 'Overstock', nameLocal: 'စတော့ များ' }
  ]

  const updateFilter = (key: keyof FilterOptions, value: any) => {
    setFilters(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const toggleArrayFilter = (key: 'categories' | 'customerSegments' | 'productStatus', value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: prev[key].includes(value)
        ? prev[key].filter(item => item !== value)
        : [...prev[key], value]
    }))
  }

  const handleApply = () => {
    onApply(filters)
    
    // Update active filters for display
    const active: string[] = []
    if (filters.dateRange !== 'month') active.push('Date Range')
    if (filters.categories.length > 0) active.push('Categories')
    if (filters.customerSegments.length > 0) active.push('Customer Segments')
    if (filters.productStatus.length > 0) active.push('Product Status')
    if (filters.priceRange.min > 0 || filters.priceRange.max < 10000000) active.push('Price Range')
    if (filters.sortBy !== 'date') active.push('Sort Order')
    if (filters.groupBy !== 'none') active.push('Group By')
    
    setActiveFilters(active)
    onClose()
  }

  const handleReset = () => {
    setFilters({
      dateRange: 'month',
      customStartDate: '',
      customEndDate: '',
      categories: [],
      priceRange: { min: 0, max: 10000000 },
      customerSegments: [],
      productStatus: [],
      sortBy: 'date',
      sortOrder: 'desc',
      groupBy: 'none'
    })
    setActiveFilters([])
  }

  const getReportSpecificFilters = () => {
    switch (reportType) {
      case 'sales-analytics':
        return (
          <>
            {/* Customer Segments */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {language === 'mm' ? 'ဖောက်သည် အုပ်စုများ' : 'Customer Segments'}
              </Label>
              <div className="grid grid-cols-2 gap-2">
                {customerSegments.map((segment) => (
                  <div
                    key={segment.id}
                    className={`p-2 border rounded cursor-pointer text-sm transition-all ${
                      filters.customerSegments.includes(segment.id)
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => toggleArrayFilter('customerSegments', segment.id)}
                  >
                    <div className="flex items-center gap-2">
                      <Users className="h-4 w-4" />
                      <span>{language === 'mm' ? segment.nameLocal : segment.name}</span>
                      {filters.customerSegments.includes(segment.id) && (
                        <CheckCircle className="h-3 w-3 text-blue-600 ml-auto" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )
      
      case 'product-performance':
        return (
          <>
            {/* Product Categories */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {language === 'mm' ? 'ကုန်ပစ္စည်း အမျိုးအစားများ' : 'Product Categories'}
              </Label>
              <div className="grid grid-cols-2 gap-2">
                {categories.map((category) => (
                  <div
                    key={category}
                    className={`p-2 border rounded cursor-pointer text-sm transition-all ${
                      filters.categories.includes(category)
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => toggleArrayFilter('categories', category)}
                  >
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      <span>{category}</span>
                      {filters.categories.includes(category) && (
                        <CheckCircle className="h-3 w-3 text-blue-600 ml-auto" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )
      
      case 'inventory-reports':
        return (
          <>
            {/* Product Status */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {language === 'mm' ? 'ကုန်ပစ္စည်း အခြေအနေ' : 'Product Status'}
              </Label>
              <div className="grid grid-cols-2 gap-2">
                {productStatuses.map((status) => (
                  <div
                    key={status.id}
                    className={`p-2 border rounded cursor-pointer text-sm transition-all ${
                      filters.productStatus.includes(status.id)
                        ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => toggleArrayFilter('productStatus', status.id)}
                  >
                    <div className="flex items-center gap-2">
                      <Package className="h-4 w-4" />
                      <span>{language === 'mm' ? status.nameLocal : status.name}</span>
                      {filters.productStatus.includes(status.id) && (
                        <CheckCircle className="h-3 w-3 text-blue-600 ml-auto" />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </>
        )
      
      default:
        return null
    }
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အဆင့်မြင့် စစ်ထုတ်မှုများ' : 'Advanced Filters'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'အသေးစိတ် စစ်ထုတ်မှု ရွေးချယ်မှုများ သတ်မှတ်ပါ'
                  : 'Configure detailed filtering options for your report'
                }
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Date Range */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {language === 'mm' ? 'ရက်စွဲ အပိုင်းအခြား' : 'Date Range'}
              </Label>
              <Select value={filters.dateRange} onValueChange={(value) => updateFilter('dateRange', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">{language === 'mm' ? 'ယနေ့' : 'Today'}</SelectItem>
                  <SelectItem value="yesterday">{language === 'mm' ? 'မနေ့က' : 'Yesterday'}</SelectItem>
                  <SelectItem value="week">{language === 'mm' ? 'ဒီအပတ်' : 'This Week'}</SelectItem>
                  <SelectItem value="lastWeek">{language === 'mm' ? 'ပြီးခဲ့သော အပတ်' : 'Last Week'}</SelectItem>
                  <SelectItem value="month">{language === 'mm' ? 'ဒီလ' : 'This Month'}</SelectItem>
                  <SelectItem value="lastMonth">{language === 'mm' ? 'ပြီးခဲ့သော လ' : 'Last Month'}</SelectItem>
                  <SelectItem value="quarter">{language === 'mm' ? 'ဒီသုံးလ' : 'This Quarter'}</SelectItem>
                  <SelectItem value="year">{language === 'mm' ? 'ဒီနှစ်' : 'This Year'}</SelectItem>
                  <SelectItem value="custom">{language === 'mm' ? 'စိတ်ကြိုက်' : 'Custom Range'}</SelectItem>
                </SelectContent>
              </Select>

              {filters.dateRange === 'custom' && (
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label className="text-xs text-gray-600">
                      {language === 'mm' ? 'စတင် ရက်စွဲ' : 'Start Date'}
                    </Label>
                    <Input
                      type="date"
                      value={filters.customStartDate}
                      onChange={(e) => updateFilter('customStartDate', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600">
                      {language === 'mm' ? 'ပြီးဆုံး ရက်စွဲ' : 'End Date'}
                    </Label>
                    <Input
                      type="date"
                      value={filters.customEndDate}
                      onChange={(e) => updateFilter('customEndDate', e.target.value)}
                    />
                  </div>
                </div>
              )}
            </div>

            {/* Price Range */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {language === 'mm' ? 'စျေးနှုန်း အပိုင်းအခြား' : 'Price Range (MMK)'}
              </Label>
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <Label className="text-xs text-gray-600">
                    {language === 'mm' ? 'အနည်းဆုံး' : 'Minimum'}
                  </Label>
                  <Input
                    type="number"
                    value={filters.priceRange.min}
                    onChange={(e) => updateFilter('priceRange', { ...filters.priceRange, min: parseInt(e.target.value) || 0 })}
                    placeholder="0"
                  />
                </div>
                <div>
                  <Label className="text-xs text-gray-600">
                    {language === 'mm' ? 'အများဆုံး' : 'Maximum'}
                  </Label>
                  <Input
                    type="number"
                    value={filters.priceRange.max}
                    onChange={(e) => updateFilter('priceRange', { ...filters.priceRange, max: parseInt(e.target.value) || 10000000 })}
                    placeholder="10000000"
                  />
                </div>
              </div>
            </div>

            {/* Sort Options */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {language === 'mm' ? 'အစီအစဉ်' : 'Sort By'}
              </Label>
              <div className="grid grid-cols-2 gap-3">
                <Select value={filters.sortBy} onValueChange={(value) => updateFilter('sortBy', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="date">{language === 'mm' ? 'ရက်စွဲ' : 'Date'}</SelectItem>
                    <SelectItem value="amount">{language === 'mm' ? 'ပမာဏ' : 'Amount'}</SelectItem>
                    <SelectItem value="quantity">{language === 'mm' ? 'အရေအတွက်' : 'Quantity'}</SelectItem>
                    <SelectItem value="name">{language === 'mm' ? 'အမည်' : 'Name'}</SelectItem>
                    <SelectItem value="category">{language === 'mm' ? 'အမျိုးအစား' : 'Category'}</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={filters.sortOrder} onValueChange={(value: 'asc' | 'desc') => updateFilter('sortOrder', value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="desc">{language === 'mm' ? 'များမှ နည်း' : 'High to Low'}</SelectItem>
                    <SelectItem value="asc">{language === 'mm' ? 'နည်းမှ များ' : 'Low to High'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Group By */}
            <div className="space-y-3">
              <Label className="text-sm font-medium">
                {language === 'mm' ? 'အုပ်စုဖွဲ့ခြင်း' : 'Group By'}
              </Label>
              <Select value={filters.groupBy} onValueChange={(value) => updateFilter('groupBy', value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">{language === 'mm' ? 'မအုပ်စုဖွဲ့' : 'No Grouping'}</SelectItem>
                  <SelectItem value="category">{language === 'mm' ? 'အမျိုးအစား' : 'Category'}</SelectItem>
                  <SelectItem value="date">{language === 'mm' ? 'ရက်စွဲ' : 'Date'}</SelectItem>
                  <SelectItem value="customer">{language === 'mm' ? 'ဖောက်သည်' : 'Customer'}</SelectItem>
                  <SelectItem value="product">{language === 'mm' ? 'ကုန်ပစ္စည်း' : 'Product'}</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Report-specific filters */}
          {getReportSpecificFilters()}

          {/* Active Filters Display */}
          {activeFilters.length > 0 && (
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
              <h4 className="font-medium text-sm mb-2">
                {language === 'mm' ? 'လက်ရှိ စစ်ထုတ်မှုများ' : 'Active Filters'}
              </h4>
              <div className="flex flex-wrap gap-2">
                {activeFilters.map((filter) => (
                  <Badge key={filter} variant="secondary" className="text-xs">
                    {filter}
                  </Badge>
                ))}
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div className="flex justify-between pt-4 border-t border-gray-200">
            <Button variant="outline" onClick={handleReset}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {language === 'mm' ? 'ပြန်လည်သတ်မှတ်' : 'Reset All'}
            </Button>
            
            <div className="flex gap-3">
              <Button variant="outline" onClick={onClose}>
                {language === 'mm' ? 'မလုပ်တော့' : 'Cancel'}
              </Button>
              <Button 
                onClick={handleApply}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
              >
                <Filter className="h-4 w-4 mr-2" />
                {language === 'mm' ? 'စစ်ထုတ်မှု အသုံးပြုရန်' : 'Apply Filters'}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
