const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
    getCategories,
    getCategory,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoryTree
} = require('../controllers/categoryController');

const router = express.Router();

// Special routes (must be before /:id route)
router.get('/tree', protect, getCategoryTree);

// CRUD routes
router.route('/')
    .get(protect, getCategories)
    .post(protect, authorize('admin', 'manager'), createCategory);

router.route('/:id')
    .get(protect, getCategory)
    .put(protect, authorize('admin', 'manager'), updateCategory)
    .delete(protect, authorize('admin', 'manager'), deleteCategory);

module.exports = router;
