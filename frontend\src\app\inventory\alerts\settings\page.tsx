'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import {
  Settings,
  ArrowLeft,
  Save,
  Mail,
  MessageSquare,
  Bell,
  Volume2,
  Clock,
  AlertTriangle,
  Package,
  Calendar,
  Smartphone,
  CheckCircle,
  X
} from 'lucide-react'

interface NotificationSettings {
  emailEnabled: boolean
  smsEnabled: boolean
  pushEnabled: boolean
  soundEnabled: boolean
  lowStockThreshold: number
  expiryWarningDays: number
  quietHoursStart: string
  quietHoursEnd: string
  emailAddress: string
  phoneNumber: string
  alertTypes: {
    lowStock: boolean
    outOfStock: boolean
    expiryWarning: boolean
    reorderPoint: boolean
    overstock: boolean
  }
  severityLevels: {
    critical: boolean
    high: boolean
    medium: boolean
    low: boolean
  }
}

export default function AlertSettingsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [testingEmail, setTestingEmail] = useState(false)
  const [testingSMS, setTestingSMS] = useState(false)

  const [settings, setSettings] = useState<NotificationSettings>({
    emailEnabled: true,
    smsEnabled: false,
    pushEnabled: true,
    soundEnabled: true,
    lowStockThreshold: 10,
    expiryWarningDays: 30,
    quietHoursStart: '22:00',
    quietHoursEnd: '08:00',
    emailAddress: user?.email || '',
    phoneNumber: '',
    alertTypes: {
      lowStock: true,
      outOfStock: true,
      expiryWarning: true,
      reorderPoint: true,
      overstock: false
    },
    severityLevels: {
      critical: true,
      high: true,
      medium: true,
      low: false
    }
  })

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchSettings()
    }
  }, [isAuthenticated])

  const fetchSettings = async () => {
    try {
      setLoading(true)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))

      // Mock settings would be fetched here
      setSettings(prev => ({
        ...prev,
        emailAddress: user?.email || ''
      }))
    } catch (error) {
      console.error('Error fetching settings:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleSave = async () => {
    try {
      setSaving(true)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      alert(language === 'mm' ? 'ဆက်တင်များ သိမ်းဆည်းပြီးပါပြီ!' : 'Settings saved successfully!')
    } catch (error) {
      console.error('Error saving settings:', error)
      alert(language === 'mm' ? 'ဆက်တင်များ သိမ်းဆည်းရာတွင် အမှားရှိပါသည်!' : 'Error saving settings!')
    } finally {
      setSaving(false)
    }
  }

  const testEmailNotification = async () => {
    try {
      setTestingEmail(true)
      // Simulate sending test email
      await new Promise(resolve => setTimeout(resolve, 2000))

      alert(language === 'mm' ? 'စမ်းသပ် အီးမေးလ် ပို့ပြီးပါပြီ!' : 'Test email sent successfully!')
    } catch (error) {
      console.error('Error sending test email:', error)
      alert(language === 'mm' ? 'စမ်းသပ် အီးမေးလ် ပို့ရာတွင် အမှားရှိပါသည်!' : 'Error sending test email!')
    } finally {
      setTestingEmail(false)
    }
  }

  const testSMSNotification = async () => {
    try {
      setTestingSMS(true)
      // Simulate sending test SMS
      await new Promise(resolve => setTimeout(resolve, 2000))

      alert(language === 'mm' ? 'စမ်းသပ် SMS ပို့ပြီးပါပြီ!' : 'Test SMS sent successfully!')
    } catch (error) {
      console.error('Error sending test SMS:', error)
      alert(language === 'mm' ? 'စမ်းသပ် SMS ပို့ရာတွင် အမှားရှိပါသည်!' : 'Error sending test SMS!')
    } finally {
      setTestingSMS(false)
    }
  }

  const updateSetting = (key: keyof NotificationSettings, value: any) => {
    setSettings(prev => ({
      ...prev,
      [key]: value
    }))
  }

  const updateAlertType = (type: keyof NotificationSettings['alertTypes'], enabled: boolean) => {
    setSettings(prev => ({
      ...prev,
      alertTypes: {
        ...prev.alertTypes,
        [type]: enabled
      }
    }))
  }

  const updateSeverityLevel = (level: keyof NotificationSettings['severityLevels'], enabled: boolean) => {
    setSettings(prev => ({
      ...prev,
      severityLevels: {
        ...prev.severityLevels,
        [level]: enabled
      }
    }))
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/inventory/alerts')}
            className="hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Alerts'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 to-purple-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Settings className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {language === 'mm' ? 'သတိပေးချက် ဆက်တင်များ' : 'Alert Settings'}
                </h1>
                <p className="text-white/80 mt-1">
                  {language === 'mm'
                    ? 'သတိပေးချက်များ နှင့် အကြောင်းကြားမှု ဆက်တင်များ စီမံခန့်ခွဲရန်'
                    : 'Manage notification preferences and alert configurations'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Notification Channels */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အကြောင်းကြားမှု လမ်းကြောင်းများ' : 'Notification Channels'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'သတိပေးချက်များ လက်ခံမည့် လမ်းကြောင်းများ ရွေးချယ်ပါ'
                  : 'Choose how you want to receive alerts'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Email Notifications */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Mail className="h-5 w-5 text-blue-600" />
                    <Label className="font-medium">
                      {language === 'mm' ? 'အီးမေးလ် သတိပေးချက်များ' : 'Email Notifications'}
                    </Label>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.emailEnabled}
                    onChange={(e) => updateSetting('emailEnabled', e.target.checked)}
                    className="rounded"
                    aria-label={language === 'mm' ? 'အီးမေးလ် သတိပေးချက်များ' : 'Email Notifications'}
                  />
                </div>

                {settings.emailEnabled && (
                  <div className="space-y-3 ml-7">
                    <div>
                      <Label className="text-sm">
                        {language === 'mm' ? 'အီးမေးလ် လိပ်စာ' : 'Email Address'}
                      </Label>
                      <Input
                        type="email"
                        value={settings.emailAddress}
                        onChange={(e) => updateSetting('emailAddress', e.target.value)}
                        placeholder="<EMAIL>"
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={testEmailNotification}
                      disabled={testingEmail}
                    >
                      {testingEmail ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-3 w-3 border-2 border-blue-600 border-t-transparent"></div>
                          {language === 'mm' ? 'ပို့နေသည်...' : 'Sending...'}
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Mail className="h-3 w-3" />
                          {language === 'mm' ? 'စမ်းသပ် အီးမေးလ်' : 'Test Email'}
                        </div>
                      )}
                    </Button>
                  </div>
                )}
              </div>

              {/* SMS Notifications */}
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <MessageSquare className="h-5 w-5 text-green-600" />
                    <Label className="font-medium">
                      {language === 'mm' ? 'SMS သတိပေးချက်များ' : 'SMS Notifications'}
                    </Label>
                  </div>
                  <input
                    type="checkbox"
                    checked={settings.smsEnabled}
                    onChange={(e) => updateSetting('smsEnabled', e.target.checked)}
                    className="rounded"
                    aria-label={language === 'mm' ? 'SMS သတိပေးချက်များ' : 'SMS Notifications'}
                  />
                </div>

                {settings.smsEnabled && (
                  <div className="space-y-3 ml-7">
                    <div>
                      <Label className="text-sm">
                        {language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone Number'}
                      </Label>
                      <Input
                        type="tel"
                        value={settings.phoneNumber}
                        onChange={(e) => updateSetting('phoneNumber', e.target.value)}
                        placeholder="+95-9-123456789"
                      />
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={testSMSNotification}
                      disabled={testingSMS}
                    >
                      {testingSMS ? (
                        <div className="flex items-center gap-2">
                          <div className="animate-spin rounded-full h-3 w-3 border-2 border-green-600 border-t-transparent"></div>
                          {language === 'mm' ? 'ပို့နေသည်...' : 'Sending...'}
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <MessageSquare className="h-3 w-3" />
                          {language === 'mm' ? 'စမ်းသပ် SMS' : 'Test SMS'}
                        </div>
                      )}
                    </Button>
                  </div>
                )}
              </div>

              {/* Push Notifications */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Smartphone className="h-5 w-5 text-purple-600" />
                  <Label className="font-medium">
                    {language === 'mm' ? 'Push သတိပေးချက်များ' : 'Push Notifications'}
                  </Label>
                </div>
                <input
                  type="checkbox"
                  checked={settings.pushEnabled}
                  onChange={(e) => updateSetting('pushEnabled', e.target.checked)}
                  className="rounded"
                  aria-label={language === 'mm' ? 'Push သတိပေးချက်များ' : 'Push Notifications'}
                />
              </div>

              {/* Sound Notifications */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Volume2 className="h-5 w-5 text-orange-600" />
                  <Label className="font-medium">
                    {language === 'mm' ? 'အသံ သတိပေးချက်များ' : 'Sound Notifications'}
                  </Label>
                </div>
                <input
                  type="checkbox"
                  checked={settings.soundEnabled}
                  onChange={(e) => updateSetting('soundEnabled', e.target.checked)}
                  className="rounded"
                  aria-label={language === 'mm' ? 'အသံ သတိပေးချက်များ' : 'Sound Notifications'}
                />
              </div>
            </CardContent>
          </Card>

          {/* Alert Thresholds */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-yellow-600" />
                {language === 'mm' ? 'သတိပေးချက် အတိုင်းအတာများ' : 'Alert Thresholds'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'သတိပေးချက်များ ပေါ်လာမည့် အတိုင်းအတာများ သတ်မှတ်ပါ'
                  : 'Configure when alerts should be triggered'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div className="space-y-3">
                <Label className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-blue-600" />
                  {language === 'mm' ? 'နည်းသော စတော့ အတိုင်းအတာ' : 'Low Stock Threshold'}
                </Label>
                <Input
                  type="number"
                  value={settings.lowStockThreshold}
                  onChange={(e) => updateSetting('lowStockThreshold', parseInt(e.target.value) || 0)}
                  placeholder="10"
                />
                <p className="text-xs text-gray-600">
                  {language === 'mm'
                    ? 'ဤ အရေအတွက် အောက်သို့ ရောက်လျှင် သတိပေးချက် ပေါ်လာမည်'
                    : 'Alert when stock falls below this number'
                  }
                </p>
              </div>

              <div className="space-y-3">
                <Label className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-red-600" />
                  {language === 'mm' ? 'သက်တမ်း သတိပေးချက် (ရက်)' : 'Expiry Warning (Days)'}
                </Label>
                <Input
                  type="number"
                  value={settings.expiryWarningDays}
                  onChange={(e) => updateSetting('expiryWarningDays', parseInt(e.target.value) || 0)}
                  placeholder="30"
                />
                <p className="text-xs text-gray-600">
                  {language === 'mm'
                    ? 'သက်တမ်း မကုန်ခင် ဤ ရက်အရေအတွက် အလိုတွင် သတိပေးမည်'
                    : 'Alert this many days before expiry'
                  }
                </p>
              </div>

              <div className="space-y-3">
                <Label className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-purple-600" />
                  {language === 'mm' ? 'ဆိတ်ငြိမ်သော အချိန်များ' : 'Quiet Hours'}
                </Label>
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <Label className="text-xs text-gray-600">
                      {language === 'mm' ? 'စတင်' : 'Start'}
                    </Label>
                    <Input
                      type="time"
                      value={settings.quietHoursStart}
                      onChange={(e) => updateSetting('quietHoursStart', e.target.value)}
                    />
                  </div>
                  <div>
                    <Label className="text-xs text-gray-600">
                      {language === 'mm' ? 'ပြီးဆုံး' : 'End'}
                    </Label>
                    <Input
                      type="time"
                      value={settings.quietHoursEnd}
                      onChange={(e) => updateSetting('quietHoursEnd', e.target.value)}
                    />
                  </div>
                </div>
                <p className="text-xs text-gray-600">
                  {language === 'mm'
                    ? 'ဤ အချိန်များအတွင်း သတိပေးချက်များ မပေါ်လာမည်'
                    : 'No notifications will be sent during these hours'
                  }
                </p>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Alert Types */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-green-600" />
              {language === 'mm' ? 'သတိပေးချက် အမျိုးအစားများ' : 'Alert Types'}
            </CardTitle>
            <CardDescription>
              {language === 'mm'
                ? 'လက်ခံလိုသော သတိပေးချက် အမျိုးအစားများ ရွေးချယ်ပါ'
                : 'Choose which types of alerts you want to receive'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(settings.alertTypes).map(([type, enabled]) => (
                <div key={type} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium">
                      {type === 'lowStock' && (language === 'mm' ? 'နည်းသော စတော့' : 'Low Stock')}
                      {type === 'outOfStock' && (language === 'mm' ? 'စတော့ ကုန်' : 'Out of Stock')}
                      {type === 'expiryWarning' && (language === 'mm' ? 'သက်တမ်း ကုန်ခါနီး' : 'Expiry Warning')}
                      {type === 'reorderPoint' && (language === 'mm' ? 'ပြန်မှာရန် အချိန်' : 'Reorder Point')}
                      {type === 'overstock' && (language === 'mm' ? 'စတော့ များ' : 'Overstock')}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {type === 'lowStock' && (language === 'mm' ? 'စတော့ နည်းလာသောအခါ' : 'When stock is running low')}
                      {type === 'outOfStock' && (language === 'mm' ? 'စတော့ လုံးဝ ကုန်သောအခါ' : 'When completely out of stock')}
                      {type === 'expiryWarning' && (language === 'mm' ? 'သက်တမ်း ကုန်ခါနီးသောအခါ' : 'When products are near expiry')}
                      {type === 'reorderPoint' && (language === 'mm' ? 'ပြန်မှာရန် အချိန် ရောက်သောအခါ' : 'When reorder point is reached')}
                      {type === 'overstock' && (language === 'mm' ? 'စတော့ များလွန်းသောအခါ' : 'When stock exceeds maximum')}
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={enabled}
                    onChange={(e) => updateAlertType(type as keyof NotificationSettings['alertTypes'], e.target.checked)}
                    className="rounded"
                    aria-label={`${type} alert type`}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Severity Levels */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-red-600" />
              {language === 'mm' ? 'အရေးကြီးမှု အဆင့်များ' : 'Severity Levels'}
            </CardTitle>
            <CardDescription>
              {language === 'mm'
                ? 'လက်ခံလိုသော အရေးကြီးမှု အဆင့်များ ရွေးချယ်ပါ'
                : 'Choose which severity levels to receive notifications for'
              }
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {Object.entries(settings.severityLevels).map(([level, enabled]) => (
                <div key={level} className="flex items-center justify-between p-3 border border-gray-200 rounded-lg">
                  <div>
                    <h4 className="font-medium">
                      {level === 'critical' && (language === 'mm' ? 'အရေးကြီးဆုံး' : 'Critical')}
                      {level === 'high' && (language === 'mm' ? 'မြင့်' : 'High')}
                      {level === 'medium' && (language === 'mm' ? 'အလယ်အလတ်' : 'Medium')}
                      {level === 'low' && (language === 'mm' ? 'နိမ့်' : 'Low')}
                    </h4>
                    <p className="text-sm text-gray-600">
                      {level === 'critical' && (language === 'mm' ? 'ချက်ချင်း ဆောင်ရွက်ရမည့် ကိစ္စများ' : 'Requires immediate action')}
                      {level === 'high' && (language === 'mm' ? 'မြန်မြန် ဆောင်ရွက်ရမည့် ကိစ္စများ' : 'Requires prompt action')}
                      {level === 'medium' && (language === 'mm' ? 'သတိပြု ဆောင်ရွက်ရမည့် ကိစ္စများ' : 'Requires attention')}
                      {level === 'low' && (language === 'mm' ? 'အချိန်ရသောအခါ ဆောင်ရွက်ရမည့် ကိစ္စများ' : 'For information only')}
                    </p>
                  </div>
                  <input
                    type="checkbox"
                    checked={enabled}
                    onChange={(e) => updateSeverityLevel(level as keyof NotificationSettings['severityLevels'], e.target.checked)}
                    className="rounded"
                    aria-label={`${level} severity level`}
                  />
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'ဆက်တင်များ သိမ်းရန်' : 'Save Settings'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
