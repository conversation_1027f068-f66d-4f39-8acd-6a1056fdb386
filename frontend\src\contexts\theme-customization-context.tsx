'use client'

import React, { createContext, useContext, useState, useEffect } from 'react'

// Theme customization types
export interface ThemeColors {
  primary: string
  secondary: string
  accent: string
  background: string
  foreground: string
  muted: string
  mutedForeground: string
  card: string
  cardForeground: string
  border: string
  input: string
  ring: string
  destructive: string
  destructiveBackground: string
  warning: string
  warningBackground: string
  success: string
  successBackground: string
}

export interface FontSettings {
  family: string
  size: 'small' | 'medium' | 'large' | 'extra-large'
  weight: 'light' | 'normal' | 'medium' | 'semibold' | 'bold'
  lineHeight: 'tight' | 'normal' | 'relaxed' | 'loose'
}

export interface ThemeCustomization {
  colors: ThemeColors
  fonts: FontSettings
  borderRadius: 'none' | 'small' | 'medium' | 'large' | 'full'
  spacing: 'compact' | 'normal' | 'comfortable' | 'spacious'
  animations: boolean
  shadows: boolean
  gradients: boolean
}

// Predefined color schemes
export const colorSchemes = {
  default: {
    primary: '#3B82F6',
    secondary: '#64748B',
    accent: '#F1F5F9',
    background: '#FFFFFF',
    foreground: '#0F172A',
    muted: '#F8FAFC',
    mutedForeground: '#64748B',
    card: '#FFFFFF',
    cardForeground: '#0F172A',
    border: '#E2E8F0',
    input: '#FFFFFF',
    ring: '#3B82F6',
    destructive: '#EF4444',
    destructiveBackground: '#FEF2F2',
    warning: '#F59E0B',
    warningBackground: '#FFFBEB',
    success: '#10B981',
    successBackground: '#F0FDF4'
  },
  myanmar: {
    primary: '#059669', // Emerald
    secondary: '#0D9488', // Teal
    accent: '#ECFDF5',
    background: '#FFFFFF',
    foreground: '#064E3B',
    muted: '#F0FDF4',
    mutedForeground: '#065F46',
    card: '#FFFFFF',
    cardForeground: '#064E3B',
    border: '#A7F3D0',
    input: '#FFFFFF',
    ring: '#059669',
    destructive: '#DC2626',
    destructiveBackground: '#FEF2F2',
    warning: '#D97706',
    warningBackground: '#FFFBEB',
    success: '#059669',
    successBackground: '#ECFDF5'
  },
  business: {
    primary: '#1E40AF', // Blue
    secondary: '#374151', // Gray
    accent: '#F3F4F6',
    background: '#FFFFFF',
    foreground: '#111827',
    muted: '#F9FAFB',
    mutedForeground: '#6B7280',
    card: '#FFFFFF',
    cardForeground: '#111827',
    border: '#D1D5DB',
    input: '#FFFFFF',
    ring: '#1E40AF',
    destructive: '#DC2626',
    destructiveBackground: '#FEF2F2',
    warning: '#D97706',
    warningBackground: '#FFFBEB',
    success: '#059669',
    successBackground: '#ECFDF5'
  },
  modern: {
    primary: '#8B5CF6', // Purple
    secondary: '#6366F1', // Indigo
    accent: '#F5F3FF',
    background: '#FFFFFF',
    foreground: '#1F2937',
    muted: '#FAFAFA',
    mutedForeground: '#6B7280',
    card: '#FFFFFF',
    cardForeground: '#1F2937',
    border: '#E5E7EB',
    input: '#FFFFFF',
    ring: '#8B5CF6',
    destructive: '#EF4444',
    destructiveBackground: '#FEF2F2',
    warning: '#F59E0B',
    warningBackground: '#FFFBEB',
    success: '#10B981',
    successBackground: '#F0FDF4'
  },
  dark: {
    primary: '#3B82F6',
    secondary: '#64748B',
    accent: '#1E293B',
    background: '#0F172A',
    foreground: '#F8FAFC',
    muted: '#1E293B',
    mutedForeground: '#94A3B8',
    card: '#1E293B',
    cardForeground: '#F8FAFC',
    border: '#334155',
    input: '#1E293B',
    ring: '#3B82F6',
    destructive: '#EF4444',
    destructiveBackground: '#7F1D1D',
    warning: '#F59E0B',
    warningBackground: '#92400E',
    success: '#10B981',
    successBackground: '#065F46'
  }
}

// Font families
export const fontFamilies = {
  system: 'system-ui, -apple-system, sans-serif',
  inter: 'Inter, system-ui, sans-serif',
  roboto: 'Roboto, system-ui, sans-serif',
  myanmar: 'Noto Sans Myanmar, Myanmar Text, sans-serif',
  mixed: 'Inter, Noto Sans Myanmar, system-ui, sans-serif'
}

interface ThemeCustomizationContextType {
  customization: ThemeCustomization
  updateColors: (colors: Partial<ThemeColors>) => void
  updateFonts: (fonts: Partial<FontSettings>) => void
  updateBorderRadius: (radius: ThemeCustomization['borderRadius']) => void
  updateSpacing: (spacing: ThemeCustomization['spacing']) => void
  updateAnimations: (enabled: boolean) => void
  updateShadows: (enabled: boolean) => void
  updateGradients: (enabled: boolean) => void
  applyColorScheme: (scheme: keyof typeof colorSchemes) => void
  resetToDefault: () => void
  exportTheme: () => string
  importTheme: (themeData: string) => boolean
}

const ThemeCustomizationContext = createContext<ThemeCustomizationContextType | undefined>(undefined)

const defaultCustomization: ThemeCustomization = {
  colors: colorSchemes.default,
  fonts: {
    family: 'mixed',
    size: 'medium',
    weight: 'normal',
    lineHeight: 'normal'
  },
  borderRadius: 'medium',
  spacing: 'normal',
  animations: true,
  shadows: true,
  gradients: true
}

export function ThemeCustomizationProvider({ children }: { children: React.ReactNode }) {
  const [customization, setCustomization] = useState<ThemeCustomization>(defaultCustomization)

  // Load saved customization on mount
  useEffect(() => {
    const saved = localStorage.getItem('theme-customization')
    if (saved) {
      try {
        const parsed = JSON.parse(saved)
        setCustomization({ ...defaultCustomization, ...parsed })
      } catch (error) {
        console.error('Failed to load theme customization:', error)
      }
    }
  }, [])

  // Save customization when it changes
  useEffect(() => {
    localStorage.setItem('theme-customization', JSON.stringify(customization))
    applyThemeToDOM(customization)
  }, [customization])

  const updateColors = (colors: Partial<ThemeColors>) => {
    setCustomization(prev => ({
      ...prev,
      colors: { ...prev.colors, ...colors }
    }))
  }

  const updateFonts = (fonts: Partial<FontSettings>) => {
    setCustomization(prev => ({
      ...prev,
      fonts: { ...prev.fonts, ...fonts }
    }))
  }

  const updateBorderRadius = (borderRadius: ThemeCustomization['borderRadius']) => {
    setCustomization(prev => ({ ...prev, borderRadius }))
  }

  const updateSpacing = (spacing: ThemeCustomization['spacing']) => {
    setCustomization(prev => ({ ...prev, spacing }))
  }

  const updateAnimations = (animations: boolean) => {
    setCustomization(prev => ({ ...prev, animations }))
  }

  const updateShadows = (shadows: boolean) => {
    setCustomization(prev => ({ ...prev, shadows }))
  }

  const updateGradients = (gradients: boolean) => {
    setCustomization(prev => ({ ...prev, gradients }))
  }

  const applyColorScheme = (scheme: keyof typeof colorSchemes) => {
    setCustomization(prev => ({
      ...prev,
      colors: colorSchemes[scheme]
    }))
  }

  const resetToDefault = () => {
    setCustomization(defaultCustomization)
  }

  const exportTheme = () => {
    return JSON.stringify(customization, null, 2)
  }

  const importTheme = (themeData: string) => {
    try {
      const parsed = JSON.parse(themeData)
      setCustomization({ ...defaultCustomization, ...parsed })
      return true
    } catch (error) {
      console.error('Failed to import theme:', error)
      return false
    }
  }

  return (
    <ThemeCustomizationContext.Provider value={{
      customization,
      updateColors,
      updateFonts,
      updateBorderRadius,
      updateSpacing,
      updateAnimations,
      updateShadows,
      updateGradients,
      applyColorScheme,
      resetToDefault,
      exportTheme,
      importTheme
    }}>
      {children}
    </ThemeCustomizationContext.Provider>
  )
}

export function useThemeCustomization() {
  const context = useContext(ThemeCustomizationContext)
  if (context === undefined) {
    throw new Error('useThemeCustomization must be used within a ThemeCustomizationProvider')
  }
  return context
}

// Apply theme to DOM
function applyThemeToDOM(customization: ThemeCustomization) {
  const root = document.documentElement

  // Apply colors as CSS variables
  Object.entries(customization.colors).forEach(([key, value]) => {
    const cssVar = `--${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`
    root.style.setProperty(cssVar, value)
  })

  // Apply font settings
  const fontFamily = fontFamilies[customization.fonts.family as keyof typeof fontFamilies]
  root.style.setProperty('--font-family', fontFamily)

  // Apply font size
  const fontSizes = {
    small: '14px',
    medium: '16px',
    large: '18px',
    'extra-large': '20px'
  }
  root.style.setProperty('--font-size-base', fontSizes[customization.fonts.size])

  // Apply font weight
  const fontWeights = {
    light: '300',
    normal: '400',
    medium: '500',
    semibold: '600',
    bold: '700'
  }
  root.style.setProperty('--font-weight-base', fontWeights[customization.fonts.weight])

  // Apply line height
  const lineHeights = {
    tight: '1.25',
    normal: '1.5',
    relaxed: '1.75',
    loose: '2'
  }
  root.style.setProperty('--line-height-base', lineHeights[customization.fonts.lineHeight])

  // Apply border radius
  const borderRadii = {
    none: '0px',
    small: '4px',
    medium: '8px',
    large: '12px',
    full: '9999px'
  }
  root.style.setProperty('--border-radius', borderRadii[customization.borderRadius])

  // Apply spacing
  const spacingMultipliers = {
    compact: '0.75',
    normal: '1',
    comfortable: '1.25',
    spacious: '1.5'
  }
  root.style.setProperty('--spacing-multiplier', spacingMultipliers[customization.spacing])

  // Apply feature toggles
  root.style.setProperty('--animations-enabled', customization.animations ? '1' : '0')
  root.style.setProperty('--shadows-enabled', customization.shadows ? '1' : '0')
  root.style.setProperty('--gradients-enabled', customization.gradients ? '1' : '0')
}
