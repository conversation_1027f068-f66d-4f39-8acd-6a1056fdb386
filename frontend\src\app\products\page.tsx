'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useTheme } from '@/contexts/theme-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  Package,
  Plus,
  Search,
  Edit,
  Eye,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Trash2,
  ImageIcon
} from 'lucide-react'

interface Product {
  id: string
  name: string
  description?: string
  sku: string
  barcode?: string
  category: {
    id: string
    name: string
    color: string
    icon: string
  }
  price: number
  cost: number
  currency: string
  taxRate: number
  inventory: {
    quantity: number
    minQuantity: number
    maxQuantity: number
    unit: string
  }
  images?: {
    url: string
    alt: string
    isPrimary: boolean
  }[]
  isActive: boolean
  isFeatured: boolean
  tags: string[]
  profitMargin: string
  stockStatus: 'in_stock' | 'low_stock' | 'out_of_stock'
  createdAt: string
  updatedAt: string
}

export default function ProductsPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const { language } = useTheme()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [products, setProducts] = useState<Product[]>([])
  const [loading, setLoading] = useState(true)
  const [refreshing, setRefreshing] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('')
  const [categories, setCategories] = useState<any[]>([])
  const [filteredProducts, setFilteredProducts] = useState<Product[]>([])
  const [allProducts, setAllProducts] = useState<Product[]>([])
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null)
  const [showDetailsModal, setShowDetailsModal] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchProducts()
      fetchCategories()
    }
  }, [isAuthenticated])

  const fetchProducts = async (showRefreshing = false) => {
    try {
      if (showRefreshing) setRefreshing(true)
      else setLoading(true)

      const response = await apiClient.getProducts()
      const productsData = response.success ? (Array.isArray(response.data) ? response.data : []) : []
      setAllProducts(productsData)
      setProducts(productsData)
      console.log('📦 Products loaded:', productsData.length)
    } catch (error) {
      console.error('Error fetching products:', error)
      setAllProducts([])
      setProducts([])
    } finally {
      setLoading(false)
      setRefreshing(false)
    }
  }

  // Debounced search function
  const debouncedSearch = useCallback(
    (() => {
      let timeoutId: NodeJS.Timeout
      return (query: string, categoryId: string) => {
        clearTimeout(timeoutId)
        timeoutId = setTimeout(() => {
          filterProducts(query, categoryId)
        }, 300) // 300ms delay
      }
    })(),
    [allProducts]
  )

  // Filter products based on search query and category
  const filterProducts = useCallback((query: string, categoryId: string) => {
    let filtered = [...allProducts]

    // Filter by search query
    if (query.trim()) {
      const searchTerm = query.toLowerCase()
      filtered = filtered.filter(product =>
        product.name.toLowerCase().includes(searchTerm) ||
        product.sku.toLowerCase().includes(searchTerm) ||
        product.description?.toLowerCase().includes(searchTerm) ||
        product.category.name.toLowerCase().includes(searchTerm) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
      )
    }

    // Filter by category
    if (categoryId) {
      filtered = filtered.filter(product =>
        (product.category as any)._id === categoryId ||
        (product.category as any).id === categoryId
      )
    }

    setProducts(filtered)
    console.log(`🔍 Filtered products: ${filtered.length} of ${allProducts.length}`)
  }, [allProducts])

  // Real-time WebSocket connection for inventory updates
  useEffect(() => {
    // Handle inventory updates
    const handleInventoryUpdate = (event: CustomEvent) => {
      const data = event.detail
      console.log('📡 Inventory update received:', data)

      setProducts(prev => prev.map(product =>
        (product as any)._id === data.productId
          ? { ...product, inventory: { ...product.inventory, quantity: data.newQuantity } }
          : product
      ))
      console.log(`📦 Product ${data.productId} inventory updated to ${data.newQuantity}`)
    }

    // Handle product sold updates
    const handleProductSold = (event: CustomEvent) => {
      const data = event.detail
      console.log('🛒 Product sold update received:', data)

      setProducts(prev => prev.map(product =>
        (product as any)._id === data.productId
          ? {
              ...product,
              inventory: {
                ...product.inventory,
                quantity: Math.max(0, product.inventory.quantity - data.quantitySold)
              }
            }
          : product
      ))
      console.log(`🛒 Product ${data.productId} sold: ${data.quantitySold} units`)
    }

    // Handle low stock alerts
    const handleLowStockAlert = (event: CustomEvent) => {
      const data = event.detail
      console.warn(`⚠️ Low stock alert for product: ${data.productName}`)
      // You can add toast notification here
    }

    // Handle currency changes
    const handleCurrencyChange = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      console.log('🔄 Products currency changed to:', newCurrency)
      // Products will automatically re-render with new currency formatting
    }

    // Add event listeners
    window.addEventListener('ws-inventory-update', handleInventoryUpdate as EventListener)
    window.addEventListener('ws-product-sold', handleProductSold as EventListener)
    window.addEventListener('ws-low-stock-alert', handleLowStockAlert as EventListener)
    window.addEventListener('global-currency-sync', handleCurrencyChange as EventListener)

    // Initialize WebSocket connection
    const initWebSocket = async () => {
      try {
        const { default: wsClient, subscribeToInventoryUpdates } = await import('@/lib/websocket')
        subscribeToInventoryUpdates()
        console.log('✅ Products WebSocket subscribed')
      } catch (error) {
        console.warn('WebSocket not available:', error)
      }
    }

    initWebSocket()

    // Cleanup function
    return () => {
      window.removeEventListener('ws-inventory-update', handleInventoryUpdate as EventListener)
      window.removeEventListener('ws-product-sold', handleProductSold as EventListener)
      window.removeEventListener('ws-low-stock-alert', handleLowStockAlert as EventListener)
      window.removeEventListener('global-currency-sync', handleCurrencyChange as EventListener)
    }

    // Fallback polling every 60 seconds
    const interval = setInterval(() => {
      fetchProducts(true)
    }, 60000)

    return () => clearInterval(interval)
  }, [])

  // Real-time settings synchronization
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'bitstech_products' && event.newValue) {
        try {
          const newProducts = JSON.parse(event.newValue)
          setAllProducts(newProducts)
          setProducts(newProducts)
          console.log('🔄 Products synced from storage')
        } catch (error) {
          console.error('Error syncing products:', error)
        }
      }

      if (event.key === 'bitstech_categories' && event.newValue) {
        try {
          const newCategories = JSON.parse(event.newValue)
          setCategories(newCategories)
          console.log('🔄 Categories synced from storage')
        } catch (error) {
          console.error('Error syncing categories:', error)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => window.removeEventListener('storage', handleStorageChange)
  }, [])

  const fetchCategories = async () => {
    try {
      const response = await apiClient.getCategories()
      const categoriesData = response.success ? (Array.isArray(response.data) ? response.data : []) : []
      setCategories(categoriesData)
    } catch (error) {
      console.error('Error fetching categories:', error)
      setCategories([])
    }
  }

  const handleSearch = () => {
    filterProducts(searchQuery, selectedCategory)
  }

  const handleCategoryFilter = (categoryId: string) => {
    setSelectedCategory(categoryId)
    debouncedSearch(searchQuery, categoryId)
  }

  const handleSearchChange = (value: string) => {
    setSearchQuery(value)
    debouncedSearch(value, selectedCategory)
  }

  const handleDeleteCategory = async (categoryId: string, categoryName: string) => {
    if (!confirm(language === 'mm'
      ? `"${categoryName}" အမျိုးအစားကို ဖျက်လိုသည်မှာ သေချာပါသလား?`
      : `Are you sure you want to delete category "${categoryName}"?`
    )) {
      return
    }

    try {
      const response = await apiClient.deleteCategory(categoryId)
      if (response.success) {
        // Remove category from local state
        setCategories(prev => prev.filter(cat => (cat as any)._id !== categoryId))

        // Clear category filter if it was selected
        if (selectedCategory === categoryId) {
          setSelectedCategory('')
          debouncedSearch(searchQuery, '')
        }

        // Trigger storage event for real-time sync
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'bitstech_categories',
          newValue: localStorage.getItem('bitstech_categories'),
          storageArea: localStorage
        }))

        alert(language === 'mm'
          ? 'အမျိုးအစား အောင်မြင်စွာ ဖျက်ပြီးပါပြီ'
          : 'Category deleted successfully'
        )
      } else {
        throw new Error(response.message || 'Failed to delete category')
      }
    } catch (error: any) {
      console.error('Error deleting category:', error)
      alert(language === 'mm'
        ? 'အမျိုးအစား ဖျက်မှု မအောင်မြင်ပါ'
        : 'Failed to delete category'
      )
    }
  }

  const getStockStatusBadge = (status: string, quantity: number) => {
    switch (status) {
      case 'out_of_stock':
        return <Badge variant="destructive" className="flex items-center gap-1">
          <XCircle className="h-3 w-3" />
          {language === 'mm' ? 'စတော့ ကုန်သွားပြီ' : 'Out of Stock'}
        </Badge>
      case 'low_stock':
        return <Badge variant="secondary" className="flex items-center gap-1 bg-orange-100 text-orange-800">
          <AlertTriangle className="h-3 w-3" />
          {language === 'mm' ? `စတော့ နည်းနေသည် (${quantity})` : `Low Stock (${quantity})`}
        </Badge>
      default:
        return <Badge variant="default" className="flex items-center gap-1 bg-green-100 text-green-800">
          <CheckCircle className="h-3 w-3" />
          {language === 'mm' ? `စတော့ ရှိသည် (${quantity})` : `In Stock (${quantity})`}
        </Badge>
    }
  }

  const formatPrice = (price: number) => {
    // Use centralized currency formatting with real-time updates
    return formatCurrency(price)
  }

  const handleViewProduct = (product: Product) => {
    setSelectedProduct(product)
    setShowDetailsModal(true)
  }

  const handleCloseDetailsModal = () => {
    setSelectedProduct(null)
    setShowDetailsModal(false)
  }



  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold">
              {language === 'mm' ? 'ကုန်ပစ္စည်းများ' : 'Products'}
            </h1>
            <p className="text-muted-foreground mt-1">
              {language === 'mm'
                ? 'ကုန်ပစ္စည်းများကို စီမံခန့်ခွဲပါ'
                : 'Manage your product inventory'
              }
            </p>
          </div>
          <Button
            onClick={() => router.push('/products/new')}
            className="flex items-center gap-2"
          >
            <Plus className="h-4 w-4" />
            {language === 'mm' ? 'ကုန်ပစ္စည်း ထည့်ရန်' : 'Add Product'}
          </Button>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-start">
              {/* Search Section */}
              <div className="flex flex-col md:flex-row gap-4 flex-1">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder={language === 'mm' ? 'ကုန်ပစ္စည်း ရှာရန်...' : 'Search products...'}
                      value={searchQuery}
                      onChange={(e) => handleSearchChange(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                      className="pl-10"
                    />
                  </div>
                </div>
                <Button onClick={handleSearch} variant="outline">
                  <Search className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ရှာရန်' : 'Search'}
                </Button>
              </div>

              {/* Product Totals Section */}
              <div className="flex flex-col sm:flex-row gap-4 lg:gap-6 min-w-fit">
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 text-center border border-blue-200 dark:border-blue-700">
                  <div className="text-2xl font-bold text-blue-600 dark:text-blue-400">
                    {products.length}
                  </div>
                  <div className="text-xs text-blue-600 dark:text-blue-400 font-medium">
                    {language === 'mm' ? 'စုစုပေါင်း ကုန်ပစ္စည်း' : 'Total Products'}
                  </div>
                </div>

                <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center border border-green-200 dark:border-green-700">
                  <div className="text-2xl font-bold text-green-600 dark:text-green-400">
                    {products.filter(p => p.stockStatus === 'in_stock').length}
                  </div>
                  <div className="text-xs text-green-600 dark:text-green-400 font-medium">
                    {language === 'mm' ? 'ရှိသော ကုန်ပစ္စည်း' : 'In Stock'}
                  </div>
                </div>

                <div className="bg-orange-50 dark:bg-orange-900/20 rounded-lg p-4 text-center border border-orange-200 dark:border-orange-700">
                  <div className="text-2xl font-bold text-orange-600 dark:text-orange-400">
                    {products.filter(p => p.stockStatus === 'low_stock').length}
                  </div>
                  <div className="text-xs text-orange-600 dark:text-orange-400 font-medium">
                    {language === 'mm' ? 'နည်းသော စတော့' : 'Low Stock'}
                  </div>
                </div>

                <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center border border-red-200 dark:border-red-700">
                  <div className="text-2xl font-bold text-red-600 dark:text-red-400">
                    {products.filter(p => p.stockStatus === 'out_of_stock').length}
                  </div>
                  <div className="text-xs text-red-600 dark:text-red-400 font-medium">
                    {language === 'mm' ? 'ကုန်သော ကုန်ပစ္စည်း' : 'Out of Stock'}
                  </div>
                </div>
              </div>
            </div>

            {/* Category Filters */}
            <div className="mt-4 flex flex-wrap gap-2">
              <Button
                variant={selectedCategory === '' ? 'default' : 'outline'}
                size="sm"
                onClick={() => handleCategoryFilter('')}
              >
                {language === 'mm' ? 'အားလုံး' : 'All'}
              </Button>
              {categories.map((category) => (
                <div key={(category as any)._id || (category as any).id} className="flex items-center gap-1">
                  <Button
                    variant={selectedCategory === ((category as any)._id || (category as any).id) ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => handleCategoryFilter((category as any)._id || (category as any).id)}
                    className="flex items-center gap-1"
                  >
                    <span
                      className="w-2 h-2 rounded-full"
                      style={{ backgroundColor: category.color }}
                      aria-hidden="true"
                    ></span>
                    {category.name}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleDeleteCategory((category as any)._id || (category as any).id, category.name)}
                    className="h-8 w-8 p-0 text-red-600 hover:text-red-700 hover:bg-red-50"
                    title={language === 'mm' ? 'အမျိုးအစား ဖျက်ရန်' : 'Delete category'}
                  >
                    <Trash2 className="h-3 w-3" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Products Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
          {products.map((product) => {
            // Get primary image or first image
            const productImages = (product as any).images || []
            const primaryImage = productImages.find((img: any) => img.isPrimary) || productImages[0]
            const hasImage = primaryImage && primaryImage.url

            return (
              <Card key={(product as any)._id || product.id} className="hover:shadow-lg transition-shadow overflow-hidden">
                {/* Product Image */}
                <div className="w-full h-40 bg-gray-100 dark:bg-gray-800 relative overflow-hidden">
                  {hasImage && (
                    <img
                      src={primaryImage.url}
                      alt={primaryImage.alt || product.name}
                      className="w-full h-full object-cover hover:scale-105 transition-transform duration-300"
                      onError={(e) => {
                        // Fallback to placeholder if image fails to load
                        const target = e.target as HTMLImageElement
                        target.style.display = 'none'
                        const placeholder = target.parentElement?.querySelector('.image-placeholder')
                        if (placeholder) {
                          placeholder.classList.remove('hidden')
                        }
                      }}
                    />
                  )}

                  {/* Fallback placeholder */}
                  <div className={`image-placeholder absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 ${hasImage ? 'hidden' : ''}`}>
                    <ImageIcon className="h-8 w-8 text-gray-400" />
                  </div>

                  {/* Featured badge overlay */}
                  {product.isFeatured && (
                    <div className="absolute top-2 right-2">
                      <Badge variant="secondary" className="bg-blue-600 text-white">
                        {language === 'mm' ? 'အထူး' : 'Featured'}
                      </Badge>
                    </div>
                  )}

                  {/* Stock status overlay */}
                  <div className="absolute top-2 left-2">
                    {product.stockStatus === 'out_of_stock' && (
                      <Badge variant="destructive" className="text-xs">
                        {language === 'mm' ? 'ကုန်သွားပြီ' : 'Out of Stock'}
                      </Badge>
                    )}
                    {product.stockStatus === 'low_stock' && (
                      <Badge variant="secondary" className="text-xs bg-orange-100 text-orange-800">
                        {language === 'mm' ? 'နည်းနေသည်' : 'Low Stock'}
                      </Badge>
                    )}
                  </div>
                </div>

                <CardHeader className="pb-2 px-3">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <CardTitle className="text-sm font-semibold line-clamp-1">{product.name}</CardTitle>
                      <CardDescription className="text-xs line-clamp-1 mt-1">
                        {product.description || (language === 'mm' ? 'ဖော်ပြချက် မရှိပါ' : 'No description')}
                      </CardDescription>
                    </div>
                  </div>
                </CardHeader>
              <CardContent className="space-y-2 px-3 pb-3">
                <div className="flex items-center justify-between mb-2">
                  <span className="text-xs text-gray-500 truncate">
                    {product.sku}
                  </span>
                  <Badge
                    variant="outline"
                    className="text-xs px-1 py-0"
                    style={{
                      borderColor: product.category.color,
                      color: product.category.color
                    }}
                    title={`${language === 'mm' ? 'အမျိုးအစား' : 'Category'}: ${product.category.name}`}
                  >
                    <span
                      className="w-1.5 h-1.5 rounded-full mr-1"
                      style={{ backgroundColor: product.category.color }}
                      aria-hidden="true"
                    ></span>
                    {product.category.name}
                  </Badge>
                </div>

                <div className="space-y-1 mb-2">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">
                      {language === 'mm' ? 'စျေးနှုန်း' : 'Price'}:
                    </span>
                    <span className="text-sm font-semibold">
                      {formatPrice(product.price)}
                    </span>
                  </div>
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-gray-500">
                      {language === 'mm' ? 'အမြတ်' : 'Profit'}:
                    </span>
                    <span className="text-xs text-green-600 font-medium">
                      {product.profitMargin}%
                    </span>
                  </div>
                </div>

                <div className="mb-2">
                  {getStockStatusBadge(product.stockStatus, product.inventory.quantity)}
                </div>

                <div className="flex gap-1">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex-1 h-7 text-xs"
                    onClick={() => handleViewProduct(product)}
                    title={language === 'mm' ? 'ကုန်ပစ္စည်း အသေးစိတ် ကြည့်ရန်' : 'View Product Details'}
                  >
                    <Eye className="h-3 w-3 mr-1" />
                    {language === 'mm' ? 'ကြည့်' : 'Details'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="h-7 w-7 p-0"
                    onClick={() => router.push(`/products/${product.id}/edit`)}
                  >
                    <Edit className="h-3 w-3" />
                  </Button>
                </div>
              </CardContent>
            </Card>
            )
          })}
        </div>

        {products.length === 0 && !loading && (
          <Card>
            <CardContent className="text-center py-12">
              <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                {language === 'mm' ? 'ကုန်ပစ္စည်း မရှိပါ' : 'No products found'}
              </h3>
              <p className="text-gray-500 mb-4">
                {language === 'mm'
                  ? 'ကုန်ပစ္စည်းအသစ် ထည့်ပြီး စတင်ပါ'
                  : 'Get started by adding your first product'
                }
              </p>
              <Button onClick={() => router.push('/products/new')}>
                <Plus className="h-4 w-4 mr-2" />
                {language === 'mm' ? 'ကုန်ပစ္စည်း ထည့်ရန်' : 'Add Product'}
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Product Details Modal */}
        {showDetailsModal && selectedProduct && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
            <div className="bg-white dark:bg-gray-800 rounded-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto">
              <div className="p-6">
                <div className="flex items-center justify-between mb-4">
                  <h2 className="text-xl font-bold text-gray-900 dark:text-white">
                    {language === 'mm' ? 'ကုန်ပစ္စည်း အသေးစိတ်' : 'Product Details'}
                  </h2>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleCloseDetailsModal}
                    className="text-gray-500 hover:text-gray-700"
                  >
                    <XCircle className="h-5 w-5" />
                  </Button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Product Images */}
                  <div className="space-y-4">
                    {(() => {
                      const productImages = (selectedProduct as any).images || []
                      const primaryImage = productImages.find((img: any) => img.isPrimary) || productImages[0]
                      const hasImages = productImages.length > 0

                      return (
                        <>
                          {/* Main Image Display */}
                          <div className="aspect-square bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center overflow-hidden">
                            {primaryImage ? (
                              <img
                                src={primaryImage.url}
                                alt={primaryImage.alt || selectedProduct.name}
                                className="w-full h-full object-cover rounded-lg"
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement
                                  target.style.display = 'none'
                                  const placeholder = target.parentElement?.querySelector('.image-placeholder')
                                  if (placeholder) {
                                    placeholder.classList.remove('hidden')
                                  }
                                }}
                              />
                            ) : null}

                            {/* Fallback placeholder */}
                            <div className={`image-placeholder ${primaryImage ? 'hidden' : ''}`}>
                              <ImageIcon className="h-16 w-16 text-gray-400" />
                            </div>
                          </div>

                          {/* Image Thumbnails */}
                          {hasImages && productImages.length > 1 && (
                            <div className="grid grid-cols-4 gap-2">
                              {productImages.map((image: any, index: number) => (
                                <div
                                  key={`thumb-${index}`}
                                  className={`aspect-square bg-gray-100 dark:bg-gray-700 rounded-md overflow-hidden cursor-pointer border-2 transition-colors ${
                                    image.isPrimary ? 'border-blue-500' : 'border-transparent hover:border-gray-300'
                                  }`}
                                  onClick={() => {
                                    // Update primary image when thumbnail is clicked
                                    const updatedImages = productImages.map((img: any, i: number) => ({
                                      ...img,
                                      isPrimary: i === index
                                    }))
                                    setSelectedProduct(prev => prev ? {
                                      ...prev,
                                      images: updatedImages
                                    } : null)
                                  }}
                                >
                                  <img
                                    src={image.url}
                                    alt={image.alt || `${selectedProduct.name} ${index + 1}`}
                                    className="w-full h-full object-cover"
                                  />
                                </div>
                              ))}
                            </div>
                          )}
                        </>
                      )
                    })()}
                    {/* Quick Actions */}
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => {
                          handleCloseDetailsModal()
                          router.push(`/products/${selectedProduct.id}/edit`)
                        }}
                        className="flex-1"
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        {language === 'mm' ? 'ပြင်ဆင်ရန်' : 'Edit'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          handleCloseDetailsModal()
                          router.push(`/pos?product=${selectedProduct.id}`)
                        }}
                        className="flex-1"
                      >
                        <Plus className="h-4 w-4 mr-2" />
                        {language === 'mm' ? 'ရောင်းရန်' : 'Sell'}
                      </Button>
                    </div>
                  </div>

                  {/* Product Information */}
                  <div className="space-y-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                        {selectedProduct.name}
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {selectedProduct.description || (language === 'mm' ? 'ဖော်ပြချက် မရှိပါ' : 'No description available')}
                      </p>
                    </div>

                    <div className="grid grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">{language === 'mm' ? 'SKU' : 'SKU'}:</span>
                          <span className="text-sm font-medium">{selectedProduct.sku}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">{language === 'mm' ? 'ဘားကုဒ်' : 'Barcode'}:</span>
                          <span className="text-sm font-medium">{selectedProduct.barcode}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">{language === 'mm' ? 'အမျိုးအစား' : 'Category'}:</span>
                          <span className="text-sm font-medium">{selectedProduct.category.name}</span>
                        </div>
                      </div>

                      <div className="space-y-2">
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">{language === 'mm' ? 'စျေးနှုန်း' : 'Price'}:</span>
                          <span className="text-sm font-bold text-green-600">{formatPrice(selectedProduct.price)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">{language === 'mm' ? 'ကုန်ကျစရိတ်' : 'Cost'}:</span>
                          <span className="text-sm font-medium">{formatPrice(selectedProduct.cost)}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm text-gray-500">{language === 'mm' ? 'အမြတ်' : 'Profit'}:</span>
                          <span className="text-sm font-bold text-blue-600">
                            {formatPrice(selectedProduct.price - selectedProduct.cost)}
                          </span>
                        </div>
                      </div>
                    </div>

                    {/* Inventory Information */}
                    <div className="border-t pt-4">
                      <h4 className="font-medium text-gray-900 dark:text-white mb-3">
                        {language === 'mm' ? 'စတော့ အချက်အလက်' : 'Inventory Information'}
                      </h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">{language === 'mm' ? 'လက်ကျန်' : 'In Stock'}:</span>
                            <span className={`text-sm font-bold ${
                              selectedProduct.inventory.quantity <= selectedProduct.inventory.minQuantity
                                ? 'text-red-600'
                                : 'text-green-600'
                            }`}>
                              {selectedProduct.inventory.quantity} {selectedProduct.inventory.unit}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">{language === 'mm' ? 'အနည်းဆုံး' : 'Min Stock'}:</span>
                            <span className="text-sm font-medium">{selectedProduct.inventory.minQuantity}</span>
                          </div>
                        </div>
                        <div className="space-y-2">
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">{language === 'mm' ? 'အများဆုံး' : 'Max Stock'}:</span>
                            <span className="text-sm font-medium">{selectedProduct.inventory.maxQuantity}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-sm text-gray-500">{language === 'mm' ? 'ယူနစ်' : 'Unit'}:</span>
                            <span className="text-sm font-medium">{selectedProduct.inventory.unit}</span>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Tags */}
                    {selectedProduct.tags && selectedProduct.tags.length > 0 && (
                      <div className="border-t pt-4">
                        <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                          {language === 'mm' ? 'တဂ်များ' : 'Tags'}
                        </h4>
                        <div className="flex flex-wrap gap-2">
                          {selectedProduct.tags.map((tag, index) => (
                            <span
                              key={index}
                              className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 text-xs rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
