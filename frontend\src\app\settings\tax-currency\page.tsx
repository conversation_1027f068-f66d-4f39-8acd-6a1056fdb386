'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import { useSettings, useTranslation, Currency } from '@/contexts/settings-context'
import { useTheme } from '@/contexts/theme-context'
import { useCurrency } from '@/contexts/currency-context'
import {
  CreditCard,
  ArrowLeft,
  Save,
  Check,
  AlertCircle,
  DollarSign,
  Percent,
  Calculator,
  TrendingUp,
  Plus,
  Trash2,
  RefreshCw,
  Globe,
  Edit
} from 'lucide-react'

interface TaxRate {
  id: string
  name: string
  rate: number
  description: string
  isDefault: boolean
}

interface CurrencySettings {
  primary: string
  symbol: string
  position: 'before' | 'after'
  decimalPlaces: number
  thousandSeparator: string
  decimalSeparator: string
}

export default function TaxCurrencySettingsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language, availableCurrencies, getCurrencyInfo } = useSettings()
  const { currentCurrency, setCurrency, exchangeRates, updateExchangeRates } = useCurrency()

  // Exchange rates for manual management
  const defaultExchangeRates = {
    MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
    USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
    THB: { rate: 0.017, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
  }

  const { t } = useTranslation()
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [editingRates, setEditingRates] = useState(false)
  const [errors, setErrors] = useState<{[key: string]: string}>({})
  const [customRates, setCustomRates] = useState(exchangeRates || defaultExchangeRates)

  // Load exchange rates from localStorage on mount
  useEffect(() => {
    const savedRates = localStorage.getItem('bitstech_exchange_rates')
    if (savedRates) {
      try {
        const parsedRates = JSON.parse(savedRates)
        setCustomRates(parsedRates)
      } catch (error) {
        console.error('Error loading exchange rates:', error)
      }
    } else {
      setCustomRates(exchangeRates || defaultExchangeRates)
    }
  }, [])

  // Sync customRates with exchangeRates when exchangeRates change
  useEffect(() => {
    setCustomRates(exchangeRates)
  }, [exchangeRates])

  const [currencySettings, setCurrencySettings] = useState<CurrencySettings>({
    primary: currentCurrency,
    symbol: getCurrencyInfo(currentCurrency).symbol,
    position: 'after',
    decimalPlaces: currentCurrency === 'MMK' ? 0 : 2,
    thousandSeparator: ',',
    decimalSeparator: '.'
  })

  const [taxRates, setTaxRates] = useState<TaxRate[]>([
    {
      id: '1',
      name: 'Standard Tax',
      rate: 0,
      description: 'Standard tax rate for most products',
      isDefault: true
    },
    {
      id: '2',
      name: 'Electronics Tax',
      rate: 10,
      description: 'Higher tax rate for electronic items',
      isDefault: false
    },
    {
      id: '3',
      name: 'Tax Free',
      rate: 0,
      description: 'No tax for essential items',
      isDefault: false
    }
  ])

  const [newTaxRate, setNewTaxRate] = useState({
    name: '',
    rate: 0,
    description: ''
  })

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  const handleCurrencyChange = (field: keyof CurrencySettings, value: any) => {
    setCurrencySettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addTaxRate = () => {
    if (!newTaxRate.name.trim() || newTaxRate.rate < 0) return

    const taxRate: TaxRate = {
      id: Date.now().toString(),
      name: newTaxRate.name,
      rate: newTaxRate.rate,
      description: newTaxRate.description,
      isDefault: false
    }

    setTaxRates(prev => [...prev, taxRate])
    setNewTaxRate({ name: '', rate: 0, description: '' })
  }

  const removeTaxRate = (id: string) => {
    setTaxRates(prev => prev.filter(rate => rate.id !== id))
  }

  const setDefaultTaxRate = (id: string) => {
    setTaxRates(prev => prev.map(rate => ({
      ...rate,
      isDefault: rate.id === id
    })))
  }

  const updateExchangeRate = (currency: string, newRate: number) => {
    setCustomRates(prev => ({
      ...prev,
      [currency]: {
        ...prev[currency as keyof typeof prev],
        rate: newRate
      }
    }))
  }

  const saveExchangeRates = async () => {
    try {
      setSaving(true)

      // Update currency context with new exchange rates
      await updateExchangeRates(customRates)

      // Exchange rates are managed by useCurrency hook

      // Save to localStorage directly
      localStorage.setItem('bitstech_exchange_rates', JSON.stringify(customRates))

      // Dispatch event to update theme context
      window.dispatchEvent(new CustomEvent('exchange-rates-updated', {
        detail: { rates: customRates }
      }))

      setEditingRates(false)
      setSaved(true)
      setTimeout(() => setSaved(false), 3000)

      console.log('Exchange rates saved and synced:', customRates)
    } catch (error) {
      console.error('Error saving exchange rates:', error)
      alert('Failed to save exchange rates. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  const resetToDefaultRates = () => {
    const defaultRates = {
      MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
      USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
      THB: { rate: 0.016, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
    }
    setCustomRates(defaultRates)
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Apply currency change immediately
      setCurrency(currencySettings.primary as any)

      // Save exchange rates if editing
      if (editingRates) {
        await saveExchangeRates()
      }

      // Save settings to localStorage directly (skip API calls)
      localStorage.setItem('bitstech_tax_settings', JSON.stringify(taxRates))
      localStorage.setItem('bitstech_currency_settings', JSON.stringify(currencySettings))

      console.log('Settings saved locally:', { taxRates, currencySettings })

      // Update both theme and currency contexts
      window.dispatchEvent(new CustomEvent('currency-changed', {
        detail: { currency: currencySettings.primary }
      }))

      // Also update theme context currency
      window.dispatchEvent(new CustomEvent('theme-currency-update', {
        detail: { currency: currencySettings.primary }
      }))

      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.warn('Settings save failed, using mock behavior:', error)
      // Still show success for demo purposes
      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } finally {
      setSaving(false)
    }
  }

  // Update currency settings when context currency changes
  useEffect(() => {
    const currencyInfo = getCurrencyInfo(currentCurrency)
    setCurrencySettings(prev => ({
      ...prev,
      primary: currentCurrency,
      symbol: currencyInfo.symbol,
      decimalPlaces: currentCurrency === 'MMK' ? 0 : 2
    }))
  }, [currentCurrency, getCurrencyInfo])

  const formatPreview = (amount: number) => {
    const formatted = amount.toLocaleString('en-US', {
      minimumFractionDigits: currencySettings.decimalPlaces,
      maximumFractionDigits: currencySettings.decimalPlaces
    })

    return currencySettings.position === 'before'
      ? `${currencySettings.symbol}${formatted}`
      : `${formatted} ${currencySettings.symbol}`
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-green-50 dark:hover:bg-green-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-600 to-emerald-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <CreditCard className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {language === 'mm' ? 'အခွန် နှင့် ငွေကြေး' : 'Tax & Currency'}
                </h1>
                <p className="text-white/80 mt-1">
                  {language === 'mm'
                    ? 'အခွန်နှုန်းများ နှင့် ငွေကြေး ဆက်တင်များ ပြင်ဆင်ပါ'
                    : 'Configure tax rates and currency settings'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          {/* Currency Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <DollarSign className="h-5 w-5 text-green-600" />
                {language === 'mm' ? 'ငွေကြေး ဆက်တင်များ' : 'Currency Settings'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'သင့်လုပ်ငန်း အတွက် ငွေကြေး ပုံစံများ သတ်မှတ်ပါ'
                  : 'Configure currency format for your business'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Primary Currency */}
              <div className="space-y-2">
                <Label>{language === 'mm' ? 'အဓိက ငွေကြေး' : 'Primary Currency'}</Label>
                <Select
                  value={currencySettings.primary}
                  onValueChange={(value) => handleCurrencyChange('primary', value)}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={language === 'mm' ? 'ငွေကြေး ရွေးချယ်ပါ' : 'Select currency'} />
                  </SelectTrigger>
                  <SelectContent>
                    {availableCurrencies.map((curr) => (
                      <SelectItem key={curr.code} value={curr.code}>
                        {language === 'mm' ? curr.nameLocal : curr.name} ({curr.code})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Currency Symbol */}
              <div className="space-y-2">
                <Label>{language === 'mm' ? 'ငွေကြေး သင်္ကေត' : 'Currency Symbol'}</Label>
                <Input
                  value={currencySettings.symbol}
                  onChange={(e) => handleCurrencyChange('symbol', e.target.value)}
                  placeholder="K"
                />
              </div>

              {/* Symbol Position */}
              <div className="space-y-2">
                <Label>{language === 'mm' ? 'သင်္ကေត နေရာ' : 'Symbol Position'}</Label>
                <div className="flex gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="position"
                      value="before"
                      checked={currencySettings.position === 'before'}
                      onChange={(e) => handleCurrencyChange('position', e.target.value)}
                    />
                    <span>{language === 'mm' ? 'ရှေ့မှာ' : 'Before'} ({currencySettings.symbol}1,000)</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="position"
                      value="after"
                      checked={currencySettings.position === 'after'}
                      onChange={(e) => handleCurrencyChange('position', e.target.value)}
                    />
                    <span>{language === 'mm' ? 'နောက်မှာ' : 'After'} (1,000 {currencySettings.symbol})</span>
                  </label>
                </div>
              </div>

              {/* Decimal Places */}
              <div className="space-y-2">
                <Label>{language === 'mm' ? 'ဒဿမ နေရာများ' : 'Decimal Places'}</Label>
                <Select
                  value={currencySettings.decimalPlaces.toString()}
                  onValueChange={(value) => handleCurrencyChange('decimalPlaces', parseInt(value))}
                >
                  <SelectTrigger className="w-full">
                    <SelectValue placeholder={language === 'mm' ? 'ဒဿမ နေရာ ရွေးချယ်ပါ' : 'Select decimal places'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">0 (1,000)</SelectItem>
                    <SelectItem value="2">2 (1,000.00)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* Preview */}
              <div className="p-4 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-lg border border-green-200 dark:border-green-800">
                <Label className="text-sm font-medium text-green-800 dark:text-green-200">
                  {language === 'mm' ? 'နမူနာ ပြသချက်' : 'Preview'}
                </Label>
                <div className="mt-2 space-y-1">
                  <div className="text-lg font-semibold">{formatPreview(1234567)}</div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">{formatPreview(99.99)}</div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Tax Rates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Percent className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အခွန် နှုန်းများ' : 'Tax Rates'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ကုန်ပစ္စည်းများ အတွက် အခွန်နှုန်းများ သတ်မှတ်ပါ'
                  : 'Set up tax rates for different product categories'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Existing Tax Rates */}
              <div className="space-y-3">
                {taxRates.map((rate) => (
                  <div key={rate.id} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{rate.name}</span>
                        {rate.isDefault && (
                          <Badge variant="secondary" className="bg-blue-100 text-blue-700 text-xs">
                            {language === 'mm' ? 'မူလ' : 'Default'}
                          </Badge>
                        )}
                      </div>
                      <div className="text-sm text-gray-500">{rate.description}</div>
                    </div>
                    <div className="flex items-center gap-3">
                      <div className="flex items-center gap-2">
                        <Input
                          type="number"
                          value={rate.rate}
                          onChange={(e) => {
                            const newRate = parseFloat(e.target.value) || 0
                            setTaxRates(prev => prev.map(r =>
                              r.id === rate.id ? { ...r, rate: newRate } : r
                            ))
                          }}
                          className="w-16 h-8 text-xs text-center"
                          min="0"
                          max="100"
                          step="0.1"
                        />
                        <span className="text-sm">%</span>
                      </div>
                      {!rate.isDefault && (
                        <div className="flex gap-1">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => setDefaultTaxRate(rate.id)}
                            className="text-blue-600 hover:text-blue-700"
                          >
                            <TrendingUp className="h-4 w-4" />
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => removeTaxRate(rate.id)}
                            className="text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>

              {/* Add New Tax Rate */}
              <div className="border-t pt-4">
                <Label className="text-sm font-medium mb-3 block">
                  {language === 'mm' ? 'အခွန်နှုန်း အသစ် ထည့်ရန်' : 'Add New Tax Rate'}
                </Label>
                <div className="space-y-3">
                  <Input
                    placeholder={language === 'mm' ? 'အခွန်နှုန်း အမည်' : 'Tax rate name'}
                    value={newTaxRate.name}
                    onChange={(e) => setNewTaxRate(prev => ({ ...prev, name: e.target.value }))}
                  />
                  <div className="grid grid-cols-2 gap-3">
                    <Input
                      type="number"
                      placeholder="Rate %"
                      value={newTaxRate.rate}
                      onChange={(e) => setNewTaxRate(prev => ({ ...prev, rate: parseFloat(e.target.value) || 0 }))}
                    />
                    <Button
                      onClick={addTaxRate}
                      disabled={!newTaxRate.name.trim()}
                      className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700"
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      {language === 'mm' ? 'ထည့်ရန်' : 'Add'}
                    </Button>
                  </div>
                  <Input
                    placeholder={language === 'mm' ? 'ဖော်ပြချက်' : 'Description (optional)'}
                    value={newTaxRate.description}
                    onChange={(e) => setNewTaxRate(prev => ({ ...prev, description: e.target.value }))}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Exchange Rates */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Globe className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'ငွေလဲနှုန်းများ' : 'Exchange Rates'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ငွေကြေး လဲလှယ်နှုန်းများ သတ်မှတ်ပါ'
                  : 'Configure currency exchange rates'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Exchange Rate Controls */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="outline" className="text-xs">
                    {language === 'mm' ? 'အခြေခံ: MMK' : 'Base: MMK'}
                  </Badge>
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={resetToDefaultRates}
                    disabled={editingRates}
                    className="text-xs"
                  >
                    <RefreshCw className="h-3 w-3 mr-1" />
                    {language === 'mm' ? 'မူလအတိုင်း' : 'Reset to Default'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setEditingRates(!editingRates)}
                    className="text-xs"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    {editingRates ? (language === 'mm' ? 'ပယ်ဖျက်' : 'Cancel') : (language === 'mm' ? 'ပြင်ဆင်' : 'Edit')}
                  </Button>
                </div>
              </div>

              {/* Exchange Rate List */}
              <div className="space-y-3">
                {Object.entries(customRates).map(([code, data]) => (
                  <div key={code} className="flex items-center justify-between p-3 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="flex items-center gap-3">
                      <span className="text-lg">{data.flag}</span>
                      <div>
                        <div className="font-medium text-sm">{data.name}</div>
                        <div className="text-xs text-gray-500">{code}</div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <span className="text-xs text-gray-500">1 {code} =</span>
                      {editingRates ? (
                        <Input
                          type="number"
                          step="0.01"
                          value={(1 / data.rate).toFixed(2)}
                          onChange={(e) => {
                            const newValue = parseFloat(e.target.value) || 0
                            updateExchangeRate(code, newValue > 0 ? 1 / newValue : 0)
                          }}
                          className="w-20 h-8 text-xs"
                        />
                      ) : (
                        <span className="font-mono text-sm font-medium">
                          {(1 / data.rate).toFixed(2)}
                        </span>
                      )}
                      <span className="text-xs text-gray-500">MMK</span>
                    </div>
                  </div>
                ))}
              </div>

              {editingRates && (
                <div className="pt-3 border-t">
                  <Button
                    onClick={saveExchangeRates}
                    className="w-full bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700"
                  >
                    <Save className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'နှုန်းများ သိမ်းရန်' : 'Save Rates'}
                  </Button>
                </div>
              )}

              {/* Exchange Rate Info */}
              <div className="p-3 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg border border-purple-200 dark:border-purple-800">
                <div className="text-xs text-purple-800 dark:text-purple-200 font-medium mb-1">
                  {language === 'mm' ? 'အချက်အလက်' : 'Information'}
                </div>
                <div className="text-xs text-purple-600 dark:text-purple-300">
                  {language === 'mm'
                    ? 'ငွေလဲနှုန်းများကို လက်ဖြင့် သတ်မှတ်နိုင်ပါသည်။'
                    : 'Exchange rates can be manually configured by users.'
                  }
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
              </div>
            ) : saved ? (
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းပြီး' : 'Saved'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းရန်' : 'Save Changes'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
