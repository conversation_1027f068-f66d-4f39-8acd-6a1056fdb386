'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useSettings, useTranslation } from '@/contexts/settings-context'
import {
  BookOpen,
  ArrowLeft,
  Search,
  FileText,
  Settings,
  Users,
  ShoppingCart,
  BarChart3,
  Shield,
  Database,
  Palette,
  Receipt,
  Bell,
  Download,
  ExternalLink,
  ChevronRight,
  HelpCircle,
  Video,
  MessageCircle,
  Mail
} from 'lucide-react'
import { SystemArchitecture } from '@/components/diagrams/system-architecture'
import { SystemFlow } from '@/components/diagrams/system-flow'

interface DocumentationSection {
  id: string
  title: string
  titleLocal: string
  description: string
  descriptionLocal: string
  icon: React.ReactNode
  articles: DocumentationArticle[]
  category: 'getting-started' | 'features' | 'advanced' | 'troubleshooting'
}

interface DocumentationArticle {
  id: string
  title: string
  titleLocal: string
  description: string
  descriptionLocal: string
  content: string
  contentLocal: string
  tags: string[]
  difficulty: 'beginner' | 'intermediate' | 'advanced'
  estimatedTime: string
  lastUpdated: string
}

export default function DocumentationPage() {
  const router = useRouter()
  const { isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState<string>('all')
  const [selectedArticle, setSelectedArticle] = useState<DocumentationArticle | null>(null)

  const documentationSections: DocumentationSection[] = [
    {
      id: 'getting-started',
      title: 'Getting Started',
      titleLocal: 'စတင်အသုံးပြုခြင်း',
      description: 'Learn the basics of BitsTech POS system',
      descriptionLocal: 'BitsTech POS စနစ်၏ အခြေခံများကို လေ့လာပါ',
      icon: <BookOpen className="h-5 w-5" />,
      category: 'getting-started',
      articles: [
        {
          id: 'initial-setup',
          title: 'Initial Setup',
          titleLocal: 'ကနဦး ပြင်ဆင်ခြင်း',
          description: 'Set up your POS system for the first time',
          descriptionLocal: 'သင့် POS စနစ်ကို ပထမဆုံးအကြိမ် ပြင်ဆင်ပါ',
          content: `# Initial Setup Guide

## Overview
Welcome to BitsTech POS System! This comprehensive guide will help you set up your point of sale system for the first time.

## Prerequisites
- Computer with internet connection
- MongoDB database (local or cloud)
- Node.js 18+ installed
- Basic understanding of web applications

## Step 1: System Installation
1. Clone the repository from GitHub
2. Install dependencies using npm or yarn
3. Configure environment variables (.env file)
4. Start the application (npm run dev)

## Step 2: Company Information Setup
1. Navigate to Settings > Company Information
2. Fill in your business details:
   - Company name and description
   - Address and contact information
   - Tax ID and registration numbers
   - Upload company logo (supports JPG, PNG)
3. Save the settings

## Step 3: Currency and Tax Configuration
1. Go to Settings > Tax & Currency
2. Select your primary currency (USD, MMK, THB)
3. Configure tax rates for your region
4. Set up exchange rates if using multiple currencies

## Step 4: User Account Creation
1. Access Settings > User Management
2. Create admin and staff accounts
3. Assign appropriate roles and permissions
4. Set up user profiles with contact information

## Step 5: Product Categories Setup
1. Navigate to Products section
2. Create product categories for organization
3. Set up category colors and descriptions
4. Organize your inventory structure

## Step 6: Receipt Template Configuration
1. Go to Settings > Receipt Templates
2. Choose from available templates
3. Customize header, footer, and layout
4. Test print functionality

## Next Steps
- Add your first products to inventory
- Configure notification preferences
- Set up appearance and theme settings
- Explore POS transaction features
- Train staff on system usage

## Troubleshooting Common Issues
- **Database Connection**: Check MongoDB service status
- **Environment Variables**: Verify .env file configuration
- **Port Conflicts**: Ensure ports 3000 and 3001 are available
- **Permission Issues**: Check file system permissions

## Support Resources
- System documentation
- Video tutorials
- Community forums
- Email support: <EMAIL>`,
          contentLocal: `# ကနဦး ပြင်ဆင်မှု လမ်းညွှန်

## ခြုံငုံသုံးသပ်ချက်
BitsTech POS စနစ်သို့ ကြိုဆိုပါသည်! ဤ အပြည့်အစုံ လမ်းညွှန်သည် သင့်ရောင်းချရေး စနစ်ကို ပထမဆုံးအကြိမ် ပြင်ဆင်ရာတွင် အကူအညီပေးပါမည်။

## လိုအပ်ချက်များ
- အင်တာနက် ချိတ်ဆက်မှုရှိသော ကွန်ပျူတာ
- MongoDB ဒေတာဘေ့စ် (local သို့မဟုတ် cloud)
- Node.js 18+ ထည့်သွင်းထားရန်
- ဝက်ဘ် အပလီကေးရှင်းများ အခြေခံ နားလည်မှု

## အဆင့် ၁: စနစ် ထည့်သွင်းခြင်း
1. GitHub မှ repository ကို clone လုပ်ပါ
2. npm သို့မဟုတ် yarn သုံးပြီး dependencies များ ထည့်သွင်းပါ
3. Environment variables များ ပြင်ဆင်ပါ (.env file)
4. အပလီကေးရှင်း စတင်ပါ (npm run dev)

## အဆင့် ၂: ကုမ္ပဏီ အချက်အလက် ပြင်ဆင်ခြင်း
1. Settings > Company Information သို့ သွားပါ
2. သင့်လုပ်ငန်း အသေးစိတ်များ ဖြည့်စွက်ပါ:
   - ကုမ္ပဏီ အမည်နှင့် ဖော်ပြချက်
   - လိပ်စာနှင့် ဆက်သွယ်ရေး အချက်အလက်များ
   - အခွန် ID နှင့် မှတ်ပုံတင် နံပါတ်များ
   - ကုမ္ပဏီ လိုဂို တင်ပါ (JPG, PNG ပံ့ပိုး)
3. ဆက်တင်များ သိမ်းဆည်းပါ

## အဆင့် ၃: ငွေကြေးနှင့် အခွန် ပြင်ဆင်ခြင်း
1. Settings > Tax & Currency သို့ သွားပါ
2. သင့် အဓိက ငွေကြေး ရွေးချယ်ပါ (USD, MMK, THB)
3. သင့်ဒေသအတွက် အခွန်နှုန်းများ ပြင်ဆင်ပါ
4. ငွေကြေး အများအပြား သုံးပါက လဲလှယ်နှုန်းများ သတ်မှတ်ပါ

## အဆင့် ၄: အသုံးပြုသူ အကောင့် ဖန်တီးခြင်း
1. Settings > User Management သို့ သွားပါ
2. admin နှင့် ဝန်ထမ်း အကောင့်များ ဖန်တီးပါ
3. သင့်လျော်သော အခန်းကဏ္ဍများနှင့် ခွင့်ပြုချက်များ ပေးပါ
4. ဆက်သွယ်ရေး အချက်အလက်များပါသော အသုံးပြုသူ ပရိုဖိုင်များ ပြင်ဆင်ပါ

## အဆင့် ၅: ကုန်ပစ္စည်း အမျိုးအစားများ ပြင်ဆင်ခြင်း
1. Products ကဏ္ဍသို့ သွားပါ
2. စုစည်းမှုအတွက် ကုန်ပစ္စည်း အမျိုးအစားများ ဖန်တီးပါ
3. အမျိုးအစား အရောင်များနှင့် ဖော်ပြချက်များ သတ်မှတ်ပါ
4. သင့် စတော့ ဖွဲ့စည်းပုံ ပြင်ဆင်ပါ

## နောက်ထပ် အဆင့်များ
- စတော့သို့ ပထမဆုံး ကုန်ပစ္စည်းများ ထည့်ပါ
- အကြောင်းကြားချက် နှစ်သက်ရာများ ပြင်ဆင်ပါ
- အပြင်အဆင်နှင့် အပြင်အဆင် ဆက်တင်များ သတ်မှတ်ပါ
- POS ငွေလွှဲပြောင်းမှု လုပ်ဆောင်ချက်များ လေ့လာပါ

## ပံ့ပိုးမှု အရင်းအမြစ်များ
- စနစ် လမ်းညွှန်စာများ
- ဗီဒီယို လမ်းညွှန်များ
- အသိုင်းအဝိုင်း ဖိုရမ်များ
- အီးမေးလ် ပံ့ပိုးမှု: <EMAIL>`,
          tags: ['setup', 'configuration', 'first-time'],
          difficulty: 'beginner',
          estimatedTime: '15 minutes',
          lastUpdated: '2024-01-15'
        },
        {
          id: 'user-accounts',
          title: 'Creating User Accounts',
          titleLocal: 'အသုံးပြုသူ အကောင့်များ ဖန်တီးခြင်း',
          description: 'How to create and manage user accounts',
          descriptionLocal: 'အသုံးပြုသူ အကောင့်များကို ဖန်တီးပြီး စီမံခန့်ခွဲနည်း',
          content: `# Creating User Accounts

## Overview
Learn how to create and manage user accounts in BitsTech POS system with proper roles and permissions for secure and efficient operations.

## Practical Usage Examples

### Example 1: Creating a Cashier Account
**Scenario**: Adding a new cashier named "Ma Thida" to your team

**Step-by-step Process**:
1. Navigate to Settings > User Management
2. Click "Add New User" button
3. Fill in the details:
   - Full Name: "Ma Thida"
   - Email: "<EMAIL>"
   - Phone: "+95 9 ***********"
   - Username: "thida_cashier"
   - Password: "SecurePass123!"
4. Select Role: "Cashier"
5. Set Permissions:
   - ✅ POS Operations
   - ✅ Customer Interaction
   - ✅ Basic Inventory View
   - ❌ Admin Settings
   - ❌ Financial Reports
6. Save the account

**Result**: Ma Thida can now log in and process sales transactions but cannot access sensitive settings.

### Example 2: Creating a Manager Account
**Scenario**: Promoting an existing cashier to manager role

**Process**:
1. Find the user in User Management
2. Click "Edit" next to their name
3. Change Role from "Cashier" to "Manager"
4. Update Permissions:
   - ✅ All Cashier permissions
   - ✅ Sales Reports
   - ✅ Staff Management
   - ✅ Inventory Management
   - ✅ Customer Management
5. Apply changes

**Usage Demonstration**: The manager can now view daily sales reports, manage staff schedules, and handle inventory adjustments.

### Example 3: Temporary Staff Account
**Scenario**: Creating a temporary account for part-time help during busy season

**Setup**:
1. Create account with "Staff" role
2. Set limited permissions:
   - ✅ Product Information View
   - ✅ Customer Service Functions
   - ❌ POS Operations
   - ❌ Any Management Functions
3. Set account expiration date
4. Provide training materials access

**Real-world Application**: Perfect for seasonal workers who help with customer inquiries but don't handle money.

## User Roles and Permissions

### Admin Role
- Full system access and control
- User management capabilities
- System settings configuration
- Financial reports access
- Data backup and restore

### Manager Role
- Store operations management
- Sales and inventory reports
- Staff performance monitoring
- Customer management
- Limited system settings

### Cashier Role
- POS transaction processing
- Customer interaction
- Basic inventory viewing
- Sales receipt generation
- Limited reporting access

### Staff Role
- Specific task assignments
- Limited system access
- Basic product information
- Customer service functions

## Step-by-Step User Creation

### 1. Access User Management
- Navigate to Settings > User Management
- Click "Add New User" button
- Ensure you have admin privileges

### 2. Basic Information
Fill in the following details:
- **Full Name**: Employee's complete name
- **Email Address**: Valid email for notifications
- **Phone Number**: Contact information
- **Username**: Unique system identifier
- **Password**: Strong password (min 8 characters)

### 3. Role Assignment
- Select appropriate role from dropdown
- Review role permissions
- Assign additional specific permissions if needed

### 4. Profile Configuration
- Upload profile picture (optional)
- Set work schedule
- Add department information
- Configure notification preferences

### 5. Security Settings
- Enable two-factor authentication
- Set password expiration policy
- Configure login restrictions
- Set session timeout duration

## Managing Existing Users

### Editing User Information
1. Find user in the user list
2. Click "Edit" button
3. Modify necessary information
4. Save changes

### Changing Passwords
1. Select user account
2. Click "Reset Password"
3. Generate new password or set custom
4. Notify user of password change

### Updating Permissions
1. Access user profile
2. Navigate to permissions tab
3. Modify role or specific permissions
4. Apply changes

### Deactivating Accounts
1. Select user to deactivate
2. Click "Deactivate" option
3. Confirm deactivation
4. User loses system access immediately

## Best Practices

### Security Guidelines
- Use strong, unique passwords
- Enable two-factor authentication
- Regular password updates
- Monitor login activities
- Limit administrative access

### Permission Management
- Assign minimal required permissions
- Regular permission audits
- Role-based access control
- Document permission changes
- Train users on their access levels

### Account Maintenance
- Regular user account reviews
- Remove unused accounts promptly
- Update contact information
- Monitor user activity logs
- Backup user data regularly

## Troubleshooting

### Common Issues
- **Login Problems**: Check username/password, account status
- **Permission Errors**: Verify role assignments
- **Email Issues**: Confirm email settings
- **Password Reset**: Use admin reset function

### Support Resources
- User management documentation
- Video tutorials
- System administrator guide
- Technical support contact`,
          contentLocal: `# အသုံးပြုသူ အကောင့်များ ဖန်တီးခြင်း

## ခြုံငုံသုံးသပ်ချက်
BitsTech POS စနစ်တွင် လုံခြုံပြီး ထိရောက်သော လုပ်ဆောင်မှုများအတွက် သင့်လျော်သော အခန်းကဏ္ဍများနှင့် ခွင့်ပြုချက်များဖြင့် အသုံးပြုသူ အကောင့်များ ဖန်တီးပြီး စီမံခန့်ခွဲနည်းကို လေ့လာပါ။

## အသုံးပြုသူ အခန်းကဏ္ဍများနှင့် ခွင့်ပြုချက်များ

### Admin အခန်းကဏ္ဍ
- စနစ် အပြည့်အဝ ထိန်းချုပ်ခွင့်
- အသုံးပြုသူ စီမံခန့်ခွဲမှု စွမ်းရည်များ
- စနစ် ဆက်တင်များ ပြင်ဆင်ခြင်း
- ငွေကြေး အစီရင်ခံစာများ ရယူခွင့်
- ဒေတာ အရန်သိမ်းခြင်းနှင့် ပြန်လည်ရယူခြင်း

### Manager အခန်းကဏ္ဍ
- စတိုး လုပ်ငန်းများ စီမံခန့်ခွဲမှု
- ရောင်းချမှုနှင့် စတော့ အစီရင်ခံစာများ
- ဝန်ထမ်း စွမ်းဆောင်ရည် စောင့်ကြည့်ခြင်း
- ဖောက်သည် စီမံခန့်ခွဲမှု
- ကန့်သတ်ထားသော စနစ် ဆက်တင်များ

### Cashier အခန်းကဏ္ဍ
- POS ငွေလွှဲပြောင်းမှု လုပ်ဆောင်ခြင်း
- ဖောက်သည် ဆက်ဆံရေး
- အခြေခံ စတော့ ကြည့်ရှုခြင်း
- ရောင်းချမှု ဘောင်ချာ ထုတ်ပေးခြင်း
- ကန့်သတ်ထားသော အစီရင်ခံစာ ရယူခွင့်

### Staff အခန်းကဏ္ဍ
- သီးခြား တာဝန် ပေးအပ်ချက်များ
- ကန့်သတ်ထားသော စနစ် ရယူခွင့်
- အခြေခံ ကုန်ပစ္စည်း အချက်အလက်များ
- ဖောက်သည် ဝန်ဆောင်မှု လုပ်ဆောင်ချက်များ

## အဆင့်ဆင့် အသုံးပြုသူ ဖန်တီးခြင်း

### ၁. အသုံးပြုသူ စီမံခန့်ခွဲမှု ရယူခြင်း
- Settings > User Management သို့ သွားပါ
- "Add New User" ခလုတ် နှိပ်ပါ
- admin ခွင့်ပြုချက်များ ရှိကြောင်း သေချာပါစေ

### ၂. အခြေခံ အချက်အလက်များ
အောက်ပါ အသေးစိတ်များ ဖြည့်စွက်ပါ:
- **အမည် အပြည့်အစုံ**: ဝန်ထမ်း၏ အပြည့်အစုံ အမည်
- **အီးမေးလ် လိပ်စာ**: အကြောင်းကြားချက်များအတွက် တရားဝင် အီးမေးလ်
- **ဖုန်း နံပါတ်**: ဆက်သွယ်ရေး အချက်အလက်
- **အသုံးပြုသူ အမည်**: စနစ်တွင် ထူးခြားသော အမည်
- **စကားဝှက်**: ခိုင်မာသော စကားဝှက် (အနည်းဆုံး ၈ လုံး)

### ၃. အခန်းကဏ္ဍ ပေးအပ်ခြင်း
- dropdown မှ သင့်လျော်သော အခန်းကဏ္ဍ ရွေးချယ်ပါ
- အခန်းကဏ္ဍ ခွင့်ပြုချက်များ ပြန်လည်စစ်ဆေးပါ
- လိုအပ်ပါက အပိုင်းအခြား သီးခြား ခွင့်ပြုချက်များ ပေးပါ

## အကောင့် ထိန်းသိမ်းမှု အကောင်းဆုံး နည်းလမ်းများ

### လုံခြုံရေး လမ်းညွှန်ချက်များ
- ခိုင်မာပြီး ထူးခြားသော စကားဝှက်များ သုံးပါ
- နှစ်ဆင့် အတည်ပြုမှု ဖွင့်ပါ
- စကားဝှက် ပုံမှန် အပ်ဒိတ်လုပ်ပါ
- လော့ဂ်အင် လုပ်ဆောင်ချက်များ စောင့်ကြည့်ပါ
- စီမံခန့်ခွဲမှု ရယူခွင့် ကန့်သတ်ပါ`,
          tags: ['users', 'accounts', 'permissions'],
          difficulty: 'beginner',
          estimatedTime: '10 minutes',
          lastUpdated: '2024-01-15'
        }
      ]
    },
    {
      id: 'pos-features',
      title: 'POS Features',
      titleLocal: 'POS လုပ်ဆောင်ချက်များ',
      description: 'Learn about point of sale functionality',
      descriptionLocal: 'ရောင်းချရေး လုပ်ဆောင်ချက်များကို လေ့လာပါ',
      icon: <ShoppingCart className="h-5 w-5" />,
      category: 'features',
      articles: [
        {
          id: 'making-sales',
          title: 'Making Sales',
          titleLocal: 'ရောင်းချခြင်း',
          description: 'How to process sales transactions',
          descriptionLocal: 'ရောင်းချမှု ငွေကြေးလွှဲပြောင်းမှုများကို လုပ်ဆောင်နည်း',
          content: `# Making Sales - Complete POS Guide

## Overview
Master the art of processing sales transactions efficiently using BitsTech POS system with real-world scenarios and practical examples.

## Practical Usage Examples

### Example 1: Simple Cash Sale
**Scenario**: Customer buys 2 bottles of water and 1 snack

**Step-by-step Process**:
1. **Access POS**: Click "POS" from main navigation
2. **Add Products**:
   - Search "water" → Select "Drinking Water 500ml"
   - Click quantity "+" to make it 2
   - Search "snack" → Select "Potato Chips"
3. **Review Cart**:
   - Drinking Water 500ml × 2 = 1,000 MMK
   - Potato Chips × 1 = 500 MMK
   - Subtotal: 1,500 MMK
   - Tax (5%): 75 MMK
   - **Total: 1,575 MMK**
4. **Process Payment**:
   - Click "Cash Payment"
   - Enter received amount: 2,000 MMK
   - System calculates change: 425 MMK
5. **Complete Sale**:
   - Click "Complete Sale"
   - Print receipt automatically
   - Give change to customer

**Result**: Sale recorded, inventory updated, receipt printed.

### Example 2: Card Payment with Discount
**Scenario**: Regular customer with loyalty discount buying multiple items

**Process**:
1. **Add Products to Cart**:
   - Scan barcode or search products
   - Rice 5kg × 1 = 15,000 MMK
   - Cooking Oil 1L × 2 = 8,000 MMK
   - Onions 1kg × 1 = 2,000 MMK
   - **Subtotal: 25,000 MMK**

2. **Apply Customer & Discount**:
   - Click "Add Customer"
   - Search "Daw Mya" → Select customer
   - System shows: "5% Loyalty Discount Available"
   - Click "Apply Discount"
   - **New Subtotal: 23,750 MMK**
   - Tax (5%): 1,187 MMK
   - **Final Total: 24,937 MMK**

3. **Card Payment**:
   - Click "Card Payment"
   - Select payment method: "Visa/Mastercard"
   - Enter card details or use card reader
   - Wait for approval
   - Print receipt

**Usage Demonstration**: Customer loyalty program automatically applied, payment processed securely.

### Example 3: Mixed Payment Method
**Scenario**: Customer pays partially with cash and card

**Advanced Process**:
1. **Cart Total**: 45,000 MMK
2. **Split Payment**:
   - Click "Split Payment"
   - Cash amount: 20,000 MMK
   - Card amount: 25,000 MMK
3. **Process Each Payment**:
   - First: Process cash (20,000 MMK)
   - Second: Process card (25,000 MMK)
4. **Verify Total**: System confirms full payment received
5. **Complete Transaction**: Generate receipt showing both payment methods

### Example 4: Return/Exchange Process
**Scenario**: Customer returns defective product

**Return Process**:
1. **Access Returns**: Click "Returns" in POS
2. **Find Original Sale**:
   - Enter receipt number or phone number
   - Select transaction from list
3. **Select Return Items**:
   - Choose defective product
   - Select reason: "Defective"
   - Enter return quantity
4. **Process Refund**:
   - Choose refund method (cash/card/store credit)
   - Calculate refund amount
   - Update inventory (+1 to defective stock)
5. **Complete Return**: Print return receipt

### Example 5: Bulk Sale to Business Customer
**Scenario**: Restaurant owner buying supplies in bulk

**Bulk Sale Process**:
1. **Add Business Customer**:
   - Search "Golden Restaurant"
   - Apply business discount (10%)
2. **Add Bulk Items**:
   - Rice 25kg bags × 5 = 375,000 MMK
   - Cooking Oil 5L × 10 = 200,000 MMK
   - Various spices = 50,000 MMK
   - **Subtotal: 625,000 MMK**
3. **Apply Business Discount**: 10% = 62,500 MMK discount
   - **New Subtotal: 562,500 MMK**
4. **Tax Calculation**: Commercial tax rate applied
5. **Payment Terms**:
   - Option 1: Immediate payment
   - Option 2: 30-day credit terms
6. **Generate Invoice**: Business invoice with tax details

## Advanced POS Features

### Barcode Scanning
**Usage**:
- Connect barcode scanner to computer
- Scan product barcode
- Product automatically added to cart
- Quantity can be adjusted manually

### Quick Sale Buttons
**Setup Custom Buttons**:
1. Go to POS Settings
2. Create quick buttons for popular items:
   - "Coffee" → Instant add coffee to cart
   - "Cigarettes" → Quick tobacco products
   - "Phone Credit" → Mobile top-up options

### Daily Sales Summary
**End-of-day Process**:
1. Click "Daily Summary" in POS
2. Review:
   - Total sales amount
   - Number of transactions
   - Payment method breakdown
   - Top-selling products
3. Print daily report
4. Prepare cash drawer for counting

## Troubleshooting Common Issues

### Payment Failures
**Problem**: Card payment declined
**Solution**:
1. Try payment again
2. Check card details
3. Use alternative payment method
4. Contact bank if persistent

### Barcode Issues
**Problem**: Barcode won't scan
**Solution**:
1. Clean barcode scanner lens
2. Manually enter product code
3. Check product database
4. Add missing products

### Receipt Printer Problems
**Problem**: Receipt won't print
**Solution**:
1. Check printer connection
2. Verify paper loaded correctly
3. Restart printer
4. Use backup receipt option`,
          contentLocal: `# ရောင်းချခြင်း - အပြည့်အစုံ လမ်းညွှန်

## ခြုံငုံသုံးသပ်ချက်
BitsTech POS စနစ်ကို အသုံးပြုပြီး ရောင်းချမှု ငွေလွှဲပြောင်းမှုများကို ထိရောက်စွာ လုပ်ဆောင်နည်းကို လေ့လာပါ။ ဤ လမ်းညွှန်သည် အခြေခံ ရောင်းချမှုမှ အဆင့်မြင့် လုပ်ဆောင်ချက်များအထိ အားလုံးကို ပါဝင်သည်။

## ရောင်းချမှုဖြင့် စတင်ခြင်း

### ၁. POS စနစ် ရယူခြင်း
- ပင်မ dashboard သို့ သွားပါ
- Sidebar menu မှ "POS" ကို နှိပ်ပါ
- သို့မဟုတ် "New Sale" quick action button ကို သုံးပါ

### ၂. အခြေခံ ရောင်းချမှု လုပ်ငန်းစဉ်

#### အဆင့် ၁: ရောင်းချမှု အသစ် စတင်ခြင်း
1. "New Sale" button ကို နှိပ်ပါ
2. POS interface ပွင့်လာမည်
3. ဘယ်ဘက်တွင် ကုန်ပစ္စည်း အမျိုးအစားများ မြင်ရမည်
4. ညာဘက်တွင် shopping cart ရှိမည်

#### အဆင့် ၂: ကုန်ပစ္စည်းများ ထည့်ခြင်း
**နည်းလမ်း ၁: အမျိုးအစားများ ကြည့်ရှုခြင်း**
1. ကုန်ပစ္စည်း အမျိုးအစား တစ်ခုကို နှိပ်ပါ
2. ရရှိနိုင်သော ကုန်ပစ္စည်းများကို ကြည့်ရှုပါ
3. cart ထဲသို့ ထည့်ရန် ကုန်ပစ္စည်းကို နှိပ်ပါ
4. အရေအတွက် default အနေဖြင့် ၁ ဖြစ်မည်

**နည်းလမ်း ၂: ကုန်ပစ္စည်းများ ရှာဖွေခြင်း**
1. အပေါ်ရှိ search bar ကို သုံးပါ
2. ကုန်ပစ္စည်း အမည် သို့မဟုတ် barcode ကို ရိုက်ပါ
3. ရှာဖွေမှု ရလဒ်များမှ ရွေးချယ်ပါ
4. ကုန်ပစ္စည်း အလိုအလျောက် cart ထဲသို့ ထည့်သွားမည်

#### အဆင့် ၃: Cart ထဲရှိ ပစ္စည်းများ ပြုပြင်ခြင်း
- **အရေအတွက် ပြောင်းလဲခြင်း**: +/- buttons များ နှိပ်ပါ သို့မဟုတ် အရေအတွက် ရိုက်ပါ
- **ပစ္စည်း ဖယ်ရှားခြင်း**: trash icon ကို နှိပ်ပါ
- **လျှော့စျေး ပေးခြင်း**: discount button နှိပ်ပြီး ရာခိုင်နှုန်း သို့မဟုတ် ပမာণ ထည့်ပါ

#### အဆင့် ၄: ငွေပေးချေမှု လုပ်ဆောင်ခြင်း
1. စုစုပေါင်း ပမာণကို ပြန်လည်စစ်ဆေးပါ
2. ငွေပေးချေမှု နည်းလမ်း ရွေးချယ်ပါ:
   - လက်ငင်းငွေ
   - ခရက်ဒစ်/ဒက်ဘစ် ကတ်
   - မိုဘိုင်း ငွေပေးချေမှု
   - ဘဏ် လွှဲပြောင်းမှု

## အဆင့်မြင့် ရောင်းချမှု လုပ်ဆောင်ချက်များ

### ၁. လျှော့စျေးများ နှင့် ပရိုမိုးရှင်းများ
- ပစ္စည်း အဆင့်တွင် လျှော့စျေး
- Cart အဆင့်တွင် လျှော့စျေး
- အစုလိုက် လျှော့စျေးများ

### ၂. အခွန် တွက်ချက်မှုများ
- ဆက်တင်များ အခြေခံ၍ အခွန် အလိုအလျောက် တွက်ချက်
- ကုန်ပစ္စည်း အမျိုးအစား အမျိုးမျိုးအတွက် အခွန်နှုန်း မတူညီမှု
- အခွန်ပါ သို့မဟုတ် အခွန်မပါ စျေးနှုန်း

### ၃. ငွေပေးချေမှု နည်းလမ်း အများအပြား
**ခွဲ၍ ငွေပေးချေမှု:**
1. ပထမ ငွေပေးချေမှု နည်းလမ်းအတွက် တစ်စိတ်တစ်ပိုင်း ပမာণ ထည့်ပါ
2. "Add Payment" ကို နှိပ်ပါ
3. ဒုတိယ ငွေပေးချေမှု နည်းလမ်း ရွေးချယ်ပါ
4. အပြည့်အဝ ပမာণ ပြည့်သည်အထိ ဆက်လုပ်ပါ

## အကောင်းဆုံး အလေ့အကျင့်များ

### ၁. ထိရောက်သော လုပ်ငန်းစဉ်
- Keyboard shortcuts များ လေ့လာပါ
- မြန်ဆန်မှုအတွက် barcode scanner သုံးပါ
- မကြာခဏ ရောင်းသော ပစ္စည်းများကို လွယ်ကူစွာ ရယူနိုင်အောင် ထားပါ

### ၂. ဖောက်သည် ဝန်ဆောင်မှု
- ဖောက်သည်များကို နွေးထွေးစွာ ကြိုဆိုပါ
- ပရိုမိုးရှင်းများနှင့် လျှော့စျေးများ ရှင်းပြပါ
- ဘောင်ချာ ရွေးချယ်စရာများ ပေးပါ (ပုံနှိပ်/အီးမေးလ်)

### ၃. တိကျမှု
- အရေအတွက်များကို နှစ်ကြိမ် စစ်ဆေးပါ
- ဖောက်သည် အချက်အလက်များ အတည်ပြုပါ
- ငွေပေးချေမှု ပမာဏများ အတည်ပြုပါ

ဤ အပြည့်အစုံ လမ်းညွှန်သည် BitsTech POS စနစ်တွင် ရောင်းချမှု လုပ်ဆောင်ခြင်း၏ ရှုထောင့် အားလုံးကို ပါဝင်သည်။ ရောင်းချမှု ငွေလွှဲပြောင်းမှု အမျိုးအစား အားလုံးကို ကိုင်တွယ်ရာတွင် ကျွမ်းကျင်လာရန် ဤ လုပ်ထုံးလုပ်နည်းများကို လေ့ကျင့်ပါ။`,
          tags: ['sales', 'transactions', 'payment'],
          difficulty: 'beginner',
          estimatedTime: '20 minutes',
          lastUpdated: '2024-01-15'
        }
      ]
    },
    {
      id: 'product-management',
      title: 'Product Management',
      titleLocal: 'ကုန်ပစ္စည်း စီမံခန့်ခွဲမှု',
      description: 'Manage your inventory and products',
      descriptionLocal: 'သင့် စတော့နှင့် ကုန်ပစ္စည်းများကို စီမံခန့်ခွဲပါ',
      icon: <FileText className="h-5 w-5" />,
      category: 'features',
      articles: [
        {
          id: 'adding-products',
          title: 'Adding Products',
          titleLocal: 'ကုန်ပစ္စည်းများ ထည့်ခြင်း',
          description: 'How to add new products to your inventory',
          descriptionLocal: 'သင့် စတော့ထဲသို့ ကုန်ပစ္စည်းအသစ်များ ထည့်နည်း',
          content: `# Product Management - Complete Guide

## Overview
Learn how to effectively manage your inventory, add products, organize categories, and maintain stock levels using BitsTech POS system.

## Practical Usage Examples

### Example 1: Adding a New Food Product
**Scenario**: Adding "Instant Noodles - Chicken Flavor" to your convenience store

**Step-by-step Process**:
1. **Navigate to Products**: Click "Products" from main menu
2. **Start Adding Product**: Click "Add New Product" button
3. **Basic Information**:
   - Product Name: "Instant Noodles - Chicken Flavor"
   - Product Name (MM): "ခေါက်ဆွဲခြောက် - ကြက်သားအရသာ"
   - SKU: "NOODLE-CHICK-001" (auto-generated)
   - Barcode: "8851234567890"
4. **Category & Pricing**:
   - Category: "Food & Beverages" → "Instant Food"
   - Cost Price: 350 MMK
   - Selling Price: 500 MMK
   - Profit Margin: 42.9% (auto-calculated)
5. **Inventory Details**:
   - Initial Stock: 100 units
   - Minimum Stock Alert: 20 units
   - Maximum Stock: 500 units
   - Unit of Measure: "Piece"
6. **Additional Information**:
   - Description: "Delicious chicken-flavored instant noodles"
   - Supplier: "Myanmar Food Co., Ltd."
   - Expiry Date: "2024-12-31"
   - Storage Location: "Shelf A-3"
7. **Upload Product Image**: Add product photo for easy identification
8. **Save Product**: Click "Save Product"

**Result**: Product added to inventory, available for sale, stock tracking enabled.

### Example 2: Creating Product Categories
**Scenario**: Organizing products for a grocery store

**Category Structure Setup**:
1. **Main Categories**:
   - Food & Beverages
   - Personal Care
   - Household Items
   - Electronics
   - Clothing

2. **Sub-categories for Food & Beverages**:
   - Fresh Produce → Fruits, Vegetables, Herbs
   - Dairy Products → Milk, Cheese, Yogurt
   - Meat & Seafood → Fresh Meat, Frozen Fish
   - Beverages → Soft Drinks, Juices, Water
   - Snacks → Chips, Cookies, Candy

**Implementation Process**:
1. Go to Products → Categories
2. Click "Add Category"
3. Create main category: "Food & Beverages"
4. Set category color: Green
5. Add description: "All food and beverage items"
6. Create sub-categories under main category
7. Assign products to appropriate categories

**Usage Demonstration**: Easy product navigation, better inventory organization, improved sales reporting by category.

### Example 3: Bulk Product Import
**Scenario**: Adding 200 products from supplier catalog

**Bulk Import Process**:
1. **Prepare Excel File**:
   - Download template from Products → Import
   - Fill in product details:
     - Column A: Product Name
     - Column B: SKU
     - Column C: Barcode
     - Column D: Category
     - Column E: Cost Price
     - Column F: Selling Price
     - Column G: Initial Stock
2. **Import Process**:
   - Click "Import Products"
   - Upload Excel file
   - Map columns to system fields
   - Preview import data
   - Validate all entries
3. **Review & Confirm**:
   - Check for duplicates
   - Verify pricing
   - Confirm stock quantities
   - Import products
4. **Post-Import Tasks**:
   - Add product images
   - Set up stock alerts
   - Configure supplier information

**Result**: 200 products added efficiently, ready for sale.

### Example 4: Stock Management
**Scenario**: Managing inventory levels and stock alerts

**Daily Stock Management**:
1. **Check Low Stock Alerts**:
   - Dashboard shows "15 products low in stock"
   - Click alert to view list
   - Products below minimum threshold highlighted

2. **Stock Adjustment Example**:
   - Product: "Coca Cola 330ml"
   - Current Stock: 8 units
   - Minimum Alert: 20 units
   - **Action Required**: Reorder stock

3. **Reorder Process**:
   - Click "Reorder" next to product
   - System suggests order quantity: 100 units
   - Adjust if needed: 150 units
   - Select supplier: "Beverage Distributors Ltd."
   - Generate purchase order
   - Send to supplier

4. **Receiving Stock**:
   - When delivery arrives, go to Inventory → Receive Stock
   - Scan purchase order barcode
   - Verify received quantities
   - Update stock levels automatically

**Usage Demonstration**: Automated stock monitoring, efficient reordering, accurate inventory tracking.

### Example 5: Product Variants Management
**Scenario**: Managing different sizes/colors of same product

**Variant Setup Example - T-Shirts**:
1. **Create Master Product**: "Cotton T-Shirt"
2. **Add Variants**:
   - Size variants: S, M, L, XL, XXL
   - Color variants: White, Black, Blue, Red
   - Combined variants: White-S, White-M, Black-L, etc.

3. **Variant Configuration**:
   - Each variant has unique SKU: "TSHIRT-WHT-S"
   - Different pricing if needed
   - Separate stock tracking
   - Individual barcodes

4. **POS Integration**:
   - Cashier selects "Cotton T-Shirt"
   - System shows available variants
   - Select specific size/color
   - Stock automatically deducted from correct variant

**Real-world Application**: Perfect for clothing stores, electronics with different specifications, or any products with multiple options.

## Advanced Product Features

### Product Bundles
**Example**: "Breakfast Combo"
- Components: Bread + Butter + Jam
- Bundle Price: 2,500 MMK (vs 3,000 MMK individual)
- Stock tracking for each component
- Automatic inventory deduction

### Seasonal Products
**Setup**:
1. Mark products as seasonal
2. Set active periods (e.g., December-January for winter items)
3. Automatic hiding when out of season
4. Seasonal sales reporting

### Product Analytics
**Track Performance**:
- Best-selling products
- Slow-moving inventory
- Profit margins by product
- Seasonal trends
- Customer preferences

## Inventory Optimization Tips

### ABC Analysis
**Categorize Products**:
- **A Products**: High value, 20% of items, 80% of revenue
- **B Products**: Medium value, 30% of items, 15% of revenue
- **C Products**: Low value, 50% of items, 5% of revenue

**Management Strategy**:
- A Products: Tight control, frequent monitoring
- B Products: Moderate control, periodic review
- C Products: Simple control, basic tracking

### Stock Rotation (FIFO)
**Implementation**:
1. Label products with received dates
2. Arrange older stock in front
3. System alerts for near-expiry items
4. Automatic FIFO in POS system

### Supplier Management
**Best Practices**:
- Maintain multiple suppliers per product
- Track supplier performance
- Negotiate better terms
- Monitor delivery times
- Quality control processes`,
          contentLocal: `# ကုန်ပစ္စည်း စီမံခန့်ခွဲမှု - အပြည့်အစုံ လမ်းညွှန်

## ခြုံငုံသုံးသပ်ချက်
BitsTech POS စနစ်ကို အသုံးပြုပြီး သင့် စတော့ကို ထိရောက်စွာ စီမံခန့်ခွဲနည်း၊ ကုန်ပစ္စည်းများ ထည့်နည်း၊ အမျိုးအစားများ ပြင်ဆင်နည်းနှင့် စတော့ အဆင့်များ ထိန်းသိမ်းနည်းကို လေ့လာပါ။

## လက်တွေ့ အသုံးပြုပုံ ဥပမာများ

### ဥပမာ ၁: အစားအသောက် ကုန်ပစ္စည်းအသစ် ထည့်ခြင်း
**အခြေအနေ**: သင့် convenience store မှာ "ခေါက်ဆွဲခြောက် - ကြက်သားအရသာ" ထည့်ခြင်း

**အဆင့်ဆင့် လုပ်ငန်းစဉ်**:
1. **ကုန်ပစ္စည်းများသို့ သွားခြင်း**: ပင်မ menu မှ "Products" ကို နှိပ်ပါ
2. **ကုန်ပစ္စည်း ထည့်ခြင်း စတင်ခြင်း**: "Add New Product" ခလုတ် နှိပ်ပါ
3. **အခြေခံ အချက်အလက်များ**:
   - ကုန်ပစ္စည်း အမည်: "Instant Noodles - Chicken Flavor"
   - ကုန်ပစ္စည်း အမည် (မြန်မာ): "ခေါက်ဆွဲခြောက် - ကြက်သားအရသာ"
   - SKU: "NOODLE-CHICK-001" (အလိုအလျောက် ထုတ်ပေး)
   - ဘားကုဒ်: "8851234567890"
4. **အမျိုးအစားနှင့် စျေးနှုန်း**:
   - အမျိုးအစား: "Food & Beverages" → "Instant Food"
   - ဝယ်စျေး: 350 ကျပ်
   - ရောင်းစျေး: 500 ကျပ်
   - အမြတ်အစွန်း: 42.9% (အလိုအလျောက် တွက်ချက်)
5. **စတော့ အသေးစိတ်များ**:
   - ကနဦး စတော့: 100 ခု
   - အနည်းဆုံး စတော့ သတိပေးချက်: 20 ခု
   - အများဆုံး စတော့: 500 ခု
   - တိုင်းတာမှု ယူနစ်: "ခု"
6. **နောက်ထပ် အချက်အလက်များ**:
   - ဖော်ပြချက်: "အရသာရှိသော ကြက်သားအရသာ ခေါက်ဆွဲခြောက်"
   - ပေးသွင်းသူ: "Myanmar Food Co., Ltd."
   - သက်တမ်းကုန်ဆုံးရက်: "2024-12-31"
   - သိုလှောင်ရာ နေရာ: "Shelf A-3"
7. **ကုန်ပစ္စည်း ပုံ တင်ခြင်း**: လွယ်ကူစွာ မှတ်မိရန် ကုန်ပစ္စည်း ဓာတ်ပုံ ထည့်ပါ
8. **ကုန်ပစ္စည်း သိမ်းဆည်းခြင်း**: "Save Product" နှိပ်ပါ

**ရလဒ်**: ကုန်ပစ္စည်း စတော့ထဲ ထည့်ပြီး၊ ရောင်းချရန် အဆင်သင့်၊ စတော့ ခြေရာခံမှု ဖွင့်ပြီး။

### ဥပမာ ၂: ကုန်ပစ္စည်း အမျိုးအစားများ ဖန်တီးခြင်း
**အခြေအနေ**: ကုန်စုံဆိုင်အတွက် ကုန်ပစ္စည်းများ ပြင်ဆင်ခြင်း

**အမျိုးအစား ဖွဲ့စည်းပုံ ပြင်ဆင်ခြင်း**:
1. **ပင်မ အမျိုးအစားများ**:
   - အစားအသောက်နှင့် ယမကာများ
   - ကိုယ်ရေးကိုယ်တာ စောင့်ရှောက်မှု
   - အိမ်သုံး ပစ္စည်းများ
   - အီလက်ထရွန်နစ်
   - အဝတ်အထည်

2. **အစားအသောက်နှင့် ယမကာများ အတွက် ခွဲအမျိုးအစားများ**:
   - လတ်ဆတ်သော ထွက်ပစ္စည်းများ → သစ်သီးများ၊ ဟင်းသီးဟင်းရွက်များ၊ ဟင်းရွက်များ
   - နို့ထွက်ပစ္စည်းများ → နို့၊ ချိစ်၊ ဒိန်ချဉ်
   - အသားနှင့် ပင်လယ်စာများ → လတ်ဆတ်သော အသား၊ အေးခဲထားသော ငါး
   - ယမကာများ → အချိုရည်များ၊ သစ်သီးရည်များ၊ ရေ
   - snack များ → ချစ်ပ်များ၊ ကွတ်ကီးများ၊ သကြား

**အကောင်အထည်ဖော် လုပ်ငန်းစဉ်**:
1. Products → Categories သို့ သွားပါ
2. "Add Category" နှိပ်ပါ
3. ပင်မ အမျိုးအစား ဖန်တီးပါ: "Food & Beverages"
4. အမျိုးအစား အရောင် သတ်မှတ်ပါ: အစိမ်း
5. ဖော်ပြချက် ထည့်ပါ: "အစားအသောက်နှင့် ယမကာ ပစ္စည်းများ အားလုံး"
6. ပင်မ အမျိုးအစားအောက်မှာ ခွဲအမျိုးအစားများ ဖန်တီးပါ
7. ကုန်ပစ္စည်းများကို သင့်လျော်သော အမျိုးအစားများသို့ ခွဲခြားပါ

**အသုံးပြုပုံ သရုပ်ပြမှု**: လွယ်ကူသော ကုန်ပစ္စည်း ရှာဖွေမှု၊ ပိုမိုကောင်းမွန်သော စတော့ ပြင်ဆင်မှု၊ အမျိုးအစားအလိုက် ပိုမိုကောင်းမွန်သော ရောင်းချမှု အစီရင်ခံမှု။`,
          tags: ['products', 'inventory', 'categories'],
          difficulty: 'beginner',
          estimatedTime: '15 minutes',
          lastUpdated: '2024-01-15'
        }
      ]
    },
    {
      id: 'settings-config',
      title: 'Settings & Configuration',
      titleLocal: 'ဆက်တင်များနှင့် ပြင်ဆင်ခြင်း',
      description: 'Configure system settings and preferences',
      descriptionLocal: 'စနစ် ဆက်တင်များနှင့် နှစ်သက်ရာများကို ပြင်ဆင်ပါ',
      icon: <Settings className="h-5 w-5" />,
      category: 'advanced',
      articles: [
        {
          id: 'appearance-settings',
          title: 'Appearance Settings',
          titleLocal: 'အပြင်အဆင် ဆက်တင်များ',
          description: 'Customize themes, colors, and layout',
          descriptionLocal: 'အပြင်အဆင်များ၊ အရောင်များနှင့် လေးအောက်များကို စိတ်ကြိုက်ပြုလုပ်ပါ',
          content: `# Settings & Configuration - Complete Guide

## Overview
Master all system settings and configurations to customize BitsTech POS according to your business needs with practical examples and real-world scenarios.

## Practical Usage Examples

### Example 1: Company Information Setup
**Scenario**: Setting up "Golden Mart Convenience Store" business information

**Complete Setup Process**:
1. **Navigate to Settings**: Settings → Company Information
2. **Basic Business Details**:
   - Company Name: "Golden Mart Convenience Store"
   - Company Name (MM): "ရွှေစျေး အဆင်ပြေဆိုင်"
   - Business Type: "Retail Store"
   - Registration Number: "REG-2024-001234"
   - Tax ID: "TAX-*********"
3. **Contact Information**:
   - Address: "No. 123, Main Street, Yangon"
   - Phone: "+95 9 ***********"
   - Email: "<EMAIL>"
   - Website: "www.goldenmart.com"
4. **Logo Upload**:
   - Click "Upload Logo"
   - Select company logo file (JPG/PNG, max 2MB)
   - Preview and confirm
5. **Business Description**:
   - "Your neighborhood convenience store for daily essentials"
6. **Save Settings**: All information appears on receipts and invoices

**Result**: Professional business branding across all documents and system interfaces.

### Example 2: Tax & Currency Configuration
**Scenario**: Myanmar business with multiple currency support

**Currency Setup**:
1. **Primary Currency**: Myanmar Kyat (MMK)
2. **Secondary Currencies**:
   - US Dollar (USD) - for imported goods
   - Thai Baht (THB) - for border trade
3. **Exchange Rates** (updated daily):
   - 1 USD = 2,100 MMK
   - 1 THB = 60 MMK

**Tax Configuration**:
1. **Commercial Tax**: 5% (standard rate)
2. **Special Goods Tax**:
   - Alcohol: 15%
   - Tobacco: 20%
   - Luxury items: 10%
3. **Tax-exempt Items**:
   - Basic food items
   - Medicine
   - Educational materials

**Implementation Process**:
1. Go to Settings → Tax & Currency
2. Set MMK as primary currency
3. Add USD and THB with current rates
4. Configure tax rates by product category
5. Set up automatic tax calculation
6. Enable currency conversion in POS

**Usage Demonstration**: Customers can pay in any currency, system automatically converts and applies correct tax rates.

### Example 3: Receipt Template Customization
**Scenario**: Creating branded receipt for restaurant

**Template Design Process**:
1. **Header Section**:
   - Company logo (centered)
   - Restaurant name in large font
   - "Authentic Myanmar Cuisine"
   - Address and contact details
2. **Body Section**:
   - Table number and server name
   - Itemized order list
   - Quantity, price per item
   - Subtotal calculations
3. **Footer Section**:
   - Tax breakdown
   - Total amount
   - Payment method
   - "Thank you for dining with us!"
   - Social media QR codes

**Customization Steps**:
1. Settings → Receipt Templates
2. Select "Restaurant" template
3. Upload logo and set header text
4. Choose font sizes and alignment
5. Configure footer message
6. Add QR codes for Facebook/Instagram
7. Test print and adjust layout
8. Save as default template

**Real-world Application**: Professional receipts enhance brand image and customer experience.

### Example 4: User Management & Permissions
**Scenario**: Setting up role-based access for retail chain

**Role Structure**:
1. **Store Manager**:
   - Full store operations access
   - Staff management
   - Sales reports
   - Inventory management
   - Limited system settings

2. **Shift Supervisor**:
   - POS operations
   - Staff scheduling
   - Daily reports
   - Customer service
   - Basic inventory checks

3. **Cashier**:
   - POS transactions only
   - Customer interaction
   - Basic product lookup
   - No administrative access

4. **Stock Clerk**:
   - Inventory management
   - Receiving goods
   - Stock counting
   - No sales access

**Implementation Example**:
1. **Create Manager Account**:
   - Name: "Ko Thant"
   - Role: Store Manager
   - Permissions: All except system admin
   - Department: "Main Store"
   - Shift: "Day Shift (8 AM - 8 PM)"

2. **Create Cashier Account**:
   - Name: "Ma Aye"
   - Role: Cashier
   - Permissions: POS only
   - Department: "Front Counter"
   - Shift: "Morning (8 AM - 2 PM)"

**Usage Demonstration**: Each user sees only relevant features, improving security and reducing confusion.

### Example 5: Notification Settings Configuration
**Scenario**: Setting up alerts for efficient store management

**Alert Categories Setup**:
1. **Inventory Alerts**:
   - Low stock warning: When stock < 20 units
   - Out of stock alert: When stock = 0
   - Expiry date warning: 30 days before expiry
   - Overstock alert: When stock > maximum level

2. **Sales Alerts**:
   - Daily sales target: Alert if below 80% by 6 PM
   - High-value transaction: Alert for sales > 100,000 MMK
   - Return/refund: Immediate notification to manager
   - Payment failures: Real-time alerts

3. **System Alerts**:
   - Login attempts: Failed login notifications
   - Data backup: Daily backup completion status
   - System updates: Available update notifications
   - Hardware issues: Printer/scanner problems

**Configuration Process**:
1. Settings → Notifications
2. Enable relevant alert types
3. Set threshold values
4. Choose notification methods:
   - In-app notifications
   - Email alerts
   - SMS notifications (if configured)
5. Set recipient roles for each alert type
6. Configure notification timing

**Real-world Application**: Proactive management prevents stockouts, identifies issues early, and improves operational efficiency.

### Example 6: Appearance & Theme Customization
**Scenario**: Customizing system appearance for brand consistency

**Brand Color Scheme**:
- Primary Color: #1E40AF (Blue)
- Secondary Color: #059669 (Green)
- Accent Color: #DC2626 (Red)
- Background: Light theme with blue accents

**Customization Process**:
1. **Theme Selection**:
   - Settings → Appearance
   - Choose "Light" theme as base
   - Enable custom color scheme

2. **Color Configuration**:
   - Primary: Set to brand blue (#1E40AF)
   - Secondary: Set to brand green (#059669)
   - Accent: Set to brand red (#DC2626)
   - Preview changes in real-time

3. **Layout Preferences**:
   - Sidebar: Compact style
   - Header: Default height
   - Font size: Medium
   - Border radius: Rounded

4. **Accessibility Options**:
   - High contrast: Disabled
   - Color blind friendly: Enabled
   - Reduced motion: Disabled

**Result**: System interface matches brand colors, improving visual consistency and professional appearance.

## Advanced Configuration Examples

### Multi-Store Setup
**Scenario**: Managing 3 store locations

**Configuration**:
1. **Store Profiles**:
   - Main Store: Downtown location
   - Branch 1: Shopping mall
   - Branch 2: Residential area

2. **Inventory Management**:
   - Centralized product catalog
   - Location-specific stock levels
   - Inter-store transfers
   - Consolidated reporting

3. **User Access**:
   - Store-specific user accounts
   - Cross-store manager access
   - Centralized admin control

### Integration Settings
**Payment Gateway Setup**:
1. Configure Stripe for card payments
2. Set up local payment methods
3. Enable mobile payment options
4. Configure payment reconciliation

**Email Configuration**:
1. SMTP server setup
2. Email templates for receipts
3. Automated notifications
4. Marketing email integration

### Backup & Security
**Data Protection Setup**:
1. **Automated Backups**:
   - Daily database backups
   - Cloud storage integration
   - Backup verification
   - Restore procedures

2. **Security Settings**:
   - Password policies
   - Session timeouts
   - Two-factor authentication
   - Access logging

3. **Compliance Configuration**:
   - Tax reporting setup
   - Audit trail maintenance
   - Data retention policies
   - Privacy settings

## Best Practices

### Regular Maintenance
1. **Weekly Tasks**:
   - Review user access logs
   - Check backup status
   - Update exchange rates
   - Review notification settings

2. **Monthly Tasks**:
   - User permission audit
   - System performance review
   - Security update installation
   - Configuration backup

3. **Quarterly Tasks**:
   - Complete system review
   - User training updates
   - Hardware maintenance
   - Compliance verification

### Performance Optimization
1. **Database Maintenance**:
   - Regular cleanup of old data
   - Index optimization
   - Query performance monitoring
   - Storage space management

2. **User Experience**:
   - Interface customization based on feedback
   - Workflow optimization
   - Training material updates
   - Feature usage analysis`,
          contentLocal: `# ဆက်တင်များနှင့် ပြင်ဆင်ခြင်း - အပြည့်အစုံ လမ်းညွှန်

## ခြုံငုံသုံးသပ်ချက်
လက်တွေ့ ဥပမာများနှင့် လက်တွေ့ အခြေအနေများဖြင့် သင့်လုပ်ငန်း လိုအပ်ချက်များအတိုင်း BitsTech POS ကို စိတ်ကြိုက်ပြုလုပ်ရန် စနစ် ဆက်တင်များနှင့် ပြင်ဆင်ခြင်းများ အားလုံးကို ကျွမ်းကျင်စွာ လုပ်ဆောင်ပါ။

## လက်တွေ့ အသုံးပြုပုံ ဥပမာများ

### ဥပမာ ၁: ကုမ္ပဏီ အချက်အလက် ပြင်ဆင်ခြင်း
**အခြေအနေ**: "ရွှေစျေး အဆင်ပြေဆိုင်" လုပ်ငန်း အချက်အလက် ပြင်ဆင်ခြင်း

**အပြည့်အစုံ ပြင်ဆင်မှု လုပ်ငန်းစဉ်**:
1. **ဆက်တင်များသို့ သွားခြင်း**: Settings → Company Information
2. **အခြေခံ လုပ်ငန်း အသေးစိတ်များ**:
   - ကုမ္ပဏီ အမည်: "Golden Mart Convenience Store"
   - ကုမ္ပဏီ အမည် (မြန်မာ): "ရွှေစျေး အဆင်ပြေဆိုင်"
   - လုပ်ငန်း အမျိုးအစား: "လက်လီ ဆိုင်"
   - မှတ်ပုံတင် နံပါတ်: "REG-2024-001234"
   - အခွန် ID: "TAX-*********"
3. **ဆက်သွယ်ရေး အချက်အလက်များ**:
   - လိပ်စာ: "နံပါတ် ၁၂၃၊ ပင်မလမ်း၊ ရန်ကုန်"
   - ဖုန်း: "+95 9 ***********"
   - အီးမေးလ်: "<EMAIL>"
   - ဝက်ဘ်ဆိုက်: "www.goldenmart.com"
4. **လိုဂို တင်ခြင်း**:
   - "Upload Logo" နှိပ်ပါ
   - ကုမ္ပဏီ လိုဂို ဖိုင် ရွေးပါ (JPG/PNG, အများဆုံး 2MB)
   - အစမ်းကြည့်ပြီး အတည်ပြုပါ
5. **လုပ်ငန်း ဖော်ပြချက်**:
   - "သင့်ရပ်ကွက်ရှိ နေ့စဉ် လိုအပ်သော ပစ္စည်းများအတွက် အဆင်ပြေဆိုင်"
6. **ဆက်တင်များ သိမ်းဆည်းခြင်း**: အချက်အလက် အားလုံး ဘောင်ချာများနှင့် ငွေတောင်းခံလွှာများတွင် ပေါ်လာသည်

**ရလဒ်**: စနစ် interface များနှင့် စာရွက်စာတမ်း အားလုံးတွင် ပရော်ဖက်ရှင်နယ် လုပ်ငန်း အမှတ်တံဆိပ်။

### ဥပမာ ၂: အခွန်နှင့် ငွေကြေး ပြင်ဆင်ခြင်း
**အခြေအနေ**: ငွေကြေး အများအပြား ပံ့ပိုးမှုရှိသော မြန်မာ လုပ်ငန်း

**ငွေကြေး ပြင်ဆင်ခြင်း**:
1. **အဓိက ငွေကြေး**: မြန်မာ ကျပ် (MMK)
2. **ဒုတိယ ငွေကြေးများ**:
   - အမေရိကန် ဒေါ်လာ (USD) - တင်သွင်းကုန်များအတွက်
   - ထိုင်း ဘတ် (THB) - နယ်စပ် ကုန်သွယ်မှုအတွက်
3. **လဲလှယ်နှုန်းများ** (နေ့စဉ် အပ်ဒိတ်):
   - 1 USD = 2,100 MMK
   - 1 THB = 60 MMK

**အခွန် ပြင်ဆင်ခြင်း**:
1. **ကူးသန်းရောင်းဝယ်ရေး အခွန်**: 5% (ပုံမှန် နှုန်း)
2. **အထူး ကုန်ပစ္စည်း အခွန်**:
   - အရက်: 15%
   - ဆေးလိပ်: 20%
   - ဇိမ်ခံ ပစ္စည်းများ: 10%
3. **အခွန် ကင်းလွတ် ပစ္စည်းများ**:
   - အခြေခံ အစားအသောက်များ
   - ဆေးဝါးများ
   - ပညာရေး ပစ္စည်းများ

**အကောင်အထည်ဖော် လုပ်ငန်းစဉ်**:
1. Settings → Tax & Currency သို့ သွားပါ
2. MMK ကို အဓိက ငွေကြေးအဖြစ် သတ်မှတ်ပါ
3. လက်ရှိ နှုန်းများဖြင့် USD နှင့် THB ထည့်ပါ
4. ကုန်ပစ္စည်း အမျိုးအစားအလိုက် အခွန်နှုန်းများ ပြင်ဆင်ပါ
5. အလိုအလျောက် အခွန် တွက်ချက်မှု သတ်မှတ်ပါ
6. POS တွင် ငွေကြေး ပြောင်းလဲမှု ဖွင့်ပါ

**အသုံးပြုပုံ သရုပ်ပြမှု**: ဖောက်သည်များ မည်သည့် ငွေကြေးဖြင့်မဆို ပေးချေနိုင်၊ စနစ်က အလိုအလျောက် ပြောင်းလဲပြီး မှန်ကန်သော အခွန်နှုန်းများ ကျင့်သုံးသည်။`,
          tags: ['appearance', 'themes', 'customization'],
          difficulty: 'intermediate',
          estimatedTime: '25 minutes',
          lastUpdated: '2024-01-15'
        }
      ]
    }
  ]

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  const filteredSections = documentationSections.filter(section => {
    if (selectedCategory !== 'all' && section.category !== selectedCategory) {
      return false
    }
    
    if (searchQuery) {
      const query = searchQuery.toLowerCase()
      const titleMatch = (language === 'mm' ? section.titleLocal : section.title).toLowerCase().includes(query)
      const descMatch = (language === 'mm' ? section.descriptionLocal : section.description).toLowerCase().includes(query)
      const articleMatch = section.articles.some(article => 
        (language === 'mm' ? article.titleLocal : article.title).toLowerCase().includes(query) ||
        (language === 'mm' ? article.descriptionLocal : article.description).toLowerCase().includes(query) ||
        article.tags.some(tag => tag.toLowerCase().includes(query))
      )
      return titleMatch || descMatch || articleMatch
    }
    
    return true
  })

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'beginner': return 'bg-green-100 text-green-700 dark:bg-green-900/20 dark:text-green-400'
      case 'intermediate': return 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/20 dark:text-yellow-400'
      case 'advanced': return 'bg-red-100 text-red-700 dark:bg-red-900/20 dark:text-red-400'
      default: return 'bg-gray-100 text-gray-700 dark:bg-gray-900/20 dark:text-gray-400'
    }
  }

  if (isLoading) {
    return (
      <MainLayout>
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </MainLayout>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 to-indigo-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <BookOpen className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {language === 'mm' ? 'စနစ် လမ်းညွှန်' : 'System Documentation'}
                </h1>
                <p className="text-white/80 mt-1">
                  {language === 'mm'
                    ? 'BitsTech POS စနစ်အတွက် အပြည့်အစုံ လမ်းညွှန်နှင့် အကူအညီများ'
                    : 'Complete guides and help for BitsTech POS system'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Search and Filters */}
        <div className="flex flex-col sm:flex-row gap-4">
          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
            <Input
              placeholder={language === 'mm' ? 'လမ်းညွှန်များ ရှာရန်...' : 'Search documentation...'}
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={selectedCategory === 'all' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('all')}
            >
              {language === 'mm' ? 'အားလုံး' : 'All'}
            </Button>
            <Button
              variant={selectedCategory === 'getting-started' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('getting-started')}
            >
              {language === 'mm' ? 'စတင်ခြင်း' : 'Getting Started'}
            </Button>
            <Button
              variant={selectedCategory === 'features' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('features')}
            >
              {language === 'mm' ? 'လုပ်ဆောင်ချက်များ' : 'Features'}
            </Button>
            <Button
              variant={selectedCategory === 'advanced' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedCategory('advanced')}
            >
              {language === 'mm' ? 'အဆင့်မြင့်' : 'Advanced'}
            </Button>
          </div>
        </div>

        {/* System Diagrams Section */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Database className="h-5 w-5 text-blue-600" />
              {language === 'mm' ? 'စနစ် ဗိသုကာ နှင့် လုပ်ငန်းစဉ်' : 'System Architecture & Flow'}
            </CardTitle>
            <p className="text-gray-600 dark:text-gray-400">
              {language === 'mm'
                ? 'BitsTech POS စနစ်၏ နည်းပညာ ဖွဲ့စည်းပုံ နှင့် လုပ်ငန်းစဉ် ပုံကြမ်းများ'
                : 'Technical structure and workflow diagrams of BitsTech POS system'
              }
            </p>
          </CardHeader>
          <CardContent className="space-y-8">
            <div>
              <h4 className="font-medium mb-4 text-blue-600 flex items-center gap-2">
                <Database className="h-4 w-4" />
                {language === 'mm' ? 'စနစ် ဗိသုကာ' : 'System Architecture'}
              </h4>
              <SystemArchitecture language={language as 'en' | 'mm'} />
            </div>

            <div>
              <h4 className="font-medium mb-4 text-green-600 flex items-center gap-2">
                <BarChart3 className="h-4 w-4" />
                {language === 'mm' ? 'စနစ် လုပ်ငန်းစဉ်' : 'System Flow'}
              </h4>
              <SystemFlow language={language as 'en' | 'mm'} />
            </div>
          </CardContent>
        </Card>

        {/* Documentation Sections */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {filteredSections.map((section) => (
            <Card key={section.id} className="hover:shadow-lg transition-all duration-300">
              <CardHeader>
                <CardTitle className="flex items-center gap-3">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                    {section.icon}
                  </div>
                  {language === 'mm' ? section.titleLocal : section.title}
                </CardTitle>
                <CardDescription>
                  {language === 'mm' ? section.descriptionLocal : section.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {section.articles.map((article) => (
                    <div
                      key={article.id}
                      className="p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 cursor-pointer transition-colors"
                      onClick={() => setSelectedArticle(article)}
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <h4 className="font-medium text-sm mb-1">
                            {language === 'mm' ? article.titleLocal : article.title}
                          </h4>
                          <p className="text-xs text-gray-600 dark:text-gray-400 mb-2">
                            {language === 'mm' ? article.descriptionLocal : article.description}
                          </p>
                          <div className="flex items-center gap-2 flex-wrap">
                            <Badge className={`text-xs ${getDifficultyColor(article.difficulty)}`}>
                              {article.difficulty}
                            </Badge>
                            <span className="text-xs text-gray-500">
                              {article.estimatedTime}
                            </span>
                            {article.tags.slice(0, 2).map((tag) => (
                              <Badge key={tag} variant="outline" className="text-xs">
                                {tag}
                              </Badge>
                            ))}
                          </div>
                        </div>
                        <ChevronRight className="h-4 w-4 text-gray-400 ml-2 flex-shrink-0" />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Help Section */}
        <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 border-purple-200 dark:border-purple-800">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-purple-700 dark:text-purple-300">
              <HelpCircle className="h-5 w-5" />
              {language === 'mm' ? 'အမြန် အကူအညီ' : 'Quick Help'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <Button variant="outline" className="flex items-center gap-2 h-auto p-4">
                <Video className="h-5 w-5 text-blue-600" />
                <div className="text-left">
                  <div className="font-medium text-sm">
                    {language === 'mm' ? 'ဗီဒီယို လမ်းညွှန်များ' : 'Video Tutorials'}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'အမြင်အာရုံ လမ်းညွှန်များ' : 'Visual step-by-step guides'}
                  </div>
                </div>
              </Button>
              
              <Button variant="outline" className="flex items-center gap-2 h-auto p-4">
                <MessageCircle className="h-5 w-5 text-green-600" />
                <div className="text-left">
                  <div className="font-medium text-sm">
                    {language === 'mm' ? 'လူမှုကွန်ယက် အကူအညီ' : 'Community Support'}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'အသုံးပြုသူများ အကူအညီ' : 'Get help from other users'}
                  </div>
                </div>
              </Button>
              
              <Button variant="outline" className="flex items-center gap-2 h-auto p-4">
                <Mail className="h-5 w-5 text-orange-600" />
                <div className="text-left">
                  <div className="font-medium text-sm">
                    {language === 'mm' ? 'အီးမေးလ် အကူအညီ' : 'Email Support'}
                  </div>
                  <div className="text-xs text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'တိုက်ရိုက် အကူအညီ ရယူပါ' : 'Get direct assistance'}
                  </div>
                </div>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Article Modal */}
      {selectedArticle && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-900 rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-start justify-between">
                <div>
                  <h2 className="text-2xl font-bold mb-2">
                    {language === 'mm' ? selectedArticle.titleLocal : selectedArticle.title}
                  </h2>
                  <p className="text-gray-600 dark:text-gray-400 mb-4">
                    {language === 'mm' ? selectedArticle.descriptionLocal : selectedArticle.description}
                  </p>
                  <div className="flex items-center gap-3">
                    <Badge className={getDifficultyColor(selectedArticle.difficulty)}>
                      {selectedArticle.difficulty}
                    </Badge>
                    <span className="text-sm text-gray-500">
                      {selectedArticle.estimatedTime}
                    </span>
                    <span className="text-sm text-gray-500">
                      {language === 'mm' ? 'နောက်ဆုံး ပြင်ဆင်ခဲ့သည်' : 'Last updated'}: {selectedArticle.lastUpdated}
                    </span>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedArticle(null)}
                  className="text-gray-500 hover:text-gray-700"
                >
                  ✕
                </Button>
              </div>
            </div>
            
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              <div className="prose dark:prose-invert max-w-none">
                <p className="whitespace-pre-line">
                  {language === 'mm' ? selectedArticle.contentLocal : selectedArticle.content}
                </p>
              </div>
              
              <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
                <div className="flex flex-wrap gap-2">
                  {selectedArticle.tags.map((tag) => (
                    <Badge key={tag} variant="outline">
                      {tag}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </MainLayout>
  )
}
