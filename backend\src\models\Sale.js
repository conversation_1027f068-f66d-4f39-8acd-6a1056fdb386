const mongoose = require('mongoose');

const SaleItemSchema = new mongoose.Schema({
    product: {
        type: mongoose.Schema.ObjectId,
        ref: 'Product',
        required: true
    },
    productName: {
        type: String,
        required: true
    },
    sku: {
        type: String,
        required: true
    },
    quantity: {
        type: Number,
        required: true,
        min: [1, 'Quantity must be at least 1']
    },
    unitPrice: {
        type: Number,
        required: true,
        min: [0, 'Unit price cannot be negative']
    },
    totalPrice: {
        type: Number,
        required: true,
        min: [0, 'Total price cannot be negative']
    },
    discount: {
        type: Number,
        default: 0,
        min: [0, 'Discount cannot be negative']
    },
    tax: {
        type: Number,
        default: 0,
        min: [0, 'Tax cannot be negative']
    }
});

const SaleSchema = new mongoose.Schema({
    saleNumber: {
        type: String,
        required: true,
        unique: true
    },
    cashier: {
        type: mongoose.Schema.ObjectId,
        ref: 'User',
        required: true
    },
    customer: {
        name: String,
        email: String,
        phone: String,
        address: String
    },
    items: [SaleItemSchema],
    subtotal: {
        type: Number,
        required: true,
        min: [0, 'Subtotal cannot be negative']
    },
    totalDiscount: {
        type: Number,
        default: 0,
        min: [0, 'Total discount cannot be negative']
    },
    totalTax: {
        type: Number,
        default: 0,
        min: [0, 'Total tax cannot be negative']
    },
    totalAmount: {
        type: Number,
        required: true,
        min: [0, 'Total amount cannot be negative']
    },
    currency: {
        type: String,
        enum: ['MMK', 'THB', 'USD'],
        default: 'MMK'
    },
    paymentMethod: {
        type: String,
        enum: ['cash', 'card', 'kbz', 'wave', 'nug', 'bank', 'digital', 'bank_transfer', 'other'],
        required: true
    },
    paymentDetails: {
        cardType: String,
        cardLast4: String,
        transactionId: String,
        reference: String
    },
    amountPaid: {
        type: Number,
        required: true,
        min: [0, 'Amount paid cannot be negative']
    },
    changeAmount: {
        type: Number,
        default: 0,
        min: [0, 'Change amount cannot be negative']
    },
    status: {
        type: String,
        enum: ['pending', 'completed', 'cancelled', 'refunded'],
        default: 'completed'
    },
    notes: {
        type: String,
        maxlength: [500, 'Notes cannot be more than 500 characters']
    },
    receipt: {
        printed: {
            type: Boolean,
            default: false
        },
        emailed: {
            type: Boolean,
            default: false
        },
        number: String
    },
    refund: {
        isRefunded: {
            type: Boolean,
            default: false
        },
        refundAmount: {
            type: Number,
            default: 0
        },
        refundDate: Date,
        refundReason: String,
        refundedBy: {
            type: mongoose.Schema.ObjectId,
            ref: 'User'
        }
    }
}, {
    timestamps: true
});

// Generate sale number
SaleSchema.pre('save', async function(next) {
    if (this.isNew) {
        const date = new Date();
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        
        // Find the last sale of the day
        const lastSale = await this.constructor.findOne({
            saleNumber: new RegExp(`^${year}${month}${day}`)
        }).sort({ saleNumber: -1 });
        
        let sequence = 1;
        if (lastSale) {
            const lastSequence = parseInt(lastSale.saleNumber.slice(-4));
            sequence = lastSequence + 1;
        }
        
        this.saleNumber = `${year}${month}${day}${String(sequence).padStart(4, '0')}`;
    }
    next();
});

// Virtual for total items
SaleSchema.virtual('totalItems').get(function() {
    return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Ensure virtual fields are serialized
SaleSchema.set('toJSON', {
    virtuals: true
});

module.exports = mongoose.model('Sale', SaleSchema);
