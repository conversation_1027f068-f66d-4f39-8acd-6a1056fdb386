'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { useAuth } from '@/contexts/auth-context'
import { useSettings, useTranslation } from '@/contexts/settings-context'
import { SettingsSearch } from '@/components/settings/settings-search'
import {
  Settings,
  Building2,
  CreditCard,
  Receipt,
  Users,
  Palette,
  Database,
  Shield,
  Bell,
  Globe,
  ChevronRight,
  Sparkles,
  Zap,
  Star,
  Search,
  Command,
  BookOpen,
  Home,
  FileText
} from 'lucide-react'

interface SettingCategory {
  id: string
  title: string
  titleMM: string
  description: string
  descriptionMM: string
  icon: any
  href: string
  color: string
  gradient: string
  isNew?: boolean
  isPro?: boolean
}

export default function SettingsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const { t } = useTranslation()
  const router = useRouter()
  const [searchOpen, setSearchOpen] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Keyboard shortcut for search
  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleKeyDown = (e: KeyboardEvent) => {
      if ((e.metaKey || e.ctrlKey) && e.key === 'k') {
        e.preventDefault()
        setSearchOpen(true)
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [])

  const settingCategories: SettingCategory[] = [
    {
      id: 'homepage-content',
      title: 'Homepage Content',
      titleMM: 'ပင်မစာမျက်နှာ အကြောင်းအရာ',
      description: 'Edit homepage text, titles, descriptions, and feature content',
      descriptionMM: 'ပင်မစာမျက်နှာရှိ စာသားများ၊ ခေါင်းစဉ်များ၊ ဖော်ပြချက်များ နှင့် လုပ်ဆောင်ချက် အကြောင်းအရာများ ပြင်ဆင်ပါ',
      icon: Home,
      href: '/settings/homepage-content',
      color: 'text-emerald-600',
      gradient: 'from-emerald-500 to-teal-500',
      isNew: true
    },
    {
      id: 'company',
      title: 'Company Information',
      titleMM: 'ကုမ္ပဏီ အချက်အလက်များ',
      description: 'Manage your business details, logo, and contact information',
      descriptionMM: 'သင့်လုပ်ငန်း အသေးစိတ်များ၊ လိုဂို နှင့် ဆက်သွယ်ရေး အချက်အလက်များ စီမံခန့်ခွဲပါ',
      icon: Building2,
      href: '/settings/company',
      color: 'text-blue-600',
      gradient: 'from-blue-500 to-cyan-500'
    },
    {
      id: 'tax-currency',
      title: 'Tax & Currency',
      titleMM: 'အခွန် နှင့် ငွေကြေး',
      description: 'Configure tax rates, currency settings, and pricing rules',
      descriptionMM: 'အခွန်နှုန်း၊ ငွေကြေး ဆက်တင်များ နှင့် စျေးနှုန်း စည်းမျဉ်းများ ပြင်ဆင်ပါ',
      icon: CreditCard,
      href: '/settings/tax-currency',
      color: 'text-green-600',
      gradient: 'from-green-500 to-emerald-500'
    },
    {
      id: 'receipts',
      title: 'Receipt Templates',
      titleMM: 'ဘောက်ချာ ပုံစံများ',
      description: 'Customize receipt layouts, add your branding, and set print options',
      descriptionMM: 'ဘောက်ချာ ပုံစံများ ပြင်ဆင်ပြီး သင့်အမှတ်တံဆိပ် ထည့်သွင်းပါ',
      icon: Receipt,
      href: '/settings/receipts',
      color: 'text-purple-600',
      gradient: 'from-purple-500 to-pink-500',
      isNew: true
    },
    {
      id: 'users',
      title: 'User Management',
      titleMM: 'အသုံးပြုသူ စီမံခန့်ခွဲမှု',
      description: 'Manage user accounts, roles, and permissions',
      descriptionMM: 'အသုံးပြုသူ အကောင့်များ၊ အခန်းကဏ္ဍများ နှင့် ခွင့်ပြုချက်များ စီမံခန့်ခွဲပါ',
      icon: Users,
      href: '/settings/users',
      color: 'text-orange-600',
      gradient: 'from-orange-500 to-red-500'
    },
    {
      id: 'appearance',
      title: 'Appearance & Theme',
      titleMM: 'အသွင်အပြင် နှင့် အပြင်အဆင်',
      description: 'Customize colors, themes, and interface preferences',
      descriptionMM: 'အရောင်များ၊ အပြင်အဆင်များ နှင့် မျက်နှာပြင် ရွေးချယ်မှုများ ပြင်ဆင်ပါ',
      icon: Palette,
      href: '/settings/appearance',
      color: 'text-pink-600',
      gradient: 'from-pink-500 to-rose-500'
    },
    {
      id: 'notifications',
      title: 'Notifications',
      titleMM: 'အကြောင်းကြားချက်များ',
      description: 'Configure alerts, email notifications, and system messages',
      descriptionMM: 'သတိပေးချက်များ၊ အီးမေးလ် အကြောင်းကြားချက်များ နှင့် စနစ် မက်ဆေ့ချ်များ ပြင်ဆင်ပါ',
      icon: Bell,
      href: '/settings/notifications',
      color: 'text-yellow-600',
      gradient: 'from-yellow-500 to-orange-500',
      isNew: true
    },
    {
      id: 'localization',
      title: 'Language & Region',
      titleMM: 'ဘာသာစကား နှင့် ဒေသ',
      description: 'Set language preferences, date formats, and regional settings',
      descriptionMM: 'ဘာသာစကား ရွေးချယ်မှုများ၊ ရက်စွဲ ပုံစံများ နှင့် ဒေသဆိုင်ရာ ဆက်တင်များ သတ်မှတ်ပါ',
      icon: Globe,
      href: '/settings/localization',
      color: 'text-indigo-600',
      gradient: 'from-indigo-500 to-blue-500'
    },
    {
      id: 'security',
      title: 'Security & Privacy',
      titleMM: 'လုံခြုံရေး နှင့် ကိုယ်ရေးကိုယ်တာ',
      description: 'Manage security settings, backup options, and data privacy',
      descriptionMM: 'လုံခြုံရေး ဆက်တင်များ၊ အရန်သိမ်းမှု ရွေးချယ်မှုများ နှင့် ဒေတာ ကိုယ်ရေးကိုယ်တာ စီမံခန့်ခွဲပါ',
      icon: Shield,
      href: '/settings/security',
      color: 'text-red-600',
      gradient: 'from-red-500 to-pink-500',
      isPro: true
    },
    {
      id: 'database',
      title: 'Data Management',
      titleMM: 'ဒေတာ စီမံခန့်ခွဲမှု',
      description: 'Backup, restore, import/export data, and system maintenance',
      descriptionMM: 'အရန်သိမ်းမှု၊ ပြန်လည်ရယူမှု၊ ဒေတာ တင်သွင်း/ထုတ်ယူမှု နှင့် စနစ် ပြုပြင်ထိန်းသိမ်းမှု',
      icon: Database,
      href: '/settings/data-management',
      color: 'text-teal-600',
      gradient: 'from-teal-500 to-cyan-500',
      isPro: true
    },
    {
      id: 'footer',
      title: 'Footer Settings',
      titleMM: 'Footer ဆက်တင်များ',
      description: 'Customize footer content and appearance',
      descriptionMM: 'Footer အကြောင်းအရာနှင့် အပြင်အဆင် စိတ်ကြိုက်ပြုလုပ်ပါ',
      icon: Building2,
      href: '/settings/footer',
      color: 'text-orange-600',
      gradient: 'from-orange-500 to-red-500',
      isNew: true
    },
    {
      id: 'documentation',
      title: 'Documentation & Help',
      titleMM: 'စနစ် လမ်းညွှန် နှင့် အကူအညီ',
      description: 'System guides, tutorials, and help resources',
      descriptionMM: 'စနစ် လမ်းညွှန်များ၊ သင်ခန်းစာများ နှင့် အကူအညီ အရင်းအမြစ်များ',
      icon: BookOpen,
      href: '/settings/documentation',
      color: 'text-blue-600',
      gradient: 'from-blue-500 to-indigo-500',
      isNew: true
    }
  ]

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-8">
        {/* Header with Glassmorphism Effect */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 via-purple-600 to-pink-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4 mb-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Settings className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-4xl font-bold">
                  {language === 'mm' ? 'ဆက်တင်များ' : 'Settings'}
                </h1>
                <p className="text-white/80 text-lg mt-1">
                  {language === 'mm'
                    ? 'သင့် BitsTech POS စနစ်ကို ပြင်ဆင်ပြီး စိတ်ကြိုက်ပြုလုပ်ပါ'
                    : 'Configure and customize your BitsTech POS system'
                  }
                </p>
              </div>
            </div>

            {/* Search Bar */}
            <div className="mt-6">
              <div className="relative max-w-md">
                <Button
                  variant="outline"
                  onClick={() => setSearchOpen(true)}
                  className="w-full justify-start bg-white/10 backdrop-blur-sm border-white/20 text-white hover:bg-white/20 transition-all duration-300"
                >
                  <Search className="h-4 w-4 mr-3" />
                  <span className="text-white/80">
                    {language === 'mm' ? 'ဆက်တင်များ ရှာဖွေရန်...' : 'Search settings...'}
                  </span>
                  <div className="ml-auto flex items-center gap-1 text-xs text-white/60">
                    <Command className="h-3 w-3" />
                    <span>K</span>
                  </div>
                </Button>
              </div>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center gap-3">
                  <Sparkles className="h-5 w-5 text-yellow-300" />
                  <div>
                    <div className="text-sm text-white/70">
                      {language === 'mm' ? 'စနစ် အခြေအနေ' : 'System Status'}
                    </div>
                    <div className="font-semibold text-green-300">
                      {language === 'mm' ? 'ကောင်းမွန်' : 'Excellent'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center gap-3">
                  <Zap className="h-5 w-5 text-blue-300" />
                  <div>
                    <div className="text-sm text-white/70">
                      {language === 'mm' ? 'နောက်ဆုံး အပ်ဒိတ်' : 'Last Updated'}
                    </div>
                    <div className="font-semibold">
                      {language === 'mm' ? 'ယနေ့' : 'Today'}
                    </div>
                  </div>
                </div>
              </div>

              <div className="bg-white/10 backdrop-blur-sm rounded-xl p-4">
                <div className="flex items-center gap-3">
                  <Star className="h-5 w-5 text-purple-300" />
                  <div>
                    <div className="text-sm text-white/70">
                      {language === 'mm' ? 'ဗားရှင်း' : 'Version'}
                    </div>
                    <div className="font-semibold">v2.1.0</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Settings Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {settingCategories.map((category) => {
            const Icon = category.icon
            return (
              <Card
                key={category.id}
                className="group hover:shadow-2xl transition-all duration-300 hover:-translate-y-1 cursor-pointer border-0 bg-gradient-to-br from-white to-gray-50 dark:from-gray-800 dark:to-gray-900 overflow-hidden relative"
                onClick={() => router.push(category.href)}
              >
                {/* Gradient Background */}
                <div className={`absolute inset-0 bg-gradient-to-br ${category.gradient} opacity-0 group-hover:opacity-10 transition-opacity duration-300`}></div>

                <CardHeader className="relative z-10">
                  <div className="flex items-start justify-between">
                    <div className={`p-3 rounded-xl bg-gradient-to-br ${category.gradient} text-white shadow-lg`}>
                      <Icon className="h-6 w-6" />
                    </div>
                    <div className="flex gap-2">
                      {category.isNew && (
                        <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs">
                          {language === 'mm' ? 'အသစ်' : 'New'}
                        </Badge>
                      )}
                      {category.isPro && (
                        <Badge variant="secondary" className="bg-purple-100 text-purple-700 text-xs">
                          {language === 'mm' ? 'ပရို' : 'Pro'}
                        </Badge>
                      )}
                    </div>
                  </div>

                  <CardTitle className="text-xl group-hover:text-transparent group-hover:bg-clip-text group-hover:bg-gradient-to-r group-hover:from-blue-600 group-hover:to-purple-600 transition-all duration-300">
                    {language === 'mm' ? category.titleMM : category.title}
                  </CardTitle>

                  <CardDescription className="text-gray-600 dark:text-gray-400 leading-relaxed">
                    {language === 'mm' ? category.descriptionMM : category.description}
                  </CardDescription>
                </CardHeader>

                <CardContent className="relative z-10">
                  <Button
                    variant="ghost"
                    className="w-full justify-between group-hover:bg-white/50 dark:group-hover:bg-gray-800/50 transition-colors duration-300"
                  >
                    <span>{language === 'mm' ? 'ပြင်ဆင်ရန်' : 'Configure'}</span>
                    <ChevronRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                  </Button>
                </CardContent>
              </Card>
            )
          })}
        </div>

        {/* Settings Search Modal */}
        <SettingsSearch isOpen={searchOpen} onClose={() => setSearchOpen(false)} />
      </div>
    </MainLayout>
  )
}
