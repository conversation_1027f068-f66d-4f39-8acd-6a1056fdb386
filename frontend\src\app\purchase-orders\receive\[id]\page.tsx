'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import {
  Package,
  ArrowLeft,
  Scan,
  CheckCircle,
  AlertTriangle,
  Save,
  Camera,
  FileText,
  User,
  Calendar,
  MapPin,
  Phone,
  Mail,
  Truck,
  Clock,
  Plus,
  Minus,
  X
} from 'lucide-react'

interface POItem {
  _id: string
  productName: string
  sku: string
  quantity: number
  receivedQuantity: number
  pendingQuantity: number
  unitCost: number
  totalCost: number
  condition: 'good' | 'damaged' | 'defective'
  notes: string
  receivingQuantity: number
}

interface PurchaseOrder {
  _id: string
  orderNumber: string
  supplier: {
    name: string
    code: string
    contactPerson: {
      name: string
      email?: string
      phone?: string
    }
    address?: string
  }
  orderDate: string
  expectedDeliveryDate: string
  status: string
  priority: string
  totalItems: number
  totalAmount: number
  currency: string
  completionPercentage: number
  items: POItem[]
}

export default function ReceivePOPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const poId = params.id as string

  const [purchaseOrder, setPurchaseOrder] = useState<PurchaseOrder | null>(null)
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [deliveryNotes, setDeliveryNotes] = useState('')
  const [receivedBy, setReceivedBy] = useState('')
  const [deliveryDate, setDeliveryDate] = useState(new Date().toISOString().split('T')[0])

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated && poId) {
      fetchPurchaseOrder()
    }
  }, [isAuthenticated, poId])

  const fetchPurchaseOrder = async () => {
    try {
      setLoading(true)

      // Mock data for the specific PO
      const mockPO: PurchaseOrder = {
        _id: poId,
        orderNumber: 'PO-240102-002',
        supplier: {
          name: 'Logitech Myanmar',
          code: 'SUP-LOG-002',
          contactPerson: {
            name: 'Mary Johnson',
            email: '<EMAIL>',
            phone: '+95-1-345678'
          },
          address: '123 Business Street, Yangon, Myanmar'
        },
        orderDate: '2024-01-02T14:30:00Z',
        expectedDeliveryDate: '2024-01-10T14:30:00Z',
        status: 'partially_received',
        priority: 'high',
        totalItems: 50,
        totalAmount: 7200000,
        currency: 'MMK',
        completionPercentage: 60,
        items: [
          {
            _id: '3',
            productName: 'Logitech MX Master 3S',
            sku: 'MOU001',
            quantity: 30,
            receivedQuantity: 18,
            pendingQuantity: 12,
            unitCost: 120000,
            totalCost: 3600000,
            condition: 'good',
            notes: '',
            receivingQuantity: 0
          },
          {
            _id: '4',
            productName: 'Logitech MX Keys',
            sku: 'KEY001',
            quantity: 20,
            receivedQuantity: 12,
            pendingQuantity: 8,
            unitCost: 180000,
            totalCost: 3600000,
            condition: 'good',
            notes: '',
            receivingQuantity: 0
          }
        ]
      }

      setPurchaseOrder(mockPO)
      setReceivedBy(`${user?.firstName} ${user?.lastName}`)
    } catch (error) {
      console.error('Error fetching purchase order:', error)
    } finally {
      setLoading(false)
    }
  }

  const updateItemReceivingQuantity = (itemId: string, quantity: number) => {
    if (!purchaseOrder) return

    setPurchaseOrder(prev => ({
      ...prev!,
      items: prev!.items.map(item =>
        item._id === itemId
          ? { ...item, receivingQuantity: Math.max(0, Math.min(quantity, item.pendingQuantity)) }
          : item
      )
    }))
  }

  const updateItemCondition = (itemId: string, condition: 'good' | 'damaged' | 'defective') => {
    if (!purchaseOrder) return

    setPurchaseOrder(prev => ({
      ...prev!,
      items: prev!.items.map(item =>
        item._id === itemId ? { ...item, condition } : item
      )
    }))
  }

  const updateItemNotes = (itemId: string, notes: string) => {
    if (!purchaseOrder) return

    setPurchaseOrder(prev => ({
      ...prev!,
      items: prev!.items.map(item =>
        item._id === itemId ? { ...item, notes } : item
      )
    }))
  }

  const handleReceiveAll = () => {
    if (!purchaseOrder) return

    setPurchaseOrder(prev => ({
      ...prev!,
      items: prev!.items.map(item => ({
        ...item,
        receivingQuantity: item.pendingQuantity
      }))
    }))
  }

  const handleSaveReceiving = async () => {
    if (!purchaseOrder) return

    setSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Calculate totals
      const totalReceiving = purchaseOrder.items.reduce((sum, item) => sum + item.receivingQuantity, 0)

      if (totalReceiving === 0) {
        alert('Please specify quantities to receive')
        return
      }

      alert(`Successfully received ${totalReceiving} items!`)
      router.push('/purchase-orders/receive')
    } catch (error) {
      console.error('Error saving receiving:', error)
      alert('Error saving receiving data')
    } finally {
      setSaving(false)
    }
  }

  const formatPrice = (price: number, currency: string) => {
    return `${price.toLocaleString()} ${currency}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case 'good':
        return <Badge className="bg-green-100 text-green-800">Good</Badge>
      case 'damaged':
        return <Badge className="bg-yellow-100 text-yellow-800">Damaged</Badge>
      case 'defective':
        return <Badge className="bg-red-100 text-red-800">Defective</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated || !purchaseOrder) {
    return null
  }

  const totalReceiving = purchaseOrder.items.reduce((sum, item) => sum + item.receivingQuantity, 0)
  const totalPending = purchaseOrder.items.reduce((sum, item) => sum + item.pendingQuantity, 0)

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/purchase-orders/receive')}
            className="hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Receiving'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-green-600 to-blue-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Package className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'ကုန်ပစ္စည်း လက်ခံခြင်း' : 'Receive Items'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {purchaseOrder.orderNumber} - {purchaseOrder.supplier.name}
                  </p>
                </div>
              </div>
              <div className="text-right">
                <p className="text-white/80 text-sm">
                  {language === 'mm' ? 'စောင့်ဆိုင်းနေသော ပစ္စည်းများ' : 'Pending Items'}
                </p>
                <p className="text-2xl font-bold">{totalPending}</p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Supplier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'ပေးသွင်းသူ အချက်အလက်' : 'Supplier Information'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <h3 className="font-semibold">{purchaseOrder.supplier.name}</h3>
                <p className="text-sm text-gray-600">{purchaseOrder.supplier.code}</p>
              </div>

              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-gray-500" />
                  <span className="text-sm">{purchaseOrder.supplier.contactPerson.name}</span>
                </div>

                {purchaseOrder.supplier.contactPerson.phone && (
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{purchaseOrder.supplier.contactPerson.phone}</span>
                  </div>
                )}

                {purchaseOrder.supplier.contactPerson.email && (
                  <div className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{purchaseOrder.supplier.contactPerson.email}</span>
                  </div>
                )}

                {purchaseOrder.supplier.address && (
                  <div className="flex items-start gap-2">
                    <MapPin className="h-4 w-4 text-gray-500 mt-0.5" />
                    <span className="text-sm">{purchaseOrder.supplier.address}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Delivery Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Truck className="h-5 w-5 text-green-600" />
                {language === 'mm' ? 'ပေးပို့မှု အချက်အလက်' : 'Delivery Information'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div>
                  <Label className="text-sm">
                    {language === 'mm' ? 'မျှော်လင့်ထားသော ရက်စွဲ' : 'Expected Date'}
                  </Label>
                  <div className="flex items-center gap-2 mt-1">
                    <Calendar className="h-4 w-4 text-gray-500" />
                    <span className="text-sm">{formatDate(purchaseOrder.expectedDeliveryDate)}</span>
                  </div>
                </div>

                <div>
                  <Label className="text-sm">
                    {language === 'mm' ? 'လက်ခံသည့် ရက်စွဲ' : 'Delivery Date'}
                  </Label>
                  <Input
                    type="date"
                    value={deliveryDate}
                    onChange={(e) => setDeliveryDate(e.target.value)}
                    className="mt-1"
                    aria-label={language === 'mm' ? 'လက်ခံသည့် ရက်စွဲ' : 'Delivery Date'}
                  />
                </div>

                <div>
                  <Label className="text-sm">
                    {language === 'mm' ? 'လက်ခံသူ' : 'Received By'}
                  </Label>
                  <Input
                    value={receivedBy}
                    onChange={(e) => setReceivedBy(e.target.value)}
                    className="mt-1"
                    aria-label={language === 'mm' ? 'လက်ခံသူ' : 'Received By'}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Receiving Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'လက်ခံမှု အနှစ်ချုပ်' : 'Receiving Summary'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div className="text-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">{totalPending}</p>
                  <p className="text-sm text-gray-600">
                    {language === 'mm' ? 'စောင့်ဆိုင်း' : 'Pending'}
                  </p>
                </div>
                <div className="text-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">{totalReceiving}</p>
                  <p className="text-sm text-gray-600">
                    {language === 'mm' ? 'လက်ခံမည်' : 'Receiving'}
                  </p>
                </div>
              </div>

              <div>
                <Label className="text-sm">
                  {language === 'mm' ? 'ပေးပို့မှု မှတ်ချက်များ' : 'Delivery Notes'}
                </Label>
                <textarea
                  className="w-full mt-1 p-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800 text-sm"
                  rows={3}
                  value={deliveryNotes}
                  onChange={(e) => setDeliveryNotes(e.target.value)}
                  placeholder={language === 'mm' ? 'မှတ်ချက်များ ထည့်ပါ...' : 'Add delivery notes...'}
                  aria-label={language === 'mm' ? 'ပေးပို့မှု မှတ်ချက်များ' : 'Delivery Notes'}
                />
              </div>

              <Button
                onClick={handleReceiveAll}
                variant="outline"
                className="w-full"
              >
                <Package className="h-4 w-4 mr-2" />
                {language === 'mm' ? 'အားလုံး လက်ခံရန်' : 'Receive All'}
              </Button>
            </CardContent>
          </Card>

          {/* Items to Receive */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Scan className="h-5 w-5 text-orange-600" />
                {language === 'mm' ? 'လက်ခံရန် ပစ္စည်းများ' : 'Items to Receive'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'လက်ခံမည့် ပစ္စည်းများ၏ အရေအတွက် နှင့် အခြေအနေ သတ်မှတ်ပါ'
                  : 'Specify quantities and condition for items being received'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {purchaseOrder.items.map((item) => (
                  <div key={item._id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                    <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                      {/* Product Info */}
                      <div className="lg:col-span-2">
                        <h4 className="font-medium">{item.productName}</h4>
                        <p className="text-sm text-gray-600">SKU: {item.sku}</p>
                        <p className="text-sm text-gray-600">
                          {language === 'mm' ? 'စောင့်ဆိုင်း' : 'Pending'}: {item.pendingQuantity} / {item.quantity}
                        </p>
                      </div>

                      {/* Quantity Controls */}
                      <div>
                        <Label className="text-sm">
                          {language === 'mm' ? 'လက်ခံမည့် အရေအတွက်' : 'Receiving Qty'}
                        </Label>
                        <div className="flex items-center gap-2 mt-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateItemReceivingQuantity(item._id, item.receivingQuantity - 1)}
                            disabled={item.receivingQuantity <= 0}
                            aria-label={`${language === 'mm' ? 'လျှော့ရန်' : 'Decrease quantity'} ${item.productName}`}
                          >
                            <Minus className="h-3 w-3" />
                          </Button>
                          <Input
                            type="number"
                            value={item.receivingQuantity}
                            onChange={(e) => updateItemReceivingQuantity(item._id, parseInt(e.target.value) || 0)}
                            className="w-20 text-center"
                            min="0"
                            max={item.pendingQuantity}
                            aria-label={`${language === 'mm' ? 'လက်ခံမည့် အရေအတွက်' : 'Receiving quantity'} ${item.productName}`}
                          />
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => updateItemReceivingQuantity(item._id, item.receivingQuantity + 1)}
                            disabled={item.receivingQuantity >= item.pendingQuantity}
                            aria-label={`${language === 'mm' ? 'တိုးရန်' : 'Increase quantity'} ${item.productName}`}
                          >
                            <Plus className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>

                      {/* Condition */}
                      <div>
                        <Label className="text-sm">
                          {language === 'mm' ? 'အခြေအနေ' : 'Condition'}
                        </Label>
                        <select
                          value={item.condition}
                          onChange={(e) => updateItemCondition(item._id, e.target.value as any)}
                          className="w-full mt-1 p-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800 text-sm"
                          aria-label={language === 'mm' ? 'အခြေအနေ' : 'Condition'}
                        >
                          <option value="good">{language === 'mm' ? 'ကောင်းမွန်' : 'Good'}</option>
                          <option value="damaged">{language === 'mm' ? 'ပျက်စီး' : 'Damaged'}</option>
                          <option value="defective">{language === 'mm' ? 'ချို့ယွင်း' : 'Defective'}</option>
                        </select>
                      </div>

                      {/* Unit Cost */}
                      <div>
                        <Label className="text-sm">
                          {language === 'mm' ? 'တစ်ခု ကုန်ကျစရိတ်' : 'Unit Cost'}
                        </Label>
                        <p className="text-sm font-medium mt-1">
                          {formatPrice(item.unitCost, purchaseOrder.currency)}
                        </p>
                      </div>

                      {/* Notes */}
                      <div>
                        <Label className="text-sm">
                          {language === 'mm' ? 'မှတ်ချက်များ' : 'Notes'}
                        </Label>
                        <Input
                          value={item.notes}
                          onChange={(e) => updateItemNotes(item._id, e.target.value)}
                          placeholder={language === 'mm' ? 'မှတ်ချက်...' : 'Notes...'}
                          className="mt-1"
                          aria-label={`${language === 'mm' ? 'မှတ်ချက်များ' : 'Notes'} ${item.productName}`}
                        />
                      </div>
                    </div>

                    {/* Condition Badge */}
                    <div className="mt-3 flex items-center gap-2">
                      {getConditionBadge(item.condition)}
                      {item.receivingQuantity > 0 && (
                        <Badge className="bg-blue-100 text-blue-800">
                          {language === 'mm' ? 'လက်ခံမည်' : 'Receiving'}: {item.receivingQuantity}
                        </Badge>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4">
          <Button
            variant="outline"
            onClick={() => router.push('/purchase-orders/receive')}
          >
            {language === 'mm' ? 'မလုပ်တော့' : 'Cancel'}
          </Button>

          <Button
            onClick={handleSaveReceiving}
            disabled={saving || totalReceiving === 0}
            className="bg-gradient-to-r from-green-600 to-blue-600 hover:from-green-700 hover:to-blue-700"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Processing...'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'လက်ခံမှု သိမ်းရန်' : 'Complete Receiving'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
