const ExchangeRate = require('../models/ExchangeRate');
const asyncHandler = require('../utils/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get current exchange rates
// @route   GET /api/exchange-rates
// @access  Public
const getExchangeRates = asyncHandler(async (req, res, next) => {
    const exchangeRates = await ExchangeRate.getCurrentRates();
    
    res.status(200).json({
        success: true,
        data: exchangeRates
    });
});

// @desc    Update exchange rates
// @route   PUT /api/exchange-rates
// @access  Private (Admin, Manager)
const updateExchangeRates = asyncHandler(async (req, res, next) => {
    const { rates } = req.body;
    
    if (!rates) {
        return next(new ErrorResponse('Exchange rates data is required', 400));
    }
    
    // Validate rates structure
    const validCurrencies = ['MMK', 'USD', 'THB'];
    const requiredFields = ['rate', 'symbol', 'name', 'flag'];
    
    for (const currency of validCurrencies) {
        if (rates[currency]) {
            for (const field of requiredFields) {
                if (rates[currency][field] === undefined) {
                    return next(new ErrorResponse(`Missing ${field} for ${currency}`, 400));
                }
            }
            
            // Validate rate is a positive number
            if (typeof rates[currency].rate !== 'number' || rates[currency].rate <= 0) {
                return next(new ErrorResponse(`Invalid rate for ${currency}`, 400));
            }
        }
    }
    
    // Get current exchange rates
    let exchangeRates = await ExchangeRate.getCurrentRates();
    
    // Update rates
    exchangeRates = await exchangeRates.updateRates(rates, req.user.id);
    
    res.status(200).json({
        success: true,
        data: exchangeRates,
        message: 'Exchange rates updated successfully'
    });
});

// @desc    Get exchange rate history
// @route   GET /api/exchange-rates/history
// @access  Private (Admin, Manager)
const getExchangeRateHistory = asyncHandler(async (req, res, next) => {
    const { page = 1, limit = 10 } = req.query;
    
    const exchangeRates = await ExchangeRate.find()
        .populate('updatedBy', 'firstName lastName email')
        .sort({ lastUpdated: -1 })
        .limit(limit * 1)
        .skip((page - 1) * limit);
    
    const total = await ExchangeRate.countDocuments();
    
    res.status(200).json({
        success: true,
        data: exchangeRates,
        pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            total,
            pages: Math.ceil(total / limit)
        }
    });
});

// @desc    Reset to default exchange rates
// @route   POST /api/exchange-rates/reset
// @access  Private (Admin)
const resetExchangeRates = asyncHandler(async (req, res, next) => {
    const defaultRates = {
        MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
        USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
        THB: { rate: 0.016, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
    };
    
    // Get current exchange rates
    let exchangeRates = await ExchangeRate.getCurrentRates();
    
    // Update with default rates
    exchangeRates = await exchangeRates.updateRates(defaultRates, req.user.id);
    exchangeRates.source = 'system';
    await exchangeRates.save();
    
    res.status(200).json({
        success: true,
        data: exchangeRates,
        message: 'Exchange rates reset to default values'
    });
});

// @desc    Fetch live exchange rates from external API
// @route   POST /api/exchange-rates/fetch-live
// @access  Private (Admin, Manager)
const fetchLiveRates = asyncHandler(async (req, res, next) => {
    try {
        // In a real application, you would fetch from a currency API
        // For demo purposes, we'll simulate with slight variations
        const variation = 0.95 + Math.random() * 0.1; // ±5% variation
        
        const liveRates = {
            MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
            USD: { rate: 0.00048 * variation, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
            THB: { rate: 0.016 * variation, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
        };
        
        // Get current exchange rates
        let exchangeRates = await ExchangeRate.getCurrentRates();
        
        // Update with live rates
        exchangeRates = await exchangeRates.updateRates(liveRates, req.user.id);
        exchangeRates.source = 'api';
        await exchangeRates.save();
        
        res.status(200).json({
            success: true,
            data: exchangeRates,
            message: 'Live exchange rates fetched and updated'
        });
    } catch (error) {
        return next(new ErrorResponse('Failed to fetch live exchange rates', 500));
    }
});

module.exports = {
    getExchangeRates,
    updateExchangeRates,
    getExchangeRateHistory,
    resetExchangeRates,
    fetchLiveRates
};
