'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useTheme } from '@/contexts/theme-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  Package,
  Save,
  ArrowLeft,
  Upload,
  X,
  Plus,
  Trash2,
  ImageIcon,
  Barcode,
  DollarSign,
  Hash,
  FileText,
  Tag,
  Warehouse
} from 'lucide-react'

interface Product {
  _id: string
  name: string
  description: string
  sku: string
  barcode: string
  category: {
    _id: string
    name: string
    color: string
  }
  price: number
  cost: number
  inventory: {
    quantity: number
    minQuantity: number
    maxQuantity: number
    unit: string
  }
  images?: {
    url: string
    alt: string
    isPrimary: boolean
  }[]
  image?: string // Keep for backward compatibility
  tags: string[]
  isActive: boolean
}

interface Category {
  _id: string
  name: string
  color: string
}

export default function EditProductPage() {
  const { id } = useParams()
  const router = useRouter()
  const { isAuthenticated } = useAuth()
  const { language } = useTheme()
  const { formatCurrency } = useCurrency()

  const [product, setProduct] = useState<Product | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [newTag, setNewTag] = useState('')

  // Form data
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    sku: '',
    barcode: '',
    categoryId: '',
    price: 0,
    cost: 0,
    quantity: 0,
    minQuantity: 0,
    maxQuantity: 0,
    unit: 'pcs',
    image: '', // Keep for backward compatibility
    images: [] as {
      url: string
      alt: string
      isPrimary: boolean
    }[],
    tags: [] as string[],
    isActive: true
  })

  const t = {
    editProduct: language === 'mm' ? 'ကုန်ပစ္စည်း ပြင်ဆင်ရန်' : 'Edit Product',
    productInfo: language === 'mm' ? 'ကုန်ပစ္စည်း အချက်အလက်' : 'Product Information',
    productName: language === 'mm' ? 'ကုန်ပစ္စည်း အမည်' : 'Product Name',
    description: language === 'mm' ? 'ဖော်ပြချက်' : 'Description',
    category: language === 'mm' ? 'အမျိုးအစား' : 'Category',
    selectCategory: language === 'mm' ? 'အမျိုးအစား ရွေးချယ်ပါ' : 'Select Category',
    pricing: language === 'mm' ? 'စျေးနှုန်း သတ်မှတ်ခြင်း' : 'Pricing',
    sellingPrice: language === 'mm' ? 'ရောင်းစျေး' : 'Selling Price',
    costPrice: language === 'mm' ? 'ကုန်ကျစရိတ်' : 'Cost Price',
    profit: language === 'mm' ? 'အမြတ်' : 'Profit',
    margin: language === 'mm' ? 'အမြတ်နှုန်း' : 'Margin',
    inventory: language === 'mm' ? 'စတော့ စီမံခန့်ခွဲမှု' : 'Inventory Management',
    currentStock: language === 'mm' ? 'လက်ရှိ စတော့' : 'Current Stock',
    minStock: language === 'mm' ? 'အနည်းဆုံး စတော့' : 'Minimum Stock',
    maxStock: language === 'mm' ? 'အများဆုံး စတော့' : 'Maximum Stock',
    unit: language === 'mm' ? 'ယူနစ်' : 'Unit',
    productCodes: language === 'mm' ? 'ကုန်ပစ္စည်း ကုဒ်များ' : 'Product Codes',
    sku: language === 'mm' ? 'SKU ကုဒ်' : 'SKU Code',
    barcode: language === 'mm' ? 'ဘားကုဒ်' : 'Barcode',
    generateBarcode: language === 'mm' ? 'ဘားကုဒ် ထုတ်ပေးရန်' : 'Generate Barcode',
    productImage: language === 'mm' ? 'ကုန်ပစ္စည်း ပုံ' : 'Product Image',
    uploadImage: language === 'mm' ? 'ပုံ တင်ရန်' : 'Upload Image',
    tags: language === 'mm' ? 'တဂ်များ' : 'Tags',
    addTag: language === 'mm' ? 'တဂ် ထည့်ရန်' : 'Add Tag',
    save: language === 'mm' ? 'သိမ်းဆည်းရန်' : 'Save Changes',
    cancel: language === 'mm' ? 'ပယ်ဖျက်ရန်' : 'Cancel',
    saving: language === 'mm' ? 'သိမ်းဆည်းနေသည်...' : 'Saving...',
    required: language === 'mm' ? 'လိုအပ်သည်' : 'Required',
    optional: language === 'mm' ? 'ရွေးချယ်ခွင့်' : 'Optional'
  }

  useEffect(() => {
    if (isAuthenticated && id) {
      fetchProduct()
      fetchCategories()
    }
  }, [isAuthenticated, id])

  const fetchProduct = async () => {
    try {
      setLoading(true)
      const response = await apiClient.getProduct(id as string)
      
      if (response.success && response.data) {
        const productData = response.data
        setProduct(productData)
        
        // Populate form data
        setFormData({
          name: productData.name || '',
          description: productData.description || '',
          sku: productData.sku || '',
          barcode: productData.barcode || '',
          categoryId: productData.category?._id || '',
          price: productData.price || 0,
          cost: productData.cost || 0,
          quantity: productData.inventory?.quantity || 0,
          minQuantity: productData.inventory?.minQuantity || 0,
          maxQuantity: productData.inventory?.maxQuantity || 0,
          unit: productData.inventory?.unit || 'pcs',
          image: productData.image || '', // Keep for backward compatibility
          images: productData.images || (productData.image ? [{
            url: productData.image,
            alt: productData.name || 'Product image',
            isPrimary: true
          }] : []),
          tags: productData.tags || [],
          isActive: productData.isActive !== false
        })
        
        console.log('✅ Product loaded for editing:', productData.name)
      } else {
        console.error('Failed to load product:', response.error)
        router.push('/products')
      }
    } catch (error) {
      console.error('Error fetching product:', error)
      router.push('/products')
    } finally {
      setLoading(false)
    }
  }

  const fetchCategories = async () => {
    try {
      const response = await apiClient.getCategories()
      if (response.success) {
        setCategories(response.data || [])
      }
    } catch (error) {
      console.error('Error fetching categories:', error)
    }
  }

  const handleInputChange = (field: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addTag = () => {
    if (newTag.trim() && !formData.tags.includes(newTag.trim())) {
      setFormData(prev => ({
        ...prev,
        tags: [...prev.tags, newTag.trim()]
      }))
      setNewTag('')
    }
  }

  const removeTag = (tagToRemove: string) => {
    setFormData(prev => ({
      ...prev,
      tags: prev.tags.filter(tag => tag !== tagToRemove)
    }))
  }

  const generateSKU = () => {
    const timestamp = Date.now().toString().slice(-6)
    const randomStr = Math.random().toString(36).substring(2, 5).toUpperCase()
    const categoryPrefix = categories.find(cat => cat._id === formData.categoryId)?.name?.substring(0, 3).toUpperCase() || 'PRD'
    handleInputChange('sku', `${categoryPrefix}-${randomStr}-${timestamp}`)
  }

  const generateBarcode = () => {
    // Generate EAN-13 compatible barcode (13 digits)
    const timestamp = Date.now().toString()
    const randomDigits = Math.floor(Math.random() * 1000000).toString().padStart(6, '0')
    const barcode = `${timestamp.slice(-7)}${randomDigits}`.slice(0, 13)
    handleInputChange('barcode', barcode)
  }

  const calculateProfit = () => {
    return formData.price - formData.cost
  }

  const calculateMargin = () => {
    if (formData.price === 0) return 0
    return ((formData.price - formData.cost) / formData.price) * 100
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files
    if (!files) return

    Array.from(files).forEach(file => {
      if (file.type.startsWith('image/')) {
        const reader = new FileReader()
        reader.onload = (e) => {
          const imageUrl = e.target?.result as string
          setFormData(prev => ({
            ...prev,
            images: [...prev.images, {
              url: imageUrl,
              alt: file.name,
              isPrimary: prev.images.length === 0 // First image is primary
            }]
          }))
        }
        reader.readAsDataURL(file)
      }
    })

    // Reset input
    event.target.value = ''
  }

  const removeImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }

  const setPrimaryImage = (index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.map((img, i) => ({
        ...img,
        isPrimary: i === index
      }))
    }))
  }

  const handleSave = async () => {
    try {
      setSaving(true)

      // Validation
      if (!formData.name.trim()) {
        alert(t.required + ': ' + t.productName)
        return
      }

      if (!formData.categoryId) {
        alert(t.required + ': ' + t.category)
        return
      }

      if (formData.price <= 0) {
        alert(t.required + ': ' + t.sellingPrice)
        return
      }

      // Prepare update data
      const updateData = {
        name: formData.name.trim(),
        description: formData.description.trim(),
        sku: formData.sku.trim(),
        barcode: formData.barcode.trim(),
        category: formData.categoryId,
        price: Number(formData.price),
        cost: Number(formData.cost),
        inventory: {
          quantity: Number(formData.quantity),
          minQuantity: Number(formData.minQuantity),
          maxQuantity: Number(formData.maxQuantity),
          unit: formData.unit
        },
        images: formData.images,
        image: formData.images.length > 0 ? formData.images.find(img => img.isPrimary)?.url || formData.images[0].url : formData.image, // Backward compatibility
        tags: formData.tags,
        isActive: formData.isActive
      }

      const response = await apiClient.updateProduct(id as string, updateData)

      if (response.success) {
        console.log('✅ Product updated successfully')
        router.push('/products')
      } else {
        alert('Failed to update product: ' + (response.error || 'Unknown error'))
      }
    } catch (error) {
      console.error('Error updating product:', error)
      alert('Error updating product. Please try again.')
    } finally {
      setSaving(false)
    }
  }

  if (!isAuthenticated) {
    return null
  }

  if (loading) {
    return (
      <MainLayout language={language}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <Package className="h-12 w-12 mx-auto mb-4 animate-spin text-blue-600" />
            <p className="text-gray-600 dark:text-gray-400">
              {language === 'mm' ? 'ကုန်ပစ္စည်း ရယူနေသည်...' : 'Loading product...'}
            </p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              onClick={() => router.push('/products')}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              {language === 'mm' ? 'ပြန်သွားရန်' : 'Back'}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {t.editProduct}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {product?.name}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            <Button
              variant="outline"
              onClick={() => router.push('/products')}
            >
              {t.cancel}
            </Button>
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {saving ? (
                <>
                  <Package className="h-4 w-4 mr-2 animate-spin" />
                  {t.saving}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {t.save}
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Product Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-blue-600" />
                  {t.productInfo}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="name">{t.productName} *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => handleInputChange('name', e.target.value)}
                      placeholder={language === 'mm' ? 'ကုန်ပစ္စည်း အမည် ရိုက်ပါ' : 'Enter product name'}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="category">{t.category} *</Label>
                    <Select value={formData.categoryId} onValueChange={(value) => handleInputChange('categoryId', value)}>
                      <SelectTrigger>
                        <SelectValue placeholder={t.selectCategory} />
                      </SelectTrigger>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category._id} value={category._id}>
                            <div className="flex items-center gap-2">
                              <div
                                className="w-3 h-3 rounded-full"
                                style={{ backgroundColor: category.color }}
                              ></div>
                              {category.name}
                            </div>
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="description">{t.description}</Label>
                  <Textarea
                    id="description"
                    value={formData.description}
                    onChange={(e) => handleInputChange('description', e.target.value)}
                    placeholder={language === 'mm' ? 'ကုန်ပစ္စည်း ဖော်ပြချက်' : 'Product description'}
                    rows={3}
                  />
                </div>
              </CardContent>
            </Card>

            {/* Pricing */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5 text-green-600" />
                  {t.pricing}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="price">{t.sellingPrice} *</Label>
                    <Input
                      id="price"
                      type="number"
                      value={formData.price}
                      onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="cost">{t.costPrice}</Label>
                    <Input
                      id="cost"
                      type="number"
                      value={formData.cost}
                      onChange={(e) => handleInputChange('cost', parseFloat(e.target.value) || 0)}
                      placeholder="0.00"
                    />
                  </div>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <div className="text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400">{t.profit}</p>
                    <p className={`text-lg font-bold ${calculateProfit() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {formatCurrency(calculateProfit())}
                    </p>
                  </div>
                  <div className="text-center">
                    <p className="text-sm text-gray-600 dark:text-gray-400">{t.margin}</p>
                    <p className={`text-lg font-bold ${calculateMargin() >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                      {calculateMargin().toFixed(1)}%
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Inventory */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Warehouse className="h-5 w-5 text-purple-600" />
                  {t.inventory}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="quantity">{t.currentStock}</Label>
                    <Input
                      id="quantity"
                      type="number"
                      value={formData.quantity}
                      onChange={(e) => handleInputChange('quantity', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="minQuantity">{t.minStock}</Label>
                    <Input
                      id="minQuantity"
                      type="number"
                      value={formData.minQuantity}
                      onChange={(e) => handleInputChange('minQuantity', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="maxQuantity">{t.maxStock}</Label>
                    <Input
                      id="maxQuantity"
                      type="number"
                      value={formData.maxQuantity}
                      onChange={(e) => handleInputChange('maxQuantity', parseInt(e.target.value) || 0)}
                      placeholder="0"
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="unit">{t.unit}</Label>
                    <Select value={formData.unit} onValueChange={(value) => handleInputChange('unit', value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="pcs">Pieces</SelectItem>
                        <SelectItem value="kg">Kilograms</SelectItem>
                        <SelectItem value="lbs">Pounds</SelectItem>
                        <SelectItem value="liters">Liters</SelectItem>
                        <SelectItem value="meters">Meters</SelectItem>
                        <SelectItem value="boxes">Boxes</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Product Codes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Hash className="h-5 w-5 text-orange-600" />
                  {t.productCodes}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="sku">
                    {t.sku}
                    <span className="text-xs text-gray-500 ml-2">
                      {language === 'mm' ? '(အလိုအလျောက်)' : '(Auto-generated)'}
                    </span>
                  </Label>
                  <div className="flex gap-2">
                    <Input
                      id="sku"
                      value={formData.sku || ''}
                      onChange={(e) => handleInputChange('sku', e.target.value)}
                      placeholder={language === 'mm' ? 'PRD-XXX-123456' : 'PRD-XXX-123456'}
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={generateSKU}
                      title={language === 'mm' ? 'SKU အသစ် ထုတ်ရန်' : 'Generate new SKU'}
                    >
                      <Hash className="h-4 w-4 mr-1" />
                      {language === 'mm' ? 'ထုတ်ပေးရန်' : 'Generate'}
                    </Button>
                  </div>
                  <p className="text-xs text-gray-500">
                    {language === 'mm'
                      ? 'ကုန်ပစ္စည်း အမည် သို့မဟုတ် အမျိုးအစား ပြောင်းလဲသောအခါ SKU အသစ် ထုတ်လို့ရပါသည်'
                      : 'You can generate a new SKU based on product name or category'
                    }
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="barcode">{t.barcode}</Label>
                  <div className="flex gap-2">
                    <Input
                      id="barcode"
                      value={formData.barcode}
                      onChange={(e) => handleInputChange('barcode', e.target.value)}
                      placeholder="123456789"
                    />
                    <Button
                      type="button"
                      variant="outline"
                      onClick={generateBarcode}
                      className="px-3"
                    >
                      <Barcode className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Product Images */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ImageIcon className="h-5 w-5 text-blue-600" />
                  {t.productImage}
                  <span className="text-xs text-gray-500 ml-2">
                    {language === 'mm' ? '(များစွာ ရွေးချယ်နိုင်)' : '(Multiple images)'}
                  </span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Image Upload Area */}
                <div className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                  <input
                    type="file"
                    id="image-upload"
                    multiple
                    accept="image/*"
                    onChange={handleImageUpload}
                    className="hidden"
                    title={language === 'mm' ? 'ပုံများ ရွေးချယ်ရန်' : 'Select images to upload'}
                    aria-label={language === 'mm' ? 'ပုံများ ရွေးချယ်ရန်' : 'Select images to upload'}
                  />
                  <ImageIcon className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                  <div>
                    <Button
                      variant="outline"
                      className="w-full"
                      onClick={() => document.getElementById('image-upload')?.click()}
                    >
                      <Upload className="h-4 w-4 mr-2" />
                      {language === 'mm' ? 'ပုံများ တင်ရန်' : 'Upload Images'}
                    </Button>
                    <p className="text-xs text-gray-500 mt-2">
                      {language === 'mm'
                        ? 'PNG, JPG, GIF (အများဆုံး 5MB စီ) - များစွာ ရွေးချယ်နိုင်သည်'
                        : 'PNG, JPG, GIF up to 5MB each - Multiple selection allowed'
                      }
                    </p>
                  </div>
                </div>

                {/* Image Gallery */}
                {formData.images.length > 0 && (
                  <div className="space-y-4">
                    <h4 className="text-sm font-medium">
                      {language === 'mm' ? 'ပုံများ' : 'Images'} ({formData.images.length})
                    </h4>
                    <div className="grid grid-cols-2 gap-4">
                      {formData.images.map((image, index) => (
                        <div
                          key={index}
                          className={`relative group border-2 rounded-lg overflow-hidden ${
                            image.isPrimary ? 'border-blue-500' : 'border-gray-200 dark:border-gray-600'
                          }`}
                        >
                          <img
                            src={image.url}
                            alt={image.alt}
                            className="w-full h-32 object-cover"
                          />

                          {/* Image Controls */}
                          <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center gap-2">
                            {!image.isPrimary && (
                              <Button
                                size="sm"
                                variant="secondary"
                                onClick={() => setPrimaryImage(index)}
                                title={language === 'mm' ? 'အဓိက ပုံ အဖြစ် သတ်မှတ်ရန်' : 'Set as primary image'}
                              >
                                <ImageIcon className="h-4 w-4" />
                              </Button>
                            )}
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => removeImage(index)}
                              title={language === 'mm' ? 'ပုံ ဖျက်ရန်' : 'Remove image'}
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>

                          {/* Primary Badge */}
                          {image.isPrimary && (
                            <div className="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded">
                              {language === 'mm' ? 'အဓိက' : 'Primary'}
                            </div>
                          )}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Tags */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Tag className="h-5 w-5 text-pink-600" />
                  {t.tags}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex gap-2">
                  <Input
                    value={newTag}
                    onChange={(e) => setNewTag(e.target.value)}
                    placeholder={language === 'mm' ? 'တဂ် အမည်' : 'Tag name'}
                    onKeyPress={(e) => e.key === 'Enter' && addTag()}
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addTag}
                    className="px-3"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>
                <div className="flex flex-wrap gap-2">
                  {formData.tags.map((tag, index) => (
                    <Badge
                      key={index}
                      variant="secondary"
                      className="flex items-center gap-1"
                    >
                      {tag}
                      <button
                        type="button"
                        onClick={() => removeTag(tag)}
                        className="ml-1 hover:text-red-600"
                        title={language === 'mm' ? 'တဂ် ဖျက်ရန်' : 'Remove tag'}
                        aria-label={language === 'mm' ? 'တဂ် ဖျက်ရန်' : 'Remove tag'}
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
