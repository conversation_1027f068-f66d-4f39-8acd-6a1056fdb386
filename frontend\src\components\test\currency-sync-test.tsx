'use client'

import React, { useState, useEffect } from 'react'
import { useCurrency } from '@/contexts/currency-context'
import { useTheme } from '@/contexts/theme-context'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export function CurrencySyncTest() {
  const { currentCurrency, formatCurrency } = useCurrency()
  const { currency: themeCurrency, toggleCurrency } = useTheme()
  const [syncEvents, setSyncEvents] = useState<string[]>([])
  const [testAmount] = useState(50000) // 50,000 MMK for testing

  useEffect(() => {
    const handleGlobalSync = (event: CustomEvent) => {
      const timestamp = new Date().toLocaleTimeString()
      setSyncEvents(prev => [
        `${timestamp}: Global sync - ${event.detail.currency}`,
        ...prev.slice(0, 9) // Keep last 10 events
      ])
    }

    const handleCurrencyChanged = (event: CustomEvent) => {
      const timestamp = new Date().toLocaleTimeString()
      setSyncEvents(prev => [
        `${timestamp}: Currency changed - ${event.detail.currency}`,
        ...prev.slice(0, 9)
      ])
    }

    const handleCurrencyToggle = (event: CustomEvent) => {
      const timestamp = new Date().toLocaleTimeString()
      setSyncEvents(prev => [
        `${timestamp}: Currency toggle - ${event.detail.newCurrency}`,
        ...prev.slice(0, 9)
      ])
    }

    window.addEventListener('global-currency-sync', handleGlobalSync as EventListener)
    window.addEventListener('currency-changed', handleCurrencyChanged as EventListener)
    window.addEventListener('currency-toggle', handleCurrencyToggle as EventListener)

    return () => {
      window.removeEventListener('global-currency-sync', handleGlobalSync as EventListener)
      window.removeEventListener('currency-changed', handleCurrencyChanged as EventListener)
      window.removeEventListener('currency-toggle', handleCurrencyToggle as EventListener)
    }
  }, [])

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Currency Synchronization Test</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <h3 className="font-semibold mb-2">CurrencyProvider</h3>
            <p>Current: <span className="font-mono">{currentCurrency}</span></p>
            <p>Formatted: <span className="font-mono">{formatCurrency(testAmount)}</span></p>
          </div>
          <div>
            <h3 className="font-semibold mb-2">ThemeProvider</h3>
            <p>Current: <span className="font-mono">{themeCurrency}</span></p>
          </div>
        </div>

        <div className="flex gap-2">
          <Button onClick={toggleCurrency} variant="outline">
            Toggle Currency (Theme)
          </Button>
          <Button 
            onClick={() => setSyncEvents([])} 
            variant="outline"
            size="sm"
          >
            Clear Events
          </Button>
        </div>

        <div>
          <h3 className="font-semibold mb-2">Sync Events (Last 10)</h3>
          <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded-md max-h-48 overflow-y-auto">
            {syncEvents.length === 0 ? (
              <p className="text-gray-500 text-sm">No events yet. Try toggling currency.</p>
            ) : (
              <div className="space-y-1">
                {syncEvents.map((event, index) => (
                  <div key={index} className="text-xs font-mono">
                    {event}
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div className="text-sm text-gray-600 dark:text-gray-400">
          <p><strong>Test Instructions:</strong></p>
          <ol className="list-decimal list-inside space-y-1">
            <li>Click "Toggle Currency" to change currency</li>
            <li>Watch both providers sync to same currency</li>
            <li>Check formatted amount updates correctly</li>
            <li>Verify events are logged below</li>
          </ol>
        </div>
      </CardContent>
    </Card>
  )
}
