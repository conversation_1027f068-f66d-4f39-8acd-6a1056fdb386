const mongoose = require('mongoose');
require('dotenv').config();

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos';

async function resetDatabase() {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI);
        console.log('✅ Connected to MongoDB');

        // Get database instance
        const db = mongoose.connection.db;
        
        console.log('🗑️ Dropping entire database...');
        await db.dropDatabase();
        console.log('✅ Database dropped successfully');

        console.log('🔄 Disconnecting from MongoDB...');
        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');

        console.log('🎉 Database reset completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('❌ Error resetting database:', error);
        process.exit(1);
    }
}

// Run the reset
resetDatabase();
