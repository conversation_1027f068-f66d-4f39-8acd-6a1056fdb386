'use client'

import { <PERSON>actN<PERSON>, useState, useEffect } from 'react'
import { Header } from './header'
import { Sidebar } from './sidebar'
import { Footer } from './footer'
import { NotificationCenter } from '@/components/notifications/notification-center'
import { useSettings } from '@/contexts/settings-context'
import { useTheme } from '@/contexts/theme-context'

import { ThemeSettings } from '@/components/theme/theme-settings'
import { KeyboardShortcutsHelp, useKeyboardShortcuts } from '@/hooks/use-keyboard-shortcuts'
import { cn } from '@/lib/utils'

interface MainLayoutProps {
  children: ReactNode
  showSidebar?: boolean
  language?: string
}

export function MainLayout({
  children,
  showSidebar = true,
  language: propLanguage
}: MainLayoutProps) {
  const [isNotificationOpen, setIsNotificationOpen] = useState(false)
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [showThemeSettings, setShowThemeSettings] = useState(false)
  const [showShortcuts, setShowShortcuts] = useState(false)
  const { language: settingsLanguage } = useSettings()
  const { theme } = useTheme()

  // Use prop language if provided, otherwise use settings language
  const language = propLanguage || settingsLanguage

  // Initialize keyboard shortcuts
  useKeyboardShortcuts(language as 'en' | 'mm')

  // Handle responsive sidebar
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setSidebarOpen(false)
      }
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  // Handle custom events
  useEffect(() => {
    const handleToggleTheme = () => setShowThemeSettings(true)
    const handleShowShortcuts = () => setShowShortcuts(true)
    const handleCloseModal = () => {
      setShowThemeSettings(false)
      setShowShortcuts(false)
      setSidebarOpen(false)
      setIsNotificationOpen(false)
    }

    window.addEventListener('toggle-theme', handleToggleTheme)
    window.addEventListener('show-shortcuts', handleShowShortcuts)
    window.addEventListener('close-modal', handleCloseModal)

    return () => {
      window.removeEventListener('toggle-theme', handleToggleTheme)
      window.removeEventListener('show-shortcuts', handleShowShortcuts)
      window.removeEventListener('close-modal', handleCloseModal)
    }
  }, [])

  return (
    <div className="min-h-screen bg-background text-foreground">
      {/* Mobile Sidebar Overlay */}
      {sidebarOpen && (
        <div
          className="fixed inset-0 z-40 bg-black/50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      <Header
        language={language as 'en' | 'mm'}
        onNotificationClick={() => setIsNotificationOpen(true)}
        onMenuClick={() => setSidebarOpen(true)}
        onThemeClick={() => setShowThemeSettings(true)}
      />

      <div className="flex pt-16">
        {showSidebar && (
          <Sidebar
            language={language as 'en' | 'mm'}
            isOpen={sidebarOpen}
            onClose={() => setSidebarOpen(false)}
          />
        )}

        <main className={cn(
          "flex-1 overflow-auto min-h-[calc(100vh-4rem)]",
          showSidebar ? "lg:ml-64" : ""
        )}>
          <div className="container-responsive padding-responsive">
            <div className="animate-fadeIn duration-100">
              {children}
            </div>
          </div>
          <Footer />
        </main>
      </div>

      <NotificationCenter
        isOpen={isNotificationOpen}
        onClose={() => setIsNotificationOpen(false)}
        language={language as 'en' | 'mm'}
      />

      {/* Theme Settings Modal */}
      <ThemeSettings
        isOpen={showThemeSettings}
        onClose={() => setShowThemeSettings(false)}
        language={language as 'en' | 'mm'}
      />

      {/* Keyboard Shortcuts Help */}
      <KeyboardShortcutsHelp
        isOpen={showShortcuts}
        onClose={() => setShowShortcuts(false)}
        language={language as 'en' | 'mm'}
      />
    </div>
  )
}
