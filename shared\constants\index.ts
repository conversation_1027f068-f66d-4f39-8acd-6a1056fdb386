// User Roles
export const USER_ROLES = {
  ADMIN: 'admin',
  MANAGER: 'manager',
  CASHIER: 'cashier'
} as const;

// Languages
export const LANGUAGES = {
  ENGLISH: 'en',
  MYANMAR: 'mm'
} as const;

// Currencies
export const CURRENCIES = {
  MMK: 'MMK',
  THB: 'THB',
  USD: 'USD'
} as const;

// Currency Symbols
export const CURRENCY_SYMBOLS = {
  MMK: 'Ks',
  THB: '฿',
  USD: '$'
} as const;

// Themes
export const THEMES = {
  LIGHT: 'light',
  DARK: 'dark',
  AUTO: 'auto'
} as const;

// Payment Methods
export const PAYMENT_METHODS = {
  CASH: 'cash',
  CARD: 'card',
  DIGITAL: 'digital',
  BANK_TRANSFER: 'bank_transfer',
  OTHER: 'other'
} as const;

// Sale Status
export const SALE_STATUS = {
  PENDING: 'pending',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded'
} as const;

// Stock Status
export const STOCK_STATUS = {
  IN_STOCK: 'in_stock',
  LOW_STOCK: 'low_stock',
  OUT_OF_STOCK: 'out_of_stock'
} as const;

// API Endpoints
export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/login',
    REGISTER: '/auth/register',
    LOGOUT: '/auth/logout',
    ME: '/auth/me',
    PROFILE: '/auth/profile',
    CHANGE_PASSWORD: '/auth/change-password'
  },
  USERS: {
    LIST: '/users',
    CREATE: '/users',
    GET: '/users/:id',
    UPDATE: '/users/:id',
    DELETE: '/users/:id'
  },
  PRODUCTS: {
    LIST: '/products',
    CREATE: '/products',
    GET: '/products/:id',
    UPDATE: '/products/:id',
    DELETE: '/products/:id',
    SEARCH: '/products/search',
    CATEGORIES: '/products/categories'
  },
  CATEGORIES: {
    LIST: '/categories',
    CREATE: '/categories',
    GET: '/categories/:id',
    UPDATE: '/categories/:id',
    DELETE: '/categories/:id'
  },
  SALES: {
    LIST: '/sales',
    CREATE: '/sales',
    GET: '/sales/:id',
    UPDATE: '/sales/:id',
    DELETE: '/sales/:id',
    REFUND: '/sales/:id/refund'
  },
  POS: {
    CART: '/pos/cart',
    CHECKOUT: '/pos/checkout',
    RECEIPT: '/pos/receipt/:id'
  },
  INVENTORY: {
    LIST: '/inventory',
    UPDATE: '/inventory/:id',
    LOW_STOCK: '/inventory/low-stock',
    MOVEMENTS: '/inventory/movements'
  },
  REPORTS: {
    SALES: '/reports/sales',
    PRODUCTS: '/reports/products',
    INVENTORY: '/reports/inventory',
    FINANCIAL: '/reports/financial'
  },
  SETTINGS: {
    COMPANY: '/settings/company',
    TAX: '/settings/tax',
    CURRENCY: '/settings/currency',
    PREFERENCES: '/settings/preferences'
  }
} as const;

// Permissions
export const PERMISSIONS = {
  USERS: {
    VIEW: 'users:view',
    CREATE: 'users:create',
    UPDATE: 'users:update',
    DELETE: 'users:delete'
  },
  PRODUCTS: {
    VIEW: 'products:view',
    CREATE: 'products:create',
    UPDATE: 'products:update',
    DELETE: 'products:delete'
  },
  SALES: {
    VIEW: 'sales:view',
    CREATE: 'sales:create',
    UPDATE: 'sales:update',
    DELETE: 'sales:delete',
    REFUND: 'sales:refund'
  },
  INVENTORY: {
    VIEW: 'inventory:view',
    UPDATE: 'inventory:update'
  },
  REPORTS: {
    VIEW: 'reports:view',
    EXPORT: 'reports:export'
  },
  SETTINGS: {
    VIEW: 'settings:view',
    UPDATE: 'settings:update'
  },
  POS: {
    USE: 'pos:use'
  }
} as const;

// Default Permissions by Role
export const DEFAULT_PERMISSIONS = {
  [USER_ROLES.ADMIN]: [
    ...Object.values(PERMISSIONS.USERS),
    ...Object.values(PERMISSIONS.PRODUCTS),
    ...Object.values(PERMISSIONS.SALES),
    ...Object.values(PERMISSIONS.INVENTORY),
    ...Object.values(PERMISSIONS.REPORTS),
    ...Object.values(PERMISSIONS.SETTINGS),
    ...Object.values(PERMISSIONS.POS)
  ],
  [USER_ROLES.MANAGER]: [
    PERMISSIONS.USERS.VIEW,
    PERMISSIONS.USERS.CREATE,
    PERMISSIONS.USERS.UPDATE,
    ...Object.values(PERMISSIONS.PRODUCTS),
    ...Object.values(PERMISSIONS.SALES),
    ...Object.values(PERMISSIONS.INVENTORY),
    ...Object.values(PERMISSIONS.REPORTS),
    PERMISSIONS.SETTINGS.VIEW,
    ...Object.values(PERMISSIONS.POS)
  ],
  [USER_ROLES.CASHIER]: [
    PERMISSIONS.PRODUCTS.VIEW,
    PERMISSIONS.SALES.VIEW,
    PERMISSIONS.SALES.CREATE,
    PERMISSIONS.INVENTORY.VIEW,
    PERMISSIONS.REPORTS.VIEW,
    ...Object.values(PERMISSIONS.POS)
  ]
} as const;

// Validation Rules
export const VALIDATION_RULES = {
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 128,
    REQUIRE_UPPERCASE: false,
    REQUIRE_LOWERCASE: false,
    REQUIRE_NUMBERS: false,
    REQUIRE_SYMBOLS: false
  },
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 50,
    PATTERN: /^[a-zA-Z0-9_]+$/
  },
  EMAIL: {
    PATTERN: /^\w+([\.-]?\w+)*@\w+([\.-]?\w+)*(\.\w{2,3})+$/
  },
  PHONE: {
    MAX_LENGTH: 20,
    PATTERN: /^[\+]?[0-9\-\(\)\s]+$/
  },
  PRODUCT: {
    NAME: {
      MIN_LENGTH: 1,
      MAX_LENGTH: 100
    },
    SKU: {
      MIN_LENGTH: 1,
      MAX_LENGTH: 50,
      PATTERN: /^[A-Z0-9\-_]+$/
    },
    PRICE: {
      MIN: 0,
      MAX: 999999999
    }
  }
} as const;

// File Upload
export const FILE_UPLOAD = {
  MAX_SIZE: 5 * 1024 * 1024, // 5MB
  ALLOWED_TYPES: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
  ALLOWED_EXTENSIONS: ['.jpg', '.jpeg', '.png', '.gif', '.webp']
} as const;

// Pagination
export const PAGINATION = {
  DEFAULT_PAGE: 1,
  DEFAULT_LIMIT: 20,
  MAX_LIMIT: 100
} as const;

// Date Formats
export const DATE_FORMATS = {
  DISPLAY: 'DD/MM/YYYY',
  DISPLAY_WITH_TIME: 'DD/MM/YYYY HH:mm',
  API: 'YYYY-MM-DD',
  API_WITH_TIME: 'YYYY-MM-DD HH:mm:ss'
} as const;

// Local Storage Keys
export const STORAGE_KEYS = {
  AUTH_TOKEN: 'bitestech_auth_token',
  USER_PREFERENCES: 'bitestech_user_preferences',
  CART: 'bitestech_cart',
  LANGUAGE: 'bitestech_language',
  THEME: 'bitestech_theme'
} as const;
