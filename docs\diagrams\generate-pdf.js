const puppeteer = require('puppeteer');
const path = require('path');
const fs = require('fs');

/**
 * Generate PDF from HTML file with Mermaid diagrams
 * Usage: node generate-pdf.js
 */

async function generatePDF() {
    console.log('🚀 Starting PDF generation...');
    
    try {
        // Launch browser
        const browser = await puppeteer.launch({
            headless: true,
            args: ['--no-sandbox', '--disable-setuid-sandbox']
        });
        
        const page = await browser.newPage();
        
        // Set page size and margins
        await page.setViewport({ width: 1200, height: 800 });
        
        // Get the HTML file path
        const htmlPath = path.join(__dirname, 'pdf-export.html');
        const htmlContent = fs.readFileSync(htmlPath, 'utf8');
        
        console.log('📄 Loading HTML content...');
        
        // Set content and wait for Mermaid to render
        await page.setContent(htmlContent, { 
            waitUntil: 'networkidle0',
            timeout: 30000 
        });
        
        // Wait for Mermaid diagrams to render
        console.log('⏳ Waiting for Mermaid diagrams to render...');
        await page.waitForTimeout(5000);
        
        // Check if Mermaid diagrams are rendered
        await page.waitForFunction(() => {
            const mermaidElements = document.querySelectorAll('.mermaid');
            return Array.from(mermaidElements).every(el => 
                el.querySelector('svg') !== null
            );
        }, { timeout: 30000 });
        
        console.log('✅ Mermaid diagrams rendered successfully');
        
        // Generate PDF
        const pdfPath = path.join(__dirname, 'BitesTech-POS-System-Diagrams.pdf');
        
        await page.pdf({
            path: pdfPath,
            format: 'A4',
            printBackground: true,
            margin: {
                top: '1cm',
                right: '1cm',
                bottom: '1cm',
                left: '1cm'
            },
            displayHeaderFooter: true,
            headerTemplate: `
                <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
                    BitesTech POS System - Architecture & Diagrams
                </div>
            `,
            footerTemplate: `
                <div style="font-size: 10px; width: 100%; text-align: center; color: #666;">
                    Page <span class="pageNumber"></span> of <span class="totalPages"></span>
                </div>
            `
        });
        
        await browser.close();
        
        console.log('🎉 PDF generated successfully!');
        console.log(`📁 File saved as: ${pdfPath}`);
        
        // Check file size
        const stats = fs.statSync(pdfPath);
        const fileSizeInMB = (stats.size / (1024 * 1024)).toFixed(2);
        console.log(`📊 File size: ${fileSizeInMB} MB`);
        
    } catch (error) {
        console.error('❌ Error generating PDF:', error);
        process.exit(1);
    }
}

// Run the PDF generation
if (require.main === module) {
    generatePDF();
}

module.exports = { generatePDF };
