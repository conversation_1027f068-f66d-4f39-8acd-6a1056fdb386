'use client'

import { useState, useEffect } from 'react'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useSettings } from '@/contexts/settings-context'
import apiClient from '@/lib/api'
import {
  ArrowLeft,
  Save,
  Check,
  Globe,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Linkedin,
  Instagram,
  Youtube,
  Heart,
  Building2
} from 'lucide-react'

interface FooterSettings {
  companyName: string
  companyNameMM: string
  description: string
  descriptionMM: string
  email: string
  phone: string
  address: string
  addressMM: string
  website: string
  socialLinks: {
    facebook?: string
    twitter?: string
    linkedin?: string
    instagram?: string
    youtube?: string
  }
  copyright: string
  copyrightMM: string
  version: string
  showSocialLinks: boolean
  showContactInfo: boolean
  showCompanyInfo: boolean
  footerStyle: 'default' | 'minimal' | 'detailed'
  backgroundColor: string
  textColor: string
}

export default function FooterSettingsPage() {
  const { isAuthenticated } = useAuth()
  const { language } = useSettings()
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)

  const [footerSettings, setFooterSettings] = useState<FooterSettings>({
    companyName: 'BitsTech POS',
    companyNameMM: 'BitsTech POS',
    description: 'Modern Point of Sale System for Your Business',
    descriptionMM: 'သင့်လုပ်ငန်းအတွက် ခေတ်မီ ရောင်းချရေး စနစ်',
    email: '<EMAIL>',
    phone: '+95 9 ***********',
    address: 'Yangon, Myanmar',
    addressMM: 'ရန်ကုန်၊ မြန်မာ',
    website: 'https://bitstech.com',
    socialLinks: {
      facebook: 'https://facebook.com/bitstech',
      twitter: 'https://twitter.com/bitstech',
      linkedin: 'https://linkedin.com/company/bitstech',
      instagram: 'https://instagram.com/bitstech',
      youtube: 'https://youtube.com/bitstech'
    },
    copyright: '© 2024 BitsTech. All rights reserved.',
    copyrightMM: '© ၂၀၂၄ BitsTech။ မူပိုင်ခွင့် အားလုံး ရယူထားသည်။',
    version: 'v1.0.0',
    showSocialLinks: true,
    showContactInfo: true,
    showCompanyInfo: true,
    footerStyle: 'default',
    backgroundColor: '#1F2937',
    textColor: '#FFFFFF'
  })

  const t = {
    en: {
      title: 'Footer Settings',
      subtitle: 'Customize footer content and appearance',
      companyInfo: 'Company Information',
      companyName: 'Company Name',
      companyNameMM: 'Company Name (Myanmar)',
      description: 'Description',
      descriptionMM: 'Description (Myanmar)',
      contactInfo: 'Contact Information',
      email: 'Email',
      phone: 'Phone',
      address: 'Address',
      addressMM: 'Address (Myanmar)',
      website: 'Website',
      socialLinks: 'Social Media Links',
      facebook: 'Facebook',
      twitter: 'Twitter',
      linkedin: 'LinkedIn',
      instagram: 'Instagram',
      youtube: 'YouTube',
      footerContent: 'Footer Content',
      copyright: 'Copyright Text',
      copyrightMM: 'Copyright Text (Myanmar)',
      version: 'Version',
      displayOptions: 'Display Options',
      showSocialLinks: 'Show Social Links',
      showContactInfo: 'Show Contact Information',
      showCompanyInfo: 'Show Company Information',
      footerStyle: 'Footer Style',
      appearance: 'Appearance',
      backgroundColor: 'Background Color',
      textColor: 'Text Color',
      save: 'Save Settings',
      saving: 'Saving...',
      saved: 'Settings saved successfully!',
      preview: 'Preview',
      backToSettings: 'Back to Settings'
    },
    mm: {
      title: 'Footer ဆက်တင်များ',
      subtitle: 'Footer အကြောင်းအရာနှင့် အပြင်အဆင် စိတ်ကြိုက်ပြုလုပ်ပါ',
      companyInfo: 'ကုမ္ပဏီ အချက်အလက်များ',
      companyName: 'ကုမ္ပဏီ အမည်',
      companyNameMM: 'ကုမ္ပဏီ အမည် (မြန်မာ)',
      description: 'ဖော်ပြချက်',
      descriptionMM: 'ဖော်ပြချက် (မြန်မာ)',
      contactInfo: 'ဆက်သွယ်ရေး အချက်အလက်များ',
      email: 'အီးမေးလ်',
      phone: 'ဖုန်း',
      address: 'လိပ်စာ',
      addressMM: 'လိပ်စာ (မြန်မာ)',
      website: 'ဝက်ဘ်ဆိုက်',
      socialLinks: 'လူမှုကွန်ယက် လင့်များ',
      facebook: 'Facebook',
      twitter: 'Twitter',
      linkedin: 'LinkedIn',
      instagram: 'Instagram',
      youtube: 'YouTube',
      footerContent: 'Footer အကြောင်းအရာ',
      copyright: 'မူပိုင်ခွင့် စာသား',
      copyrightMM: 'မူပိုင်ခွင့် စာသား (မြန်မာ)',
      version: 'ဗားရှင်း',
      displayOptions: 'ပြသမှု ရွေးချယ်မှုများ',
      showSocialLinks: 'လူမှုကွန်ယက် လင့်များ ပြသရန်',
      showContactInfo: 'ဆက်သွယ်ရေး အချက်အလက် ပြသရန်',
      showCompanyInfo: 'ကုမ္ပဏီ အချက်အလက် ပြသရန်',
      footerStyle: 'Footer ပုံစံ',
      appearance: 'အပြင်အဆင်',
      backgroundColor: 'နောက်ခံ အရောင်',
      textColor: 'စာသား အရောင်',
      save: 'ဆက်တင်များ သိမ်းဆည်းရန်',
      saving: 'သိမ်းဆည်းနေသည်...',
      saved: 'ဆက်တင်များ အောင်မြင်စွာ သိမ်းဆည်းပြီး!',
      preview: 'အစမ်းကြည့်ရှုမှု',
      backToSettings: 'ဆက်တင်များသို့ ပြန်သွားရန်'
    }
  }

  const text = t[language as keyof typeof t] || t.en

  useEffect(() => {
    if (!isAuthenticated) return
    loadFooterSettings()
  }, [isAuthenticated])

  const loadFooterSettings = async () => {
    try {
      setLoading(true)
      
      // Try to load from API
      // Use fallback since API method doesn't exist
      const response = { success: false, error: 'Footer settings API not implemented yet' }
      if (response.success && (response as any).data) {
        setFooterSettings({ ...footerSettings, ...(response as any).data })
      } else {
        // Load from localStorage as fallback
        const stored = localStorage.getItem('bitstech_footer_settings')
        if (stored) {
          try {
            const parsed = JSON.parse(stored)
            setFooterSettings({ ...footerSettings, ...parsed })
          } catch (error) {
            console.error('Error parsing stored footer settings:', error)
          }
        }
      }
    } catch (error) {
      console.error('Error loading footer settings:', error)
      // Try localStorage fallback
      const stored = localStorage.getItem('bitstech_footer_settings')
      if (stored) {
        try {
          const parsed = JSON.parse(stored)
          setFooterSettings({ ...footerSettings, ...parsed })
        } catch (parseError) {
          console.error('Error parsing stored footer settings:', parseError)
        }
      }
    } finally {
      setLoading(false)
    }
  }

  const updateField = (field: keyof FooterSettings, value: any) => {
    setFooterSettings(prev => ({ ...prev, [field]: value }))
  }

  const updateSocialLink = (platform: string, url: string) => {
    setFooterSettings(prev => ({
      ...prev,
      socialLinks: {
        ...prev.socialLinks,
        [platform]: url
      }
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Save to localStorage first
      localStorage.setItem('bitstech_footer_settings', JSON.stringify(footerSettings))

      // Try to save to API
      try {
        // Use fallback since API method doesn't exist
        const response = { success: false, error: 'Footer settings save API not implemented yet' }
      } catch (apiError) {
        console.warn('API save failed, saved locally:', apiError)
      }

      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error('Error saving footer settings:', error)
      setSaved(false)
    } finally {
      setSaving(false)
    }
  }

  if (!isAuthenticated) {
    return null
  }

  if (loading) {
    return (
      <MainLayout language={language}>
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <p className="text-gray-600 dark:text-gray-400">Loading footer settings...</p>
          </div>
        </div>
      </MainLayout>
    )
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => window.history.back()}
              className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              {text.backToSettings}
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                {text.title}
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                {text.subtitle}
              </p>
            </div>
          </div>
          <div className="flex items-center gap-3">
            {saved && (
              <Badge className="bg-green-100 text-green-800 border-green-200">
                <Check className="h-3 w-3 mr-1" />
                {text.saved}
              </Badge>
            )}
            <Button
              onClick={handleSave}
              disabled={saving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {saving ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                  {text.saving}
                </>
              ) : (
                <>
                  <Save className="h-4 w-4 mr-2" />
                  {text.save}
                </>
              )}
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Settings Panel */}
          <div className="space-y-6">
            {/* Company Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Building2 className="h-5 w-5 text-blue-600" />
                  {text.companyInfo}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="companyName">{text.companyName}</Label>
                    <Input
                      id="companyName"
                      value={footerSettings.companyName}
                      onChange={(e) => updateField('companyName', e.target.value)}
                      placeholder="BitsTech POS"
                    />
                  </div>
                  <div>
                    <Label htmlFor="companyNameMM">{text.companyNameMM}</Label>
                    <Input
                      id="companyNameMM"
                      value={footerSettings.companyNameMM}
                      onChange={(e) => updateField('companyNameMM', e.target.value)}
                      placeholder="BitsTech POS"
                    />
                  </div>
                  <div>
                    <Label htmlFor="description">{text.description}</Label>
                    <Textarea
                      id="description"
                      value={footerSettings.description}
                      onChange={(e) => updateField('description', e.target.value)}
                      placeholder="Modern Point of Sale System for Your Business"
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="descriptionMM">{text.descriptionMM}</Label>
                    <Textarea
                      id="descriptionMM"
                      value={footerSettings.descriptionMM}
                      onChange={(e) => updateField('descriptionMM', e.target.value)}
                      placeholder="သင့်လုပ်ငန်းအတွက် ခေတ်မီ ရောင်းချရေး စနစ်"
                      rows={3}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Contact Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Phone className="h-5 w-5 text-green-600" />
                  {text.contactInfo}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="email">{text.email}</Label>
                    <div className="relative">
                      <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="email"
                        type="email"
                        value={footerSettings.email}
                        onChange={(e) => updateField('email', e.target.value)}
                        placeholder="<EMAIL>"
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="phone">{text.phone}</Label>
                    <div className="relative">
                      <Phone className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="phone"
                        value={footerSettings.phone}
                        onChange={(e) => updateField('phone', e.target.value)}
                        placeholder="+95 9 ***********"
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="address">{text.address}</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Textarea
                        id="address"
                        value={footerSettings.address}
                        onChange={(e) => updateField('address', e.target.value)}
                        placeholder="Yangon, Myanmar"
                        className="pl-10"
                        rows={2}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="addressMM">{text.addressMM}</Label>
                    <div className="relative">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Textarea
                        id="addressMM"
                        value={footerSettings.addressMM}
                        onChange={(e) => updateField('addressMM', e.target.value)}
                        placeholder="ရန်ကုန်၊ မြန်မာ"
                        className="pl-10"
                        rows={2}
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="website">{text.website}</Label>
                    <div className="relative">
                      <Globe className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="website"
                        type="url"
                        value={footerSettings.website}
                        onChange={(e) => updateField('website', e.target.value)}
                        placeholder="https://bitstech.com"
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Social Media Links */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Facebook className="h-5 w-5 text-blue-600" />
                  {text.socialLinks}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="facebook">{text.facebook}</Label>
                    <div className="relative">
                      <Facebook className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="facebook"
                        type="url"
                        value={footerSettings.socialLinks.facebook || ''}
                        onChange={(e) => updateSocialLink('facebook', e.target.value)}
                        placeholder="https://facebook.com/yourpage"
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="twitter">{text.twitter}</Label>
                    <div className="relative">
                      <Twitter className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="twitter"
                        type="url"
                        value={footerSettings.socialLinks.twitter || ''}
                        onChange={(e) => updateSocialLink('twitter', e.target.value)}
                        placeholder="https://twitter.com/yourhandle"
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="linkedin">{text.linkedin}</Label>
                    <div className="relative">
                      <Linkedin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="linkedin"
                        type="url"
                        value={footerSettings.socialLinks.linkedin || ''}
                        onChange={(e) => updateSocialLink('linkedin', e.target.value)}
                        placeholder="https://linkedin.com/company/yourcompany"
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="instagram">{text.instagram}</Label>
                    <div className="relative">
                      <Instagram className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="instagram"
                        type="url"
                        value={footerSettings.socialLinks.instagram || ''}
                        onChange={(e) => updateSocialLink('instagram', e.target.value)}
                        placeholder="https://instagram.com/yourhandle"
                        className="pl-10"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="youtube">{text.youtube}</Label>
                    <div className="relative">
                      <Youtube className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                      <Input
                        id="youtube"
                        type="url"
                        value={footerSettings.socialLinks.youtube || ''}
                        onChange={(e) => updateSocialLink('youtube', e.target.value)}
                        placeholder="https://youtube.com/yourchannel"
                        className="pl-10"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Footer Content */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-600" />
                  {text.footerContent}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="copyright">{text.copyright}</Label>
                    <Input
                      id="copyright"
                      value={footerSettings.copyright}
                      onChange={(e) => updateField('copyright', e.target.value)}
                      placeholder="© 2024 BitsTech. All rights reserved."
                    />
                  </div>
                  <div>
                    <Label htmlFor="copyrightMM">{text.copyrightMM}</Label>
                    <Input
                      id="copyrightMM"
                      value={footerSettings.copyrightMM}
                      onChange={(e) => updateField('copyrightMM', e.target.value)}
                      placeholder="© ၂၀၂၄ BitsTech။ မူပိုင်ခွင့် အားလုံး ရယူထားသည်။"
                    />
                  </div>
                  <div>
                    <Label htmlFor="version">{text.version}</Label>
                    <Input
                      id="version"
                      value={footerSettings.version}
                      onChange={(e) => updateField('version', e.target.value)}
                      placeholder="v1.0.0"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Display Options */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Globe className="h-5 w-5 text-purple-600" />
                  {text.displayOptions}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="showCompanyInfo">{text.showCompanyInfo}</Label>
                    <input
                      type="checkbox"
                      id="showCompanyInfo"
                      checked={footerSettings.showCompanyInfo}
                      onChange={(e) => updateField('showCompanyInfo', e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="showContactInfo">{text.showContactInfo}</Label>
                    <input
                      type="checkbox"
                      id="showContactInfo"
                      checked={footerSettings.showContactInfo}
                      onChange={(e) => updateField('showContactInfo', e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                  </div>
                  <div className="flex items-center justify-between">
                    <Label htmlFor="showSocialLinks">{text.showSocialLinks}</Label>
                    <input
                      type="checkbox"
                      id="showSocialLinks"
                      checked={footerSettings.showSocialLinks}
                      onChange={(e) => updateField('showSocialLinks', e.target.checked)}
                      className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Appearance Settings */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-pink-600" />
                  {text.appearance}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <Label htmlFor="footerStyle">{text.footerStyle}</Label>
                    <select
                      id="footerStyle"
                      value={footerSettings.footerStyle}
                      onChange={(e) => updateField('footerStyle', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    >
                      <option value="default">Default</option>
                      <option value="minimal">Minimal</option>
                      <option value="detailed">Detailed</option>
                    </select>
                  </div>
                  <div>
                    <Label htmlFor="backgroundColor">{text.backgroundColor}</Label>
                    <div className="flex gap-2">
                      <input
                        type="color"
                        id="backgroundColor"
                        value={footerSettings.backgroundColor}
                        onChange={(e) => updateField('backgroundColor', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                      />
                      <Input
                        value={footerSettings.backgroundColor}
                        onChange={(e) => updateField('backgroundColor', e.target.value)}
                        placeholder="#1F2937"
                        className="flex-1"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="textColor">{text.textColor}</Label>
                    <div className="flex gap-2">
                      <input
                        type="color"
                        id="textColor"
                        value={footerSettings.textColor}
                        onChange={(e) => updateField('textColor', e.target.value)}
                        className="w-12 h-10 border border-gray-300 rounded cursor-pointer"
                      />
                      <Input
                        value={footerSettings.textColor}
                        onChange={(e) => updateField('textColor', e.target.value)}
                        placeholder="#FFFFFF"
                        className="flex-1"
                      />
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Preview Panel */}
          <div className="space-y-6">
            {/* Footer Preview */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-600" />
                  {text.preview}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div 
                  className="rounded-lg p-6 text-white"
                  style={{ 
                    backgroundColor: footerSettings.backgroundColor,
                    color: footerSettings.textColor 
                  }}
                >
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {/* Company Info */}
                    {footerSettings.showCompanyInfo && (
                      <div>
                        <div className="flex items-center gap-2 mb-3">
                          <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                            <span className="text-white font-bold text-xs">B</span>
                          </div>
                          <h3 className="font-bold text-sm">
                            {language === 'mm' ? footerSettings.companyNameMM : footerSettings.companyName}
                          </h3>
                        </div>
                        <p className="text-xs opacity-80 mb-3">
                          {language === 'mm' ? footerSettings.descriptionMM : footerSettings.description}
                        </p>
                      </div>
                    )}

                    {/* Contact Info */}
                    {footerSettings.showContactInfo && (
                      <div>
                        <h4 className="font-semibold text-sm mb-3">Contact</h4>
                        <div className="space-y-2 text-xs">
                          {footerSettings.email && (
                            <div className="flex items-center gap-2">
                              <Mail className="h-3 w-3" />
                              <span>{footerSettings.email}</span>
                            </div>
                          )}
                          {footerSettings.phone && (
                            <div className="flex items-center gap-2">
                              <Phone className="h-3 w-3" />
                              <span>{footerSettings.phone}</span>
                            </div>
                          )}
                          {footerSettings.address && (
                            <div className="flex items-center gap-2">
                              <MapPin className="h-3 w-3" />
                              <span>{language === 'mm' ? footerSettings.addressMM : footerSettings.address}</span>
                            </div>
                          )}
                        </div>
                      </div>
                    )}

                    {/* Social Links */}
                    {footerSettings.showSocialLinks && (
                      <div>
                        <h4 className="font-semibold text-sm mb-3">Follow Us</h4>
                        <div className="flex gap-2">
                          {footerSettings.socialLinks.facebook && (
                            <div className="w-6 h-6 bg-blue-600 rounded flex items-center justify-center">
                              <Facebook className="h-3 w-3" />
                            </div>
                          )}
                          {footerSettings.socialLinks.twitter && (
                            <div className="w-6 h-6 bg-sky-500 rounded flex items-center justify-center">
                              <Twitter className="h-3 w-3" />
                            </div>
                          )}
                          {footerSettings.socialLinks.linkedin && (
                            <div className="w-6 h-6 bg-blue-700 rounded flex items-center justify-center">
                              <Linkedin className="h-3 w-3" />
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Bottom Bar */}
                  <div className="border-t border-gray-600 mt-4 pt-4">
                    <div className="flex justify-between items-center text-xs">
                      <span>{language === 'mm' ? footerSettings.copyrightMM : footerSettings.copyright}</span>
                      <span>Version {footerSettings.version}</span>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
