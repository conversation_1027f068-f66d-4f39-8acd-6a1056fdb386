'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import apiClient from '@/lib/api'

interface Notification {
  id: string
  type: 'info' | 'success' | 'warning' | 'error'
  title: string
  message: string
  timestamp: Date
  read: boolean
  action?: {
    label: string
    url: string
  }
}

interface NotificationSettings {
  emailNotifications: {
    enabled: boolean
    lowStock: boolean
    newOrders: boolean
    paymentReceived: boolean
    dailyReports: boolean
    weeklyReports: boolean
    systemUpdates: boolean
  }
  pushNotifications: {
    enabled: boolean
    lowStock: boolean
    newOrders: boolean
    paymentReceived: boolean
    systemAlerts: boolean
  }
  inAppNotifications: {
    enabled: boolean
    lowStock: boolean
    newOrders: boolean
    paymentReceived: boolean
    systemAlerts: boolean
    sound: boolean
  }
  smsNotifications: {
    enabled: boolean
    lowStock: boolean
    newOrders: boolean
    paymentReceived: boolean
  }
  notificationFrequency: string
  quietHours: {
    enabled: boolean
    startTime: string
    endTime: string
  }
  emailAddress: string
  phoneNumber: string
}

interface NotificationContextType {
  notifications: Notification[]
  unreadCount: number
  settings: NotificationSettings | null
  addNotification: (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => void
  markAsRead: (id: string) => void
  markAllAsRead: () => void
  removeNotification: (id: string) => void
  clearAll: () => void
  updateSettings: (settings: Partial<NotificationSettings>) => Promise<boolean>
  refreshSettings: () => Promise<void>
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined)

export function NotificationProvider({ children }: { children: ReactNode }) {
  const [notifications, setNotifications] = useState<Notification[]>([])
  const [settings, setSettings] = useState<NotificationSettings | null>(null)

  const unreadCount = notifications.filter(n => !n.read).length

  useEffect(() => {
    loadNotificationSettings()
    loadNotifications()
    
    // Set up periodic check for new notifications
    const interval = setInterval(() => {
      checkForNewNotifications()
    }, 30000) // Check every 30 seconds

    return () => clearInterval(interval)
  }, [])

  const loadNotificationSettings = async () => {
    try {
      // Use fallback since API method doesn't exist
      const response = { success: false, data: {} }
      if (response.success && response.data) {
        setSettings((response as any).data)
      } else {
        // Default settings
        setSettings({
          emailNotifications: {
            enabled: true,
            lowStock: true,
            newOrders: true,
            paymentReceived: true,
            dailyReports: false,
            weeklyReports: true,
            systemUpdates: true
          },
          pushNotifications: {
            enabled: true,
            lowStock: true,
            newOrders: true,
            paymentReceived: true,
            systemAlerts: true
          },
          inAppNotifications: {
            enabled: true,
            lowStock: true,
            newOrders: true,
            paymentReceived: true,
            systemAlerts: true,
            sound: true
          },
          smsNotifications: {
            enabled: false,
            lowStock: false,
            newOrders: false,
            paymentReceived: false
          },
          notificationFrequency: 'immediate',
          quietHours: {
            enabled: false,
            startTime: '22:00',
            endTime: '08:00'
          },
          emailAddress: '',
          phoneNumber: ''
        })
      }
    } catch (error) {
      console.error('Error loading notification settings:', error)
    }
  }

  const loadNotifications = () => {
    // Load from localStorage
    const stored = localStorage.getItem('bitstech_notifications')
    if (stored) {
      try {
        const parsed = JSON.parse(stored)
        setNotifications(parsed.map((n: any) => ({
          ...n,
          timestamp: new Date(n.timestamp)
        })))
      } catch (error) {
        console.error('Error parsing stored notifications:', error)
      }
    }
  }

  const saveNotifications = (newNotifications: Notification[]) => {
    localStorage.setItem('bitstech_notifications', JSON.stringify(newNotifications))
  }

  const checkForNewNotifications = async () => {
    if (!settings?.inAppNotifications.enabled) return

    try {
      // Check for low stock alerts
      if (settings.inAppNotifications.lowStock) {
        const inventoryResponse = await apiClient.getInventoryAlerts()
        if (inventoryResponse.success && (inventoryResponse as any).data?.length > 0) {
          (inventoryResponse as any).data.forEach((alert: any) => {
            addNotification({
              type: 'warning',
              title: 'Low Stock Alert',
              message: `${alert.productName} is running low (${alert.quantity} remaining)`,
              action: {
                label: 'View Inventory',
                url: '/inventory'
              }
            })
          })
        }
      }

      // Check for new orders
      if (settings.inAppNotifications.newOrders) {
        // Use fallback since API method doesn't exist
        const salesResponse = { success: false, data: [] }
        if (salesResponse.success && (salesResponse as any).data?.length > 0) {
          const recentSales = (salesResponse as any).data.filter((sale: any) => {
            const saleTime = new Date(sale.createdAt)
            const now = new Date()
            return (now.getTime() - saleTime.getTime()) < 300000 // Last 5 minutes
          })

          recentSales.forEach((sale: any) => {
            addNotification({
              type: 'success',
              title: 'New Order',
              message: `Order #${sale.saleNumber} - $${sale.total}`,
              action: {
                label: 'View Order',
                url: `/sales/${sale._id}`
              }
            })
          })
        }
      }
    } catch (error) {
      console.error('Error checking for notifications:', error)
    }
  }

  const addNotification = (notification: Omit<Notification, 'id' | 'timestamp' | 'read'>) => {
    const newNotification: Notification = {
      ...notification,
      id: Date.now().toString(),
      timestamp: new Date(),
      read: false
    }

    setNotifications(prev => {
      const updated = [newNotification, ...prev].slice(0, 50) // Keep only last 50
      saveNotifications(updated)
      return updated
    })

    // Play sound if enabled
    if (settings?.inAppNotifications.sound) {
      try {
        const audio = new Audio('/sounds/notification.mp3')
        audio.volume = 0.3
        audio.play().catch(() => {
          // Ignore audio play errors
        })
      } catch (error) {
        // Ignore audio errors
      }
    }
  }

  const markAsRead = (id: string) => {
    setNotifications(prev => {
      const updated = prev.map(n => n.id === id ? { ...n, read: true } : n)
      saveNotifications(updated)
      return updated
    })
  }

  const markAllAsRead = () => {
    setNotifications(prev => {
      const updated = prev.map(n => ({ ...n, read: true }))
      saveNotifications(updated)
      return updated
    })
  }

  const removeNotification = (id: string) => {
    setNotifications(prev => {
      const updated = prev.filter(n => n.id !== id)
      saveNotifications(updated)
      return updated
    })
  }

  const clearAll = () => {
    setNotifications([])
    localStorage.removeItem('bitstech_notifications')
  }

  const updateSettings = async (newSettings: Partial<NotificationSettings>): Promise<boolean> => {
    try {
      const updatedSettings = { ...settings, ...newSettings }
      // Use fallback since API method doesn't exist
      const response = { success: false, error: 'Notification settings API not implemented yet' }

      if (response.success) {
        setSettings(updatedSettings as NotificationSettings)
        return true
      }
      return false
    } catch (error) {
      console.error('Error updating notification settings:', error)
      return false
    }
  }

  const refreshSettings = async () => {
    await loadNotificationSettings()
  }

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    settings,
    addNotification,
    markAsRead,
    markAllAsRead,
    removeNotification,
    clearAll,
    updateSettings,
    refreshSettings
  }

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  )
}

export function useNotifications() {
  const context = useContext(NotificationContext)
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider')
  }
  return context
}

export type { Notification, NotificationSettings }
