const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import User model
const User = require('../src/models/User');

const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos';
        await mongoose.connect(mongoURI);
        console.log('✅ MongoDB connected for login test');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error.message);
        process.exit(1);
    }
};

async function testLogin() {
    try {
        console.log('🔄 Testing User Login...\n');

        await connectDB();

        // Test credentials
        const testCredentials = [
            { email: '<EMAIL>', password: 'admin123' },
            { email: '<EMAIL>', password: 'manager123' },
            { email: '<EMAIL>', password: 'cashier123' }
        ];

        for (const cred of testCredentials) {
            console.log(`🔐 Testing login for: ${cred.email}`);

            // Find user by email
            const user = await User.findOne({ email: cred.email }).select('+password');
            
            if (!user) {
                console.log(`❌ User not found: ${cred.email}\n`);
                continue;
            }

            console.log(`✅ User found: ${user.username} (${user.role})`);
            console.log(`   Password hash: ${user.password.substring(0, 20)}...`);

            // Test password
            const isMatch = await bcrypt.compare(cred.password, user.password);
            console.log(`   Password match: ${isMatch ? '✅ Valid' : '❌ Invalid'}`);

            if (!isMatch) {
                // Try to rehash and update password
                console.log(`   🔄 Rehashing password for ${user.email}...`);
                const salt = await bcrypt.genSalt(10);
                const hashedPassword = await bcrypt.hash(cred.password, salt);
                
                await User.findByIdAndUpdate(user._id, { password: hashedPassword });
                console.log(`   ✅ Password updated for ${user.email}`);
            }

            console.log('');
        }

        // List all users
        console.log('👥 All Users in Database:');
        const allUsers = await User.find({}).select('username email role isActive');
        allUsers.forEach(user => {
            console.log(`   - ${user.username} (${user.email}) - ${user.role} - ${user.isActive ? 'Active' : 'Inactive'}`);
        });

        await mongoose.disconnect();
        console.log('\n✅ Login test completed!');
        process.exit(0);

    } catch (error) {
        console.error('❌ Error during login test:', error);
        process.exit(1);
    }
}

// Run the test
testLogin();
