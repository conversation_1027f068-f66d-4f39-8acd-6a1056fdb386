'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useSettings, useTranslation } from '@/contexts/settings-context'
import { useNotifications } from '@/contexts/notification-context'
import apiClient from '@/lib/api'
import {
  Bell,
  ArrowLeft,
  Save,
  Check,
  Mail,
  MessageSquare,
  AlertTriangle,
  TrendingDown,
  Package,
  DollarSign,
  Users,
  Shield,
  Volume2,
  VolumeX,
  Smartphone,
  Monitor,
  Send,
  TestTube,
  Loader2,
  Zap,
  Phone,
  CheckCircle
} from 'lucide-react'

interface NotificationSetting {
  id: string
  title: string
  titleLocal: string
  description: string
  descriptionLocal: string
  category: 'inventory' | 'sales' | 'system' | 'security'
  icon: any
  enabled: boolean
  email: boolean
  push: boolean
  sms: boolean
  sound: boolean
  threshold?: number
}

export default function NotificationsSettingsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [testing, setTesting] = useState<string | null>(null)
  const [testResults, setTestResults] = useState<Record<string, 'success' | 'failed' | null>>({})

  const [globalSettings, setGlobalSettings] = useState({
    emailNotifications: true,
    pushNotifications: true,
    smsNotifications: false,
    soundEnabled: true,
    quietHours: {
      enabled: false,
      start: '22:00',
      end: '08:00'
    }
  })

  const [notifications, setNotifications] = useState<NotificationSetting[]>([
    {
      id: 'low-stock',
      title: 'Low Stock Alert',
      titleLocal: 'ကုန်ပစ္စည်း နည်းပါးမှု သတိပေးချက်',
      description: 'Alert when product quantity falls below minimum threshold',
      descriptionLocal: 'ကုန်ပစ္စည်း အရေအတွက် အနည်းဆုံး ကန့်သတ်ချက်အောက် ကျဆင်းသောအခါ သတိပေးပါ',
      category: 'inventory',
      icon: Package,
      enabled: true,
      email: true,
      push: true,
      sms: false,
      sound: true,
      threshold: 10
    },
    {
      id: 'out-of-stock',
      title: 'Out of Stock',
      titleLocal: 'ကုန်ပစ္စည်း ကုန်ခမ်းမှု',
      description: 'Immediate alert when products are completely out of stock',
      descriptionLocal: 'ကုန်ပစ္စည်းများ လုံးဝ ကုန်ခမ်းသောအခါ ချက်ချင်း သတိပေးပါ',
      category: 'inventory',
      icon: AlertTriangle,
      enabled: true,
      email: true,
      push: true,
      sms: true,
      sound: true
    },
    {
      id: 'daily-sales',
      title: 'Daily Sales Report',
      titleLocal: 'နေ့စဉ် ရောင်းအား အစီရင်ခံစာ',
      description: 'Daily summary of sales performance',
      descriptionLocal: 'ရောင်းအား စွမ်းဆောင်ရည်၏ နေ့စဉ် အနှစ်ချုပ်',
      category: 'sales',
      icon: TrendingDown,
      enabled: true,
      email: true,
      push: false,
      sms: false,
      sound: false
    },
    {
      id: 'high-value-sale',
      title: 'High Value Sale',
      titleLocal: 'တန်ဖိုးမြင့် ရောင်းချမှု',
      description: 'Alert for sales above specified amount',
      descriptionLocal: 'သတ်မှတ်ထားသော ပမာဏထက် ကျော်လွန်သော ရောင်းချမှုများ အတွက် သတိပေးပါ',
      category: 'sales',
      icon: DollarSign,
      enabled: true,
      email: false,
      push: true,
      sms: false,
      sound: true,
      threshold: 1000000
    },
    {
      id: 'new-user',
      title: 'New User Registration',
      titleLocal: 'အသုံးပြုသူ အသစ် မှတ်ပုံတင်မှု',
      description: 'Notification when new users are added to the system',
      descriptionLocal: 'စနစ်တွင် အသုံးပြုသူ အသစ်များ ထည့်သွင်းသောအခါ အကြောင်းကြားပါ',
      category: 'system',
      icon: Users,
      enabled: true,
      email: true,
      push: false,
      sms: false,
      sound: false
    },
    {
      id: 'system-backup',
      title: 'System Backup Status',
      titleLocal: 'စနစ် အရန်သိမ်းမှု အခြေအနေ',
      description: 'Daily backup completion status',
      descriptionLocal: 'နေ့စဉ် အရန်သိမ်းမှု ပြီးစီးမှု အခြေအနေ',
      category: 'system',
      icon: Shield,
      enabled: true,
      email: true,
      push: false,
      sms: false,
      sound: false
    },
    {
      id: 'failed-login',
      title: 'Failed Login Attempts',
      titleLocal: 'လော့ဂ်အင် မအောင်မြင်မှုများ',
      description: 'Alert for multiple failed login attempts',
      descriptionLocal: 'လော့ဂ်အင် မအောင်မြင်မှု များစွာ ရှိသောအခါ သတိပေးပါ',
      category: 'security',
      icon: Shield,
      enabled: true,
      email: true,
      push: true,
      sms: true,
      sound: true,
      threshold: 3
    },
    {
      id: 'data-export',
      title: 'Data Export Complete',
      titleLocal: 'ဒေတာ ထုတ်ယူမှု ပြီးစီးမှု',
      description: 'Notification when data export operations complete',
      descriptionLocal: 'ဒေတာ ထုတ်ယူမှု လုပ်ငန်းများ ပြီးစီးသောအခါ အကြောင်းကြားပါ',
      category: 'system',
      icon: Monitor,
      enabled: false,
      email: true,
      push: false,
      sms: false,
      sound: false
    }
  ])

  // Load notification settings
  const loadNotificationSettings = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.getNotificationSettings()

      if (response.success) {
        const settings = response.data

        // Update global settings
        setGlobalSettings({
          emailNotifications: settings.emailNotifications,
          pushNotifications: settings.pushNotifications,
          smsNotifications: settings.smsNotifications,
          soundEnabled: settings.soundEnabled,
          quietHours: settings.quietHours || {
            enabled: false,
            start: '22:00',
            end: '08:00'
          }
        })

        // Update notification categories
        if (settings.categories) {
          const updatedNotifications = notifications.map(notification => {
            const categorySettings = settings.categories[notification.category]
            const notificationSettings = categorySettings?.[notification.id.replace('-', '')]

            if (notificationSettings) {
              return {
                ...notification,
                enabled: notificationSettings.enabled,
                email: notificationSettings.email,
                push: notificationSettings.push,
                sms: notificationSettings.sms,
                threshold: notificationSettings.threshold || notification.threshold
              }
            }
            return notification
          })
          setNotifications(updatedNotifications)
        }
      } else {
        setError(response.message || 'Failed to load notification settings')
      }
    } catch (error) {
      console.error('Error loading notification settings:', error)
      setError('Failed to load notification settings')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    } else if (isAuthenticated) {
      loadNotificationSettings()
    }
  }, [isAuthenticated, isLoading, router])

  const updateNotification = (id: string, field: keyof NotificationSetting, value: any) => {
    setNotifications(prev => prev.map(notification =>
      notification.id === id
        ? { ...notification, [field]: value }
        : notification
    ))
  }

  const updateGlobalSetting = (field: string, value: any) => {
    setGlobalSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Prepare notification settings data
      const categories: any = {}

      notifications.forEach(notification => {
        if (!categories[notification.category]) {
          categories[notification.category] = {}
        }

        const key = notification.id.replace('-', '')
        categories[notification.category][key] = {
          enabled: notification.enabled,
          email: notification.email,
          push: notification.push,
          sms: notification.sms,
          threshold: notification.threshold
        }
      })

      const settingsData = {
        ...globalSettings,
        categories
      }

      const response = await apiClient.updateNotificationSettings(settingsData)

      if (response.success) {
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)
      } else {
        throw new Error(response.error || 'Failed to save notification settings')
      }
    } catch (error) {
      console.error('Error saving notifications:', error)
      alert(language === 'mm'
        ? 'အကြောင်းကြားချက် ဆက်တင်များ သိမ်းဆည်းမှု မအောင်မြင်ပါ'
        : 'Failed to save notification settings'
      )
    } finally {
      setSaving(false)
    }
  }

  const handleTestNotification = async (type: 'email' | 'push' | 'sms') => {
    setTesting(type)
    setTestResults(prev => ({ ...prev, [type]: null }))

    try {
      const response = await apiClient.testNotification('test', type)

      if (response.success) {
        setTestResults(prev => ({ ...prev, [type]: 'success' }))

        const typeNames = {
          email: language === 'mm' ? 'အီးမေးလ်' : 'Email',
          push: language === 'mm' ? 'တွန်းပို့' : 'Push',
          sms: language === 'mm' ? 'SMS' : 'SMS'
        }

        alert(language === 'mm'
          ? `${typeNames[type]} စမ်းသပ်မှု အောင်မြင်ပါသည်`
          : `${typeNames[type]} test notification sent successfully`
        )
      } else {
        throw new Error(response.error || 'Test notification failed')
      }
    } catch (error) {
      console.error('Test notification failed:', error)
      setTestResults(prev => ({ ...prev, [type]: 'failed' }))

      const typeNames = {
        email: language === 'mm' ? 'အီးမေးလ်' : 'Email',
        push: language === 'mm' ? 'တွန်းပို့' : 'Push',
        sms: language === 'mm' ? 'SMS' : 'SMS'
      }

      alert(language === 'mm'
        ? `${typeNames[type]} စမ်းသပ်မှု မအောင်မြင်ပါ`
        : `${typeNames[type]} test notification failed`
      )
    } finally {
      setTesting(null)
    }
  }

  const getCategoryColor = (category: string) => {
    switch (category) {
      case 'inventory': return 'text-blue-600'
      case 'sales': return 'text-green-600'
      case 'system': return 'text-purple-600'
      case 'security': return 'text-red-600'
      default: return 'text-gray-600'
    }
  }

  const getCategoryBg = (category: string) => {
    switch (category) {
      case 'inventory': return 'bg-blue-100 dark:bg-blue-900/20'
      case 'sales': return 'bg-green-100 dark:bg-green-900/20'
      case 'system': return 'bg-purple-100 dark:bg-purple-900/20'
      case 'security': return 'bg-red-100 dark:bg-red-900/20'
      default: return 'bg-gray-100 dark:bg-gray-900/20'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-yellow-50 dark:hover:bg-yellow-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-yellow-600 to-orange-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Bell className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {language === 'mm' ? 'အကြောင်းကြားချက်များ' : 'Notifications'}
                </h1>
                <p className="text-white/80 mt-1">
                  {language === 'mm'
                    ? 'သတိပေးချက်များ၊ အီးမေးလ် အကြောင်းကြားချက်များ နှင့် စနစ် မက်ဆေ့ချ်များ ပြင်ဆင်ပါ'
                    : 'Configure alerts, email notifications, and system messages'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Global Settings */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bell className="h-5 w-5 text-yellow-600" />
                {language === 'mm' ? 'အထွေထွေ ဆက်တင်များ' : 'Global Settings'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'အကြောင်းကြားချက် နည်းလမ်းများ နှင့် အထွေထွေ ရွေးချယ်မှုများ'
                  : 'Notification methods and general preferences'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Notification Methods */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'အကြောင်းကြားမှု နည်းလမ်းများ' : 'Notification Methods'}
                </Label>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-blue-600" />
                      <span className="text-sm">
                        {language === 'mm' ? 'အီးမေးလ် အကြောင်းကြားချက်များ' : 'Email Notifications'}
                      </span>
                    </div>
                    <input
                      type="checkbox"
                      checked={globalSettings.emailNotifications}
                      onChange={(e) => updateGlobalSetting('emailNotifications', e.target.checked)}
                      className="rounded"
                      aria-label={language === 'mm' ? 'အီးမေးလ် အကြောင်းကြားချက်များ' : 'Email Notifications'}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <Smartphone className="h-4 w-4 text-green-600" />
                      <span className="text-sm">
                        {language === 'mm' ? 'တွန်းပို့ အကြောင်းကြားချက်များ' : 'Push Notifications'}
                      </span>
                    </div>
                    <input
                      type="checkbox"
                      checked={globalSettings.pushNotifications}
                      onChange={(e) => updateGlobalSetting('pushNotifications', e.target.checked)}
                      className="rounded"
                      aria-label={language === 'mm' ? 'တွန်းပို့ အကြောင်းကြားချက်များ' : 'Push Notifications'}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      <MessageSquare className="h-4 w-4 text-purple-600" />
                      <span className="text-sm">
                        {language === 'mm' ? 'SMS အကြောင်းကြားချက်များ' : 'SMS Notifications'}
                      </span>
                    </div>
                    <input
                      type="checkbox"
                      checked={globalSettings.smsNotifications}
                      onChange={(e) => updateGlobalSetting('smsNotifications', e.target.checked)}
                      className="rounded"
                      aria-label={language === 'mm' ? 'SMS အကြောင်းကြားချက်များ' : 'SMS Notifications'}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-2">
                      {globalSettings.soundEnabled ? (
                        <Volume2 className="h-4 w-4 text-orange-600" />
                      ) : (
                        <VolumeX className="h-4 w-4 text-gray-400" />
                      )}
                      <span className="text-sm">
                        {language === 'mm' ? 'အသံ သတိပေးချက်များ' : 'Sound Alerts'}
                      </span>
                    </div>
                    <input
                      type="checkbox"
                      checked={globalSettings.soundEnabled}
                      onChange={(e) => updateGlobalSetting('soundEnabled', e.target.checked)}
                      className="rounded"
                      aria-label={language === 'mm' ? 'အသံ သတိပေးချက်များ' : 'Sound Alerts'}
                    />
                  </div>
                </div>
              </div>

              {/* Quiet Hours */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'ဆိတ်ငြိမ်သော အချိန်များ' : 'Quiet Hours'}
                </Label>

                <div className="flex items-center justify-between">
                  <span className="text-sm">
                    {language === 'mm' ? 'ဆိတ်ငြိမ်သော အချိန် ဖွင့်ရန်' : 'Enable Quiet Hours'}
                  </span>
                  <input
                    type="checkbox"
                    checked={globalSettings.quietHours.enabled}
                    onChange={(e) => updateGlobalSetting('quietHours', {
                      ...globalSettings.quietHours,
                      enabled: e.target.checked
                    })}
                    className="rounded"
                    aria-label={language === 'mm' ? 'ဆိတ်ငြိမ်သော အချိန် ဖွင့်ရန်' : 'Enable Quiet Hours'}
                  />
                </div>

                {globalSettings.quietHours.enabled && (
                  <div className="grid grid-cols-2 gap-3">
                    <div className="space-y-1">
                      <Label className="text-xs">
                        {language === 'mm' ? 'စတင်ချိန်' : 'Start Time'}
                      </Label>
                      <Input
                        type="time"
                        value={globalSettings.quietHours.start}
                        onChange={(e) => updateGlobalSetting('quietHours', {
                          ...globalSettings.quietHours,
                          start: e.target.value
                        })}
                        className="text-sm"
                      />
                    </div>
                    <div className="space-y-1">
                      <Label className="text-xs">
                        {language === 'mm' ? 'ပြီးဆုံးချိန်' : 'End Time'}
                      </Label>
                      <Input
                        type="time"
                        value={globalSettings.quietHours.end}
                        onChange={(e) => updateGlobalSetting('quietHours', {
                          ...globalSettings.quietHours,
                          end: e.target.value
                        })}
                        className="text-sm"
                      />
                    </div>
                  </div>
                )}
              </div>

              {/* Test Notifications */}
              <div className="space-y-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'အကြောင်းကြားချက် စမ်းသပ်မှု' : 'Test Notifications'}
                </Label>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestNotification('email')}
                    disabled={testing === 'email' || !globalSettings.emailNotifications}
                    className="flex items-center gap-2"
                  >
                    {testing === 'email' ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : testResults.email === 'success' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : testResults.email === 'failed' ? (
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                    ) : (
                      <Mail className="h-4 w-4" />
                    )}
                    {language === 'mm' ? 'အီးမေးလ် စမ်းသပ်ရန်' : 'Test Email'}
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestNotification('push')}
                    disabled={testing === 'push' || !globalSettings.pushNotifications}
                    className="flex items-center gap-2"
                  >
                    {testing === 'push' ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : testResults.push === 'success' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : testResults.push === 'failed' ? (
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                    ) : (
                      <Smartphone className="h-4 w-4" />
                    )}
                    {language === 'mm' ? 'တွန်းပို့ စမ်းသပ်ရန်' : 'Test Push'}
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleTestNotification('sms')}
                    disabled={testing === 'sms' || !globalSettings.smsNotifications}
                    className="flex items-center gap-2"
                  >
                    {testing === 'sms' ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : testResults.sms === 'success' ? (
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    ) : testResults.sms === 'failed' ? (
                      <AlertTriangle className="h-4 w-4 text-red-600" />
                    ) : (
                      <MessageSquare className="h-4 w-4" />
                    )}
                    {language === 'mm' ? 'SMS စမ်းသပ်ရန်' : 'Test SMS'}
                  </Button>
                </div>

                <p className="text-xs text-gray-600 dark:text-gray-400">
                  {language === 'mm'
                    ? 'စမ်းသပ်မှု အကြောင်းကြားချက်များ သင့်အီးမေးလ်၊ ဖုန်း သို့မဟုတ် စက်ပစ္စည်းသို့ ပို့ပေးပါမည်'
                    : 'Test notifications will be sent to your email, phone, or device'
                  }
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Notification Settings */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-orange-600" />
                {language === 'mm' ? 'အကြောင်းကြားချက် ဆက်တင်များ' : 'Notification Settings'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'တစ်ခုချင်းစီ အကြောင်းကြားချက် အမျိုးအစားများ အတွက် ဆက်တင်များ'
                  : 'Configure settings for individual notification types'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {notifications.map((notification) => {
                  const Icon = notification.icon
                  return (
                    <div key={notification.id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex items-start gap-3">
                          <div className={`p-2 rounded-lg ${getCategoryBg(notification.category)}`}>
                            <Icon className={`h-5 w-5 ${getCategoryColor(notification.category)}`} />
                          </div>
                          <div>
                            <div className="flex items-center gap-2">
                              <h4 className="font-medium">
                                {language === 'mm' ? notification.titleLocal : notification.title}
                              </h4>
                              <Badge variant="outline" className="text-xs capitalize">
                                {notification.category}
                              </Badge>
                            </div>
                            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                              {language === 'mm' ? notification.descriptionLocal : notification.description}
                            </p>
                          </div>
                        </div>
                        <input
                          type="checkbox"
                          checked={notification.enabled}
                          onChange={(e) => updateNotification(notification.id, 'enabled', e.target.checked)}
                          className="rounded"
                          aria-label={`${language === 'mm' ? 'ဖွင့်ရန်' : 'Enable'} ${language === 'mm' ? notification.titleLocal : notification.title}`}
                        />
                      </div>

                      {notification.enabled && (
                        <div className="ml-11 space-y-3">
                          {/* Notification Methods */}
                          <div className="flex items-center gap-6">
                            <label className="flex items-center gap-2 text-sm">
                              <input
                                type="checkbox"
                                checked={notification.email}
                                onChange={(e) => updateNotification(notification.id, 'email', e.target.checked)}
                                className="rounded"
                              />
                              <Mail className="h-3 w-3" />
                              Email
                            </label>
                            <label className="flex items-center gap-2 text-sm">
                              <input
                                type="checkbox"
                                checked={notification.push}
                                onChange={(e) => updateNotification(notification.id, 'push', e.target.checked)}
                                className="rounded"
                              />
                              <Smartphone className="h-3 w-3" />
                              Push
                            </label>
                            <label className="flex items-center gap-2 text-sm">
                              <input
                                type="checkbox"
                                checked={notification.sms}
                                onChange={(e) => updateNotification(notification.id, 'sms', e.target.checked)}
                                className="rounded"
                              />
                              <MessageSquare className="h-3 w-3" />
                              SMS
                            </label>
                            <label className="flex items-center gap-2 text-sm">
                              <input
                                type="checkbox"
                                checked={notification.sound}
                                onChange={(e) => updateNotification(notification.id, 'sound', e.target.checked)}
                                className="rounded"
                              />
                              <Volume2 className="h-3 w-3" />
                              Sound
                            </label>
                          </div>

                          {/* Threshold Settings */}
                          {notification.threshold !== undefined && (
                            <div className="flex items-center gap-3">
                              <Label className="text-sm">
                                {notification.id === 'low-stock'
                                  ? (language === 'mm' ? 'အနည်းဆုံး အရေအတွက်' : 'Minimum Quantity')
                                  : notification.id === 'high-value-sale'
                                  ? (language === 'mm' ? 'အမြင့်ဆုံး ပမာဏ (MMK)' : 'Minimum Amount (MMK)')
                                  : (language === 'mm' ? 'ကန့်သတ်ချက်' : 'Threshold')
                                }:
                              </Label>
                              <Input
                                type="number"
                                value={notification.threshold}
                                onChange={(e) => updateNotification(notification.id, 'threshold', parseInt(e.target.value) || 0)}
                                className="w-32 text-sm"
                                min="0"
                              />
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 text-white px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
              </div>
            ) : saved ? (
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းပြီး' : 'Saved'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းရန်' : 'Save Settings'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
