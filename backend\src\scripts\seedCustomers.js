const mongoose = require('mongoose');
const Customer = require('../models/Customer');

const sampleCustomers = [
    {
        name: 'မောင်မောင်',
        email: 'maung<PERSON><PERSON>@gmail.com',
        phone: '+95-9-123-456-789',
        address: 'လှိုင်မြို့နယ်, ရန်ကုန်',
        customerType: 'regular',
        loyaltyPoints: 150,
        totalPurchases: 5,
        totalSpent: 125000,
        notes: 'VIP customer - အမြဲတမ်း ဝယ်ယူသူ',
        isActive: true
    },
    {
        name: 'ဒေါ်မာမာ',
        email: '<EMAIL>',
        phone: '+95-9-987-654-321',
        address: 'ကမာရွတ်မြို့နယ်, ရန်ကုန်',
        customerType: 'vip',
        loyaltyPoints: 320,
        totalPurchases: 12,
        totalSpent: 450000,
        notes: 'Wholesale customer',
        isActive: true
    },
    {
        name: 'ကိုအောင်',
        email: '<EMAIL>',
        phone: '+95-9-555-123-456',
        address: 'ဗဟန်းမြို့နယ်, ရန်ကုန်',
        customerType: 'regular',
        loyaltyPoints: 75,
        totalPurchases: 3,
        totalSpent: 89000,
        notes: 'Regular customer',
        isActive: true
    },
    {
        name: 'မမထွေး',
        email: '<EMAIL>',
        phone: '+95-9-777-888-999',
        address: 'သင်္ဃန်းကျွန်းမြို့နယ်, ရန်ကုန်',
        customerType: 'regular',
        loyaltyPoints: 200,
        totalPurchases: 8,
        totalSpent: 275000,
        notes: 'Frequent buyer',
        isActive: true
    },
    {
        name: 'ကိုမင်းထွန်း',
        email: '<EMAIL>',
        phone: '+95-9-444-555-666',
        address: 'တာမွေမြို့နယ်, ရန်ကုန်',
        customerType: 'wholesale',
        loyaltyPoints: 500,
        totalPurchases: 25,
        totalSpent: 1250000,
        notes: 'Wholesale customer - bulk orders',
        isActive: true
    },
    {
        name: 'ဒေါ်စန္ဒာ',
        email: '<EMAIL>',
        phone: '+95-9-333-222-111',
        address: 'မရမ်းကုန်းမြို့နယ်, ရန်ကုန်',
        customerType: 'vip',
        loyaltyPoints: 450,
        totalPurchases: 18,
        totalSpent: 680000,
        notes: 'VIP customer - special discounts',
        isActive: true
    },
    {
        name: 'ကိုဇော်',
        email: '<EMAIL>',
        phone: '+95-9-666-777-888',
        address: 'ရန်ကင်းမြို့နယ်, ရန်ကုန်',
        customerType: 'regular',
        loyaltyPoints: 95,
        totalPurchases: 4,
        totalSpent: 156000,
        notes: 'New customer',
        isActive: true
    },
    {
        name: 'မမရီတာ',
        email: '<EMAIL>',
        phone: '+95-9-111-222-333',
        address: 'လတ်ပတ်မြို့နယ်, ရန်ကုန်',
        customerType: 'regular',
        loyaltyPoints: 180,
        totalPurchases: 7,
        totalSpent: 234000,
        notes: 'Regular customer - electronics buyer',
        isActive: true
    },
    {
        name: 'ကိုသန့်လွင်',
        email: '<EMAIL>',
        phone: '+95-9-888-999-000',
        address: 'ဒဂုံမြို့နယ်, ရန်ကုန်',
        customerType: 'wholesale',
        loyaltyPoints: 350,
        totalPurchases: 15,
        totalSpent: 890000,
        notes: 'Wholesale - restaurant supplies',
        isActive: true
    },
    {
        name: 'ဒေါ်နီလာ',
        email: '<EMAIL>',
        phone: '+95-9-123-789-456',
        address: 'မင်္ဂလာတောင်ညွန့်မြို့နယ်, ရန်ကုန်',
        customerType: 'vip',
        loyaltyPoints: 600,
        totalPurchases: 22,
        totalSpent: 1100000,
        notes: 'VIP customer - premium products',
        isActive: true
    }
];

const seedCustomers = async () => {
    try {
        console.log('🌱 Seeding customers...');
        
        // Clear existing customers
        await Customer.deleteMany({});
        console.log('🗑️ Cleared existing customers');
        
        // Create customers
        const customers = await Customer.insertMany(sampleCustomers);
        console.log(`✅ Created ${customers.length} customers`);
        
        // Display customer statistics
        const totalCustomers = await Customer.countDocuments();
        const vipCustomers = await Customer.countDocuments({ customerType: 'vip' });
        const wholesaleCustomers = await Customer.countDocuments({ customerType: 'wholesale' });
        const regularCustomers = await Customer.countDocuments({ customerType: 'regular' });
        
        console.log('\n📊 Customer Statistics:');
        console.log(`- Total Customers: ${totalCustomers}`);
        console.log(`- VIP Customers: ${vipCustomers}`);
        console.log(`- Wholesale Customers: ${wholesaleCustomers}`);
        console.log(`- Regular Customers: ${regularCustomers}`);
        
        const totalLoyaltyPoints = await Customer.aggregate([
            { $group: { _id: null, total: { $sum: '$loyaltyPoints' } } }
        ]);
        
        const totalSpent = await Customer.aggregate([
            { $group: { _id: null, total: { $sum: '$totalSpent' } } }
        ]);
        
        console.log(`- Total Loyalty Points: ${totalLoyaltyPoints[0]?.total || 0}`);
        console.log(`- Total Customer Spending: ${totalSpent[0]?.total || 0} MMK`);
        
        console.log('🎉 Customers seeding completed successfully!');
        
    } catch (error) {
        console.error('❌ Error seeding customers:', error);
        throw error;
    }
};

module.exports = { seedCustomers };

// Run if called directly
if (require.main === module) {
    const connectDB = async () => {
        try {
            const mongoURI = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos';
            await mongoose.connect(mongoURI);
            console.log('✅ MongoDB connected for customer seeding');
        } catch (error) {
            console.error('❌ MongoDB connection failed:', error.message);
            process.exit(1);
        }
    };

    const runSeed = async () => {
        try {
            await connectDB();
            await seedCustomers();
            console.log('\n🎯 Customer seeding completed successfully!');
            process.exit(0);
        } catch (error) {
            console.error('❌ Customer seeding failed:', error);
            process.exit(1);
        }
    };

    runSeed();
}
