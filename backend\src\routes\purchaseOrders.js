const express = require('express');
const { body } = require('express-validator');
const {
    getPurchaseOrders,
    getPurchaseOrder,
    createPurchaseOrder,
    updatePurchaseOrder,
    approvePurchaseOrder,
    sendOrderToSupplier,
    receiveItems,
    deletePurchaseOrder,
    getPurchaseOrderStats
} = require('../controllers/purchaseOrderController');
const { protect, authorize } = require('../middleware/auth');

const router = express.Router();

// Validation rules
const createPurchaseOrderValidation = [
    body('supplier')
        .isMongoId()
        .withMessage('Valid supplier ID is required'),
    body('expectedDeliveryDate')
        .isISO8601()
        .withMessage('Valid expected delivery date is required'),
    body('priority')
        .optional()
        .isIn(['low', 'normal', 'high', 'urgent'])
        .withMessage('Invalid priority level'),
    body('items')
        .isArray({ min: 1 })
        .withMessage('At least one item is required'),
    body('items.*.product')
        .isMongoId()
        .withMessage('Valid product ID is required'),
    body('items.*.quantity')
        .isInt({ min: 1 })
        .withMessage('Quantity must be at least 1'),
    body('items.*.unitCost')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Unit cost must be a positive number'),
    body('taxRate')
        .optional()
        .isFloat({ min: 0, max: 100 })
        .withMessage('Tax rate must be between 0 and 100'),
    body('shippingCost')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Shipping cost must be a positive number'),
    body('discountAmount')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Discount amount must be a positive number'),
    body('paymentTerms')
        .optional()
        .isIn(['cash', 'net_15', 'net_30', 'net_45', 'net_60', 'net_90', 'custom'])
        .withMessage('Invalid payment terms'),
    body('shippingMethod')
        .optional()
        .isIn(['pickup', 'delivery', 'courier', 'freight'])
        .withMessage('Invalid shipping method'),
    body('status')
        .optional()
        .isIn(['draft', 'pending'])
        .withMessage('Status must be draft or pending')
];

const updatePurchaseOrderValidation = [
    body('supplier')
        .optional()
        .isMongoId()
        .withMessage('Valid supplier ID is required'),
    body('expectedDeliveryDate')
        .optional()
        .isISO8601()
        .withMessage('Valid expected delivery date is required'),
    body('priority')
        .optional()
        .isIn(['low', 'normal', 'high', 'urgent'])
        .withMessage('Invalid priority level'),
    body('items')
        .optional()
        .isArray({ min: 1 })
        .withMessage('At least one item is required'),
    body('taxRate')
        .optional()
        .isFloat({ min: 0, max: 100 })
        .withMessage('Tax rate must be between 0 and 100'),
    body('shippingCost')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Shipping cost must be a positive number'),
    body('discountAmount')
        .optional()
        .isFloat({ min: 0 })
        .withMessage('Discount amount must be a positive number'),
    body('status')
        .optional()
        .isIn(['draft', 'pending'])
        .withMessage('Status must be draft or pending')
];

const approvalValidation = [
    body('approved')
        .isBoolean()
        .withMessage('Approved must be true or false'),
    body('comments')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Comments cannot exceed 1000 characters')
];

const receiveItemsValidation = [
    body('receivedItems')
        .isArray({ min: 1 })
        .withMessage('At least one received item is required'),
    body('receivedItems.*.itemId')
        .isMongoId()
        .withMessage('Valid item ID is required'),
    body('receivedItems.*.quantity')
        .isInt({ min: 1 })
        .withMessage('Received quantity must be at least 1'),
    body('notes')
        .optional()
        .trim()
        .isLength({ max: 1000 })
        .withMessage('Notes cannot exceed 1000 characters')
];

// Apply authentication to all routes
router.use(protect);

// Routes

// GET /api/purchase-orders - Get all purchase orders
router.get('/', getPurchaseOrders);

// GET /api/purchase-orders/stats - Get purchase order statistics
router.get('/stats', getPurchaseOrderStats);

// GET /api/purchase-orders/:id - Get single purchase order
router.get('/:id', getPurchaseOrder);

// POST /api/purchase-orders - Create new purchase order
router.post('/', createPurchaseOrderValidation, createPurchaseOrder);

// PUT /api/purchase-orders/:id - Update purchase order (draft/pending only)
router.put('/:id', updatePurchaseOrderValidation, updatePurchaseOrder);

// POST /api/purchase-orders/:id/approve - Approve/reject purchase order (managers only)
router.post('/:id/approve', authorize('admin', 'manager'), approvalValidation, approvePurchaseOrder);

// POST /api/purchase-orders/:id/send - Send order to supplier (managers only)
router.post('/:id/send', authorize('admin', 'manager'), sendOrderToSupplier);

// POST /api/purchase-orders/:id/receive - Receive items (managers only)
router.post('/:id/receive', authorize('admin', 'manager'), receiveItemsValidation, receiveItems);

// DELETE /api/purchase-orders/:id - Delete purchase order (draft only)
router.delete('/:id', deletePurchaseOrder);

module.exports = router;
