const Sale = require('../models/Sale');
const asyncHandler = require('../utils/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');
const nodemailer = require('nodemailer');

// Email configuration
const createEmailTransporter = () => {
    // For development, use a test account or configure with your email service
    return nodemailer.createTransporter({
        host: process.env.SMTP_HOST || 'smtp.gmail.com',
        port: process.env.SMTP_PORT || 587,
        secure: false,
        auth: {
            user: process.env.SMTP_USER || '<EMAIL>',
            pass: process.env.SMTP_PASS || 'your-app-password'
        }
    });
};

// Generate HTML receipt template
const generateReceiptHTML = (sale, companyInfo = {}) => {
    const {
        name = 'BitesTech POS',
        address = 'Yangon, Myanmar',
        phone = '+95-9-***********',
        email = '<EMAIL>',
        website = 'www.bitstech.com',
        taxId = 'TAX-*********'
    } = companyInfo;

    const formatPrice = (price) => `${price.toLocaleString()} ${sale.currency}`;
    const formatDate = (date) => new Date(date).toLocaleDateString();
    const formatTime = (date) => new Date(date).toLocaleTimeString();

    return `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>Receipt - ${sale.saleNumber}</title>
        <style>
            body {
                font-family: 'Courier New', monospace;
                max-width: 400px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9f9f9;
            }
            .receipt {
                background: white;
                padding: 20px;
                border-radius: 8px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .header {
                text-align: center;
                border-bottom: 2px dashed #ccc;
                padding-bottom: 15px;
                margin-bottom: 15px;
            }
            .company-name {
                font-size: 18px;
                font-weight: bold;
                margin-bottom: 10px;
            }
            .company-info {
                font-size: 12px;
                color: #666;
                line-height: 1.4;
            }
            .receipt-title {
                text-align: center;
                font-size: 20px;
                font-weight: bold;
                margin: 15px 0;
            }
            .info-row {
                display: flex;
                justify-content: space-between;
                margin: 5px 0;
                font-size: 14px;
            }
            .items-table {
                width: 100%;
                border-collapse: collapse;
                margin: 15px 0;
                border-top: 1px dashed #ccc;
                border-bottom: 1px dashed #ccc;
            }
            .items-table th {
                text-align: left;
                padding: 8px 4px;
                font-size: 12px;
                border-bottom: 1px solid #eee;
            }
            .items-table td {
                padding: 8px 4px;
                font-size: 12px;
                border-bottom: 1px solid #eee;
            }
            .item-name {
                font-weight: bold;
            }
            .item-sku {
                color: #666;
                font-size: 11px;
            }
            .totals {
                margin: 15px 0;
            }
            .total-row {
                display: flex;
                justify-content: space-between;
                margin: 5px 0;
                font-size: 14px;
            }
            .grand-total {
                font-size: 16px;
                font-weight: bold;
                border-top: 1px dashed #ccc;
                padding-top: 8px;
            }
            .payment-info {
                border-top: 1px dashed #ccc;
                padding-top: 15px;
                margin-top: 15px;
            }
            .footer {
                text-align: center;
                border-top: 1px dashed #ccc;
                padding-top: 15px;
                margin-top: 15px;
                font-size: 12px;
                color: #666;
            }
            .thank-you {
                font-weight: bold;
                margin-bottom: 5px;
            }
        </style>
    </head>
    <body>
        <div class="receipt">
            <!-- Header -->
            <div class="header">
                <div class="company-name">${name}</div>
                <div class="company-info">
                    ${address}<br>
                    ${phone}<br>
                    ${email}<br>
                    ${website}<br>
                    Tax ID: ${taxId}
                </div>
            </div>

            <!-- Receipt Title -->
            <div class="receipt-title">RECEIPT</div>

            <!-- Sale Information -->
            <div class="info-row">
                <span>Sale No:</span>
                <span><strong>${sale.saleNumber}</strong></span>
            </div>
            <div class="info-row">
                <span>Date:</span>
                <span>${formatDate(sale.createdAt)}</span>
            </div>
            <div class="info-row">
                <span>Time:</span>
                <span>${formatTime(sale.createdAt)}</span>
            </div>
            <div class="info-row">
                <span>Cashier:</span>
                <span>${sale.cashier.firstName} ${sale.cashier.lastName}</span>
            </div>
            ${sale.customer?.name ? `
            <div class="info-row">
                <span>Customer:</span>
                <span>${sale.customer.name}</span>
            </div>
            ` : ''}

            <!-- Items -->
            <table class="items-table">
                <thead>
                    <tr>
                        <th style="width: 50%">Item</th>
                        <th style="width: 15%; text-align: center">Qty</th>
                        <th style="width: 20%; text-align: right">Price</th>
                        <th style="width: 15%; text-align: right">Total</th>
                    </tr>
                </thead>
                <tbody>
                    ${sale.items.map(item => `
                    <tr>
                        <td>
                            <div class="item-name">${item.productName}</div>
                            <div class="item-sku">${item.sku}</div>
                        </td>
                        <td style="text-align: center">${item.quantity}</td>
                        <td style="text-align: right">${formatPrice(item.unitPrice)}</td>
                        <td style="text-align: right"><strong>${formatPrice(item.totalPrice)}</strong></td>
                    </tr>
                    ${item.discount > 0 ? `
                    <tr>
                        <td colspan="4" style="text-align: right; color: red; font-size: 11px">
                            Discount: -${formatPrice(item.discount)}
                        </td>
                    </tr>
                    ` : ''}
                    `).join('')}
                </tbody>
            </table>

            <!-- Totals -->
            <div class="totals">
                <div class="total-row">
                    <span>Subtotal:</span>
                    <span>${formatPrice(sale.subtotal)}</span>
                </div>
                ${sale.totalDiscount > 0 ? `
                <div class="total-row" style="color: red">
                    <span>Discount:</span>
                    <span>-${formatPrice(sale.totalDiscount)}</span>
                </div>
                ` : ''}
                ${sale.totalTax > 0 ? `
                <div class="total-row">
                    <span>Tax:</span>
                    <span>${formatPrice(sale.totalTax)}</span>
                </div>
                ` : ''}
                <div class="total-row grand-total">
                    <span>Grand Total:</span>
                    <span>${formatPrice(sale.totalAmount)}</span>
                </div>
            </div>

            <!-- Payment Information -->
            <div class="payment-info">
                <div class="total-row">
                    <span>Payment Method:</span>
                    <span style="text-transform: uppercase">${sale.paymentMethod.replace('_', ' ')}</span>
                </div>
                <div class="total-row">
                    <span>Amount Paid:</span>
                    <span>${formatPrice(sale.amountPaid)}</span>
                </div>
                ${sale.changeAmount > 0 ? `
                <div class="total-row" style="font-weight: bold">
                    <span>Change:</span>
                    <span>${formatPrice(sale.changeAmount)}</span>
                </div>
                ` : ''}
            </div>

            ${sale.notes ? `
            <div class="payment-info">
                <div style="font-weight: bold; margin-bottom: 5px">Notes:</div>
                <div style="font-size: 12px">${sale.notes}</div>
            </div>
            ` : ''}

            <!-- Footer -->
            <div class="footer">
                <div class="thank-you">Thank you for your business!</div>
                <div>Please visit us again</div>
                <div style="margin-top: 10px; color: #999">Powered by BitesTech POS</div>
            </div>
        </div>
    </body>
    </html>
    `;
};

// @desc    Send receipt via email
// @route   POST /api/receipts/email/:saleId
// @access  Private
const emailReceipt = asyncHandler(async (req, res, next) => {
    const { saleId } = req.params;
    const { email, customerName } = req.body;

    if (!email) {
        return next(new ErrorResponse('Email address is required', 400));
    }

    // Get sale data
    const sale = await Sale.findById(saleId)
        .populate('cashier', 'firstName lastName username email')
        .populate('items.product', 'name sku category');

    if (!sale) {
        return next(new ErrorResponse(`Sale not found with id of ${saleId}`, 404));
    }

    try {
        // Create email transporter
        const transporter = createEmailTransporter();

        // Generate receipt HTML
        const receiptHTML = generateReceiptHTML(sale);

        // Email options
        const mailOptions = {
            from: process.env.SMTP_FROM || 'BitesTech POS <<EMAIL>>',
            to: email,
            subject: `Receipt - ${sale.saleNumber} | BitesTech POS`,
            html: receiptHTML,
            text: `Receipt for Sale #${sale.saleNumber}\n\nTotal: ${sale.totalAmount.toLocaleString()} ${sale.currency}\nDate: ${new Date(sale.createdAt).toLocaleDateString()}\n\nThank you for your business!`
        };

        // Send email
        await transporter.sendMail(mailOptions);

        // Update sale receipt status
        await Sale.findByIdAndUpdate(saleId, {
            'receipt.emailed': true,
            'receipt.emailedAt': new Date()
        });

        res.status(200).json({
            success: true,
            message: 'Receipt sent successfully',
            data: {
                saleNumber: sale.saleNumber,
                email: email,
                sentAt: new Date()
            }
        });

    } catch (error) {
        console.error('Email sending error:', error);
        return next(new ErrorResponse('Failed to send email receipt', 500));
    }
});

// @desc    Mark receipt as printed
// @route   PUT /api/receipts/print/:saleId
// @access  Private
const markAsPrinted = asyncHandler(async (req, res, next) => {
    const { saleId } = req.params;

    const sale = await Sale.findByIdAndUpdate(
        saleId,
        {
            'receipt.printed': true,
            'receipt.printedAt': new Date()
        },
        { new: true }
    );

    if (!sale) {
        return next(new ErrorResponse(`Sale not found with id of ${saleId}`, 404));
    }

    res.status(200).json({
        success: true,
        message: 'Receipt marked as printed',
        data: sale.receipt
    });
});

// @desc    Get receipt data
// @route   GET /api/receipts/:saleId
// @access  Private
const getReceipt = asyncHandler(async (req, res, next) => {
    const { saleId } = req.params;

    const sale = await Sale.findById(saleId)
        .populate('cashier', 'firstName lastName username email')
        .populate('items.product', 'name sku category');

    if (!sale) {
        return next(new ErrorResponse(`Sale not found with id of ${saleId}`, 404));
    }

    res.status(200).json({
        success: true,
        data: sale
    });
});

module.exports = {
    emailReceipt,
    markAsPrinted,
    getReceipt
};
