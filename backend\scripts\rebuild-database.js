const mongoose = require('mongoose');
require('dotenv').config();

// Import models
const User = require('../src/models/User');
const Product = require('../src/models/Product');
const Category = require('../src/models/Category');
const Sale = require('../src/models/Sale');
const Customer = require('../src/models/Customer');
const Settings = require('../src/models/Settings');

// MongoDB connection
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos';

// Sample data
const categories = [
    {
        name: 'Laptops',
        description: 'Gaming and Business Laptops',
        color: '#3B82F6',
        icon: 'laptop',
        isActive: true
    },
    {
        name: 'Monitors',
        description: 'Gaming and Professional Monitors',
        color: '#10B981',
        icon: 'monitor',
        isActive: true
    },
    {
        name: 'Accessories',
        description: 'Computer Accessories',
        color: '#F59E0B',
        icon: 'mouse',
        isActive: true
    },
    {
        name: 'Storage',
        description: 'Hard Drives and SSDs',
        color: '#8B5CF6',
        icon: 'hard-drive',
        isActive: true
    },
    {
        name: 'Networking',
        description: 'Routers and Network Equipment',
        color: '#EF4444',
        icon: 'wifi',
        isActive: true
    },
    {
        name: 'Peripherals',
        description: 'Keyboards, Mice, and Other Peripherals',
        color: '#06B6D4',
        icon: 'keyboard',
        isActive: true
    }
];

const users = [
    {
        username: 'admin',
        firstName: 'Admin',
        lastName: 'User',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9QmjytWIAp/Rs7HMmKOEKhsPiVlbQBWMU.zVJ8rGvjmQYvN5Iq', // admin123
        role: 'admin',
        phone: '+95-9-111-111-111',
        isActive: true,
        permissions: [
            { module: 'all', actions: ['create', 'read', 'update', 'delete'] }
        ]
    },
    {
        username: 'manager',
        firstName: 'Manager',
        lastName: 'User',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9QmjytWIAp/Rs7HMmKOEKhsPiVlbQBWMU.zVJ8rGvjmQYvN5Iq', // manager123
        role: 'manager',
        phone: '+95-9-222-222-222',
        isActive: true,
        permissions: [
            { module: 'products', actions: ['create', 'read', 'update'] },
            { module: 'inventory', actions: ['read', 'update'] },
            { module: 'sales', actions: ['read'] }
        ]
    },
    {
        username: 'cashier',
        firstName: 'Cashier',
        lastName: 'User',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9QmjytWIAp/Rs7HMmKOEKhsPiVlbQBWMU.zVJ8rGvjmQYvN5Iq', // cashier123
        role: 'cashier',
        phone: '+95-9-333-333-333',
        isActive: true,
        permissions: [
            { module: 'pos', actions: ['read', 'create'] },
            { module: 'products', actions: ['read'] }
        ]
    },
    {
        username: 'supervisor',
        firstName: 'Supervisor',
        lastName: 'User',
        email: '<EMAIL>',
        password: '$2b$10$rQZ9QmjytWIAp/Rs7HMmKOEKhsPiVlbQBWMU.zVJ8rGvjmQYvN5Iq', // supervisor123
        role: 'manager',
        phone: '+95-9-444-444-444',
        isActive: true,
        permissions: [
            { module: 'sales', actions: ['read', 'update'] },
            { module: 'reports', actions: ['read'] },
            { module: 'users', actions: ['read'] }
        ]
    }
];

async function rebuildDatabase() {
    try {
        console.log('🔄 Connecting to MongoDB...');
        await mongoose.connect(MONGODB_URI);
        console.log('✅ Connected to MongoDB');

        // Drop all collections
        console.log('🗑️ Dropping all collections...');
        const collections = await mongoose.connection.db.listCollections().toArray();
        for (const collection of collections) {
            await mongoose.connection.db.dropCollection(collection.name);
            console.log(`   ✅ Dropped ${collection.name}`);
        }

        console.log('🏗️ Creating fresh collections...');

        // Create Categories
        console.log('📂 Creating categories...');
        const createdCategories = await Category.insertMany(categories);
        console.log(`   ✅ Created ${createdCategories.length} categories`);

        // Create Users
        console.log('👥 Creating users...');
        const createdUsers = await User.insertMany(users);
        console.log(`   ✅ Created ${createdUsers.length} users`);

        // Create Products
        console.log('📦 Creating products...');
        const products = [
            {
                name: 'Gaming Laptop ROG Strix',
                description: 'High-performance gaming laptop with RTX 4060',
                sku: 'LAP-ROG-001',
                price: 2500000,
                cost: 2000000,
                category: createdCategories[0]._id, // Laptops
                inventory: { quantity: 15, minQuantity: 5, maxQuantity: 50 },
                images: [{ url: '/images/laptop-rog.jpg', alt: 'ROG Strix Laptop', isPrimary: true }],
                isActive: true
            },
            {
                name: 'Business Laptop ThinkPad',
                description: 'Professional business laptop',
                sku: 'LAP-TP-001',
                price: 1800000,
                cost: 1400000,
                category: createdCategories[0]._id, // Laptops
                inventory: { quantity: 20, minQuantity: 5, maxQuantity: 40 },
                images: [{ url: '/images/laptop-thinkpad.jpg', alt: 'ThinkPad Laptop', isPrimary: true }],
                isActive: true
            },
            {
                name: '27" Gaming Monitor',
                description: '144Hz QHD Gaming Monitor',
                sku: 'MON-GM-001',
                price: 650000,
                cost: 500000,
                category: createdCategories[1]._id, // Monitors
                inventory: { quantity: 25, minQuantity: 10, maxQuantity: 60 },
                images: [{ url: '/images/monitor-gaming.jpg', alt: 'Gaming Monitor', isPrimary: true }],
                isActive: true
            },
            {
                name: 'Wireless Gaming Mouse',
                description: 'RGB Wireless Gaming Mouse',
                sku: 'ACC-MS-001',
                price: 85000,
                cost: 60000,
                category: createdCategories[2]._id, // Accessories
                inventory: { quantity: 50, minQuantity: 20, maxQuantity: 100 },
                images: [{ url: '/images/mouse-gaming.jpg', alt: 'Gaming Mouse', isPrimary: true }],
                isActive: true
            },
            {
                name: 'Mechanical Keyboard',
                description: 'RGB Mechanical Gaming Keyboard',
                sku: 'PER-KB-001',
                price: 150000,
                cost: 120000,
                category: createdCategories[5]._id, // Peripherals
                inventory: { quantity: 30, minQuantity: 15, maxQuantity: 80 },
                images: [{ url: '/images/keyboard-mechanical.jpg', alt: 'Mechanical Keyboard', isPrimary: true }],
                isActive: true
            },
            {
                name: '1TB SSD Drive',
                description: 'High-speed NVMe SSD 1TB',
                sku: 'STO-SSD-001',
                price: 180000,
                cost: 140000,
                category: createdCategories[3]._id, // Storage
                inventory: { quantity: 40, minQuantity: 15, maxQuantity: 100 },
                images: [{ url: '/images/ssd-drive.jpg', alt: 'SSD Drive', isPrimary: true }],
                isActive: true
            },
            {
                name: 'WiFi Router AC1200',
                description: 'Dual-band wireless router',
                sku: 'NET-RT-001',
                price: 120000,
                cost: 90000,
                category: createdCategories[4]._id, // Networking
                inventory: { quantity: 35, minQuantity: 10, maxQuantity: 70 },
                images: [{ url: '/images/wifi-router.jpg', alt: 'WiFi Router', isPrimary: true }],
                isActive: true
            }
        ];

        const createdProducts = await Product.insertMany(products);
        console.log(`   ✅ Created ${createdProducts.length} products`);

        // Create Settings
        console.log('⚙️ Creating settings...');
        const settings = new Settings({
            storeName: 'BitsTech POS',
            storeEmail: '<EMAIL>',
            storePhone: '+95-9-***********',
            storeAddress: 'Yangon, Myanmar',
            currency: 'MMK',
            taxRate: 0,
            receiptFooter: 'Thank you for shopping with BitsTech!',
            lowStockThreshold: 10
        });
        await settings.save();
        console.log('   ✅ Created settings');

        // Create Exchange Rates
        console.log('💱 Creating exchange rates...');
        const exchangeRates = [
            { currency: 'MMK', rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
            { currency: 'USD', rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
            { currency: 'THB', rate: 0.016, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
        ];
        
        await mongoose.connection.db.collection('exchangerates').insertMany(exchangeRates);
        console.log('   ✅ Created exchange rates');

        console.log('🔄 Disconnecting from MongoDB...');
        await mongoose.disconnect();
        console.log('✅ Disconnected from MongoDB');

        console.log('🎉 Database rebuild completed successfully!');
        console.log('📊 Summary:');
        console.log(`   - Categories: ${createdCategories.length}`);
        console.log(`   - Users: ${createdUsers.length}`);
        console.log(`   - Products: ${createdProducts.length}`);
        console.log(`   - Settings: 1`);
        console.log(`   - Exchange Rates: ${exchangeRates.length}`);
        
        process.exit(0);
    } catch (error) {
        console.error('❌ Error rebuilding database:', error);
        process.exit(1);
    }
}

// Run the rebuild
rebuildDatabase();
