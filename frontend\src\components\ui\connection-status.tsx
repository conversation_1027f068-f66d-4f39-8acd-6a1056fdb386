'use client'

import React, { useState, useEffect } from 'react'
import { Wifi, WifiOff, Cloud, CloudOff, Sync, AlertCircle } from 'lucide-react'
import { useAuth } from '@/contexts/auth-context'
import { Badge } from '@/components/ui/badge'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'

export function ConnectionStatus() {
  const { isOnline } = useAuth()
  const [syncStatus, setSyncStatus] = useState<'idle' | 'syncing' | 'error'>('idle')
  const [lastSync, setLastSync] = useState<Date | null>(null)
  const [pendingOperations, setPendingOperations] = useState(0)

  useEffect(() => {
    // Check for pending sync operations
    const checkPendingOperations = () => {
      if (typeof window !== 'undefined') {
        const syncQueue = localStorage.getItem('bitstech_sync_queue')
        const operations = syncQueue ? JSON.parse(syncQueue) : []
        setPendingOperations(operations.length)
        
        const lastSyncTime = localStorage.getItem('bitstech_last_sync')
        if (lastSyncTime) {
          setLastSync(new Date(lastSyncTime))
        }
      }
    }

    checkPendingOperations()
    
    // Check every 10 seconds
    const interval = setInterval(checkPendingOperations, 10000)
    
    return () => clearInterval(interval)
  }, [])

  const getStatusInfo = () => {
    if (!isOnline) {
      return {
        icon: WifiOff,
        color: 'bg-red-500',
        text: 'Offline',
        description: 'Working in offline mode. Data will sync when connection is restored.'
      }
    }

    if (pendingOperations > 0) {
      return {
        icon: Sync,
        color: 'bg-yellow-500 animate-pulse',
        text: 'Syncing',
        description: `Syncing ${pendingOperations} pending operations...`
      }
    }

    return {
      icon: Wifi,
      color: 'bg-green-500',
      text: 'Online',
      description: 'Connected and synchronized'
    }
  }

  const status = getStatusInfo()
  const StatusIcon = status.icon

  return (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="flex items-center space-x-2">
            <div className="relative">
              <div className={`w-3 h-3 rounded-full ${status.color}`} />
              <StatusIcon className="w-4 h-4 text-gray-600 absolute -top-0.5 -right-0.5" />
            </div>
            <Badge variant={isOnline ? 'default' : 'destructive'} className="text-xs">
              {status.text}
            </Badge>
            {pendingOperations > 0 && (
              <Badge variant="outline" className="text-xs">
                {pendingOperations} pending
              </Badge>
            )}
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-1">
            <p className="font-medium">{status.description}</p>
            {lastSync && (
              <p className="text-xs text-gray-500">
                Last sync: {lastSync.toLocaleTimeString()}
              </p>
            )}
            {!isOnline && pendingOperations > 0 && (
              <p className="text-xs text-yellow-600">
                {pendingOperations} operations will sync when online
              </p>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )
}

export function DetailedConnectionStatus() {
  const { isOnline } = useAuth()
  const [syncQueue, setSyncQueue] = useState<any[]>([])
  const [lastSync, setLastSync] = useState<Date | null>(null)

  useEffect(() => {
    const updateStatus = () => {
      if (typeof window !== 'undefined') {
        const queue = localStorage.getItem('bitstech_sync_queue')
        setSyncQueue(queue ? JSON.parse(queue) : [])
        
        const lastSyncTime = localStorage.getItem('bitstech_last_sync')
        if (lastSyncTime) {
          setLastSync(new Date(lastSyncTime))
        }
      }
    }

    updateStatus()
    const interval = setInterval(updateStatus, 5000)
    
    return () => clearInterval(interval)
  }, [])

  return (
    <div className="bg-white rounded-lg border p-4 space-y-4">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold">Connection Status</h3>
        <ConnectionStatus />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Connection Status */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm text-gray-700">Network</h4>
          <div className="flex items-center space-x-2">
            {isOnline ? (
              <>
                <Cloud className="w-5 h-5 text-green-500" />
                <span className="text-green-700">Connected</span>
              </>
            ) : (
              <>
                <CloudOff className="w-5 h-5 text-red-500" />
                <span className="text-red-700">Disconnected</span>
              </>
            )}
          </div>
        </div>

        {/* Sync Status */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm text-gray-700">Sync Queue</h4>
          <div className="flex items-center space-x-2">
            {syncQueue.length > 0 ? (
              <>
                <Sync className="w-5 h-5 text-yellow-500" />
                <span className="text-yellow-700">{syncQueue.length} pending</span>
              </>
            ) : (
              <>
                <Sync className="w-5 h-5 text-green-500" />
                <span className="text-green-700">Up to date</span>
              </>
            )}
          </div>
        </div>

        {/* Last Sync */}
        <div className="space-y-2">
          <h4 className="font-medium text-sm text-gray-700">Last Sync</h4>
          <div className="text-sm text-gray-600">
            {lastSync ? (
              <span>{lastSync.toLocaleString()}</span>
            ) : (
              <span>Never</span>
            )}
          </div>
        </div>
      </div>

      {/* Pending Operations */}
      {syncQueue.length > 0 && (
        <div className="space-y-2">
          <h4 className="font-medium text-sm text-gray-700">Pending Operations</h4>
          <div className="space-y-1 max-h-32 overflow-y-auto">
            {syncQueue.map((operation, index) => (
              <div key={index} className="flex items-center justify-between text-xs bg-gray-50 p-2 rounded">
                <span className="font-medium">{operation.type}</span>
                <span className="text-gray-500">
                  {new Date(operation.timestamp).toLocaleTimeString()}
                </span>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Offline Mode Info */}
      {!isOnline && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
          <div className="flex items-start space-x-2">
            <AlertCircle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div className="space-y-1">
              <h4 className="font-medium text-yellow-800">Offline Mode Active</h4>
              <p className="text-sm text-yellow-700">
                You're currently working offline. All changes are being saved locally 
                and will automatically sync when your connection is restored.
              </p>
              <ul className="text-xs text-yellow-600 space-y-1 mt-2">
                <li>• Sales transactions are saved locally</li>
                <li>• Product data is cached for offline access</li>
                <li>• Changes will sync automatically when online</li>
              </ul>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
