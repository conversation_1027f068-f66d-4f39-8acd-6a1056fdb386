const mongoose = require('mongoose');

const exchangeRateSchema = new mongoose.Schema({
    baseCurrency: {
        type: String,
        required: true,
        enum: ['MMK', 'USD', 'THB'],
        default: 'MMK'
    },
    rates: {
        MMK: {
            rate: { type: Number, required: true, default: 1 },
            symbol: { type: String, required: true, default: 'K' },
            name: { type: String, required: true, default: 'Myanmar Kyat' },
            flag: { type: String, required: true, default: '🇲🇲' }
        },
        USD: {
            rate: { type: Number, required: true, default: 0.00048 },
            symbol: { type: String, required: true, default: '$' },
            name: { type: String, required: true, default: 'US Dollar' },
            flag: { type: String, required: true, default: '🇺🇸' }
        },
        THB: {
            rate: { type: Number, required: true, default: 0.016 },
            symbol: { type: String, required: true, default: '฿' },
            name: { type: String, required: true, default: 'Thai Baht' },
            flag: { type: String, required: true, default: '🇹🇭' }
        }
    },
    lastUpdated: {
        type: Date,
        default: Date.now
    },
    updatedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    isActive: {
        type: Boolean,
        default: true
    },
    source: {
        type: String,
        enum: ['manual', 'api', 'system'],
        default: 'manual'
    }
}, {
    timestamps: true
});

// Index for faster queries
exchangeRateSchema.index({ isActive: 1, lastUpdated: -1 });

// Static method to get current rates
exchangeRateSchema.statics.getCurrentRates = async function() {
    const currentRates = await this.findOne({ isActive: true }).sort({ lastUpdated: -1 });
    
    if (!currentRates) {
        // Create default rates if none exist
        const defaultRates = new this({
            baseCurrency: 'MMK',
            rates: {
                MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
                USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
                THB: { rate: 0.016, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
            }
        });
        await defaultRates.save();
        return defaultRates;
    }
    
    return currentRates;
};

// Instance method to update rates
exchangeRateSchema.methods.updateRates = async function(newRates, userId) {
    this.rates = { ...this.rates, ...newRates };
    this.lastUpdated = new Date();
    this.updatedBy = userId;
    this.source = 'manual';
    return await this.save();
};

module.exports = mongoose.model('ExchangeRate', exchangeRateSchema);
