'use client'

import { useState } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Eye, EyeOff, UserPlus, ArrowLeft } from 'lucide-react'

export default function RegisterPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [language, setLanguage] = useState<'en' | 'mm'>('en')
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    username: '',
    email: '',
    password: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState<{[key: string]: string}>({})

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {}

    if (!formData.firstName) {
      newErrors.firstName = language === 'en' ? 'First name is required' : 'ပထမအမည် လိုအပ်ပါသည်'
    }

    if (!formData.lastName) {
      newErrors.lastName = language === 'en' ? 'Last name is required' : 'နောက်ဆုံးအမည် လိုအပ်ပါသည်'
    }

    if (!formData.username) {
      newErrors.username = language === 'en' ? 'Username is required' : 'အသုံးပြုသူအမည် လိုအပ်ပါသည်'
    } else if (formData.username.length < 3) {
      newErrors.username = language === 'en' ? 'Username must be at least 3 characters' : 'အသုံးပြုသူအမည်သည် အနည်းဆုံး ၃ လုံး ရှိရမည်'
    }

    if (!formData.email) {
      newErrors.email = language === 'en' ? 'Email is required' : 'အီးမေးလ် လိုအပ်ပါသည်'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = language === 'en' ? 'Email is invalid' : 'အီးမေးလ် မှားယွင်းနေပါသည်'
    }

    if (!formData.password) {
      newErrors.password = language === 'en' ? 'Password is required' : 'စကားဝှက် လိုအပ်ပါသည်'
    } else if (formData.password.length < 6) {
      newErrors.password = language === 'en' ? 'Password must be at least 6 characters' : 'စကားဝှက်သည် အနည်းဆုံး ၆ လုံး ရှိရမည်'
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = language === 'en' ? 'Please confirm your password' : 'စကားဝှက် အတည်ပြုပါ'
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = language === 'en' ? 'Passwords do not match' : 'စကားဝှက်များ မတူညီပါ'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) return

    setIsLoading(true)

    try {
      // TODO: Implement actual register API call
      const response = await fetch('/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          firstName: formData.firstName,
          lastName: formData.lastName,
          username: formData.username,
          email: formData.email,
          password: formData.password
        }),
      })

      if (response.ok) {
        const data = await response.json()
        // Store token and redirect
        localStorage.setItem('token', data.token)
        router.push('/dashboard')
      } else {
        const errorData = await response.json()
        setErrors({
          general: errorData.error || (language === 'en' ? 'Registration failed' : 'အကောင့်ဖွင့်ခြင်း မအောင်မြင်ပါ')
        })
      }
    } catch (error) {
      setErrors({
        general: language === 'en' ? 'Network error. Please try again.' : 'ကွန်ယက် အမှား။ ပြန်လည်ကြိုးစားပါ။'
      })
    } finally {
      setIsLoading(false)
    }
  }

  const text = {
    en: {
      title: 'Create Account',
      subtitle: 'Sign up for your BitsTech POS account',
      firstName: 'First Name',
      lastName: 'Last Name',
      username: 'Username',
      email: 'Email',
      password: 'Password',
      confirmPassword: 'Confirm Password',
      showPassword: 'Show password',
      hidePassword: 'Hide password',
      signUp: 'Sign Up',
      signingUp: 'Creating Account...',
      haveAccount: 'Already have an account?',
      signIn: 'Sign in',
      backToHome: 'Back to Home'
    },
    mm: {
      title: 'အကောင့်ဖွင့်ရန်',
      subtitle: 'သင့် BitsTech POS အကောင့်အတွက် စာရင်းသွင်းပါ',
      firstName: 'ပထမအမည်',
      lastName: 'နောက်ဆုံးအမည်',
      username: 'အသုံးပြုသူအမည်',
      email: 'အီးမေးလ်',
      password: 'စကားဝှက်',
      confirmPassword: 'စကားဝှက် အတည်ပြုရန်',
      showPassword: 'စကားဝှက် ပြရန်',
      hidePassword: 'စကားဝှက် ဖုံးရန်',
      signUp: 'အကောင့်ဖွင့်ရန်',
      signingUp: 'အကောင့်ဖွင့်နေသည်...',
      haveAccount: 'အကောင့်ရှိပြီးသားလား?',
      signIn: 'ဝင်ရောက်ရန်',
      backToHome: 'ပင်မစာမျက်နှာသို့'
    }
  }

  const t = text[language]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {/* Back to Home */}
        <div className="mb-6">
          <Link href="/">
            <Button variant="ghost" className="text-gray-600 dark:text-gray-400">
              <ArrowLeft className="mr-2 h-4 w-4" />
              {t.backToHome}
            </Button>
          </Link>
        </div>

        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <div className="flex items-center justify-center mb-4">
              <div className="w-12 h-12 bg-blue-600 rounded-xl flex items-center justify-center">
                <span className="text-white font-bold text-lg">BT</span>
              </div>
            </div>

            <CardTitle className="text-2xl text-center">{t.title}</CardTitle>
            <CardDescription className="text-center">
              {t.subtitle}
            </CardDescription>

            {/* Language Toggle */}
            <div className="flex justify-center pt-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setLanguage(language === 'en' ? 'mm' : 'en')}
                className="text-xs"
              >
                {language === 'en' ? 'မြန်မာ' : 'English'}
              </Button>
            </div>
          </CardHeader>

          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-4">
              {errors.general && (
                <div className="p-3 text-sm text-red-600 bg-red-50 dark:bg-red-900/20 rounded-md">
                  {errors.general}
                </div>
              )}

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <label htmlFor="firstName" className="text-sm font-medium">
                    {t.firstName}
                  </label>
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={errors.firstName ? 'border-red-500' : ''}
                  />
                  {errors.firstName && (
                    <p className="text-xs text-red-600">{errors.firstName}</p>
                  )}
                </div>

                <div className="space-y-2">
                  <label htmlFor="lastName" className="text-sm font-medium">
                    {t.lastName}
                  </label>
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={errors.lastName ? 'border-red-500' : ''}
                  />
                  {errors.lastName && (
                    <p className="text-xs text-red-600">{errors.lastName}</p>
                  )}
                </div>
              </div>

              <div className="space-y-2">
                <label htmlFor="username" className="text-sm font-medium">
                  {t.username}
                </label>
                <Input
                  id="username"
                  name="username"
                  type="text"
                  value={formData.username}
                  onChange={handleInputChange}
                  className={errors.username ? 'border-red-500' : ''}
                />
                {errors.username && (
                  <p className="text-sm text-red-600">{errors.username}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="email" className="text-sm font-medium">
                  {t.email}
                </label>
                <Input
                  id="email"
                  name="email"
                  type="email"
                  value={formData.email}
                  onChange={handleInputChange}
                  placeholder="<EMAIL>"
                  className={errors.email ? 'border-red-500' : ''}
                />
                {errors.email && (
                  <p className="text-sm text-red-600">{errors.email}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="password" className="text-sm font-medium">
                  {t.password}
                </label>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange}
                    className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-500" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-500" />
                    )}
                  </Button>
                </div>
                {errors.password && (
                  <p className="text-sm text-red-600">{errors.password}</p>
                )}
              </div>

              <div className="space-y-2">
                <label htmlFor="confirmPassword" className="text-sm font-medium">
                  {t.confirmPassword}
                </label>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={errors.confirmPassword ? 'border-red-500 pr-10' : 'pr-10'}
                  />
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeOff className="h-4 w-4 text-gray-500" />
                    ) : (
                      <Eye className="h-4 w-4 text-gray-500" />
                    )}
                  </Button>
                </div>
                {errors.confirmPassword && (
                  <p className="text-sm text-red-600">{errors.confirmPassword}</p>
                )}
              </div>

              <Button
                type="submit"
                className="w-full"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-white border-t-transparent" />
                    {t.signingUp}
                  </>
                ) : (
                  <>
                    <UserPlus className="mr-2 h-4 w-4" />
                    {t.signUp}
                  </>
                )}
              </Button>
            </form>

            <div className="mt-6 text-center text-sm">
              <span className="text-gray-600 dark:text-gray-400">
                {t.haveAccount}{' '}
              </span>
              <Link
                href="/auth/login"
                className="text-blue-600 hover:text-blue-500 font-medium"
              >
                {t.signIn}
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
