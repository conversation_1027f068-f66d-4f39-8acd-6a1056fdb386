# PDF Generation Guide

## 📋 Overview

ဒီ guide က BitesTech POS System diagrams တွေကို PDF format အဖြစ် export လုပ်နည်းကို ရှင်းပြထားပါတယ်။

## 🛠️ Prerequisites

### Required Software
- **Node.js**: Version 16.0 သို့မဟုတ် အသစ်ကျော်
- **npm**: Node.js နှင့်အတူ ပါဝင်ပါတယ်
- **Internet Connection**: Mermaid CDN အတွက်

### System Requirements
- **RAM**: 2GB အနည်းဆုံး
- **Storage**: 500MB available space
- **OS**: Windows, macOS, Linux

## 🚀 Quick Start

### Method 1: Automatic Setup
```bash
# Navigate to diagrams folder
cd docs/diagrams

# Install dependencies and generate PDF
npm run setup
```

### Method 2: Manual Steps
```bash
# Navigate to diagrams folder
cd docs/diagrams

# Install Puppeteer
npm install

# Generate PDF
npm run generate-pdf
```

## 📁 Files Overview

### Core Files
- **`pdf-export.html`**: HTML template with embedded Mermaid diagrams
- **`generate-pdf.js`**: Node.js script for PDF generation
- **`package.json`**: Dependencies and scripts configuration

### Generated Files
- **`BitesTech-POS-System-Diagrams.pdf`**: Final PDF output
- **`node_modules/`**: Installed dependencies

## 🎯 PDF Content

### Included Sections
1. **System Architecture Overview**
   - Frontend/Backend structure
   - Technology stack details
   - Component relationships

2. **System Flow & Workflows**
   - User authentication flow
   - POS terminal operations
   - Main navigation flow

3. **Project Structure**
   - Complete folder hierarchy
   - Code organization
   - File naming conventions

4. **Key Features**
   - Feature highlights
   - Capabilities overview
   - Business benefits

### PDF Features
- **Professional Layout**: Clean, organized design
- **High-Quality Diagrams**: Vector-based Mermaid charts
- **Print-Ready**: A4 format with proper margins
- **Page Numbers**: Header and footer information
- **Color Coding**: Consistent visual theme

## ⚙️ Customization Options

### Modifying Content
```javascript
// In pdf-export.html, update diagrams:
<div class="mermaid">
graph TD
    // Your custom Mermaid syntax here
</div>
```

### Styling Changes
```css
/* In pdf-export.html <style> section */
.section h2 {
    color: #your-color;
    /* Your custom styles */
}
```

### PDF Settings
```javascript
// In generate-pdf.js, modify PDF options:
await page.pdf({
    format: 'A4',           // Page size
    printBackground: true,   // Include colors
    margin: {               // Page margins
        top: '1cm',
        right: '1cm',
        bottom: '1cm',
        left: '1cm'
    }
});
```

## 🔧 Troubleshooting

### Common Issues

#### 1. Puppeteer Installation Failed
```bash
# Clear npm cache
npm cache clean --force

# Install with legacy peer deps
npm install --legacy-peer-deps puppeteer
```

#### 2. Mermaid Diagrams Not Rendering
- Check internet connection (CDN required)
- Increase timeout in generate-pdf.js:
```javascript
await page.waitForTimeout(10000); // Increase to 10 seconds
```

#### 3. PDF Generation Timeout
```javascript
// Increase timeout in generate-pdf.js
await page.waitForFunction(() => {
    // ... existing code
}, { timeout: 60000 }); // Increase to 60 seconds
```

#### 4. Memory Issues
```bash
# Run with increased memory
node --max-old-space-size=4096 generate-pdf.js
```

### Error Messages

#### "Error: Failed to launch the browser process"
**Solution**: Install required dependencies
```bash
# Ubuntu/Debian
sudo apt-get install -y gconf-service libasound2 libatk1.0-0 libc6 libcairo2

# CentOS/RHEL
sudo yum install -y alsa-lib atk cups-libs gtk3 libXcomposite
```

#### "TimeoutError: waiting for function failed"
**Solution**: Check HTML content and Mermaid syntax
- Validate Mermaid diagrams at https://mermaid.live/
- Ensure all diagram syntax is correct

## 📊 Performance Tips

### Optimization Strategies
1. **Reduce Diagram Complexity**: Simplify large diagrams
2. **Optimize Images**: Use appropriate image sizes
3. **Memory Management**: Close browser properly
4. **Batch Processing**: Generate multiple PDFs efficiently

### Monitoring Generation
```javascript
// Add progress logging
console.log('📄 Loading HTML content...');
console.log('⏳ Waiting for Mermaid diagrams...');
console.log('✅ Diagrams rendered successfully');
console.log('🎉 PDF generated successfully!');
```

## 🔄 Updating Diagrams

### Workflow
1. **Modify Diagrams**: Update Mermaid syntax in HTML
2. **Test Locally**: Preview in browser first
3. **Generate PDF**: Run generation script
4. **Validate Output**: Check PDF quality
5. **Commit Changes**: Save to version control

### Best Practices
- **Test in Browser**: Preview HTML before PDF generation
- **Validate Syntax**: Use Mermaid live editor
- **Check Responsiveness**: Ensure diagrams fit page width
- **Version Control**: Track changes in Git

## 📱 Alternative Methods

### Online Tools
1. **Mermaid Live Editor**: https://mermaid.live/
   - Export as PNG/SVG
   - Manual PDF compilation

2. **Browser Print**: 
   - Open HTML in browser
   - Print to PDF (Ctrl+P)
   - Manual page breaks

### CI/CD Integration
```yaml
# GitHub Actions example
- name: Generate PDF
  run: |
    cd docs/diagrams
    npm install
    npm run generate-pdf
```

## 📞 Support

### Getting Help
- **Documentation**: Check this guide first
- **GitHub Issues**: Report bugs and feature requests
- **Community**: Join development discussions
- **Email**: <EMAIL>

### Contributing
1. **Fork Repository**: Create your own copy
2. **Make Changes**: Improve diagrams or scripts
3. **Test Thoroughly**: Ensure PDF generation works
4. **Submit PR**: Request merge to main branch

---

**Note**: PDF generation လုပ်တဲ့အခါ internet connection လိုအပ်ပါတယ် Mermaid CDN အတွက်။ Offline generation အတွက် local Mermaid setup လုပ်နိုင်ပါတယ်။

**Last Updated**: [Current Date]  
**Version**: 1.0  
**Maintainer**: BitesTech Development Team
