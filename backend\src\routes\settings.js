const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
    getSettings,
    updateGeneralSettings,
    updateTaxSettings,
    updateCurrencySettings,
    updateThemeSettings,
    updateReceiptSettings,
    updateInventorySettings,
    updatePOSSettings,
    updateNotificationSettings,
    updateSecuritySettings,
    backupDatabase,
    getBackupHistory,
    testNotification,
    getSecurityLogs,
    resetSettings
} = require('../controllers/settingsController');

// Import Settings model if it exists, otherwise use fallback
let Settings;
try {
    Settings = require('../models/Settings');
} catch (error) {
    console.warn('Settings model not found, using fallback');
    Settings = null;
}

const router = express.Router();

// Default settings (in production, store in database)
const defaultSettings = {
    notifications: {
        email: true,
        push: true,
        sms: false,
        sound: true
    },
    receipt: {
        footerText: 'Thank you for your business!',
        showFooter: true
    }
};

let systemSettings = {
    company: {
        name: 'BitsTech POS',
        email: '<EMAIL>',
        phone: '+95-9-***********',
        address: 'Yangon, Myanmar',
        logo: '',
        website: 'https://bitstech.com'
    },
    tax: {
        defaultRate: 5,
        includedInPrice: false,
        taxNumber: 'TAX-001-2024'
    },
    currency: {
        primary: 'MMK',
        symbol: 'Ks',
        exchangeRates: {
            USD: 2100,
            THB: 60
        }
    },
    receipt: {
        template: 'default',
        showLogo: true,
        showTaxNumber: true,
        footerText: 'Thank you for your business!'
    },
    pos: {
        autoLogout: 30, // minutes
        soundEnabled: true,
        defaultPaymentMethod: 'cash'
    }
};

// @route   GET /api/settings/company
// @desc    Get company settings
// @access  Private
router.get('/company', protect, (req, res) => {
    res.json({
        success: true,
        data: systemSettings.company
    });
});

// @route   PUT /api/settings/company
// @desc    Update company settings
// @access  Private (Admin only)
router.put('/company', protect, authorize('admin'), (req, res) => {
    try {
        const { name, email, phone, address, logo, website } = req.body;

        if (name) systemSettings.company.name = name;
        if (email) systemSettings.company.email = email;
        if (phone) systemSettings.company.phone = phone;
        if (address) systemSettings.company.address = address;
        if (logo !== undefined) systemSettings.company.logo = logo;
        if (website) systemSettings.company.website = website;

        res.json({
            success: true,
            message: 'Company settings updated successfully',
            data: systemSettings.company
        });
    } catch (error) {
        console.error('Error updating company settings:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating company settings'
        });
    }
});

// @route   GET /api/settings/tax
// @desc    Get tax settings
// @access  Private
router.get('/tax', protect, (req, res) => {
    res.json({
        success: true,
        data: systemSettings.tax
    });
});

// @route   PUT /api/settings/tax
// @desc    Update tax settings
// @access  Private (Admin, Manager)
router.put('/tax', protect, authorize('admin', 'manager'), (req, res) => {
    try {
        const { defaultRate, includedInPrice, taxNumber } = req.body;

        if (defaultRate !== undefined) {
            if (defaultRate < 0 || defaultRate > 100) {
                return res.status(400).json({
                    success: false,
                    message: 'Tax rate must be between 0 and 100'
                });
            }
            systemSettings.tax.defaultRate = defaultRate;
        }

        if (includedInPrice !== undefined) systemSettings.tax.includedInPrice = includedInPrice;
        if (taxNumber) systemSettings.tax.taxNumber = taxNumber;

        res.json({
            success: true,
            message: 'Tax settings updated successfully',
            data: systemSettings.tax
        });
    } catch (error) {
        console.error('Error updating tax settings:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating tax settings'
        });
    }
});

// @route   GET /api/settings/currency
// @desc    Get currency settings
// @access  Private
router.get('/currency', protect, (req, res) => {
    res.json({
        success: true,
        data: systemSettings.currency
    });
});

// @route   PUT /api/settings/currency
// @desc    Update currency settings
// @access  Private (Admin)
router.put('/currency', protect, authorize('admin'), (req, res) => {
    try {
        const { primary, symbol, exchangeRates } = req.body;

        if (primary) systemSettings.currency.primary = primary;
        if (symbol) systemSettings.currency.symbol = symbol;
        if (exchangeRates) {
            systemSettings.currency.exchangeRates = {
                ...systemSettings.currency.exchangeRates,
                ...exchangeRates
            };
        }

        res.json({
            success: true,
            message: 'Currency settings updated successfully',
            data: systemSettings.currency
        });
    } catch (error) {
        console.error('Error updating currency settings:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating currency settings'
        });
    }
});

// All routes require authentication
router.use(protect);

// Get all settings
router.get('/', getSettings);

// Get specific settings
router.get('/notifications', async (req, res) => {
    try {
        let settings = defaultSettings;
        if (Settings) {
            settings = await Settings.findOne() || defaultSettings;
        }
        res.json({
            success: true,
            data: settings.notifications || defaultSettings.notifications
        });
    } catch (error) {
        console.error('Error fetching notification settings:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch notification settings'
        });
    }
});

router.get('/footer', async (req, res) => {
    try {
        let settings = defaultSettings;
        if (Settings) {
            settings = await Settings.findOne() || defaultSettings;
        }
        res.json({
            success: true,
            data: {
                footerText: settings.receipt?.footerText || defaultSettings.receipt.footerText,
                showFooter: settings.receipt?.showFooter !== false
            }
        });
    } catch (error) {
        console.error('Error fetching footer settings:', error);
        res.status(500).json({
            success: false,
            message: 'Failed to fetch footer settings'
        });
    }
});

// Update specific settings (Admin and Manager)
router.put('/general', authorize('admin', 'manager'), updateGeneralSettings);
router.put('/tax', authorize('admin', 'manager'), updateTaxSettings);
router.put('/currency', authorize('admin', 'manager'), updateCurrencySettings);
router.put('/receipt', authorize('admin', 'manager'), updateReceiptSettings);
router.put('/inventory', authorize('admin', 'manager'), updateInventorySettings);
router.put('/pos', authorize('admin', 'manager'), updatePOSSettings);

// Theme and notification settings (all authenticated users can update)
router.put('/theme', updateThemeSettings);
router.put('/notifications', updateNotificationSettings);

// Security settings (Admin only)
router.put('/security', authorize('admin'), updateSecuritySettings);

// Database management (Admin only)
router.post('/backup', authorize('admin'), backupDatabase);
router.get('/backup-history', authorize('admin'), getBackupHistory);

// Testing and monitoring (Admin only)
router.post('/test-notification', authorize('admin'), testNotification);
router.get('/security-logs', authorize('admin'), getSecurityLogs);

// Reset settings (Admin only)
router.post('/reset', authorize('admin'), resetSettings);

module.exports = router;
