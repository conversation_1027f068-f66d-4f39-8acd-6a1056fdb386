# Detailed Folder Structure

## Project Structure Diagram

```mermaid
graph TD
    A[BitesTech POS] --> B[frontend/]
    A --> C[backend/]
    A --> D[shared/]
    A --> E[docs/]
    
    B --> B1[src/]
    B --> B2[public/]
    B --> B3[package.json]
    
    B1 --> B11[components/]
    B1 --> B12[pages/]
    B1 --> B13[hooks/]
    B1 --> B14[utils/]
    B1 --> B15[styles/]
    B1 --> B16[locales/]
    B1 --> B17[store/]
    
    B11 --> B111[ui/]
    B11 --> B112[pos/]
    B11 --> B113[dashboard/]
    B11 --> B114[layout/]
    
    B12 --> B121[dashboard/]
    B12 --> B122[pos/]
    B12 --> B123[products/]
    B12 --> B124[sales/]
    B12 --> B125[reports/]
    B12 --> B126[inventory/]
    B12 --> B127[users/]
    B12 --> B128[settings/]
    
    C --> C1[src/]
    C --> C2[package.json]
    
    C1 --> C11[controllers/]
    C1 --> C12[models/]
    C1 --> C13[routes/]
    C1 --> C14[middleware/]
    C1 --> C15[utils/]
    C1 --> C16[config/]
    
    D --> D1[types/]
    D --> D2[constants/]
    D --> D3[interfaces/]
```

## Complete Directory Structure

```
BitesTech-POS/
├── 📁 frontend/                    # React + Next.js Frontend Application
│   ├── 📁 src/
│   │   ├── 📁 components/         # Reusable React Components
│   │   │   ├── 📁 ui/            # Basic UI Components
│   │   │   │   ├── Button.tsx
│   │   │   │   ├── Input.tsx
│   │   │   │   ├── Modal.tsx
│   │   │   │   ├── Card.tsx
│   │   │   │   ├── Table.tsx
│   │   │   │   └── index.ts
│   │   │   ├── 📁 pos/           # POS Terminal Components
│   │   │   │   ├── ProductGrid.tsx
│   │   │   │   ├── Cart.tsx
│   │   │   │   ├── PaymentModal.tsx
│   │   │   │   ├── Receipt.tsx
│   │   │   │   └── BarcodeScanner.tsx
│   │   │   ├── 📁 dashboard/     # Dashboard Components
│   │   │   │   ├── StatsCard.tsx
│   │   │   │   ├── SalesChart.tsx
│   │   │   │   ├── RecentSales.tsx
│   │   │   │   └── QuickActions.tsx
│   │   │   ├── 📁 layout/        # Layout Components
│   │   │   │   ├── Header.tsx
│   │   │   │   ├── Sidebar.tsx
│   │   │   │   ├── Footer.tsx
│   │   │   │   └── Layout.tsx
│   │   │   └── 📁 forms/         # Form Components
│   │   │       ├── ProductForm.tsx
│   │   │       ├── UserForm.tsx
│   │   │       └── SettingsForm.tsx
│   │   ├── 📁 pages/             # Next.js Pages
│   │   │   ├── 📁 api/           # API Routes
│   │   │   ├── 📁 auth/          # Authentication Pages
│   │   │   │   ├── login.tsx
│   │   │   │   └── register.tsx
│   │   │   ├── 📁 dashboard/     # Dashboard Pages
│   │   │   │   └── index.tsx
│   │   │   ├── 📁 pos/           # POS Terminal Pages
│   │   │   │   └── index.tsx
│   │   │   ├── 📁 products/      # Product Management
│   │   │   │   ├── index.tsx
│   │   │   │   ├── [id].tsx
│   │   │   │   └── new.tsx
│   │   │   ├── 📁 sales/         # Sales Management
│   │   │   │   ├── index.tsx
│   │   │   │   └── [id].tsx
│   │   │   ├── 📁 reports/       # Reports Pages
│   │   │   │   ├── index.tsx
│   │   │   │   ├── sales.tsx
│   │   │   │   └── inventory.tsx
│   │   │   ├── 📁 inventory/     # Inventory Management
│   │   │   │   └── index.tsx
│   │   │   ├── 📁 users/         # User Management
│   │   │   │   ├── index.tsx
│   │   │   │   └── [id].tsx
│   │   │   ├── 📁 settings/      # Settings Pages
│   │   │   │   ├── index.tsx
│   │   │   │   ├── company.tsx
│   │   │   │   ├── tax.tsx
│   │   │   │   └── appearance.tsx
│   │   │   └── index.tsx         # Home Page
│   │   ├── 📁 hooks/             # Custom React Hooks
│   │   │   ├── useAuth.ts
│   │   │   ├── useLocalStorage.ts
│   │   │   ├── useTheme.ts
│   │   │   └── useLanguage.ts
│   │   ├── 📁 utils/             # Utility Functions
│   │   │   ├── api.ts
│   │   │   ├── formatters.ts
│   │   │   ├── validators.ts
│   │   │   └── constants.ts
│   │   ├── 📁 styles/            # Styling Files
│   │   │   ├── globals.css
│   │   │   ├── components.css
│   │   │   └── themes.css
│   │   ├── 📁 locales/           # Internationalization
│   │   │   ├── 📁 en/
│   │   │   │   ├── common.json
│   │   │   │   ├── pos.json
│   │   │   │   └── dashboard.json
│   │   │   └── 📁 mm/
│   │   │       ├── common.json
│   │   │       ├── pos.json
│   │   │       └── dashboard.json
│   │   ├── 📁 store/             # State Management
│   │   │   ├── authStore.ts
│   │   │   ├── cartStore.ts
│   │   │   ├── themeStore.ts
│   │   │   └── index.ts
│   │   └── 📁 types/             # TypeScript Definitions
│   │       ├── auth.ts
│   │       ├── product.ts
│   │       ├── sale.ts
│   │       └── index.ts
│   ├── 📁 public/                # Static Assets
│   │   ├── 📁 images/
│   │   ├── 📁 icons/
│   │   ├── favicon.ico
│   │   └── manifest.json
│   ├── 📄 package.json
│   ├── 📄 next.config.js
│   ├── 📄 tailwind.config.js
│   └── 📄 tsconfig.json
│
├── 📁 backend/                    # Node.js + Express Backend
│   ├── 📁 src/
│   │   ├── 📁 controllers/       # API Controllers
│   │   │   ├── authController.js
│   │   │   ├── posController.js
│   │   │   ├── productController.js
│   │   │   ├── saleController.js
│   │   │   ├── reportController.js
│   │   │   ├── inventoryController.js
│   │   │   ├── userController.js
│   │   │   └── settingController.js
│   │   ├── 📁 models/            # Database Models
│   │   │   ├── User.js
│   │   │   ├── Product.js
│   │   │   ├── Sale.js
│   │   │   ├── Category.js
│   │   │   ├── Inventory.js
│   │   │   └── Setting.js
│   │   ├── 📁 routes/            # API Routes
│   │   │   ├── auth.js
│   │   │   ├── pos.js
│   │   │   ├── products.js
│   │   │   ├── sales.js
│   │   │   ├── reports.js
│   │   │   ├── inventory.js
│   │   │   ├── users.js
│   │   │   └── settings.js
│   │   ├── 📁 middleware/        # Express Middleware
│   │   │   ├── auth.js
│   │   │   ├── validation.js
│   │   │   ├── errorHandler.js
│   │   │   └── logger.js
│   │   ├── 📁 utils/             # Backend Utilities
│   │   │   ├── database.js
│   │   │   ├── encryption.js
│   │   │   ├── email.js
│   │   │   └── helpers.js
│   │   ├── 📁 config/            # Configuration
│   │   │   ├── database.js
│   │   │   ├── jwt.js
│   │   │   └── app.js
│   │   └── 📄 server.js          # Main Server File
│   ├── 📄 package.json
│   └── 📄 .env.example
│
├── 📁 shared/                     # Shared Code
│   ├── 📁 types/                 # Shared TypeScript Types
│   │   ├── api.ts
│   │   ├── database.ts
│   │   └── common.ts
│   ├── 📁 constants/             # Shared Constants
│   │   ├── currencies.ts
│   │   ├── roles.ts
│   │   └── status.ts
│   └── 📁 interfaces/            # Shared Interfaces
│       ├── user.ts
│       ├── product.ts
│       └── sale.ts
│
├── 📁 docs/                      # Documentation
│   ├── 📄 architecture-overview.md
│   ├── 📄 folder-structure.md
│   ├── 📄 features-flow.md
│   ├── 📄 api-documentation.md
│   ├── 📄 setup-guide.md
│   └── 📄 deployment-guide.md
│
├── 📄 .env.example               # Environment Variables Template
├── 📄 .gitignore                 # Git Ignore Rules
├── 📄 docker-compose.yml         # Docker Configuration
├── 📄 README.md                  # Project Documentation
└── 📄 package.json               # Root Package Configuration
```

## Key Directory Explanations

### Frontend Structure
- **components/**: Reusable React components organized by functionality
- **pages/**: Next.js file-based routing system
- **hooks/**: Custom React hooks for shared logic
- **store/**: State management (Zustand/Redux)
- **locales/**: Multi-language translation files

### Backend Structure
- **controllers/**: Business logic for API endpoints
- **models/**: Database schema definitions
- **routes/**: API route definitions
- **middleware/**: Express middleware functions
- **utils/**: Helper functions and utilities

### Shared Structure
- **types/**: TypeScript type definitions used by both frontend and backend
- **constants/**: Shared constants and enums
- **interfaces/**: Common interface definitions

This structure ensures:
- ✅ Clear separation of concerns
- ✅ Scalable architecture
- ✅ Easy maintenance
- ✅ Code reusability
- ✅ Type safety with TypeScript
