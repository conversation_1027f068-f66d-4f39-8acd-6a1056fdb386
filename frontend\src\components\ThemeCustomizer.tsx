'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Slider } from '@/components/ui/slider'
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { 
  Palette, 
  Type, 
  Layout, 
  Settings, 
  Download, 
  Upload, 
  RotateCcw,
  Eye,
  Sparkles,
  Zap,
  Layers
} from 'lucide-react'
import { useThemeCustomization, colorSchemes, fontFamilies } from '@/contexts/theme-customization-context'
import { useTheme } from '@/contexts/theme-context'

export function ThemeCustomizer() {
  const { language } = useTheme()
  const {
    customization,
    updateColors,
    updateFonts,
    updateBorderRadius,
    updateSpacing,
    updateAnimations,
    updateShadows,
    updateGradients,
    applyColorScheme,
    resetToDefault,
    exportTheme,
    importTheme
  } = useThemeCustomization()

  const [activeTab, setActiveTab] = useState('colors')

  const translations = {
    en: {
      title: 'Theme Customizer',
      subtitle: 'Customize the appearance of your POS system',
      tabs: {
        colors: 'Colors',
        typography: 'Typography',
        layout: 'Layout',
        effects: 'Effects'
      },
      colors: {
        title: 'Color Scheme',
        description: 'Choose or customize your color palette',
        schemes: 'Predefined Schemes',
        custom: 'Custom Colors',
        primary: 'Primary Color',
        secondary: 'Secondary Color',
        accent: 'Accent Color',
        background: 'Background',
        foreground: 'Text Color'
      },
      typography: {
        title: 'Typography',
        description: 'Customize fonts and text appearance',
        family: 'Font Family',
        size: 'Font Size',
        weight: 'Font Weight',
        lineHeight: 'Line Height'
      },
      layout: {
        title: 'Layout',
        description: 'Adjust spacing and border radius',
        borderRadius: 'Border Radius',
        spacing: 'Spacing'
      },
      effects: {
        title: 'Visual Effects',
        description: 'Enable or disable visual enhancements',
        animations: 'Animations',
        shadows: 'Shadows',
        gradients: 'Gradients'
      },
      actions: {
        preview: 'Preview',
        reset: 'Reset to Default',
        export: 'Export Theme',
        import: 'Import Theme'
      }
    },
    mm: {
      title: 'Theme ပြင်ဆင်ရန်',
      subtitle: 'သင့် POS စနစ်၏ အသွင်အပြင်ကို ပြင်ဆင်ပါ',
      tabs: {
        colors: 'အရောင်များ',
        typography: 'စာလုံးပုံစံ',
        layout: 'အပြင်အဆင်',
        effects: 'အထူးအကျိုးများ'
      },
      colors: {
        title: 'အရောင်ပုံစံ',
        description: 'သင့်အရောင်စပ်ကို ရွေးချယ် သို့မဟုတ် ပြင်ဆင်ပါ',
        schemes: 'ကြိုတင်သတ်မှတ်ထားသော ပုံစံများ',
        custom: 'စိတ်ကြိုက် အရောင်များ',
        primary: 'အဓိက အရောင်',
        secondary: 'ဒုတိယ အရောင်',
        accent: 'အထူး အရောင်',
        background: 'နောက်ခံ',
        foreground: 'စာလုံး အရောင်'
      },
      typography: {
        title: 'စာလုံးပုံစံ',
        description: 'စာလုံးများနှင့် စာသား အသွင်အပြင်ကို ပြင်ဆင်ပါ',
        family: 'စာလုံး မျိုးစု',
        size: 'စာလုံး အရွယ်အစား',
        weight: 'စာလုံး အထူ',
        lineHeight: 'လိုင်း အမြင့်'
      },
      layout: {
        title: 'အပြင်အဆင်',
        description: 'နေရာခြားမှုနှင့် ထောင့်မွေးမှုကို ချိန်ညှိပါ',
        borderRadius: 'ထောင့်မွေးမှု',
        spacing: 'နေရာခြားမှု'
      },
      effects: {
        title: 'အမြင်အာရုံ အကျိုးများ',
        description: 'အမြင်အာရုံ တိုးတက်မှုများကို ဖွင့်ပိတ်ပါ',
        animations: 'လှုပ်ရှားမှုများ',
        shadows: 'အရိပ်များ',
        gradients: 'အရောင်ရောစပ်မှုများ'
      },
      actions: {
        preview: 'ကြိုကြည့်ရန်',
        reset: 'မူလအတိုင်း ပြန်လည်သတ်မှတ်ရန်',
        export: 'Theme ထုတ်ယူရန်',
        import: 'Theme တင်သွင်းရန်'
      }
    }
  }

  const t = translations[language] || translations.en

  const handleColorChange = (colorKey: string, value: string) => {
    updateColors({ [colorKey]: value })
  }

  const handleExport = () => {
    const themeData = exportTheme()
    const blob = new Blob([themeData], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = 'bitstech-theme.json'
    a.click()
    URL.revokeObjectURL(url)
  }

  const handleImport = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        const content = e.target?.result as string
        if (importTheme(content)) {
          alert(language === 'mm' ? 'Theme အောင်မြင်စွာ တင်သွင်းပြီးပါပြီ!' : 'Theme imported successfully!')
        } else {
          alert(language === 'mm' ? 'Theme တင်သွင်းမှု မအောင်မြင်ပါ!' : 'Failed to import theme!')
        }
      }
      reader.readAsText(file)
    }
  }

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <div className="flex items-center space-x-2">
          <Palette className="h-6 w-6 text-primary" />
          <div>
            <CardTitle className={language === 'mm' ? 'font-myanmar' : ''}>{t.title}</CardTitle>
            <CardDescription className={language === 'mm' ? 'font-myanmar leading-relaxed' : ''}>
              {t.subtitle}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="colors" className="flex items-center space-x-2">
              <Palette className="h-4 w-4" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.tabs.colors}</span>
            </TabsTrigger>
            <TabsTrigger value="typography" className="flex items-center space-x-2">
              <Type className="h-4 w-4" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.tabs.typography}</span>
            </TabsTrigger>
            <TabsTrigger value="layout" className="flex items-center space-x-2">
              <Layout className="h-4 w-4" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.tabs.layout}</span>
            </TabsTrigger>
            <TabsTrigger value="effects" className="flex items-center space-x-2">
              <Sparkles className="h-4 w-4" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.tabs.effects}</span>
            </TabsTrigger>
          </TabsList>

          {/* Colors Tab */}
          <TabsContent value="colors" className="space-y-6">
            <div>
              <h3 className={`text-lg font-semibold mb-4 ${language === 'mm' ? 'font-myanmar' : ''}`}>
                {t.colors.schemes}
              </h3>
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
                {Object.entries(colorSchemes).map(([key, scheme]) => (
                  <Button
                    key={key}
                    variant="outline"
                    className="h-20 flex flex-col items-center justify-center space-y-2"
                    onClick={() => applyColorScheme(key as keyof typeof colorSchemes)}
                  >
                    <div className="flex space-x-1">
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: scheme.primary }}
                      />
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: scheme.secondary }}
                      />
                      <div 
                        className="w-3 h-3 rounded-full" 
                        style={{ backgroundColor: scheme.accent }}
                      />
                    </div>
                    <span className="text-xs capitalize">{key}</span>
                  </Button>
                ))}
              </div>
            </div>

            <div>
              <h3 className={`text-lg font-semibold mb-4 ${language === 'mm' ? 'font-myanmar' : ''}`}>
                {t.colors.custom}
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {Object.entries(customization.colors).slice(0, 8).map(([key, value]) => (
                  <div key={key} className="flex items-center space-x-3">
                    <Label className={`w-24 ${language === 'mm' ? 'font-myanmar' : ''}`}>
                      {t.colors[key as keyof typeof t.colors] || key}
                    </Label>
                    <input
                      type="color"
                      value={value}
                      onChange={(e) => handleColorChange(key, e.target.value)}
                      className="w-12 h-8 rounded border border-border cursor-pointer"
                    />
                    <Badge variant="outline" className="text-xs font-mono">
                      {value}
                    </Badge>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          {/* Typography Tab */}
          <TabsContent value="typography" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <div>
                  <Label className={language === 'mm' ? 'font-myanmar' : ''}>{t.typography.family}</Label>
                  <Select 
                    value={customization.fonts.family} 
                    onValueChange={(value) => updateFonts({ family: value })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.entries(fontFamilies).map(([key, value]) => (
                        <SelectItem key={key} value={key}>
                          <span style={{ fontFamily: value }}>{key}</span>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className={language === 'mm' ? 'font-myanmar' : ''}>{t.typography.size}</Label>
                  <Select 
                    value={customization.fonts.size} 
                    onValueChange={(value) => updateFonts({ size: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="small">Small</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="large">Large</SelectItem>
                      <SelectItem value="extra-large">Extra Large</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <div className="space-y-4">
                <div>
                  <Label className={language === 'mm' ? 'font-myanmar' : ''}>{t.typography.weight}</Label>
                  <Select 
                    value={customization.fonts.weight} 
                    onValueChange={(value) => updateFonts({ weight: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="light">Light</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="medium">Medium</SelectItem>
                      <SelectItem value="semibold">Semibold</SelectItem>
                      <SelectItem value="bold">Bold</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div>
                  <Label className={language === 'mm' ? 'font-myanmar' : ''}>{t.typography.lineHeight}</Label>
                  <Select 
                    value={customization.fonts.lineHeight} 
                    onValueChange={(value) => updateFonts({ lineHeight: value as any })}
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="tight">Tight</SelectItem>
                      <SelectItem value="normal">Normal</SelectItem>
                      <SelectItem value="relaxed">Relaxed</SelectItem>
                      <SelectItem value="loose">Loose</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {/* Typography Preview */}
            <div className="p-4 border border-border rounded-lg bg-muted/50">
              <h4 className={`text-lg font-semibold mb-2 ${language === 'mm' ? 'font-myanmar' : ''}`}>
                {language === 'mm' ? 'နမူနာ စာသား' : 'Preview Text'}
              </h4>
              <p className={language === 'mm' ? 'font-myanmar leading-relaxed' : ''}>
                {language === 'mm' ? 
                  'ဤသည်မှာ သင့် POS စနစ်တွင် အသုံးပြုမည့် စာလုံးပုံစံ၏ နမူနာ ဖြစ်ပါသည်။ မြန်မာစာလုံးများ ရှင်းရှင်းလင်းလင်း မြင်ရပါသည်။' :
                  'This is a preview of how your text will appear in the POS system. The typography settings affect readability and user experience.'
                }
              </p>
            </div>
          </TabsContent>

          {/* Layout Tab */}
          <TabsContent value="layout" className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Label className={language === 'mm' ? 'font-myanmar' : ''}>{t.layout.borderRadius}</Label>
                <Select 
                  value={customization.borderRadius} 
                  onValueChange={updateBorderRadius}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">None</SelectItem>
                    <SelectItem value="small">Small</SelectItem>
                    <SelectItem value="medium">Medium</SelectItem>
                    <SelectItem value="large">Large</SelectItem>
                    <SelectItem value="full">Full</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Label className={language === 'mm' ? 'font-myanmar' : ''}>{t.layout.spacing}</Label>
                <Select 
                  value={customization.spacing} 
                  onValueChange={updateSpacing}
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="compact">Compact</SelectItem>
                    <SelectItem value="normal">Normal</SelectItem>
                    <SelectItem value="comfortable">Comfortable</SelectItem>
                    <SelectItem value="spacious">Spacious</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </TabsContent>

          {/* Effects Tab */}
          <TabsContent value="effects" className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className={language === 'mm' ? 'font-myanmar' : ''}>{t.effects.animations}</Label>
                  <p className={`text-sm text-muted-foreground ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
                    {language === 'mm' ? 'လှုပ်ရှားမှုများကို ဖွင့်ပိတ်ပါ' : 'Enable or disable animations'}
                  </p>
                </div>
                <Switch
                  checked={customization.animations}
                  onCheckedChange={updateAnimations}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className={language === 'mm' ? 'font-myanmar' : ''}>{t.effects.shadows}</Label>
                  <p className={`text-sm text-muted-foreground ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
                    {language === 'mm' ? 'အရိပ်များကို ဖွင့်ပိတ်ပါ' : 'Enable or disable shadows'}
                  </p>
                </div>
                <Switch
                  checked={customization.shadows}
                  onCheckedChange={updateShadows}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="space-y-1">
                  <Label className={language === 'mm' ? 'font-myanmar' : ''}>{t.effects.gradients}</Label>
                  <p className={`text-sm text-muted-foreground ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
                    {language === 'mm' ? 'အရောင်ရောစပ်မှုများကို ဖွင့်ပိတ်ပါ' : 'Enable or disable gradients'}
                  </p>
                </div>
                <Switch
                  checked={customization.gradients}
                  onCheckedChange={updateGradients}
                />
              </div>
            </div>
          </TabsContent>
        </Tabs>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 mt-8 pt-6 border-t border-border">
          <Button onClick={resetToDefault} variant="outline">
            <RotateCcw className="h-4 w-4 mr-2" />
            <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.actions.reset}</span>
          </Button>
          
          <Button onClick={handleExport} variant="outline">
            <Download className="h-4 w-4 mr-2" />
            <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.actions.export}</span>
          </Button>
          
          <div>
            <input
              type="file"
              accept=".json"
              onChange={handleImport}
              className="hidden"
              id="theme-import"
            />
            <Button asChild variant="outline">
              <label htmlFor="theme-import" className="cursor-pointer">
                <Upload className="h-4 w-4 mr-2" />
                <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.actions.import}</span>
              </label>
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
