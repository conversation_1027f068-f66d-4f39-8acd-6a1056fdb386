const mongoose = require('mongoose');

const stockAdjustmentSchema = new mongoose.Schema({
    adjustmentNumber: {
        type: String,
        required: true,
        unique: true,
        trim: true
    },
    type: {
        type: String,
        enum: ['increase', 'decrease', 'correction', 'damage', 'theft', 'expired', 'return', 'transfer'],
        required: [true, 'Adjustment type is required']
    },
    reason: {
        type: String,
        required: [true, 'Reason is required'],
        trim: true,
        maxlength: [500, 'Reason cannot exceed 500 characters']
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Created by user is required']
    },
    approvedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    items: [{
        product: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Product',
            required: true
        },
        productName: {
            type: String,
            required: true
        },
        sku: {
            type: String,
            required: true
        },
        currentQuantity: {
            type: Number,
            required: true,
            min: [0, 'Current quantity cannot be negative']
        },
        adjustmentQuantity: {
            type: Number,
            required: [true, 'Adjustment quantity is required']
        },
        newQuantity: {
            type: Number,
            required: true,
            min: [0, 'New quantity cannot be negative']
        },
        unitCost: {
            type: Number,
            required: true,
            min: [0, 'Unit cost cannot be negative']
        },
        totalCost: {
            type: Number,
            required: true
        },
        notes: {
            type: String,
            maxlength: [200, 'Item notes cannot exceed 200 characters']
        }
    }],
    status: {
        type: String,
        enum: ['draft', 'pending', 'approved', 'applied', 'rejected'],
        default: 'draft'
    },
    totalItems: {
        type: Number,
        required: true,
        min: [1, 'Must have at least one item']
    },
    totalCostImpact: {
        type: Number,
        required: true
    },
    currency: {
        type: String,
        enum: ['MMK', 'USD', 'THB', 'EUR', 'SGD'],
        default: 'MMK'
    },
    adjustmentDate: {
        type: Date,
        default: Date.now
    },
    approvalDate: Date,
    appliedDate: Date,
    rejectionReason: String,
    notes: {
        type: String,
        maxlength: [1000, 'Notes cannot exceed 1000 characters']
    },
    attachments: [{
        filename: String,
        originalName: String,
        mimetype: String,
        size: Number,
        path: String,
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    location: {
        type: String,
        trim: true,
        default: 'Main Warehouse'
    },
    batchNumber: String,
    expiryDate: Date,
    supplierReference: String
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes
stockAdjustmentSchema.index({ adjustmentNumber: 1 });
stockAdjustmentSchema.index({ type: 1 });
stockAdjustmentSchema.index({ status: 1 });
stockAdjustmentSchema.index({ adjustmentDate: -1 });
stockAdjustmentSchema.index({ createdBy: 1 });
stockAdjustmentSchema.index({ 'items.product': 1 });

// Virtual for total quantity adjustment
stockAdjustmentSchema.virtual('totalQuantityAdjustment').get(function() {
    return this.items.reduce((total, item) => total + Math.abs(item.adjustmentQuantity), 0);
});

// Virtual for adjustment summary
stockAdjustmentSchema.virtual('adjustmentSummary').get(function() {
    const increases = this.items.filter(item => item.adjustmentQuantity > 0);
    const decreases = this.items.filter(item => item.adjustmentQuantity < 0);
    
    return {
        totalIncreases: increases.reduce((sum, item) => sum + item.adjustmentQuantity, 0),
        totalDecreases: Math.abs(decreases.reduce((sum, item) => sum + item.adjustmentQuantity, 0)),
        itemsIncreased: increases.length,
        itemsDecreased: decreases.length
    };
});

// Pre-save middleware
stockAdjustmentSchema.pre('save', function(next) {
    if (this.isNew && !this.adjustmentNumber) {
        // Generate adjustment number
        const date = new Date();
        const year = date.getFullYear().toString().slice(-2);
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const day = date.getDate().toString().padStart(2, '0');
        const timestamp = Date.now().toString().slice(-6);
        this.adjustmentNumber = `ADJ-${year}${month}${day}-${timestamp}`;
    }

    // Calculate totals
    this.totalItems = this.items.length;
    this.totalCostImpact = this.items.reduce((total, item) => total + item.totalCost, 0);

    // Calculate new quantities and total costs for each item
    this.items.forEach(item => {
        item.newQuantity = item.currentQuantity + item.adjustmentQuantity;
        item.totalCost = item.adjustmentQuantity * item.unitCost;
    });

    next();
});

// Static methods
stockAdjustmentSchema.statics.findByType = function(type) {
    return this.find({ type }).sort({ adjustmentDate: -1 });
};

stockAdjustmentSchema.statics.findByStatus = function(status) {
    return this.find({ status }).sort({ adjustmentDate: -1 });
};

stockAdjustmentSchema.statics.findByProduct = function(productId) {
    return this.find({ 'items.product': productId }).sort({ adjustmentDate: -1 });
};

stockAdjustmentSchema.statics.findPending = function() {
    return this.find({ 
        status: { $in: ['draft', 'pending'] }
    }).sort({ adjustmentDate: -1 });
};

stockAdjustmentSchema.statics.getAdjustmentStats = function(startDate, endDate) {
    const matchStage = {
        status: 'applied',
        appliedDate: {
            $gte: startDate,
            $lte: endDate
        }
    };

    return this.aggregate([
        { $match: matchStage },
        {
            $group: {
                _id: '$type',
                count: { $sum: 1 },
                totalCostImpact: { $sum: '$totalCostImpact' },
                totalQuantityAdjustment: { $sum: '$totalQuantityAdjustment' }
            }
        },
        { $sort: { count: -1 } }
    ]);
};

// Instance methods
stockAdjustmentSchema.methods.approve = function(approvedBy) {
    if (this.status !== 'pending') {
        throw new Error('Only pending adjustments can be approved');
    }
    
    this.status = 'approved';
    this.approvedBy = approvedBy;
    this.approvalDate = new Date();
    return this.save();
};

stockAdjustmentSchema.methods.reject = function(reason) {
    if (this.status !== 'pending') {
        throw new Error('Only pending adjustments can be rejected');
    }
    
    this.status = 'rejected';
    this.rejectionReason = reason;
    return this.save();
};

stockAdjustmentSchema.methods.apply = function() {
    if (this.status !== 'approved') {
        throw new Error('Only approved adjustments can be applied');
    }
    
    this.status = 'applied';
    this.appliedDate = new Date();
    return this.save();
};

stockAdjustmentSchema.methods.submitForApproval = function() {
    if (this.status !== 'draft') {
        throw new Error('Only draft adjustments can be submitted for approval');
    }
    
    this.status = 'pending';
    return this.save();
};

module.exports = mongoose.model('StockAdjustment', stockAdjustmentSchema);
