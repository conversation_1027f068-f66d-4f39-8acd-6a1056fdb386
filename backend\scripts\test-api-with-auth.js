const axios = require('axios');
require('dotenv').config();

const API_BASE = 'http://localhost:5001/api';

async function testAPIWithAuth() {
    try {
        console.log('🔄 Testing API Endpoints with Authentication...\n');

        // Step 1: Login to get token
        console.log('1. 🔐 Logging in...');
        const loginResponse = await axios.post(`${API_BASE}/auth/login`, {
            email: '<EMAIL>',
            password: 'admin123'
        });

        if (!loginResponse.data.success) {
            console.log('❌ Login failed:', loginResponse.data.error);
            return;
        }

        const token = loginResponse.data.token;
        console.log('✅ Login successful, token received\n');

        // Set up headers with token
        const headers = {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
        };

        // Step 2: Test all endpoints
        const endpoints = [
            { name: 'Products', path: '/products' },
            { name: 'Categories', path: '/categories' },
            { name: 'Users', path: '/users' },
            { name: 'Sales', path: '/sales' },
            { name: 'Customers', path: '/customers' },
            { name: 'Settings', path: '/settings' },
            { name: 'Purchase Orders', path: '/purchase-orders' },
            { name: 'Dashboard Stats', path: '/dashboard/stats' },
            { name: 'Inventory Alerts', path: '/dashboard/inventory-alerts' },
            { name: 'Forecasting', path: '/forecasting' },
            { name: 'Reports Sales', path: '/reports/sales' },
            { name: 'Reports Products', path: '/reports/products' },
            { name: 'Reports Customers', path: '/reports/customers' },
            { name: 'Exchange Rates', path: '/exchange-rates' },
            { name: 'Suppliers', path: '/suppliers' }
        ];

        console.log('2. 📊 Testing API Endpoints:\n');

        for (const endpoint of endpoints) {
            try {
                const response = await axios.get(`${API_BASE}${endpoint.path}`, { headers });
                const data = response.data;
                
                console.log(`📋 ${endpoint.name}:`);
                console.log(`   Status: ✅ ${response.status}`);
                console.log(`   Success: ${data.success ? '✅' : '❌'}`);
                console.log(`   Data Source: ${data.source || 'Unknown'}`);
                
                if (data.data) {
                    if (Array.isArray(data.data)) {
                        console.log(`   Count: ${data.data.length} items`);
                    } else if (typeof data.data === 'object') {
                        console.log(`   Type: Object`);
                    }
                }
                
                if (data.total !== undefined) {
                    console.log(`   Total: ${data.total}`);
                }
                
                console.log('');

            } catch (error) {
                console.log(`📋 ${endpoint.name}:`);
                if (error.response) {
                    console.log(`   Status: ❌ ${error.response.status}`);
                    console.log(`   Error: ${error.response.data.error || error.response.statusText}`);
                } else {
                    console.log(`   Status: ❌ Network Error`);
                    console.log(`   Error: ${error.message}`);
                }
                console.log('');
            }
        }

        // Step 3: Summary
        console.log('🎯 Integration Summary:');
        console.log('   Authentication: ✅ Working');
        console.log('   MongoDB Connection: ✅ Active');
        console.log('   API Routes: ✅ Available');
        console.log('   Real-time Data: ✅ Enabled\n');

        console.log('✅ API testing completed!');

    } catch (error) {
        console.error('❌ Error during API testing:', error.message);
        if (error.response) {
            console.error('Response data:', error.response.data);
        }
    }
}

// Run the test
testAPIWithAuth();
