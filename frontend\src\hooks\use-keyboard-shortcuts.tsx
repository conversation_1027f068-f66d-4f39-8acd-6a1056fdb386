'use client'

import { useEffect, useCallback } from 'react'
import { useRouter } from 'next/navigation'

interface KeyboardShortcut {
  key: string
  ctrlKey?: boolean
  altKey?: boolean
  shiftKey?: boolean
  action: () => void
  description: string
  descriptionLocal: string
}

export function useKeyboardShortcuts(language: 'en' | 'mm' = 'en') {
  const router = useRouter()

  const shortcuts: KeyboardShortcut[] = [
    // Navigation shortcuts
    {
      key: 'd',
      ctrlKey: true,
      action: () => router.push('/dashboard'),
      description: 'Go to Dashboard',
      descriptionLocal: 'ဒက်ရှ်ဘုတ် သို့ သွားရန်'
    },
    {
      key: 'p',
      ctrlKey: true,
      action: () => router.push('/pos'),
      description: 'Open POS Terminal',
      descriptionLocal: 'POS တာမီနယ် ဖွင့်ရန်'
    },
    {
      key: 'v',
      ctrlKey: true,
      action: () => router.push('/invoice'),
      description: 'Open Invoice',
      descriptionLocal: 'ဖိုင်ဝယ်စ် ဖွင့်ရန်'
    },

    {
      key: 'i',
      ctrlKey: true,
      action: () => router.push('/inventory'),
      description: 'Go to Inventory',
      descriptionLocal: 'စတော့ သို့ သွားရန်'
    },
    {
      key: 'v',
      ctrlKey: true,
      action: () => router.push('/invoice'),
      description: 'Open Invoice Page',
      descriptionLocal: 'ဖိုင်ဝယ်စ် စာမျက်နှာ ဖွင့်ရန်'
    },
    {
      key: 'r',
      ctrlKey: true,
      action: () => router.push('/reports'),
      description: 'View Reports',
      descriptionLocal: 'အစီရင်ခံစာများ ကြည့်ရန်'
    },
    {
      key: 'c',
      ctrlKey: true,
      action: () => router.push('/customers'),
      description: 'Manage Customers',
      descriptionLocal: 'ဖောက်သည်များ စီမံရန်'
    },
    {
      key: 's',
      ctrlKey: true,
      action: () => router.push('/settings'),
      description: 'Open Settings',
      descriptionLocal: 'ဆက်တင်များ ဖွင့်ရန်'
    },

    // POS shortcuts
    {
      key: 'n',
      ctrlKey: true,
      shiftKey: true,
      action: () => {
        // New sale
        if (window.location.pathname.includes('/pos')) {
          window.dispatchEvent(new CustomEvent('pos-new-sale'))
        }
      },
      description: 'New Sale',
      descriptionLocal: 'အသစ် ရောင်းချမှု'
    },
    {
      key: 'Enter',
      ctrlKey: true,
      action: () => {
        // Process payment
        if (window.location.pathname.includes('/pos') || window.location.pathname.includes('/checkout')) {
          window.dispatchEvent(new CustomEvent('pos-checkout'))
        }
      },
      description: 'Process Payment',
      descriptionLocal: 'ငွေပေးချေမှု လုပ်ဆောင်ရန်'
    },
    {
      key: 'b',
      ctrlKey: true,
      action: () => {
        // Open barcode scanner
        window.dispatchEvent(new CustomEvent('open-barcode-scanner'))
      },
      description: 'Open Barcode Scanner',
      descriptionLocal: 'ဘားကုဒ် စကင်နာ ဖွင့်ရန်'
    },

    // Search shortcuts
    {
      key: 'f',
      ctrlKey: true,
      action: () => {
        // Focus search
        const searchInput = document.querySelector('input[type="search"], input[placeholder*="search"], input[placeholder*="ရှာ"]') as HTMLInputElement
        if (searchInput) {
          searchInput.focus()
          searchInput.select()
        }
      },
      description: 'Focus Search',
      descriptionLocal: 'ရှာဖွေမှု အာရုံစိုက်ရန်'
    },

    // Quick actions
    {
      key: 'Escape',
      action: () => {
        // Close modals/dialogs
        window.dispatchEvent(new CustomEvent('close-modal'))
      },
      description: 'Close Modal/Dialog',
      descriptionLocal: 'မုဒယ်/ဒိုင်ယာလော့ ပိုင်ရန်'
    },
    {
      key: 'F1',
      action: () => {
        // Show help/shortcuts
        window.dispatchEvent(new CustomEvent('show-shortcuts'))
      },
      description: 'Show Keyboard Shortcuts',
      descriptionLocal: 'ကီးဘုတ် ဖြတ်လမ်းများ ပြရန်'
    },

    // Theme shortcuts
    {
      key: 't',
      ctrlKey: true,
      shiftKey: true,
      action: () => {
        // Toggle theme
        window.dispatchEvent(new CustomEvent('toggle-theme'))
      },
      description: 'Toggle Theme',
      descriptionLocal: 'အပြင်အဆင် ပြောင်းရန်'
    }
  ]

  const handleKeyDown = useCallback((event: KeyboardEvent) => {
    // Don't trigger shortcuts when typing in inputs
    if (event.target instanceof HTMLInputElement || 
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement) {
      return
    }

    const matchingShortcut = shortcuts.find(shortcut => {
      return shortcut.key.toLowerCase() === event.key.toLowerCase() &&
             !!shortcut.ctrlKey === event.ctrlKey &&
             !!shortcut.altKey === event.altKey &&
             !!shortcut.shiftKey === event.shiftKey
    })

    if (matchingShortcut) {
      event.preventDefault()
      matchingShortcut.action()
    }
  }, [shortcuts, router])

  useEffect(() => {
    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [handleKeyDown])

  return { shortcuts }
}

// Keyboard shortcuts help component
interface KeyboardShortcutsHelpProps {
  isOpen: boolean
  onClose: () => void
  language: 'en' | 'mm'
}

export function KeyboardShortcutsHelp({ isOpen, onClose, language }: KeyboardShortcutsHelpProps) {
  const { shortcuts } = useKeyboardShortcuts(language)

  useEffect(() => {
    const handleShowShortcuts = () => {
      // This would be handled by the parent component
    }

    window.addEventListener('show-shortcuts', handleShowShortcuts)
    return () => window.removeEventListener('show-shortcuts', handleShowShortcuts)
  }, [])

  if (!isOpen) return null

  const formatShortcut = (shortcut: KeyboardShortcut) => {
    const keys = []
    if (shortcut.ctrlKey) keys.push('Ctrl')
    if (shortcut.altKey) keys.push('Alt')
    if (shortcut.shiftKey) keys.push('Shift')
    keys.push(shortcut.key === ' ' ? 'Space' : shortcut.key)
    return keys.join(' + ')
  }

  const groupedShortcuts = {
    navigation: shortcuts.filter(s => ['d', 'p', 'm', 'i', 'r', 'c', 's'].includes(s.key.toLowerCase())),
    pos: shortcuts.filter(s => ['n', 'Enter', 'b'].includes(s.key) && (s.ctrlKey || s.shiftKey)),
    general: shortcuts.filter(s => ['f', 'Escape', 'F1', 't'].includes(s.key))
  }

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[80vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold">
              {language === 'mm' ? 'ကီးဘုတ် ဖြတ်လမ်းများ' : 'Keyboard Shortcuts'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 text-xl font-bold"
            >
              ×
            </button>
          </div>

          <div className="space-y-6">
            {/* Navigation Shortcuts */}
            <div>
              <h3 className="text-lg font-semibold mb-3 text-blue-600">
                {language === 'mm' ? 'လမ်းညွှန်မှု' : 'Navigation'}
              </h3>
              <div className="space-y-2">
                {groupedShortcuts.navigation.map((shortcut, index) => (
                  <div key={index} className="flex justify-between items-center py-2 px-3 bg-gray-50 dark:bg-gray-700 rounded">
                    <span className="text-sm">
                      {language === 'mm' ? shortcut.descriptionLocal : shortcut.description}
                    </span>
                    <kbd className="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                      {formatShortcut(shortcut)}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>

            {/* POS Shortcuts */}
            <div>
              <h3 className="text-lg font-semibold mb-3 text-green-600">
                {language === 'mm' ? 'POS လုပ်ဆောင်ချက်များ' : 'POS Actions'}
              </h3>
              <div className="space-y-2">
                {groupedShortcuts.pos.map((shortcut, index) => (
                  <div key={index} className="flex justify-between items-center py-2 px-3 bg-gray-50 dark:bg-gray-700 rounded">
                    <span className="text-sm">
                      {language === 'mm' ? shortcut.descriptionLocal : shortcut.description}
                    </span>
                    <kbd className="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                      {formatShortcut(shortcut)}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>

            {/* General Shortcuts */}
            <div>
              <h3 className="text-lg font-semibold mb-3 text-purple-600">
                {language === 'mm' ? 'အထွေထွေ' : 'General'}
              </h3>
              <div className="space-y-2">
                {groupedShortcuts.general.map((shortcut, index) => (
                  <div key={index} className="flex justify-between items-center py-2 px-3 bg-gray-50 dark:bg-gray-700 rounded">
                    <span className="text-sm">
                      {language === 'mm' ? shortcut.descriptionLocal : shortcut.description}
                    </span>
                    <kbd className="px-2 py-1 bg-gray-200 dark:bg-gray-600 rounded text-xs font-mono">
                      {formatShortcut(shortcut)}
                    </kbd>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="mt-6 pt-4 border-t border-gray-200 dark:border-gray-600">
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {language === 'mm' 
                ? 'အကြံပြုချက်: ဖြတ်လမ်းများကို အသုံးပြု၍ လျင်မြန်စွာ လုပ်ဆောင်နိုင်ပါသည်။'
                : 'Tip: Use these shortcuts to navigate and perform actions quickly.'
              }
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}
