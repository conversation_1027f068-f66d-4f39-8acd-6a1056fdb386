'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import apiClient from '@/lib/api'
import { ReceiptModal } from '@/components/pos/receipt-modal'
import {
  ArrowLeft,
  Receipt,
  User,
  Calendar,
  CreditCard,
  Package,
  DollarSign,
  Eye,
  Printer,
  Mail,
  Download,
  RefreshCw,
  FileText
} from 'lucide-react'

interface Sale {
  _id: string
  saleNumber: string
  cashier: {
    _id: string
    firstName: string
    lastName: string
    username: string
    email: string
  }
  customer: {
    name?: string
    email?: string
    phone?: string
    address?: string
  }
  items: Array<{
    _id: string
    product: {
      _id: string
      name: string
      sku: string
      category: {
        _id: string
        name: string
        color: string
      }
    }
    productName: string
    sku: string
    quantity: number
    unitPrice: number
    totalPrice: number
    discount: number
    tax: number
  }>
  subtotal: number
  totalDiscount: number
  totalTax: number
  totalAmount: number
  currency: string
  paymentMethod: string
  paymentDetails: any
  amountPaid: number
  changeAmount: number
  status: string
  notes?: string
  receipt: {
    printed: boolean
    emailed: boolean
    number?: string
  }
  totalItems: number
  createdAt: string
  updatedAt: string
}

export default function SaleDetailPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const saleId = params.id as string

  const [sale, setSale] = useState<Sale | null>(null)
  const [loading, setLoading] = useState(true)
  const [showReceipt, setShowReceipt] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated && saleId) {
      fetchSale()
    }
  }, [isAuthenticated, saleId])

  const fetchSale = async () => {
    try {
      setLoading(true)
      const data = await apiClient.getSale(saleId)
      setSale(data)
    } catch (error) {
      console.error('Error fetching sale:', error)
      router.push('/sales')
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>
      case 'refunded':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Refunded</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPaymentMethodBadge = (method: string | undefined | null) => {
    if (!method) {
      return (
        <Badge variant="outline" className="bg-gray-100 text-gray-800">
          N/A
        </Badge>
      )
    }

    const colors = {
      cash: 'bg-green-100 text-green-800',
      card: 'bg-blue-100 text-blue-800',
      digital: 'bg-purple-100 text-purple-800',
      bank_transfer: 'bg-orange-100 text-orange-800',
      other: 'bg-gray-100 text-gray-800'
    }

    return (
      <Badge variant="outline" className={colors[method as keyof typeof colors] || colors.other}>
        {method.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const formatPrice = (price: number | undefined | null, currency: string) => {
    if (typeof price !== 'number' || isNaN(price) || price === null || price === undefined) {
      return `0 ${currency}`
    }
    return `${price.toLocaleString()} ${currency}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated || !sale) {
    return null
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const text = {
    en: {
      saleDetails: 'Sale Details',
      backToSales: 'Back to Sales',
      viewReceipt: 'View Receipt',
      printReceipt: 'Print Receipt',
      emailReceipt: 'Email Receipt',
      downloadPDF: 'Download PDF',
      refresh: 'Refresh',
      saleNumber: 'Sale Number',
      date: 'Date',
      cashier: 'Cashier',
      customer: 'Customer',
      status: 'Status',
      paymentMethod: 'Payment Method',
      items: 'Items',
      product: 'Product',
      quantity: 'Quantity',
      unitPrice: 'Unit Price',
      total: 'Total',
      subtotal: 'Subtotal',
      discount: 'Discount',
      tax: 'Tax',
      grandTotal: 'Grand Total',
      amountPaid: 'Amount Paid',
      change: 'Change',
      notes: 'Notes',
      receiptInfo: 'Receipt Information',
      printed: 'Printed',
      emailed: 'Emailed',
      yes: 'Yes',
      no: 'No'
    },
    mm: {
      saleDetails: 'ရောင်းအား အသေးစိတ်',
      backToSales: 'ရောင်းအားများသို့ ပြန်သွားရန်',
      viewReceipt: 'ဘောင်ချာ ကြည့်ရန်',
      printReceipt: 'ဘောင်ချာ ပရင့်ထုတ်ရန်',
      emailReceipt: 'ဘောင်ချာ အီးမေးလ်ပို့ရန်',
      downloadPDF: 'PDF ဒေါင်းလုဒ်',
      refresh: 'ပြန်လည်ရယူရန်',
      saleNumber: 'ရောင်းအား နံပါတ်',
      date: 'ရက်စွဲ',
      cashier: 'ငွေကောင်တာ',
      customer: 'ဝယ်သူ',
      status: 'အခြေအနေ',
      paymentMethod: 'ငွေချေမှု',
      items: 'ပစ္စည်းများ',
      product: 'ကုန်ပစ္စည်း',
      quantity: 'အရေအတွက်',
      unitPrice: 'တစ်ခုဈေး',
      total: 'စုစုပေါင်း',
      subtotal: 'ခွဲစုစုပေါင်း',
      discount: 'လျှော့စျေး',
      tax: 'အခွန်',
      grandTotal: 'စုစုပေါင်း',
      amountPaid: 'ပေးငွေ',
      change: 'ပြန်ငွေ',
      notes: 'မှတ်ချက်',
      receiptInfo: 'ဘောင်ချာ အချက်အလက်',
      printed: 'ပရင့်ထုတ်ပြီး',
      emailed: 'အီးမေးလ်ပို့ပြီး',
      yes: 'ဟုတ်',
      no: 'မဟုတ်'
    }
  }

  const t = text[language]

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.push('/sales')}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t.backToSales}
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {t.saleDetails}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                #{sale.saleNumber}
              </p>
            </div>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchSale}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {t.refresh}
            </Button>
            <Button variant="outline" onClick={() => router.push(`/invoice?saleId=${sale._id}`)}>
              <FileText className="h-4 w-4 mr-2" />
              {language === 'mm' ? 'ဖိုင်ဝယ်စ်' : 'Invoice'}
            </Button>
            <Button onClick={() => setShowReceipt(true)}>
              <Receipt className="h-4 w-4 mr-2" />
              {t.viewReceipt}
            </Button>
          </div>
        </div>

        {/* Sale Information */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Receipt className="h-5 w-5" />
                {t.saleDetails}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex justify-between">
                <span className="text-gray-600">{t.saleNumber}:</span>
                <span className="font-mono">{sale.saleNumber}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{t.date}:</span>
                <span>{formatDate(sale.createdAt)}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{t.status}:</span>
                {getStatusBadge(sale.status)}
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600">{t.paymentMethod}:</span>
                {getPaymentMethodBadge(sale.paymentMethod)}
              </div>
            </CardContent>
          </Card>

          {/* Cashier Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                {t.cashier}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="font-medium">
                {sale.cashier.firstName} {sale.cashier.lastName}
              </div>
              <div className="text-sm text-gray-600">
                @{sale.cashier.username}
              </div>
              <div className="text-sm text-gray-600">
                {sale.cashier.email}
              </div>
            </CardContent>
          </Card>

          {/* Customer Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                {t.customer}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {sale.customer.name ? (
                <div className="space-y-2">
                  <div className="font-medium">{sale.customer.name}</div>
                  {sale.customer.email && (
                    <div className="text-sm text-gray-600">{sale.customer.email}</div>
                  )}
                  {sale.customer.phone && (
                    <div className="text-sm text-gray-600">{sale.customer.phone}</div>
                  )}
                  {sale.customer.address && (
                    <div className="text-sm text-gray-600">{sale.customer.address}</div>
                  )}
                </div>
              ) : (
                <div className="text-gray-500">Walk-in Customer</div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Items */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5" />
              {t.items} ({sale.totalItems})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-2">{t.product}</th>
                    <th className="text-center py-2">{t.quantity}</th>
                    <th className="text-right py-2">{t.unitPrice}</th>
                    <th className="text-right py-2">{t.total}</th>
                  </tr>
                </thead>
                <tbody>
                  {sale.items.map((item) => (
                    <tr key={item._id} className="border-b">
                      <td className="py-3">
                        <div>
                          <div className="font-medium">{item.productName}</div>
                          <div className="text-sm text-gray-500">
                            {item.sku} • {item.product?.category?.name}
                          </div>
                        </div>
                      </td>
                      <td className="text-center py-3">{item.quantity}</td>
                      <td className="text-right py-3">
                        {formatPrice(item.unitPrice, sale.currency)}
                      </td>
                      <td className="text-right py-3 font-medium">
                        {formatPrice(item.totalPrice, sale.currency)}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Totals */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              Payment Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex justify-between">
                <span>{t.subtotal}:</span>
                <span>{formatPrice(sale.subtotal, sale.currency)}</span>
              </div>
              
              {sale.totalDiscount > 0 && (
                <div className="flex justify-between text-red-600">
                  <span>{t.discount}:</span>
                  <span>-{formatPrice(sale.totalDiscount, sale.currency)}</span>
                </div>
              )}
              
              {sale.totalTax > 0 && (
                <div className="flex justify-between">
                  <span>{t.tax}:</span>
                  <span>{formatPrice(sale.totalTax, sale.currency)}</span>
                </div>
              )}
              
              <div className="border-t pt-3">
                <div className="flex justify-between text-lg font-bold">
                  <span>{t.grandTotal}:</span>
                  <span>{formatPrice(sale.totalAmount, sale.currency)}</span>
                </div>
              </div>
              
              <div className="flex justify-between">
                <span>{t.amountPaid}:</span>
                <span>{formatPrice(sale.amountPaid, sale.currency)}</span>
              </div>
              
              {sale.changeAmount > 0 && (
                <div className="flex justify-between font-medium">
                  <span>{t.change}:</span>
                  <span>{formatPrice(sale.changeAmount, sale.currency)}</span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Notes and Receipt Info */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Notes */}
          {sale.notes && (
            <Card>
              <CardHeader>
                <CardTitle>{t.notes}</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-700 dark:text-gray-300">{sale.notes}</p>
              </CardContent>
            </Card>
          )}

          {/* Receipt Information */}
          <Card>
            <CardHeader>
              <CardTitle>{t.receiptInfo}</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div className="flex justify-between">
                <span>{t.printed}:</span>
                <span>{sale.receipt.printed ? t.yes : t.no}</span>
              </div>
              <div className="flex justify-between">
                <span>{t.emailed}:</span>
                <span>{sale.receipt.emailed ? t.yes : t.no}</span>
              </div>
              {sale.receipt.number && (
                <div className="flex justify-between">
                  <span>Receipt Number:</span>
                  <span className="font-mono">{sale.receipt.number}</span>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Receipt Modal */}
      {showReceipt && (
        <ReceiptModal
          isOpen={showReceipt}
          onClose={() => setShowReceipt(false)}
          saleData={sale}
          language={language}
        />
      )}
    </MainLayout>
  )
}
