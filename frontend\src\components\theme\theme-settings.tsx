'use client'

import { useState } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useTheme } from '@/contexts/theme-context'
import {
  Palette,
  Sun,
  Moon,
  Monitor,
  Check,
  Sparkles,
  Zap,
  Heart,
  Star,
  X
} from 'lucide-react'

interface ThemeSettingsProps {
  isOpen: boolean
  onClose: () => void
  language: 'en' | 'mm'
}

export function ThemeSettings({ isOpen, onClose, language }: ThemeSettingsProps) {
  const { theme, setTheme, colorScheme, setColorScheme } = useTheme()

  if (!isOpen) return null

  const themes = [
    {
      id: 'light' as const,
      name: language === 'mm' ? 'အလင်း' : 'Light',
      icon: Sun,
      description: language === 'mm' ? 'အလင်းရောင် အပြင်အဆင်' : 'Light appearance'
    },
    {
      id: 'dark' as const,
      name: language === 'mm' ? 'အမှောင်' : 'Dark',
      icon: Moon,
      description: language === 'mm' ? 'အမှောင်ရောင် အပြင်အဆင်' : 'Dark appearance'
    },
    {
      id: 'system' as const,
      name: language === 'mm' ? 'စနစ်' : 'System',
      icon: Monitor,
      description: language === 'mm' ? 'စနစ် ဆက်တင်အတိုင်း' : 'Follow system setting'
    }
  ]

  const colorSchemes = [
    {
      id: 'blue' as const,
      name: language === 'mm' ? 'အပြာ' : 'Blue',
      color: 'bg-blue-500',
      preview: 'from-blue-500 to-blue-600'
    },
    {
      id: 'green' as const,
      name: language === 'mm' ? 'အစိမ်း' : 'Green',
      color: 'bg-green-500',
      preview: 'from-green-500 to-green-600'
    },
    {
      id: 'purple' as const,
      name: language === 'mm' ? 'ခရမ်းရောင်' : 'Purple',
      color: 'bg-purple-500',
      preview: 'from-purple-500 to-purple-600'
    },
    {
      id: 'orange' as const,
      name: language === 'mm' ? 'လိမ္မော်ရောင်' : 'Orange',
      color: 'bg-orange-500',
      preview: 'from-orange-500 to-orange-600'
    },
    {
      id: 'pink' as const,
      name: language === 'mm' ? 'ပန်းရောင်' : 'Pink',
      color: 'bg-pink-500',
      preview: 'from-pink-500 to-pink-600'
    },
    {
      id: 'indigo' as const,
      name: language === 'mm' ? 'နက်ပြာ' : 'Indigo',
      color: 'bg-indigo-500',
      preview: 'from-indigo-500 to-indigo-600'
    }
  ]

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center gap-2">
                <Palette className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'အပြင်အဆင် ဆက်တင်များ' : 'Theme Settings'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'သင့်နှစ်သက်သော အရောင်နှင့် အပြင်အဆင် ရွေးချယ်ပါ'
                  : 'Customize your preferred colors and appearance'
                }
              </CardDescription>
            </div>
            <Button variant="ghost" size="sm" onClick={onClose}>
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>

        <CardContent className="space-y-6">
          {/* Theme Mode Selection */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Sun className="h-5 w-5 text-yellow-500" />
              {language === 'mm' ? 'အပြင်အဆင် မုဒ်' : 'Appearance Mode'}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              {themes.map((themeOption) => {
                const Icon = themeOption.icon
                const isSelected = theme === themeOption.id

                return (
                  <Button
                    key={themeOption.id}
                    variant={isSelected ? 'default' : 'outline'}
                    onClick={() => setTheme(themeOption.id)}
                    className="h-auto p-4 flex flex-col items-center gap-2"
                  >
                    <Icon className="h-6 w-6" />
                    <div className="text-center">
                      <p className="font-medium">{themeOption.name}</p>
                      <p className="text-xs opacity-70">{themeOption.description}</p>
                    </div>
                    {isSelected && (
                      <Check className="h-4 w-4 text-green-600" />
                    )}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* Color Scheme Selection */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-pink-500" />
              {language === 'mm' ? 'အရောင် အစီအစဉ်' : 'Color Scheme'}
            </h3>

            <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
              {colorSchemes.map((scheme) => {
                const isSelected = colorScheme === scheme.id

                return (
                  <Button
                    key={scheme.id}
                    variant="outline"
                    onClick={() => setColorScheme(scheme.id)}
                    className={`h-auto p-4 flex flex-col items-center gap-3 relative ${
                      isSelected ? 'ring-2 ring-offset-2 ring-current' : ''
                    }`}
                  >
                    <div className={`w-12 h-12 rounded-full bg-gradient-to-br ${scheme.preview} shadow-lg`} />
                    <span className="font-medium">{scheme.name}</span>

                    {isSelected && (
                      <div className="absolute top-2 right-2">
                        <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <Check className="h-3 w-3 text-white" />
                        </div>
                      </div>
                    )}
                  </Button>
                )
              })}
            </div>
          </div>

          {/* Preview Section */}
          <div className="space-y-3">
            <h3 className="text-lg font-semibold flex items-center gap-2">
              <Zap className="h-5 w-5 text-blue-500" />
              {language === 'mm' ? 'အစမ်းကြည့်ရှုမှု' : 'Preview'}
            </h3>

            <Card className="p-4">
              <div className="space-y-4">
                {/* Sample Header */}
                <div className={`p-4 rounded-lg bg-gradient-to-r ${colorSchemes.find(s => s.id === colorScheme)?.preview} text-white`}>
                  <h4 className="font-bold text-lg">
                    {language === 'mm' ? 'BitsTech POS စနစ်' : 'BitsTech POS System'}
                  </h4>
                  <p className="opacity-90">
                    {language === 'mm' ? 'ခေတ်မီ ရောင်းချမှု စီမံခန့်ခွဲမှု' : 'Modern Sales Management'}
                  </p>
                </div>

                {/* Sample Cards */}
                <div className="grid grid-cols-2 gap-3">
                  <Card className="p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Star className="h-4 w-4 text-yellow-500" />
                      <span className="font-medium">
                        {language === 'mm' ? 'ရောင်းအား' : 'Sales'}
                      </span>
                    </div>
                    <p className="text-2xl font-bold">1,250,000</p>
                    <p className="text-sm text-gray-600">MMK</p>
                  </Card>

                  <Card className="p-3">
                    <div className="flex items-center gap-2 mb-2">
                      <Heart className="h-4 w-4 text-red-500" />
                      <span className="font-medium">
                        {language === 'mm' ? 'ဖောက်သည်များ' : 'Customers'}
                      </span>
                    </div>
                    <p className="text-2xl font-bold">234</p>
                    <p className="text-sm text-gray-600">
                      {language === 'mm' ? 'လက်ရှိ' : 'Active'}
                    </p>
                  </Card>
                </div>

                {/* Sample Buttons */}
                <div className="flex gap-2">
                  <Button size="sm">
                    {language === 'mm' ? 'အဓိက ခလုတ်' : 'Primary Button'}
                  </Button>
                  <Button variant="outline" size="sm">
                    {language === 'mm' ? 'ဒုတိယ ခလုတ်' : 'Secondary Button'}
                  </Button>
                </div>
              </div>
            </Card>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-3 pt-4 border-t border-gray-200">
            <Button variant="outline" onClick={onClose}>
              {language === 'mm' ? 'မလုပ်တော့' : 'Cancel'}
            </Button>
            <Button onClick={onClose}>
              {language === 'mm' ? 'သိမ်းရန်' : 'Save Changes'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
