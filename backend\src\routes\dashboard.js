const express = require('express');
const router = express.Router();
const Product = require('../models/Product');
const Sale = require('../models/Sale');
const Category = require('../models/Category');
const { protect } = require('../middleware/auth');

// Get dashboard statistics
router.get('/stats', protect, async (req, res) => {
  try {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    // Get today's sales from MongoDB
    const todaySales = await Sale.find({
      createdAt: { $gte: today, $lt: tomorrow },
      status: 'completed'
    });

    const todayStats = todaySales.reduce((acc, sale) => {
      acc.totalAmount += sale.totalAmount || 0;
      acc.totalTransactions += 1;
      acc.totalItems += sale.items ? sale.items.reduce((sum, item) => sum + (item.quantity || 0), 0) : 0;
      return acc;
    }, { totalAmount: 0, totalTransactions: 0, totalItems: 0 });

    // Get total products count from MongoDB
    const totalProducts = await Product.countDocuments({ isActive: true });

    // Get low stock items (quantity <= 10) from MongoDB
    const lowStockItems = await Product.countDocuments({
      isActive: true,
      'inventory.quantity': { $lte: 10 }
    });

    // Get recent sales (last 5) from MongoDB
    const recentSales = await Sale.find({ status: 'completed' })
      .sort({ createdAt: -1 })
      .limit(5)
      .select('saleNumber totalAmount currency paymentMethod createdAt customer')
      .lean();

    // Get top selling products (this month) from MongoDB
    const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);
    const topProducts = await Sale.aggregate([
      {
        $match: {
          createdAt: { $gte: startOfMonth },
          status: 'completed'
        }
      },
      { $unwind: '$items' },
      {
        $group: {
          _id: '$items.product',
          totalQuantity: { $sum: '$items.quantity' },
          totalRevenue: { $sum: { $multiply: ['$items.quantity', '$items.price'] } },
          name: { $first: '$items.productName' },
          sku: { $first: '$items.productSku' }
        }
      },
      { $sort: { totalQuantity: -1 } },
      { $limit: 5 }
    ]);

    // Get sales trend (last 7 days) from MongoDB
    const sevenDaysAgo = new Date(today);
    sevenDaysAgo.setDate(sevenDaysAgo.getDate() - 6);

    const salesTrend = await Sale.aggregate([
      {
        $match: {
          createdAt: { $gte: sevenDaysAgo },
          status: 'completed'
        }
      },
      {
        $group: {
          _id: {
            $dateToString: { format: '%Y-%m-%d', date: '$createdAt' }
          },
          amount: { $sum: '$totalAmount' },
          transactions: { $sum: 1 }
        }
      },
      { $sort: { '_id': 1 } }
    ]);

    // Fill missing dates with zero values
    const salesTrendComplete = [];
    for (let i = 6; i >= 0; i--) {
      const date = new Date(today);
      date.setDate(date.getDate() - i);
      const dateStr = date.toISOString().split('T')[0];

      const dayData = salesTrend.find(item => item._id === dateStr);
      salesTrendComplete.push({
        date: dateStr,
        amount: dayData ? dayData.amount : 0,
        transactions: dayData ? dayData.transactions : 0
      });
    }

    const dashboardStats = {
      todaySales: {
        amount: todayStats.totalAmount,
        transactions: todayStats.totalTransactions,
        items: todayStats.totalItems
      },
      totalProducts: {
        count: totalProducts
      },
      lowStockItems: {
        count: lowStockItems
      },
      recentSales,
      topProducts,
      salesTrend: salesTrendComplete
    };

    res.json({
      success: true,
      source: 'MongoDB',
      data: dashboardStats
    });

  } catch (error) {
    console.error('Dashboard stats error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch dashboard statistics'
    });
  }
});

// Get inventory alerts
router.get('/inventory-alerts', protect, async (req, res) => {
  try {
    // Get low stock products (quantity <= 10) from MongoDB
    const lowStockProducts = await Product.find({
      isActive: true,
      'inventory.quantity': { $lte: 10, $gt: 0 }
    })
    .populate('category', 'name color')
    .sort({ 'inventory.quantity': 1 })
    .lean();

    // Get out of stock products from MongoDB
    const outOfStockProducts = await Product.find({
      isActive: true,
      'inventory.quantity': { $lte: 0 }
    })
    .populate('category', 'name color')
    .lean();

    const alerts = {
      lowStock: lowStockProducts.map(product => ({
        _id: product._id,
        name: product.name,
        sku: product.sku,
        quantity: product.inventory.quantity,
        unit: product.inventory.unit || 'pcs',
        category: product.category,
        type: 'low_stock',
        severity: product.inventory.quantity <= 5 ? 'high' : 'medium'
      })),
      outOfStock: outOfStockProducts.map(product => ({
        _id: product._id,
        name: product.name,
        sku: product.sku,
        category: product.category,
        type: 'out_of_stock',
        severity: 'critical'
      }))
    };

    res.json({
      success: true,
      source: 'MongoDB',
      data: alerts
    });

  } catch (error) {
    console.error('Inventory alerts error:', error);
    res.status(500).json({
      success: false,
      error: 'Failed to fetch inventory alerts'
    });
  }
});

module.exports = router;
