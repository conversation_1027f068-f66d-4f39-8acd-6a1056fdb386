const mongoose = require('mongoose');
const { seedUsers } = require('./seedUsers');
const { seedSettings } = require('./seedSettings');
const { seedCategories, seedProducts } = require('./seed');
const { seedCustomers } = require('./seedCustomers');
const { seedSales } = require('./seedSales');
const { seedInventory } = require('./seedInventory');
require('dotenv').config();

const connectDB = async () => {
    try {
        const mongoURI = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos';
        await mongoose.connect(mongoURI);
        console.log('✅ MongoDB connected for comprehensive seeding');
    } catch (error) {
        console.error('❌ MongoDB connection failed:', error.message);
        process.exit(1);
    }
};

const seedAll = async () => {
    try {
        console.log('🌱 Starting comprehensive database seeding...');
        console.log('=' .repeat(60));

        await connectDB();

        // Step 1: Seed Settings
        console.log('\n📋 Step 1: Seeding Settings...');
        await seedSettings();
        console.log('✅ Settings seeded successfully');

        // Step 2: Seed Users
        console.log('\n👥 Step 2: Seeding Users...');
        await seedUsers();
        console.log('✅ Users seeded successfully');

        // Step 3: Seed Categories
        console.log('\n📂 Step 3: Seeding Categories...');
        await seedCategories();
        console.log('✅ Categories seeded successfully');

        // Step 4: Seed Products
        console.log('\n📦 Step 4: Seeding Products...');
        await seedProducts();
        console.log('✅ Products seeded successfully');

        // Step 5: Seed Customers
        console.log('\n👥 Step 5: Seeding Customers...');
        await seedCustomers();
        console.log('✅ Customers seeded successfully');

        // Step 6: Seed Inventory Transactions
        console.log('\n📋 Step 6: Seeding Inventory Transactions...');
        await seedInventory();
        console.log('✅ Inventory transactions seeded successfully');

        // Step 7: Seed Sales Data
        console.log('\n💰 Step 7: Seeding Sales Data...');
        await seedSales();
        console.log('✅ Sales data seeded successfully');

        console.log('\n' + '=' .repeat(60));
        console.log('🎉 COMPREHENSIVE SEEDING COMPLETED SUCCESSFULLY!');
        console.log('=' .repeat(60));

        // Display summary
        const User = require('../models/User');
        const Category = require('../models/Category');
        const Product = require('../models/Product');
        const Settings = require('../models/Settings');
        const Customer = require('../models/Customer');
        const Sale = require('../models/Sale');
        const StockMovement = require('../models/StockMovement');

        const userCount = await User.countDocuments();
        const categoryCount = await Category.countDocuments();
        const productCount = await Product.countDocuments();
        const settingsCount = await Settings.countDocuments();
        const customerCount = await Customer.countDocuments();
        const saleCount = await Sale.countDocuments();
        const inventoryTransactionCount = await StockMovement.countDocuments();

        // Calculate total revenue
        const totalRevenue = await Sale.aggregate([
            { $group: { _id: null, total: { $sum: '$total' } } }
        ]);

        console.log('\n📊 COMPREHENSIVE SEEDING SUMMARY:');
        console.log(`⚙️  Settings: ${settingsCount} configuration(s)`);
        console.log(`👥 Users: ${userCount} user(s)`);
        console.log(`📂 Categories: ${categoryCount} category(ies)`);
        console.log(`📦 Products: ${productCount} product(s)`);
        console.log(`🛒 Customers: ${customerCount} customer(s)`);
        console.log(`💰 Sales: ${saleCount} transaction(s)`);
        console.log(`📋 Inventory Transactions: ${inventoryTransactionCount} record(s)`);
        console.log(`💵 Total Revenue: ${totalRevenue[0]?.total || 0} MMK`);

        console.log('\n🔐 DEFAULT LOGIN CREDENTIALS:');
        console.log('┌─────────────────────────────────────────────┐');
        console.log('│ Role        │ Email                │ Password │');
        console.log('├─────────────────────────────────────────────┤');
        console.log('│ Admin       │ <EMAIL>   │ admin123 │');
        console.log('│ Manager     │ <EMAIL> │ manager123│');
        console.log('│ Cashier     │ <EMAIL> │ cashier123│');
        console.log('│ Supervisor  │ <EMAIL>│supervisor123│');
        console.log('└─────────────────────────────────────────────┘');

        console.log('\n🌐 SYSTEM CONFIGURATION:');
        console.log('• Store Name: BitsTech POS');
        console.log('• Primary Currency: MMK (Myanmar Kyat)');
        console.log('• Default Language: English');
        console.log('• Default Theme: Light');
        console.log('• Tax Rate: 0%');
        console.log('• Low Stock Threshold: 10 items');

        console.log('\n💱 EXCHANGE RATES:');
        console.log('• 1 USD = 2,100 MMK');
        console.log('• 1 THB = 60 MMK');
        console.log('• 1 MMK = 1 MMK (base)');

        console.log('\n🚀 NEXT STEPS:');
        console.log('1. Start the backend server: npm run dev');
        console.log('2. Start the frontend server: npm run dev (in frontend folder)');
        console.log('3. Access the application at: http://localhost:3000');
        console.log('4. Login with any of the credentials above');

        console.log('\n✨ Happy coding with BitsTech POS! ✨');

    } catch (error) {
        console.error('❌ Comprehensive seeding failed:', error);
        throw error;
    }
};

const runSeed = async () => {
    try {
        await seedAll();
        console.log('\n🎯 All seeding operations completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('❌ Seeding process failed:', error);
        process.exit(1);
    }
};

// Helper function to seed specific components
const seedSpecific = async (components = []) => {
    try {
        await connectDB();
        
        console.log(`🎯 Seeding specific components: ${components.join(', ')}`);
        
        for (const component of components) {
            switch (component.toLowerCase()) {
                case 'settings':
                    console.log('📋 Seeding Settings...');
                    await seedSettings();
                    break;
                case 'users':
                    console.log('👥 Seeding Users...');
                    await seedUsers();
                    break;
                case 'categories':
                    console.log('📂 Seeding Categories...');
                    await seedCategories();
                    break;
                case 'products':
                    console.log('📦 Seeding Products...');
                    await seedProducts();
                    break;
                case 'customers':
                    console.log('🛒 Seeding Customers...');
                    await seedCustomers();
                    break;
                case 'inventory':
                    console.log('📋 Seeding Inventory...');
                    await seedInventory();
                    break;
                case 'sales':
                    console.log('💰 Seeding Sales...');
                    await seedSales();
                    break;
                default:
                    console.log(`⚠️  Unknown component: ${component}`);
            }
        }
        
        console.log('✅ Specific seeding completed!');
        process.exit(0);
    } catch (error) {
        console.error('❌ Specific seeding failed:', error);
        process.exit(1);
    }
};

// Command line interface
const args = process.argv.slice(2);
if (args.length > 0) {
    if (args[0] === '--help' || args[0] === '-h') {
        console.log('🌱 BitsTech POS Database Seeding Tool');
        console.log('\nUsage:');
        console.log('  node seedAll.js                    # Seed everything');
        console.log('  node seedAll.js settings           # Seed only settings');
        console.log('  node seedAll.js users              # Seed only users');
        console.log('  node seedAll.js categories         # Seed only categories');
        console.log('  node seedAll.js products           # Seed only products');
        console.log('  node seedAll.js users settings     # Seed multiple components');
        console.log('\nAvailable components: settings, users, categories, products, customers, inventory, sales');
        process.exit(0);
    } else {
        seedSpecific(args);
    }
} else if (require.main === module) {
    runSeed();
}

module.exports = {
    seedAll,
    seedSpecific
};
