'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useSettings, useTranslation } from '@/contexts/settings-context'
import {
  Building2,
  ArrowLeft,
  Upload,
  Save,
  MapPin,
  Phone,
  Mail,
  Globe,
  Camera,
  Check,
  AlertCircle,
  Sparkles
} from 'lucide-react'

interface CompanyInfo {
  name: string
  nameLocal: string
  description: string
  logo: string
  address: string
  city: string
  state: string
  zipCode: string
  country: string
  phone: string
  email: string
  website: string
  taxId: string
  registrationNumber: string
}

export default function CompanySettingsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const { t } = useTranslation()
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [errors, setErrors] = useState<{[key: string]: string}>({})

  const [companyInfo, setCompanyInfo] = useState<CompanyInfo>({
    name: 'BitsTech Computer Store',
    nameLocal: 'ဘစ်တက် ကွန်ပျူတာ စတိုး',
    description: 'Your trusted partner for computer hardware and accessories',
    logo: '',
    address: '123 Technology Street',
    city: 'Yangon',
    state: 'Yangon Region',
    zipCode: '11181',
    country: 'Myanmar',
    phone: '+95 9 ***********',
    email: '<EMAIL>',
    website: 'https://bitstech.com',
    taxId: 'TAX-*********',
    registrationNumber: 'REG-*********'
  })

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  const handleInputChange = (field: keyof CompanyInfo, value: string) => {
    setCompanyInfo(prev => ({
      ...prev,
      [field]: value
    }))

    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {}

    if (!companyInfo.name.trim()) {
      newErrors.name = 'Company name is required'
    }

    if (!companyInfo.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(companyInfo.email)) {
      newErrors.email = 'Invalid email format'
    }

    if (!companyInfo.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSave = async () => {
    if (!validateForm()) return

    setSaving(true)
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))

      setSaved(true)
      setTimeout(() => setSaved(false), 3000)
    } catch (error) {
      console.error('Error saving company info:', error)
    } finally {
      setSaving(false)
    }
  }

  const handleLogoUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setCompanyInfo(prev => ({
          ...prev,
          logo: e.target?.result as string
        }))
      }
      reader.readAsDataURL(file)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-blue-50 dark:hover:bg-blue-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header with Gradient */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-blue-600 to-purple-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Building2 className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {language === 'mm' ? 'ကုမ္ပဏီ အချက်အလက်များ' : 'Company Information'}
                </h1>
                <p className="text-white/80 mt-1">
                  {language === 'mm'
                    ? 'သင့်လုပ်ငန်း အသေးစိတ်များ နှင့် ဆက်သွယ်ရေး အချက်အလက်များ ပြင်ဆင်ပါ'
                    : 'Manage your business details and contact information'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Logo Upload Section */}
          <Card className="lg:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Camera className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'ကုမ္ပဏီ လိုဂို' : 'Company Logo'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'သင့်လုပ်ငန်း လိုဂို ထည့်သွင်းပါ (PNG, JPG သို့မဟုတ် SVG)'
                  : 'Upload your business logo (PNG, JPG, or SVG)'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col items-center space-y-4">
                <div className="w-32 h-32 border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-xl flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20">
                  {companyInfo.logo ? (
                    <img
                      src={companyInfo.logo}
                      alt="Company Logo"
                      className="w-full h-full object-contain rounded-xl"
                    />
                  ) : (
                    <div className="text-center">
                      <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-2" />
                      <p className="text-sm text-gray-500">
                        {language === 'mm' ? 'လိုဂို မရှိ' : 'No Logo'}
                      </p>
                    </div>
                  )}
                </div>

                <div className="w-full">
                  <Label htmlFor="logo-upload" className="sr-only">
                    {language === 'mm' ? 'လိုဂို ထည့်ရန်' : 'Upload Logo'}
                  </Label>
                  <input
                    type="file"
                    id="logo-upload"
                    accept="image/*"
                    onChange={handleLogoUpload}
                    className="hidden"
                    aria-label={language === 'mm' ? 'လိုဂို ထည့်ရန်' : 'Upload Logo'}
                  />
                  <Button
                    variant="outline"
                    className="w-full"
                    onClick={() => document.getElementById('logo-upload')?.click()}
                  >
                    <Upload className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'လိုဂို ထည့်ရန်' : 'Upload Logo'}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Company Details Form */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Sparkles className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'လုပ်ငန်း အသေးစိတ်များ' : 'Business Details'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Company Name */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">
                    {language === 'mm' ? 'ကုမ္ပဏီ အမည်' : 'Company Name'} *
                  </Label>
                  <Input
                    id="name"
                    value={companyInfo.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    className={errors.name ? 'border-red-500' : ''}
                    placeholder="Enter company name"
                  />
                  {errors.name && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.name}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="nameLocal">
                    {language === 'mm' ? 'ဒေသဘာသာ အမည်' : 'Local Name'}
                  </Label>
                  <Input
                    id="nameLocal"
                    value={companyInfo.nameLocal}
                    onChange={(e) => handleInputChange('nameLocal', e.target.value)}
                    placeholder="Enter local name"
                  />
                </div>
              </div>

              {/* Description */}
              <div className="space-y-2">
                <Label htmlFor="description">
                  {language === 'mm' ? 'ဖော်ပြချက်' : 'Description'}
                </Label>
                <Input
                  id="description"
                  value={companyInfo.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder="Brief description of your business"
                />
              </div>

              {/* Contact Information */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="phone" className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-green-600" />
                    {language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone Number'} *
                  </Label>
                  <Input
                    id="phone"
                    value={companyInfo.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    className={errors.phone ? 'border-red-500' : ''}
                    placeholder="+95 9 ***********"
                  />
                  {errors.phone && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.phone}
                    </p>
                  )}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email" className="flex items-center gap-2">
                    <Mail className="h-4 w-4 text-blue-600" />
                    {language === 'mm' ? 'အီးမေးလ်' : 'Email'} *
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={companyInfo.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className={errors.email ? 'border-red-500' : ''}
                    placeholder="<EMAIL>"
                  />
                  {errors.email && (
                    <p className="text-sm text-red-600 flex items-center gap-1">
                      <AlertCircle className="h-3 w-3" />
                      {errors.email}
                    </p>
                  )}
                </div>
              </div>

              {/* Website */}
              <div className="space-y-2">
                <Label htmlFor="website" className="flex items-center gap-2">
                  <Globe className="h-4 w-4 text-purple-600" />
                  {language === 'mm' ? 'ဝက်ဘ်ဆိုဒ်' : 'Website'}
                </Label>
                <Input
                  id="website"
                  value={companyInfo.website}
                  onChange={(e) => handleInputChange('website', e.target.value)}
                  placeholder="https://company.com"
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
              </div>
            ) : saved ? (
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းပြီး' : 'Saved'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းရန်' : 'Save Changes'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
