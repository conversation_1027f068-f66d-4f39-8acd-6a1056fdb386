'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  Download, 
  X, 
  Smartphone, 
  Monitor, 
  Zap,
  Wifi,
  Bell
} from 'lucide-react'
import { usePWA } from '@/hooks/usePWA'
import { useTheme } from '@/contexts/theme-context'

export function PWAInstallPrompt() {
  const { isInstallable, isInstalled, installApp } = usePWA()
  const { language } = useTheme()
  const [showPrompt, setShowPrompt] = useState(false)
  const [dismissed, setDismissed] = useState(false)

  useEffect(() => {
    // Show prompt after 30 seconds if installable and not dismissed
    const timer = setTimeout(() => {
      if (isInstallable && !isInstalled && !dismissed) {
        setShowPrompt(true)
      }
    }, 30000)

    return () => clearTimeout(timer)
  }, [isInstallable, isInstalled, dismissed])

  useEffect(() => {
    // Check if user previously dismissed the prompt
    const wasDismissed = localStorage.getItem('pwa-install-dismissed')
    if (wasDismissed) {
      setDismissed(true)
    }
  }, [])

  const handleInstall = async () => {
    const success = await installApp()
    if (success) {
      setShowPrompt(false)
    }
  }

  const handleDismiss = () => {
    setShowPrompt(false)
    setDismissed(true)
    localStorage.setItem('pwa-install-dismissed', 'true')
  }

  const translations = {
    en: {
      title: 'Install BitsTech POS',
      subtitle: 'Get the full app experience',
      description: 'Install our app for faster access, offline capabilities, and a native app experience.',
      install: 'Install App',
      dismiss: 'Maybe Later',
      features: {
        offline: 'Work offline',
        fast: 'Lightning fast',
        native: 'Native experience',
        notifications: 'Push notifications'
      }
    },
    mm: {
      title: 'BitsTech POS ကို Install လုပ်ပါ',
      subtitle: 'အပြည့်အဝ app အတွေ့အကြုံ ရယူပါ',
      description: 'ပိုမြန်သော ဝင်ရောက်မှု၊ အင်တာနက်မရှိချိန် အသုံးပြုနိုင်မှု နှင့် native app အတွေ့အကြုံအတွက် ကျွန်ုပ်တို့၏ app ကို install လုပ်ပါ။',
      install: 'App Install လုပ်ရန်',
      dismiss: 'နောက်မှ လုပ်မည်',
      features: {
        offline: 'အင်တာနက်မရှိချိန် အလုပ်လုပ်သည်',
        fast: 'လျင်မြန်သော အမြန်နှုန်း',
        native: 'Native အတွေ့အကြုံ',
        notifications: 'Push notifications'
      }
    }
  }

  const t = translations[language] || translations.en

  if (!isInstallable || isInstalled || !showPrompt) {
    return null
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 max-w-sm">
      <Card className="shadow-2xl border-2 border-primary/20 bg-card/95 backdrop-blur-sm">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-primary/10 rounded-lg">
                <Download className="h-5 w-5 text-primary" />
              </div>
              <div>
                <CardTitle className={`text-lg ${language === 'mm' ? 'font-myanmar' : ''}`}>
                  {t.title}
                </CardTitle>
                <CardDescription className={language === 'mm' ? 'font-myanmar leading-relaxed' : ''}>
                  {t.subtitle}
                </CardDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleDismiss}
              className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          <p className={`text-sm text-muted-foreground ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
            {t.description}
          </p>

          {/* Features */}
          <div className="grid grid-cols-2 gap-2">
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Wifi className="h-3 w-3 text-green-600" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.features.offline}</span>
            </div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Zap className="h-3 w-3 text-yellow-600" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.features.fast}</span>
            </div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Smartphone className="h-3 w-3 text-blue-600" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.features.native}</span>
            </div>
            <div className="flex items-center space-x-2 text-xs text-muted-foreground">
              <Bell className="h-3 w-3 text-purple-600" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.features.notifications}</span>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-2">
            <Button
              onClick={handleInstall}
              className="flex-1 bg-primary text-primary-foreground hover:bg-primary/90"
              size="sm"
            >
              <Download className="h-4 w-4 mr-2" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.install}</span>
            </Button>
            <Button
              onClick={handleDismiss}
              variant="outline"
              size="sm"
              className="flex-1"
            >
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.dismiss}</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Install button for header/navbar
export function PWAInstallButton() {
  const { isInstallable, isInstalled, installApp } = usePWA()
  const { language } = useTheme()

  if (!isInstallable || isInstalled) {
    return null
  }

  const handleInstall = async () => {
    await installApp()
  }

  return (
    <Button
      onClick={handleInstall}
      variant="outline"
      size="sm"
      className="hidden sm:flex items-center space-x-2"
    >
      <Download className="h-4 w-4" />
      <span className={language === 'mm' ? 'font-myanmar' : ''}>
        {language === 'mm' ? 'Install' : 'Install App'}
      </span>
    </Button>
  )
}

// Update available notification
export function PWAUpdateNotification() {
  const { updateAvailable, updateApp } = usePWA()
  const { language } = useTheme()
  const [showUpdate, setShowUpdate] = useState(false)

  useEffect(() => {
    if (updateAvailable) {
      setShowUpdate(true)
    }
  }, [updateAvailable])

  const handleUpdate = async () => {
    await updateApp()
    setShowUpdate(false)
  }

  const translations = {
    en: {
      title: 'Update Available',
      description: 'A new version of BitsTech POS is available.',
      update: 'Update Now',
      dismiss: 'Later'
    },
    mm: {
      title: 'အပ်ဒိတ် ရရှိနိုင်ပါသည်',
      description: 'BitsTech POS ၏ ဗားရှင်းအသစ် ရရှိနိုင်ပါသည်။',
      update: 'ယခုပင် အပ်ဒိတ်လုပ်ရန်',
      dismiss: 'နောက်မှ'
    }
  }

  const t = translations[language] || translations.en

  if (!showUpdate) {
    return null
  }

  return (
    <div className="fixed top-4 right-4 z-50 max-w-sm">
      <Card className="shadow-lg border border-orange-200 bg-orange-50 dark:bg-orange-950 dark:border-orange-800">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center space-x-2">
              <div className="p-2 bg-orange-100 dark:bg-orange-900 rounded-lg">
                <Zap className="h-5 w-5 text-orange-600" />
              </div>
              <div>
                <CardTitle className={`text-lg text-orange-900 dark:text-orange-100 ${language === 'mm' ? 'font-myanmar' : ''}`}>
                  {t.title}
                </CardTitle>
                <CardDescription className={`text-orange-700 dark:text-orange-300 ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
                  {t.description}
                </CardDescription>
              </div>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowUpdate(false)}
              className="h-8 w-8 p-0 text-orange-600 hover:text-orange-700"
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        </CardHeader>
        
        <CardContent>
          <div className="flex space-x-2">
            <Button
              onClick={handleUpdate}
              className="flex-1 bg-orange-600 text-white hover:bg-orange-700"
              size="sm"
            >
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.update}</span>
            </Button>
            <Button
              onClick={() => setShowUpdate(false)}
              variant="outline"
              size="sm"
              className="flex-1 border-orange-300 text-orange-700 hover:bg-orange-100"
            >
              <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.dismiss}</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
