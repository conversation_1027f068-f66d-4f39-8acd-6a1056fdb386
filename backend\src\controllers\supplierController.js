const asyncHandler = require('../utils/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');
const Supplier = require('../models/Supplier');
const Category = require('../models/Category');

// @desc    Get all suppliers
// @route   GET /api/suppliers
// @access  Private
const getSuppliers = asyncHandler(async (req, res, next) => {
    const {
        search,
        status,
        category,
        paymentTerms,
        rating,
        page = 1,
        limit = 10,
        sort = '-createdAt'
    } = req.query;

    // Build query
    let query = {};

    // Search filter
    if (search) {
        query.$or = [
            { name: { $regex: search, $options: 'i' } },
            { code: { $regex: search, $options: 'i' } },
            { 'contactPerson.name': { $regex: search, $options: 'i' } },
            { 'contactPerson.email': { $regex: search, $options: 'i' } }
        ];
    }

    // Status filter
    if (status === 'active') {
        query.isActive = true;
    } else if (status === 'inactive') {
        query.isActive = false;
    }

    // Category filter
    if (category) {
        query.categories = category;
    }

    // Payment terms filter
    if (paymentTerms) {
        query.paymentTerms = paymentTerms;
    }

    // Rating filter
    if (rating) {
        query.rating = { $gte: parseInt(rating) };
    }

    // Execute query with pagination
    const suppliers = await Supplier.find(query)
        .populate('categories', 'name color')
        .populate('createdBy', 'firstName lastName')
        .sort(sort)
        .limit(limit * 1)
        .skip((page - 1) * limit);

    // Get total count for pagination
    const total = await Supplier.countDocuments(query);

    res.status(200).json({
        success: true,
        count: suppliers.length,
        total,
        pagination: {
            page: parseInt(page),
            limit: parseInt(limit),
            pages: Math.ceil(total / limit)
        },
        data: suppliers
    });
});

// @desc    Get single supplier
// @route   GET /api/suppliers/:id
// @access  Private
const getSupplier = asyncHandler(async (req, res, next) => {
    const supplier = await Supplier.findById(req.params.id)
        .populate('categories', 'name color')
        .populate('products.product', 'name sku price')
        .populate('createdBy', 'firstName lastName')
        .populate('updatedBy', 'firstName lastName');

    if (!supplier) {
        return next(new ErrorResponse('Supplier not found', 404));
    }

    res.status(200).json({
        success: true,
        data: supplier
    });
});

// @desc    Create new supplier
// @route   POST /api/suppliers
// @access  Private
const createSupplier = asyncHandler(async (req, res, next) => {
    // Add user to req.body
    req.body.createdBy = req.user.id;

    const supplier = await Supplier.create(req.body);

    // Populate the created supplier
    await supplier.populate('categories', 'name color');
    await supplier.populate('createdBy', 'firstName lastName');

    res.status(201).json({
        success: true,
        data: supplier
    });
});

// @desc    Update supplier
// @route   PUT /api/suppliers/:id
// @access  Private
const updateSupplier = asyncHandler(async (req, res, next) => {
    let supplier = await Supplier.findById(req.params.id);

    if (!supplier) {
        return next(new ErrorResponse('Supplier not found', 404));
    }

    // Add user to req.body
    req.body.updatedBy = req.user.id;

    supplier = await Supplier.findByIdAndUpdate(req.params.id, req.body, {
        new: true,
        runValidators: true
    }).populate('categories', 'name color')
      .populate('createdBy', 'firstName lastName')
      .populate('updatedBy', 'firstName lastName');

    res.status(200).json({
        success: true,
        data: supplier
    });
});

// @desc    Delete supplier
// @route   DELETE /api/suppliers/:id
// @access  Private
const deleteSupplier = asyncHandler(async (req, res, next) => {
    const supplier = await Supplier.findById(req.params.id);

    if (!supplier) {
        return next(new ErrorResponse('Supplier not found', 404));
    }

    // Soft delete - just mark as inactive
    supplier.isActive = false;
    supplier.updatedBy = req.user.id;
    await supplier.save();

    res.status(200).json({
        success: true,
        data: {}
    });
});

// @desc    Get supplier statistics
// @route   GET /api/suppliers/stats
// @access  Private
const getSupplierStats = asyncHandler(async (req, res, next) => {
    const stats = await Supplier.aggregate([
        {
            $group: {
                _id: null,
                totalSuppliers: { $sum: 1 },
                activeSuppliers: {
                    $sum: { $cond: [{ $eq: ['$isActive', true] }, 1, 0] }
                },
                inactiveSuppliers: {
                    $sum: { $cond: [{ $eq: ['$isActive', false] }, 1, 0] }
                },
                totalOrderValue: { $sum: '$totalOrderValue' },
                averageRating: { $avg: '$rating' },
                totalOrders: { $sum: '$totalOrders' }
            }
        }
    ]);

    const result = stats[0] || {
        totalSuppliers: 0,
        activeSuppliers: 0,
        inactiveSuppliers: 0,
        totalOrderValue: 0,
        averageRating: 0,
        totalOrders: 0
    };

    res.status(200).json({
        success: true,
        data: result
    });
});

// @desc    Add product to supplier
// @route   POST /api/suppliers/:id/products
// @access  Private
const addProductToSupplier = asyncHandler(async (req, res, next) => {
    const supplier = await Supplier.findById(req.params.id);

    if (!supplier) {
        return next(new ErrorResponse('Supplier not found', 404));
    }

    await supplier.addProduct(req.body.productId, req.body);

    res.status(200).json({
        success: true,
        data: supplier
    });
});

// @desc    Remove product from supplier
// @route   DELETE /api/suppliers/:id/products/:productId
// @access  Private
const removeProductFromSupplier = asyncHandler(async (req, res, next) => {
    const supplier = await Supplier.findById(req.params.id);

    if (!supplier) {
        return next(new ErrorResponse('Supplier not found', 404));
    }

    await supplier.removeProduct(req.params.productId);

    res.status(200).json({
        success: true,
        data: supplier
    });
});

module.exports = {
    getSuppliers,
    getSupplier,
    createSupplier,
    updateSupplier,
    deleteSupplier,
    getSupplierStats,
    addProductToSupplier,
    removeProductFromSupplier
};
