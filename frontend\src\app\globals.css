@import "tailwindcss";
@import "tw-animate-css";

/* Myanmar Font Support */
@import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Myanmar:wght@300;400;500;600;700&display=swap');

.font-myanmar {
  font-family: 'Noto Sans Myanmar', '<PERSON><PERSON><PERSON>ung<PERSON>', 'Myanmar3', sans-serif;
}

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: system-ui, -apple-system, sans-serif;
  --font-mono: 'Courier New', monospace;
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.145 0 0);

  /* Custom Appearance Variables */
  --app-primary-color: #3B82F6;
  --app-secondary-color: #6B7280;
  --app-accent-color: #10B981;
  --app-border-radius: 0.375rem;
  --app-base-font-size: 16px;
  --app-font-family: 'Inter', sans-serif;
  --app-animation-duration: 0.3s;
  --app-transition-duration: 0.15s;
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.145 0 0);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.145 0 0);
  --primary: oklch(0.205 0 0);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0 0);
  --secondary-foreground: oklch(0.205 0 0);
  --muted: oklch(0.97 0 0);
  --muted-foreground: oklch(0.556 0 0);
  --accent: oklch(0.97 0 0);
  --accent-foreground: oklch(0.205 0 0);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.922 0 0);
  --input: oklch(0.922 0 0);
  --ring: oklch(0.708 0 0);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.985 0 0);
  --sidebar-foreground: oklch(0.145 0 0);
  --sidebar-primary: oklch(0.205 0 0);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.97 0 0);
  --sidebar-accent-foreground: oklch(0.205 0 0);
  --sidebar-border: oklch(0.922 0 0);
  --sidebar-ring: oklch(0.708 0 0);
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

/* Color Scheme Variables */
.scheme-blue {
  --primary: oklch(0.646 0.222 264.376);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0.05 264.376);
  --accent: oklch(0.8 0.15 264.376);
}

.scheme-green {
  --primary: oklch(0.646 0.222 142.495);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0.05 142.495);
  --accent: oklch(0.8 0.15 142.495);
}

.scheme-purple {
  --primary: oklch(0.646 0.222 303.9);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0.05 303.9);
  --accent: oklch(0.8 0.15 303.9);
}

.scheme-orange {
  --primary: oklch(0.646 0.222 41.116);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0.05 41.116);
  --accent: oklch(0.8 0.15 41.116);
}

.scheme-pink {
  --primary: oklch(0.646 0.222 16.439);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0.05 16.439);
  --accent: oklch(0.8 0.15 16.439);
}

.scheme-indigo {
  --primary: oklch(0.646 0.222 284.376);
  --primary-foreground: oklch(0.985 0 0);
  --secondary: oklch(0.97 0.05 284.376);
  --accent: oklch(0.8 0.15 284.376);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }

  /* Swiper styles for products slider */
  .products-swiper {
    padding: 0 50px;
    overflow: visible;
  }

  .products-swiper .swiper-slide {
    height: auto;
    display: flex;
    flex-direction: column;
  }

  .products-swiper .swiper-button-prev,
  .products-swiper .swiper-button-next {
    display: none;
  }

  .products-swiper .swiper-pagination {
    bottom: -40px;
  }

  .products-swiper .swiper-scrollbar {
    bottom: -20px;
    height: 4px;
    background: rgba(0, 0, 0, 0.1);
    border-radius: 2px;
  }

  .products-swiper .swiper-scrollbar-drag {
    background: #3B82F6;
    border-radius: 2px;
  }

  /* Prevent zoom on mobile */
  @media (max-width: 768px) {
    html {
      -ms-touch-action: pan-x pan-y !important;
      touch-action: pan-x pan-y !important;
    }

    body {
      -ms-touch-action: pan-x pan-y !important;
      touch-action: pan-x pan-y !important;
      -webkit-user-select: none !important;
      -webkit-touch-callout: none !important;
      -webkit-tap-highlight-color: transparent !important;
    }
  }
}

/* Responsive Design Utilities */
@layer utilities {
  .container-responsive {
    @apply w-full mx-auto px-4 sm:px-6 lg:px-8;
  }

  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4;
  }

  .text-responsive {
    @apply text-sm sm:text-base lg:text-lg;
  }

  .padding-responsive {
    @apply p-2 sm:p-4 lg:p-6;
  }

  .margin-responsive {
    @apply m-2 sm:m-4 lg:m-6;
  }

  .mobile-only {
    @apply block sm:hidden;
  }

  .tablet-only {
    @apply hidden sm:block lg:hidden;
  }

  .desktop-only {
    @apply hidden lg:block;
  }

  .mobile-tablet {
    @apply block lg:hidden;
  }

  .tablet-desktop {
    @apply hidden sm:block;
  }
}

/* Animation Keyframes */
@keyframes shimmer {
  100% {
    transform: translateX(100%);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.animate-shimmer {
  animation: shimmer 2s infinite;
}

.animate-fadeIn {
  animation: fadeIn 0.1s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.1s ease-out;
}

.animate-pulse-custom {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

/* Print Styles for Invoice */
@media print {
  @page {
    size: A4;
    margin: 0.3in;
  }

  * {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  body {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
    margin: 0;
    padding: 0;
    font-size: 12px;
  }

  /* Print-specific utilities */
  .print\:hidden {
    display: none !important;
  }

  .print\:bg-white {
    background-color: white !important;
  }

  .print\:bg-blue-600 {
    background-color: #2563eb !important;
    color: white !important;
  }

  .print\:bg-red-500 {
    background-color: #ef4444 !important;
    color: white !important;
  }

  .print\:text-white {
    color: white !important;
  }

  .print\:text-black {
    color: black !important;
  }

  .print\:text-gray-700 {
    color: #374151 !important;
  }

  .print\:text-red-600 {
    color: #dc2626 !important;
  }

  .print\:shadow-none {
    box-shadow: none !important;
  }

  .print\:rounded-none {
    border-radius: 0 !important;
  }

  .print\:border {
    border: 1px solid #d1d5db !important;
  }

  .print\:border-gray-300 {
    border-color: #d1d5db !important;
  }

  .print\:border-gray-400 {
    border-color: #9ca3af !important;
  }

  .print\:divide-gray-300 > * + * {
    border-top: 1px solid #d1d5db !important;
  }

  /* Spacing utilities */
  .print\:p-0 { padding: 0 !important; }
  .print\:p-2 { padding: 0.5rem !important; }
  .print\:p-3 { padding: 0.75rem !important; }
  .print\:p-4 { padding: 1rem !important; }
  .print\:px-2 { padding-left: 0.5rem !important; padding-right: 0.5rem !important; }
  .print\:px-4 { padding-left: 1rem !important; padding-right: 1rem !important; }
  .print\:py-1 { padding-top: 0.25rem !important; padding-bottom: 0.25rem !important; }
  .print\:pb-2 { padding-bottom: 0.5rem !important; }
  .print\:pt-2 { padding-top: 0.5rem !important; }
  .print\:pb-4 { padding-bottom: 1rem !important; }
  .print\:mb-0 { margin-bottom: 0 !important; }
  .print\:mb-1 { margin-bottom: 0.25rem !important; }
  .print\:mb-2 { margin-bottom: 0.5rem !important; }
  .print\:gap-2 { gap: 0.5rem !important; }

  /* Text sizing */
  .print\:text-xs { font-size: 0.75rem !important; line-height: 1rem !important; }
  .print\:text-sm { font-size: 0.875rem !important; line-height: 1.25rem !important; }
  .print\:text-lg { font-size: 1.125rem !important; line-height: 1.75rem !important; }
  .print\:text-2xl { font-size: 1.5rem !important; line-height: 2rem !important; }

  /* Layout utilities */
  .print\:max-w-none { max-width: none !important; }
  .print\:flex-row { flex-direction: row !important; }
  .print\:space-y-0 > * + * { margin-top: 0 !important; }
  .print\:col-span-6 { grid-column: span 6 / span 6 !important; }
  .print\:col-span-2 { grid-column: span 2 / span 2 !important; }
  .print\:w-4 { width: 1rem !important; }
  .print\:h-4 { height: 1rem !important; }
  .print\:w-2 { width: 0.5rem !important; }
  .print\:h-2 { height: 0.5rem !important; }

  /* Force colors for gradients and backgrounds */
  .bg-gradient-to-r.from-blue-600.to-blue-700,
  .bg-gradient-to-r.from-blue-600.via-blue-700.to-blue-800,
  .bg-gradient-to-r.from-blue-600.to-blue-800 {
    background: #2563eb !important;
    color: white !important;
  }

  /* Red gradient backgrounds for Template 1 */
  .bg-gradient-to-br.from-red-600.via-red-700.to-red-900,
  .bg-gradient-to-r.from-red-500.to-red-600 {
    background: linear-gradient(135deg, #dc2626, #b91c1c, #7f1d1d) !important;
    color: white !important;
  }

  .bg-gradient-to-r.from-orange-500.to-orange-600 {
    background: linear-gradient(90deg, #f97316, #ea580c) !important;
    color: white !important;
  }

  .bg-red-500 {
    background-color: #ef4444 !important;
    color: white !important;
  }

  .bg-blue-100 {
    background-color: #dbeafe !important;
  }

  .bg-gray-200 {
    background-color: #e5e7eb !important;
  }

  /* Ensure text is readable */
  .text-white {
    color: white !important;
  }

  .text-blue-100 {
    color: #dbeafe !important;
  }

  /* Page breaks */
  .page-break-before {
    page-break-before: always;
  }

  .page-break-after {
    page-break-after: always;
  }

  .page-break-inside-avoid {
    page-break-inside: avoid;
  }

  /* Grid system */
  .grid {
    display: grid !important;
  }

  .grid-cols-12 {
    grid-template-columns: repeat(12, minmax(0, 1fr)) !important;
  }

  .grid-cols-1 {
    grid-template-columns: repeat(1, minmax(0, 1fr)) !important;
  }

  .grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }

  .grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  /* Hide elements that shouldn't print */
  nav,
  .print\:hidden,
  button:not(.print-button),
  .print\:hover\:bg-transparent:hover {
    display: none !important;
    background: transparent !important;
  }

  /* Invoice container print styles */
  .invoice-container,
  div.invoice-container,
  .dark .invoice-container,
  .dark div.invoice-container {
    border: none !important;
    border-width: 0 !important;
    border-color: transparent !important;
    background-color: white !important;
    color: black !important;
    box-shadow: none !important;
    outline: none !important;
  }

  /* Force remove all borders from invoice container */
  body .invoice-container {
    border: 0 !important;
    border-style: none !important;
  }

  /* Ensure proper text colors */
  .text-gray-600,
  .text-gray-400 {
    color: #374151 !important;
  }

  .text-gray-900 {
    color: #111827 !important;
  }

  .text-gray-700 {
    color: #374151 !important;
  }

  /* A4 Print Layout */
  @page {
    size: A4;
    margin: 0.5in;
  }

  body {
    margin: 0 !important;
    padding: 0 !important;
    background: white !important;
    color: black !important;
  }

  /* A4 Invoice Container */
  .w-\[210mm\] {
    width: 100% !important;
    max-width: 100% !important;
  }

  .h-\[297mm\] {
    height: auto !important;
    min-height: auto !important;
  }

  /* Remove all borders for clean print */
  .border-gray-400 {
    border: none !important;
  }

  .print\:border-gray-800 {
    border: none !important;
  }

  .print\:border-\[1px\] {
    border: none !important;
  }

  /* Remove all thick borders */
  .border {
    border: none !important;
  }

  .border-gray-300 {
    border: none !important;
  }

  .border-gray-200 {
    border: none !important;
  }

  /* Remove black borders completely */
  .border-black {
    border: none !important;
  }

  /* Remove dividers */
  .divide-y > * + * {
    border-top: none !important;
  }

  /* Remove table borders */
  table, th, td {
    border: none !important;
  }

  /* Keep only essential table structure with no borders */
  .invoice-table {
    border-collapse: collapse;
  }

  .invoice-table th,
  .invoice-table td {
    border: none !important;
  }

  /* Remove shadows for print */
  .shadow-2xl {
    box-shadow: none !important;
  }

  /* Page break support */
  .page-break-before {
    page-break-before: always !important;
    break-before: page !important;
  }

  .print\:page-break-before {
    page-break-before: always !important;
    break-before: page !important;
  }

  /* Multi-page table support */
  table {
    page-break-inside: auto !important;
    break-inside: auto !important;
  }

  tr {
    page-break-inside: avoid !important;
    page-break-after: auto !important;
    break-inside: avoid !important;
    break-after: auto !important;
  }

  thead {
    display: table-header-group !important;
  }

  tfoot {
    display: table-footer-group !important;
  }

  /* Ensure proper page layout */
  @page {
    margin: 0.5in;
    size: A4;
  }

  /* Hide elements on subsequent pages */
  .print-first-page-only {
    display: block !important;
  }

  .print-continuation-page .print-first-page-only {
    display: none !important;
  }

  /* Compact spacing for print */
  .p-6 {
    padding: 1rem !important;
  }

  .p-4 {
    padding: 0.75rem !important;
  }

  .mb-6 {
    margin-bottom: 1rem !important;
  }

  .gap-8 {
    gap: 1rem !important;
  }

  /* Table adjustments */
  .px-4 {
    padding-left: 0.5rem !important;
    padding-right: 0.5rem !important;
  }

  .py-4 {
    padding-top: 0.5rem !important;
    padding-bottom: 0.5rem !important;
  }

  /* Text size adjustments */
  .text-lg {
    font-size: 1rem !important;
  }

  .text-sm {
    font-size: 0.875rem !important;
  }

  /* Color adjustments for print */
  .bg-gray-50 {
    background-color: #f9fafb !important;
  }

  .bg-blue-600 {
    background-color: #2563eb !important;
  }

  .text-white {
    color: white !important;
  }

  /* Additional print utilities */
  .print\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr)) !important;
  }

  .print\:grid-cols-3 {
    grid-template-columns: repeat(3, minmax(0, 1fr)) !important;
  }

  .print\:gap-4 {
    gap: 1rem !important;
  }

  .print\:break-inside-avoid {
    break-inside: avoid !important;
    page-break-inside: avoid !important;
  }

  .print\:transform-none {
    transform: none !important;
  }

  .print\:transition-none {
    transition: none !important;
  }

  .print\:hover\:bg-transparent:hover {
    background-color: transparent !important;
  }

  .print\:bg-gray-100 {
    background-color: #f3f4f6 !important;
  }

  .print\:text-gray-800 {
    color: #1f2937 !important;
  }

  .print\:bg-gray-600 {
    background-color: #4b5563 !important;
  }

  .print\:bg-gray-800 {
    background-color: #1f2937 !important;
  }

  .print\:rounded-md {
    border-radius: 0.375rem !important;
  }

  .print\:p-6 {
    padding: 1.5rem !important;
  }

  .print\:mx-0 {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }

  .print\:my-0 {
    margin-top: 0 !important;
    margin-bottom: 0 !important;
  }

  .print\:max-w-none {
    max-width: none !important;
  }

  .print\:page-break-before {
    page-break-before: always !important;
    break-before: page !important;
  }

  .print\:break-after-avoid {
    page-break-after: avoid !important;
    break-after: avoid !important;
  }

  /* Dark mode print fixes */
  .dark,
  .dark *,
  .dark .invoice-container,
  .dark .invoice-container *,
  html.dark,
  html.dark *,
  html.dark .invoice-container,
  html.dark .invoice-container * {
    background: white !important;
    color: black !important;
    background-color: white !important;
  }

  /* Force white background for all dark mode elements */
  .dark\:bg-gray-900,
  .dark\:bg-gray-800,
  .dark\:bg-gray-700,
  .dark\:text-white,
  .dark\:text-gray-100,
  .dark\:text-gray-200,
  .bg-gradient-to-br,
  .bg-gradient-to-r,
  .bg-gradient-to-l {
    background: white !important;
    background-color: white !important;
    color: black !important;
  }

  /* Preserve accent colors for headers */
  .text-white {
    color: white !important;
  }

  /* Table headers should keep their colors */
  th {
    -webkit-print-color-adjust: exact !important;
    color-adjust: exact !important;
    print-color-adjust: exact !important;
  }

  /* Override any dark mode styles */
  body.dark,
  html.dark body,
  .dark body {
    background: white !important;
    color: black !important;
  }
}
