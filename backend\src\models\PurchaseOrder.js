const mongoose = require('mongoose');

const purchaseOrderSchema = new mongoose.Schema({
    orderNumber: {
        type: String,
        required: true,
        unique: true,
        trim: true
    },
    supplier: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Supplier',
        required: [true, 'Supplier is required']
    },
    createdBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User',
        required: [true, 'Created by user is required']
    },
    approvedBy: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
    },
    items: [{
        product: {
            type: mongoose.Schema.Types.ObjectId,
            ref: 'Product',
            required: true
        },
        productName: {
            type: String,
            required: true
        },
        sku: {
            type: String,
            required: true
        },
        supplierSku: String,
        description: String,
        quantity: {
            type: Number,
            required: [true, 'Quantity is required'],
            min: [1, 'Quantity must be at least 1']
        },
        unitPrice: {
            type: Number,
            required: [true, 'Unit price is required'],
            min: [0, 'Unit price cannot be negative']
        },
        totalPrice: {
            type: Number,
            required: true
        },
        receivedQuantity: {
            type: Number,
            default: 0,
            min: [0, 'Received quantity cannot be negative']
        },
        notes: String
    }],
    orderDate: {
        type: Date,
        default: Date.now
    },
    expectedDeliveryDate: {
        type: Date,
        required: [true, 'Expected delivery date is required']
    },
    actualDeliveryDate: {
        type: Date
    },
    status: {
        type: String,
        enum: ['draft', 'pending', 'approved', 'ordered', 'partially_received', 'received', 'cancelled'],
        default: 'draft'
    },
    subtotal: {
        type: Number,
        required: true,
        min: [0, 'Subtotal cannot be negative']
    },
    taxAmount: {
        type: Number,
        default: 0,
        min: [0, 'Tax amount cannot be negative']
    },
    discountAmount: {
        type: Number,
        default: 0,
        min: [0, 'Discount amount cannot be negative']
    },
    shippingCost: {
        type: Number,
        default: 0,
        min: [0, 'Shipping cost cannot be negative']
    },
    totalAmount: {
        type: Number,
        required: true,
        min: [0, 'Total amount cannot be negative']
    },
    currency: {
        type: String,
        enum: ['MMK', 'USD', 'THB', 'EUR', 'SGD'],
        default: 'MMK'
    },
    paymentTerms: {
        type: String,
        enum: ['cash', 'net_15', 'net_30', 'net_45', 'net_60', 'net_90', 'custom'],
        default: 'net_30'
    },
    customPaymentTerms: String,
    deliveryAddress: {
        street: String,
        city: String,
        state: String,
        country: String,
        zipCode: String,
        contactPerson: String,
        contactPhone: String
    },
    notes: {
        type: String,
        maxlength: [1000, 'Notes cannot exceed 1000 characters']
    },
    internalNotes: {
        type: String,
        maxlength: [1000, 'Internal notes cannot exceed 1000 characters']
    },
    attachments: [{
        filename: String,
        originalName: String,
        mimetype: String,
        size: Number,
        path: String,
        uploadedAt: {
            type: Date,
            default: Date.now
        }
    }],
    approvalDate: Date,
    orderSentDate: Date,
    cancelledDate: Date,
    cancellationReason: String
}, {
    timestamps: true,
    toJSON: { virtuals: true },
    toObject: { virtuals: true }
});

// Indexes
purchaseOrderSchema.index({ orderNumber: 1 });
purchaseOrderSchema.index({ supplier: 1 });
purchaseOrderSchema.index({ status: 1 });
purchaseOrderSchema.index({ orderDate: -1 });
purchaseOrderSchema.index({ createdBy: 1 });

// Virtual for total items
purchaseOrderSchema.virtual('totalItems').get(function() {
    return this.items.reduce((total, item) => total + item.quantity, 0);
});

// Virtual for total received items
purchaseOrderSchema.virtual('totalReceivedItems').get(function() {
    return this.items.reduce((total, item) => total + item.receivedQuantity, 0);
});

// Virtual for completion percentage
purchaseOrderSchema.virtual('completionPercentage').get(function() {
    const totalOrdered = this.totalItems;
    const totalReceived = this.totalReceivedItems;
    return totalOrdered > 0 ? Math.round((totalReceived / totalOrdered) * 100) : 0;
});

// Virtual for is fully received
purchaseOrderSchema.virtual('isFullyReceived').get(function() {
    return this.items.every(item => item.receivedQuantity >= item.quantity);
});

// Virtual for is partially received
purchaseOrderSchema.virtual('isPartiallyReceived').get(function() {
    return this.items.some(item => item.receivedQuantity > 0) && !this.isFullyReceived;
});

// Pre-save middleware
purchaseOrderSchema.pre('save', function(next) {
    if (this.isNew && !this.orderNumber) {
        // Generate order number
        const date = new Date();
        const year = date.getFullYear().toString().slice(-2);
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const timestamp = Date.now().toString().slice(-6);
        this.orderNumber = `PO-${year}${month}-${timestamp}`;
    }

    // Calculate totals
    this.subtotal = this.items.reduce((total, item) => total + item.totalPrice, 0);
    this.totalAmount = this.subtotal + this.taxAmount + this.shippingCost - this.discountAmount;

    // Update status based on received quantities
    if (this.status === 'ordered' || this.status === 'partially_received') {
        if (this.isFullyReceived) {
            this.status = 'received';
            if (!this.actualDeliveryDate) {
                this.actualDeliveryDate = new Date();
            }
        } else if (this.isPartiallyReceived) {
            this.status = 'partially_received';
        }
    }

    next();
});

// Static methods
purchaseOrderSchema.statics.findByStatus = function(status) {
    return this.find({ status }).sort({ orderDate: -1 });
};

purchaseOrderSchema.statics.findBySupplier = function(supplierId) {
    return this.find({ supplier: supplierId }).sort({ orderDate: -1 });
};

purchaseOrderSchema.statics.findPending = function() {
    return this.find({ 
        status: { $in: ['pending', 'approved', 'ordered', 'partially_received'] }
    }).sort({ orderDate: -1 });
};

// Instance methods
purchaseOrderSchema.methods.approve = function(approvedBy) {
    this.status = 'approved';
    this.approvedBy = approvedBy;
    this.approvalDate = new Date();
    return this.save();
};

purchaseOrderSchema.methods.sendOrder = function() {
    this.status = 'ordered';
    this.orderSentDate = new Date();
    return this.save();
};

purchaseOrderSchema.methods.cancel = function(reason) {
    this.status = 'cancelled';
    this.cancelledDate = new Date();
    this.cancellationReason = reason;
    return this.save();
};

purchaseOrderSchema.methods.receiveItem = function(productId, receivedQuantity) {
    const item = this.items.find(item => item.product.toString() === productId.toString());
    if (!item) {
        throw new Error('Product not found in purchase order');
    }

    if (receivedQuantity > item.quantity) {
        throw new Error('Received quantity cannot exceed ordered quantity');
    }

    item.receivedQuantity = receivedQuantity;
    return this.save();
};

module.exports = mongoose.model('PurchaseOrder', purchaseOrderSchema);
