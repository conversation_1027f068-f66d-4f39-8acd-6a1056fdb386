const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
    getProducts,
    getProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    searchProducts
} = require('../controllers/productController');

const router = express.Router();

// Search route (must be before /:id route)
router.get('/search', protect, searchProducts);

// CRUD routes
router.route('/')
    .get(protect, getProducts)
    .post(protect, authorize('admin', 'manager'), createProduct);

router.route('/:id')
    .get(protect, getProduct)
    .put(protect, authorize('admin', 'manager'), updateProduct)
    .delete(protect, authorize('admin', 'manager'), deleteProduct);

module.exports = router;
