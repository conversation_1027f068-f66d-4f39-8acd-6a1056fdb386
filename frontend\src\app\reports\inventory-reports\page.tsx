'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import {
  Package,
  TrendingUp,
  TrendingDown,
  ArrowLeft,
  Download,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Activity,
  RefreshCw,
  Archive,
  Zap
} from 'lucide-react'

interface InventoryMetrics {
  totalProducts: number
  totalValue: number
  lowStockItems: number
  outOfStockItems: number
  turnoverRate: number
  avgDaysToSell: number
  stockAccuracy: number
  deadStockValue: number
}

interface StockLevel {
  category: string
  totalItems: number
  inStock: number
  lowStock: number
  outOfStock: number
  stockValue: number
  turnoverRate: number
}

interface ProductStock {
  _id: string
  name: string
  sku: string
  category: string
  currentStock: number
  reorderPoint: number
  maxStock: number
  unitCost: number
  stockValue: number
  lastSold: string
  turnoverRate: number
  daysInStock: number
  status: 'good' | 'low' | 'out' | 'overstock' | 'dead'
}

export default function InventoryReportsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [dateRange, setDateRange] = useState('month')
  const [categoryFilter, setCategoryFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [loading, setLoading] = useState(true)

  const [inventoryMetrics, setInventoryMetrics] = useState<InventoryMetrics>({
    totalProducts: 245,
    totalValue: 485750000,
    lowStockItems: 23,
    outOfStockItems: 8,
    turnoverRate: 4.2,
    avgDaysToSell: 87,
    stockAccuracy: 95.8,
    deadStockValue: 12450000
  })

  const [stockLevels, setStockLevels] = useState<StockLevel[]>([
    {
      category: 'Laptops',
      totalItems: 45,
      inStock: 38,
      lowStock: 5,
      outOfStock: 2,
      stockValue: 185000000,
      turnoverRate: 3.8
    },
    {
      category: 'Monitors',
      totalItems: 52,
      inStock: 47,
      lowStock: 4,
      outOfStock: 1,
      stockValue: 98500000,
      turnoverRate: 4.5
    },
    {
      category: 'Accessories',
      totalItems: 78,
      inStock: 72,
      lowStock: 6,
      outOfStock: 0,
      stockValue: 45200000,
      turnoverRate: 6.2
    },
    {
      category: 'Processors',
      totalItems: 28,
      inStock: 22,
      lowStock: 4,
      outOfStock: 2,
      stockValue: 67800000,
      turnoverRate: 2.9
    },
    {
      category: 'Memory',
      totalItems: 35,
      inStock: 30,
      lowStock: 3,
      outOfStock: 2,
      stockValue: 52400000,
      turnoverRate: 5.1
    },
    {
      category: 'Storage',
      totalItems: 7,
      inStock: 5,
      lowStock: 1,
      outOfStock: 1,
      stockValue: 36850000,
      turnoverRate: 4.8
    }
  ])

  const [productStocks, setProductStocks] = useState<ProductStock[]>([
    {
      _id: '1',
      name: 'ASUS VivoBook 15',
      sku: 'LAP001',
      category: 'Laptops',
      currentStock: 12,
      reorderPoint: 10,
      maxStock: 50,
      unitCost: 1200000,
      stockValue: 14400000,
      lastSold: '2024-01-14T15:30:00Z',
      turnoverRate: 3.8,
      daysInStock: 45,
      status: 'good'
    },
    {
      _id: '2',
      name: 'Samsung 27" Monitor',
      sku: 'MON001',
      category: 'Monitors',
      currentStock: 8,
      reorderPoint: 5,
      maxStock: 30,
      unitCost: 380000,
      stockValue: 3040000,
      lastSold: '2024-01-13T10:15:00Z',
      turnoverRate: 4.5,
      daysInStock: 32,
      status: 'good'
    },
    {
      _id: '3',
      name: 'Logitech MX Master 3S',
      sku: 'MOU001',
      category: 'Accessories',
      currentStock: 25,
      reorderPoint: 15,
      maxStock: 100,
      unitCost: 78000,
      stockValue: 1950000,
      lastSold: '2024-01-12T14:20:00Z',
      turnoverRate: 6.2,
      daysInStock: 18,
      status: 'good'
    },
    {
      _id: '4',
      name: 'Intel Core i7-13700K',
      sku: 'CPU001',
      category: 'Processors',
      currentStock: 6,
      reorderPoint: 8,
      maxStock: 25,
      unitCost: 340000,
      stockValue: 2040000,
      lastSold: '2024-01-11T16:45:00Z',
      turnoverRate: 2.9,
      daysInStock: 78,
      status: 'low'
    },
    {
      _id: '5',
      name: 'Kingston 32GB RAM',
      sku: 'RAM001',
      category: 'Memory',
      currentStock: 15,
      reorderPoint: 10,
      maxStock: 50,
      unitCost: 160000,
      stockValue: 2400000,
      lastSold: '2024-01-10T11:30:00Z',
      turnoverRate: 5.1,
      daysInStock: 28,
      status: 'good'
    },
    {
      _id: '6',
      name: 'HP LaserJet Pro',
      sku: 'PRI001',
      category: 'Printers',
      currentStock: 0,
      reorderPoint: 5,
      maxStock: 15,
      unitCost: 250000,
      stockValue: 0,
      lastSold: '2024-01-05T09:15:00Z',
      turnoverRate: 1.2,
      daysInStock: 0,
      status: 'out'
    }
  ])

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchInventoryReports()
    }
  }, [isAuthenticated, dateRange, categoryFilter, statusFilter])

  const fetchInventoryReports = async () => {
    try {
      setLoading(true)
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Apply filters
      let filteredProducts = productStocks
      
      if (categoryFilter !== 'all') {
        filteredProducts = filteredProducts.filter(p => p.category === categoryFilter)
      }
      
      if (statusFilter !== 'all') {
        filteredProducts = filteredProducts.filter(p => p.status === statusFilter)
      }
      
      setProductStocks(filteredProducts)
    } catch (error) {
      console.error('Error fetching inventory reports:', error)
    } finally {
      setLoading(false)
    }
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} MMK`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric'
    })
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'good':
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Good</Badge>
      case 'low':
        return <Badge className="bg-yellow-100 text-yellow-800"><AlertTriangle className="h-3 w-3 mr-1" />Low Stock</Badge>
      case 'out':
        return <Badge className="bg-red-100 text-red-800"><AlertTriangle className="h-3 w-3 mr-1" />Out of Stock</Badge>
      case 'overstock':
        return <Badge className="bg-blue-100 text-blue-800"><Archive className="h-3 w-3 mr-1" />Overstock</Badge>
      case 'dead':
        return <Badge className="bg-gray-100 text-gray-800"><Clock className="h-3 w-3 mr-1" />Dead Stock</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTurnoverIcon = (rate: number) => {
    if (rate >= 5) return <Zap className="h-4 w-4 text-green-600" />
    if (rate >= 3) return <TrendingUp className="h-4 w-4 text-blue-600" />
    if (rate >= 1) return <Activity className="h-4 w-4 text-yellow-600" />
    return <TrendingDown className="h-4 w-4 text-red-600" />
  }

  const getTurnoverColor = (rate: number) => {
    if (rate >= 5) return 'text-green-600'
    if (rate >= 3) return 'text-blue-600'
    if (rate >= 1) return 'text-yellow-600'
    return 'text-red-600'
  }

  const categories = [...new Set(productStocks.map(p => p.category))]

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/reports')}
            className="hover:bg-teal-50 dark:hover:bg-teal-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Reports'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-teal-600 to-cyan-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Package className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'စာရင်းဝင် အစီရင်ခံစာများ' : 'Inventory Reports'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm' 
                      ? 'စတော့ အဆင့်များ၊ လည်ပတ်မှု နှင့် စာရင်းဝင် အသိအမြင်များ'
                      : 'Stock levels, turnover, and inventory insights'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Select value={dateRange} onValueChange={setDateRange}>
                  <SelectTrigger className="w-40 bg-white/20 border-white/30 text-white">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="week">{language === 'mm' ? 'ဒီအပတ်' : 'This Week'}</SelectItem>
                    <SelectItem value="month">{language === 'mm' ? 'ဒီလ' : 'This Month'}</SelectItem>
                    <SelectItem value="quarter">{language === 'mm' ? 'ဒီသုံးလ' : 'This Quarter'}</SelectItem>
                  </SelectContent>
                </Select>
                <Button className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                  <Download className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ထုတ်ယူရန်' : 'Export'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Key Inventory Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း တန်ဖိုး' : 'Total Value'}
                  </p>
                  <p className="text-2xl font-bold">{formatCurrency(inventoryMetrics.totalValue)}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {inventoryMetrics.totalProducts} {language === 'mm' ? 'ကုန်ပစ္စည်းများ' : 'products'}
                  </p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                  <Package className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'လည်ပတ်မှု နှုန်း' : 'Turnover Rate'}
                  </p>
                  <p className="text-2xl font-bold">{inventoryMetrics.turnoverRate}x</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {inventoryMetrics.avgDaysToSell} {language === 'mm' ? 'ရက် ပျမ်းမျှ' : 'days avg'}
                  </p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                  <RefreshCw className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'နည်းသော စတော့' : 'Low Stock Items'}
                  </p>
                  <p className="text-2xl font-bold text-yellow-600">{inventoryMetrics.lowStockItems}</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'mm' ? 'အရေးပေါ် ဖြည့်စွက်ရန်' : 'Need reorder'}
                  </p>
                </div>
                <div className="p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl">
                  <AlertTriangle className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စတော့ တိကျမှု' : 'Stock Accuracy'}
                  </p>
                  <p className="text-2xl font-bold text-green-600">{inventoryMetrics.stockAccuracy}%</p>
                  <p className="text-xs text-gray-500 mt-1">
                    {language === 'mm' ? 'စနစ် တိကျမှု' : 'System accuracy'}
                  </p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">
                  {language === 'mm' ? 'အမျိုးအစား' : 'Category'}
                </label>
                <Select value={categoryFilter} onValueChange={setCategoryFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder={language === 'mm' ? 'အမျိုးအစား ရွေးချယ်ပါ' : 'Select category'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Categories'}</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category} value={category}>{category}</SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              
              <div className="flex-1">
                <label className="text-sm font-medium mb-2 block">
                  {language === 'mm' ? 'အခြေအနေ' : 'Status'}
                </label>
                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Status'}</SelectItem>
                    <SelectItem value="good">{language === 'mm' ? 'ကောင်းမွန်' : 'Good'}</SelectItem>
                    <SelectItem value="low">{language === 'mm' ? 'နည်းသော စတော့' : 'Low Stock'}</SelectItem>
                    <SelectItem value="out">{language === 'mm' ? 'စတော့ ကုန်' : 'Out of Stock'}</SelectItem>
                    <SelectItem value="overstock">{language === 'mm' ? 'စတော့ များ' : 'Overstock'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Stock Levels by Category */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'အမျိုးအစားအလိုက် စတော့ အဆင့်များ' : 'Stock Levels by Category'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'ကုန်ပစ္စည်း အမျိုးအစားများ၏ စတော့ အခြေအနေ'
                  : 'Stock status across product categories'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {stockLevels.map((level) => (
                  <div key={level.category} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{level.category}</span>
                        <Badge variant="outline" className="text-xs">
                          {level.totalItems} items
                        </Badge>
                      </div>
                      <div className="flex items-center gap-2">
                        {getTurnoverIcon(level.turnoverRate)}
                        <span className={`text-sm font-semibold ${getTurnoverColor(level.turnoverRate)}`}>
                          {level.turnoverRate}x
                        </span>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-4 gap-1 h-2">
                      <div 
                        className="bg-green-500 rounded-l" 
                        style={{ width: `${(level.inStock / level.totalItems) * 100}%` }}
                        title={`In Stock: ${level.inStock}`}
                      ></div>
                      <div 
                        className="bg-yellow-500" 
                        style={{ width: `${(level.lowStock / level.totalItems) * 100}%` }}
                        title={`Low Stock: ${level.lowStock}`}
                      ></div>
                      <div 
                        className="bg-red-500 rounded-r" 
                        style={{ width: `${(level.outOfStock / level.totalItems) * 100}%` }}
                        title={`Out of Stock: ${level.outOfStock}`}
                      ></div>
                    </div>
                    
                    <div className="flex justify-between text-xs text-gray-600">
                      <span className="text-green-600">In: {level.inStock}</span>
                      <span className="text-yellow-600">Low: {level.lowStock}</span>
                      <span className="text-red-600">Out: {level.outOfStock}</span>
                      <span className="font-medium">{formatCurrency(level.stockValue)}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Critical Stock Items */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <AlertTriangle className="h-5 w-5 text-red-600" />
                {language === 'mm' ? 'အရေးကြီး စတော့ ပစ္စည်းများ' : 'Critical Stock Items'}
              </CardTitle>
              <CardDescription>
                {language === 'mm' 
                  ? 'အရေးပေါ် ဂရုစိုက်ရမည့် ကုန်ပစ္စည်းများ'
                  : 'Items requiring immediate attention'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {productStocks.filter(p => p.status === 'low' || p.status === 'out').map((product) => (
                  <div key={product._id} className="flex items-center justify-between p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                    <div className="flex items-center gap-3">
                      <div>
                        <h4 className="font-medium">{product.name}</h4>
                        <p className="text-sm text-gray-600">{product.sku} • {product.category}</p>
                        {getStatusBadge(product.status)}
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-semibold text-red-600">{product.currentStock}</p>
                      <p className="text-xs text-gray-600">
                        {language === 'mm' ? 'ပြန်မှာရန်' : 'Reorder'}: {product.reorderPoint}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Detailed Inventory List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Package className="h-5 w-5 text-green-600" />
              {language === 'mm' ? 'အသေးစိတ် စာရင်းဝင် စာရင်း' : 'Detailed Inventory List'}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {productStocks.map((product) => (
                <div key={product._id} className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:shadow-md transition-shadow">
                  <div className="grid grid-cols-1 lg:grid-cols-6 gap-4 items-center">
                    {/* Product Info */}
                    <div className="lg:col-span-2">
                      <div className="flex items-center gap-3">
                        <div>
                          <h4 className="font-semibold">{product.name}</h4>
                          <p className="text-sm text-gray-600">{product.sku} • {product.category}</p>
                          <div className="flex items-center gap-2 mt-1">
                            {getStatusBadge(product.status)}
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Stock Levels */}
                    <div className="text-center">
                      <p className="text-lg font-bold">{product.currentStock}</p>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'လက်ရှိ' : 'Current'}</p>
                    </div>

                    <div className="text-center">
                      <p className="text-lg font-bold text-yellow-600">{product.reorderPoint}</p>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'ပြန်မှာရန်' : 'Reorder'}</p>
                    </div>

                    <div className="text-center">
                      <p className="text-lg font-bold">{formatCurrency(product.stockValue)}</p>
                      <p className="text-xs text-gray-600">{language === 'mm' ? 'တန်ဖိုး' : 'Value'}</p>
                    </div>

                    {/* Turnover & Days */}
                    <div className="text-center">
                      <div className="flex items-center justify-center gap-1 mb-1">
                        {getTurnoverIcon(product.turnoverRate)}
                        <span className={`font-semibold ${getTurnoverColor(product.turnoverRate)}`}>
                          {product.turnoverRate}x
                        </span>
                      </div>
                      <p className="text-xs text-gray-600">
                        {product.daysInStock} {language === 'mm' ? 'ရက်' : 'days'}
                      </p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
