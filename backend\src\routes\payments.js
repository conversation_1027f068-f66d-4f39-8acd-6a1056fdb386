// Payment Gateway Routes
const express = require('express');
const router = express.Router();
const { protect, authorize, auditLog } = require('../middleware/auth');
const paymentService = require('../services/paymentService');

// @desc    Get available payment gateways
// @route   GET /api/payments/gateways
// @access  Private
router.get('/gateways', protect, async (req, res) => {
    try {
        const result = await paymentService.getAvailableGateways();
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Process payment
// @route   POST /api/payments/process
// @access  Private
router.post('/process', protect, auditLog('process_payment'), async (req, res) => {
    try {
        const paymentData = req.body;
        
        // Validate required fields
        const requiredFields = ['gateway', 'amount', 'currency', 'orderId'];
        const missingFields = requiredFields.filter(field => !paymentData[field]);
        
        if (missingFields.length > 0) {
            return res.status(400).json({
                success: false,
                error: `Missing required fields: ${missingFields.join(', ')}`
            });
        }

        // Add user information to payment data
        paymentData.userId = req.user._id;
        paymentData.userEmail = req.user.email;

        const result = await paymentService.processPayment(paymentData);
        
        // Log payment attempt
        console.log(`💳 Payment processed: ${result.success ? 'SUCCESS' : 'FAILED'}`, {
            gateway: paymentData.gateway,
            amount: paymentData.amount,
            orderId: paymentData.orderId,
            user: req.user.email
        });

        res.json(result);
    } catch (error) {
        console.error('❌ Payment processing error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Verify payment status
// @route   GET /api/payments/verify/:transactionId
// @access  Private
router.get('/verify/:transactionId', protect, async (req, res) => {
    try {
        const { transactionId } = req.params;
        const { gateway } = req.query;
        
        if (!gateway) {
            return res.status(400).json({
                success: false,
                error: 'Gateway parameter is required'
            });
        }

        const result = await paymentService.verifyPayment(transactionId, gateway);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Process refund
// @route   POST /api/payments/refund
// @access  Private (Manager/Admin only)
router.post('/refund', protect, authorize('admin', 'manager'), auditLog('process_refund'), async (req, res) => {
    try {
        const { transactionId, amount, gateway, reason } = req.body;
        
        if (!transactionId || !amount || !gateway) {
            return res.status(400).json({
                success: false,
                error: 'Transaction ID, amount, and gateway are required'
            });
        }

        const result = await paymentService.refundPayment(transactionId, amount, gateway, reason);
        
        // Log refund attempt
        console.log(`💰 Refund processed: ${result.success ? 'SUCCESS' : 'FAILED'}`, {
            transactionId,
            amount,
            gateway,
            reason,
            user: req.user.email
        });

        res.json(result);
    } catch (error) {
        console.error('❌ Refund processing error:', error);
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get gateway status
// @route   GET /api/payments/gateways/:gatewayId/status
// @access  Private
router.get('/gateways/:gatewayId/status', protect, async (req, res) => {
    try {
        const { gatewayId } = req.params;
        const result = await paymentService.getGatewayStatus(gatewayId);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Process KBZ Pay payment
// @route   POST /api/payments/kbzpay
// @access  Private
router.post('/kbzpay', protect, auditLog('kbzpay_payment'), async (req, res) => {
    try {
        const { amount, customerPhone, orderId, description } = req.body;
        
        if (!amount || !customerPhone || !orderId) {
            return res.status(400).json({
                success: false,
                error: 'Amount, customer phone, and order ID are required'
            });
        }

        const paymentData = {
            gateway: 'kbzpay',
            amount,
            currency: 'MMK',
            customerPhone,
            orderId,
            description,
            userId: req.user._id,
            userEmail: req.user.email
        };

        const result = await paymentService.processPayment(paymentData);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Process Wave Money payment
// @route   POST /api/payments/wavemoney
// @access  Private
router.post('/wavemoney', protect, auditLog('wavemoney_payment'), async (req, res) => {
    try {
        const { amount, customerPhone, orderId, description } = req.body;
        
        if (!amount || !customerPhone || !orderId) {
            return res.status(400).json({
                success: false,
                error: 'Amount, customer phone, and order ID are required'
            });
        }

        const paymentData = {
            gateway: 'wavemoney',
            amount,
            currency: 'MMK',
            customerPhone,
            orderId,
            description,
            userId: req.user._id,
            userEmail: req.user.email
        };

        const result = await paymentService.processPayment(paymentData);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Process AYA Pay payment
// @route   POST /api/payments/ayapay
// @access  Private
router.post('/ayapay', protect, auditLog('ayapay_payment'), async (req, res) => {
    try {
        const { amount, customerPhone, orderId, description } = req.body;
        
        if (!amount || !customerPhone || !orderId) {
            return res.status(400).json({
                success: false,
                error: 'Amount, customer phone, and order ID are required'
            });
        }

        const paymentData = {
            gateway: 'ayapay',
            amount,
            currency: 'MMK',
            customerPhone,
            orderId,
            description,
            userId: req.user._id,
            userEmail: req.user.email
        };

        const result = await paymentService.processPayment(paymentData);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Process CB Pay payment
// @route   POST /api/payments/cbpay
// @access  Private
router.post('/cbpay', protect, auditLog('cbpay_payment'), async (req, res) => {
    try {
        const { amount, customerPhone, orderId, description } = req.body;
        
        if (!amount || !customerPhone || !orderId) {
            return res.status(400).json({
                success: false,
                error: 'Amount, customer phone, and order ID are required'
            });
        }

        const paymentData = {
            gateway: 'cbpay',
            amount,
            currency: 'MMK',
            customerPhone,
            orderId,
            description,
            userId: req.user._id,
            userEmail: req.user.email
        };

        const result = await paymentService.processPayment(paymentData);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Process MPU Card payment
// @route   POST /api/payments/mpu
// @access  Private
router.post('/mpu', protect, auditLog('mpu_payment'), async (req, res) => {
    try {
        const { amount, cardNumber, expiryDate, cvv, orderId, description } = req.body;
        
        if (!amount || !cardNumber || !expiryDate || !cvv || !orderId) {
            return res.status(400).json({
                success: false,
                error: 'Amount, card details, and order ID are required'
            });
        }

        const paymentData = {
            gateway: 'mpu',
            amount,
            currency: 'MMK',
            cardNumber,
            expiryDate,
            cvv,
            orderId,
            description,
            userId: req.user._id,
            userEmail: req.user.email
        };

        const result = await paymentService.processPayment(paymentData);
        res.json(result);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get payment history
// @route   GET /api/payments/history
// @access  Private
router.get('/history', protect, async (req, res) => {
    try {
        const { page = 1, limit = 20, gateway, status, startDate, endDate } = req.query;
        
        // In a real implementation, this would query the database
        // For now, return mock data
        const mockHistory = {
            success: true,
            data: [],
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: 0,
                pages: 0
            },
            filters: {
                gateway,
                status,
                startDate,
                endDate
            }
        };

        res.json(mockHistory);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

// @desc    Get payment analytics
// @route   GET /api/payments/analytics
// @access  Private (Manager/Admin only)
router.get('/analytics', protect, authorize('admin', 'manager'), async (req, res) => {
    try {
        const { period = '7d' } = req.query;
        
        // Mock analytics data
        const analytics = {
            success: true,
            period: period,
            data: {
                totalTransactions: 0,
                totalAmount: 0,
                successRate: 0,
                gatewayBreakdown: {},
                dailyTrends: [],
                topGateways: []
            },
            generatedAt: new Date().toISOString()
        };

        res.json(analytics);
    } catch (error) {
        res.status(500).json({
            success: false,
            error: error.message
        });
    }
});

module.exports = router;
