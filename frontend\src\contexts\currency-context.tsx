'use client'

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react'
import apiClient from '@/lib/api'

export type CurrencyCode = 'MMK' | 'USD' | 'THB'

export interface ExchangeRate {
  rate: number
  symbol: string
  name: string
  flag: string
}

export interface CurrencyContextType {
  currentCurrency: CurrencyCode
  currentCurrencyInfo: ExchangeRate
  setCurrency: (currency: CurrencyCode) => void
  exchangeRates: Record<CurrencyCode, ExchangeRate>
  convertPrice: (price: number, fromCurrency?: CurrencyCode) => number
  formatCurrency: (amount: number, currency?: CurrencyCode) => string
  updateExchangeRates: (newRates?: Record<CurrencyCode, ExchangeRate>) => Promise<void>
}

const CurrencyContext = createContext<CurrencyContextType | undefined>(undefined)

// Real-time exchange rates (updated periodically)
const DEFAULT_EXCHANGE_RATES: Record<CurrencyCode, ExchangeRate> = {
  MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
  USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
  THB: { rate: 0.016, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
}

interface CurrencyProviderProps {
  children: ReactNode
}

export function CurrencyProvider({ children }: CurrencyProviderProps) {
  const [currentCurrency, setCurrentCurrency] = useState<CurrencyCode>('MMK')
  const [exchangeRates, setExchangeRates] = useState<Record<CurrencyCode, ExchangeRate>>(DEFAULT_EXCHANGE_RATES)

  // Load saved currency and fetch exchange rates from API
  useEffect(() => {
    if (typeof window === 'undefined') return

    const initializeCurrency = async () => {
      // Load saved currency
      const savedCurrency = localStorage.getItem('bitstech_currency')
      if (savedCurrency && ['MMK', 'USD', 'THB'].includes(savedCurrency)) {
        setCurrentCurrency(savedCurrency as CurrencyCode)
      }

      // Load exchange rates from localStorage first, skip API call
      const savedRates = localStorage.getItem('bitstech_exchange_rates')
      if (savedRates) {
        try {
          const parsedRates = JSON.parse(savedRates)
          setExchangeRates(parsedRates)
          console.log('Exchange rates loaded from localStorage')
        } catch (parseError) {
          console.error('Failed to parse saved exchange rates:', parseError)
          setExchangeRates(DEFAULT_EXCHANGE_RATES)
        }
      } else {
        // Use default rates if no saved data
        setExchangeRates(DEFAULT_EXCHANGE_RATES)
        console.log('Using default exchange rates')
      }
    }

    initializeCurrency()
  }, [])

  // Save currency to localStorage when changed
  useEffect(() => {
    if (typeof window === 'undefined') return

    localStorage.setItem('bitstech_currency', currentCurrency)

    // Broadcast currency change to ALL components and pages
    window.dispatchEvent(new CustomEvent('currencyChanged', {
      detail: { currency: currentCurrency }
    }))

    // Global currency sync event
    window.dispatchEvent(new CustomEvent('global-currency-sync', {
      detail: {
        currency: currentCurrency,
        exchangeRates: exchangeRates,
        timestamp: Date.now()
      }
    }))

    // Update all currency displays immediately
    window.dispatchEvent(new CustomEvent('currency-display-update', {
      detail: {
        currency: currentCurrency,
        symbol: exchangeRates[currentCurrency]?.symbol || 'K',
        flag: exchangeRates[currentCurrency]?.flag || '🇲🇲'
      }
    }))

    console.log('💰 Global currency sync broadcasted:', currentCurrency)
  }, [currentCurrency]) // Remove exchangeRates dependency to prevent infinite loop

  // Listen for currency toggle events from theme context
  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleCurrencyToggle = (event: CustomEvent) => {
      const newCurrency = event.detail.newCurrency
      if (newCurrency && ['MMK', 'USD', 'THB'].includes(newCurrency) && newCurrency !== currentCurrency) {
        console.log('🔄 Currency toggle received from ThemeProvider:', newCurrency)
        setCurrentCurrency(newCurrency)
      }
    }

    const handleExchangeRatesUpdate = (event: CustomEvent) => {
      const newRates = event.detail.rates
      if (newRates) {
        // Direct update without API call for event-based updates
        setExchangeRates(newRates)
        localStorage.setItem('bitstech_exchange_rates', JSON.stringify(newRates))
        // Sync to other tabs via custom event
        window.dispatchEvent(new CustomEvent('settings-sync', {
          detail: { type: 'exchange-rates', data: { rates: newRates } }
        }))
      }
    }

    const handleCurrencyChanged = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      if (newCurrency && ['MMK', 'USD', 'THB'].includes(newCurrency) && newCurrency !== currentCurrency) {
        console.log('🔄 Currency changed received:', newCurrency)
        setCurrentCurrency(newCurrency)
      }
    }

    window.addEventListener('currency-toggle', handleCurrencyToggle as EventListener)
    window.addEventListener('exchange-rates-updated', handleExchangeRatesUpdate as EventListener)
    window.addEventListener('currency-changed', handleCurrencyChanged as EventListener)

    // Listen for cross-tab sync events via localStorage
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'bitstech_settings_sync' && event.newValue) {
        try {
          const syncEvent = JSON.parse(event.newValue)

          if (syncEvent.type === 'currency' && syncEvent.data.currency) {
            if (['MMK', 'USD', 'THB'].includes(syncEvent.data.currency)) {
              setCurrentCurrency(syncEvent.data.currency)
              localStorage.setItem('bitstech_current_currency', syncEvent.data.currency)
            }
          }

          if (syncEvent.type === 'exchange-rates' && syncEvent.data.rates) {
            setExchangeRates(syncEvent.data.rates)
            localStorage.setItem('bitstech_exchange_rates', JSON.stringify(syncEvent.data.rates))
          }
        } catch (error) {
          console.error('Failed to parse sync event:', error)
        }
      }
    }

    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('currency-toggle', handleCurrencyToggle as EventListener)
      window.removeEventListener('exchange-rates-updated', handleExchangeRatesUpdate as EventListener)
      window.removeEventListener('currency-changed', handleCurrencyChanged as EventListener)
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  // No automatic live rate updates - user controls exchange rates manually

  const setCurrency = (currency: CurrencyCode) => {
    if (currency !== currentCurrency) {
      console.log('💰 Setting currency to:', currency)
      setCurrentCurrency(currency)
      if (typeof window !== 'undefined') {
        localStorage.setItem('bitstech_currency', currency)
        localStorage.setItem('bitstech_current_currency', currency)

        // Broadcast currency change to all components
        window.dispatchEvent(new CustomEvent('currency-changed', {
          detail: { currency }
        }))

        // Global currency sync event (for Analytics and other pages)
        window.dispatchEvent(new CustomEvent('global-currency-sync', {
          detail: {
            currency,
            exchangeRates: exchangeRates,
            timestamp: Date.now()
          }
        }))

        // Sync to other tabs via custom event
        window.dispatchEvent(new CustomEvent('settings-sync', {
          detail: { type: 'currency', data: { currency } }
        }))

        // Cross-tab sync via localStorage
        localStorage.setItem('bitstech_currency_sync', JSON.stringify({
          currency,
          timestamp: Date.now()
        }))
      }
    }
  }

  const convertPrice = (price: number, fromCurrency: CurrencyCode = 'MMK'): number => {
    if (fromCurrency === currentCurrency) return price
    
    // Convert to MMK first (base currency)
    const priceInMMK = fromCurrency === 'MMK' ? price : price / exchangeRates[fromCurrency].rate
    
    // Then convert to target currency
    return currentCurrency === 'MMK' ? priceInMMK : priceInMMK * exchangeRates[currentCurrency].rate
  }

  const formatCurrency = (amount: number, currency: CurrencyCode = currentCurrency): string => {
    const rate = exchangeRates[currency]
    const convertedAmount = convertPrice(amount, 'MMK')

    // Format based on currency
    switch (currency) {
      case 'USD':
        return `${rate.symbol}${convertedAmount.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })}`
      case 'THB':
        return `${rate.symbol}${convertedAmount.toLocaleString('en-US', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        })}`
      case 'MMK':
      default:
        return `${Math.round(convertedAmount).toLocaleString()} ${rate.symbol}`
    }
  }

  const updateExchangeRates = async (newRates?: Record<CurrencyCode, ExchangeRate>): Promise<void> => {
    try {
      if (newRates) {
        // Update with user-provided rates only
        try {
          const response = await apiClient.updateExchangeRates(newRates)
          if (response.success) {
            setExchangeRates(response.data?.rates || newRates)
            if (typeof window !== 'undefined') {
              localStorage.setItem('bitstech_exchange_rates', JSON.stringify(response.data?.rates || newRates))
            }
            console.log('Exchange rates updated successfully')
          }
        } catch (error) {
          console.warn('API call failed, updating locally:', error)
          // Always update local state with user input
          setExchangeRates(newRates)
          if (typeof window !== 'undefined') {
            localStorage.setItem('bitstech_exchange_rates', JSON.stringify(newRates))
          }
        }
      } else {
        console.log('No rates provided for update')
      }
    } catch (error) {
      console.error('Failed to update exchange rates:', error)
      // If API fails, still update local state if newRates provided
      if (newRates) {
        setExchangeRates(newRates)
        if (typeof window !== 'undefined') {
          localStorage.setItem('bitstech_exchange_rates', JSON.stringify(newRates))
        }
      }
    }
  }

  const value: CurrencyContextType = {
    currentCurrency,
    currentCurrencyInfo: exchangeRates[currentCurrency],
    setCurrency,
    exchangeRates,
    convertPrice,
    formatCurrency,
    updateExchangeRates
  }

  return (
    <CurrencyContext.Provider value={value}>
      {children}
    </CurrencyContext.Provider>
  )
}

export function useCurrency() {
  const context = useContext(CurrencyContext)
  if (context === undefined) {
    throw new Error('useCurrency must be used within a CurrencyProvider')
  }
  return context
}
