const express = require('express');
const router = express.Router();
const asyncHandler = require('../middleware/asyncHandler');
const { protect, authorize } = require('../middleware/auth');
const Customer = require('../models/Customer');
const ErrorResponse = require('../utils/errorResponse');

// @desc    Get all customers
// @route   GET /api/customers
// @access  Private
router.get('/', protect, asyncHandler(async (req, res) => {
    const {
        search,
        customerType,
        status,
        page = 1,
        limit = 20,
        sort = '-createdAt'
    } = req.query;

    // Build query
    let query = {};

    // Search functionality
    if (search) {
        query.$or = [
            { name: { $regex: search, $options: 'i' } },
            { email: { $regex: search, $options: 'i' } },
            { phone: { $regex: search, $options: 'i' } }
        ];
    }

    // Filter by customer type
    if (customerType) {
        query.customerType = customerType;
    }

    // Filter by status
    if (status) {
        query.status = status;
    }

    // Pagination
    const startIndex = (page - 1) * limit;

    try {
        const customers = await Customer.find(query)
            .sort(sort)
            .skip(startIndex)
            .limit(limit);
            
        const total = await Customer.countDocuments(query);
        
        // Pagination result
        const pagination = {};
        
        if (startIndex + limit < total) {
            pagination.next = {
                page: page + 1,
                limit
            };
        }
        
        if (startIndex > 0) {
            pagination.prev = {
                page: page - 1,
                limit
            };
        }
        
        res.status(200).json({
            success: true,
            count: customers.length,
            total,
            pagination,
            data: customers
        });
    } catch (error) {
        return next(new ErrorResponse('Error fetching customers', 500));
    }
}));

// @desc    Get single customer
// @route   GET /api/customers/:id
// @access  Private
router.get('/:id', protect, asyncHandler(async (req, res, next) => {
    const customer = await Customer.findById(req.params.id);

    if (!customer) {
        return next(new ErrorResponse(`Customer not found with id of ${req.params.id}`, 404));
    }

    res.status(200).json({
        success: true,
        data: customer
    });
}));

// @desc    Create new customer
// @route   POST /api/customers
// @access  Private
router.post('/', protect, asyncHandler(async (req, res, next) => {
    const {
        name,
        email,
        phone,
        address,
        customerType = 'regular',
        loyaltyPoints = 0,
        totalSpent = 0,
        status = 'active'
    } = req.body;

    // Check if customer with email or phone already exists
    if (email) {
        const existingCustomer = await Customer.findOne({ email });
        if (existingCustomer) {
            return next(new ErrorResponse('Customer with this email already exists', 400));
        }
    }

    if (phone) {
        const existingCustomer = await Customer.findOne({ phone });
        if (existingCustomer) {
            return next(new ErrorResponse('Customer with this phone number already exists', 400));
        }
    }

    const customer = await Customer.create({
        name,
        email,
        phone,
        address,
        customerType,
        loyaltyPoints,
        totalSpent,
        status
    });

    res.status(201).json({
        success: true,
        data: customer
    });
}));

// @desc    Update customer
// @route   PUT /api/customers/:id
// @access  Private
router.put('/:id', protect, asyncHandler(async (req, res, next) => {
    let customer = await Customer.findById(req.params.id);

    if (!customer) {
        return next(new ErrorResponse(`Customer not found with id of ${req.params.id}`, 404));
    }

    // Check if email or phone is being updated and already exists
    if (req.body.email && req.body.email !== customer.email) {
        const existingCustomer = await Customer.findOne({ email: req.body.email });
        if (existingCustomer) {
            return next(new ErrorResponse('Customer with this email already exists', 400));
        }
    }

    if (req.body.phone && req.body.phone !== customer.phone) {
        const existingCustomer = await Customer.findOne({ phone: req.body.phone });
        if (existingCustomer) {
            return next(new ErrorResponse('Customer with this phone number already exists', 400));
        }
    }

    customer = await Customer.findByIdAndUpdate(req.params.id, req.body, {
        new: true,
        runValidators: true
    });

    res.status(200).json({
        success: true,
        data: customer
    });
}));

// @desc    Delete customer
// @route   DELETE /api/customers/:id
// @access  Private (Admin only)
router.delete('/:id', protect, authorize('admin'), asyncHandler(async (req, res, next) => {
    const customer = await Customer.findById(req.params.id);

    if (!customer) {
        return next(new ErrorResponse(`Customer not found with id of ${req.params.id}`, 404));
    }

    await customer.deleteOne();

    res.status(200).json({
        success: true,
        data: {}
    });
}));

// @desc    Get customer statistics
// @route   GET /api/customers/stats
// @access  Private
router.get('/stats/overview', protect, asyncHandler(async (req, res) => {
    const totalCustomers = await Customer.countDocuments();
    const activeCustomers = await Customer.countDocuments({ status: 'active' });
    const vipCustomers = await Customer.countDocuments({ customerType: 'vip' });
    
    // Get top customers by total spent
    const topCustomers = await Customer.find()
        .sort({ totalSpent: -1 })
        .limit(5)
        .select('name email totalSpent loyaltyPoints');

    // Get new customers this month
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);
    
    const newCustomersThisMonth = await Customer.countDocuments({
        createdAt: { $gte: startOfMonth }
    });

    res.status(200).json({
        success: true,
        data: {
            totalCustomers,
            activeCustomers,
            vipCustomers,
            newCustomersThisMonth,
            topCustomers
        }
    });
}));

module.exports = router;
