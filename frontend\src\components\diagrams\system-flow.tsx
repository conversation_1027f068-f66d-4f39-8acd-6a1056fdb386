'use client'

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface SystemFlowProps {
  language?: 'en' | 'mm'
}

export function SystemFlow({ language = 'en' }: SystemFlowProps) {
  const t = {
    en: {
      title: 'BitsTech POS System Flow',
      userLogin: 'User Login',
      dashboard: 'Dashboard',
      posSystem: 'POS System',
      productMgmt: 'Product Management',
      salesMgmt: 'Sales Management',
      customerMgmt: 'Customer Management',
      reports: 'Reports & Analytics',
      settings: 'System Settings',
      description: 'Complete system workflow showing user interactions and data flow'
    },
    mm: {
      title: 'BitsTech POS စနစ် လုပ်ငန်းစဉ်',
      userLogin: 'အသုံးပြုသူ လော့ဂ်အင်',
      dashboard: 'ဒက်ရှ်ဘုတ်',
      posSystem: 'POS စနစ်',
      productMgmt: 'ကုန်ပစ္စည်း စီမံခန့်ခွဲမှု',
      salesMgmt: 'ရောင်းချမှု စီမံခန့်ခွဲမှု',
      customerMgmt: 'ဖောက်သည် စီမံခန့်ခွဲမှု',
      reports: 'အစီရင်ခံစာများနှင့် ခွဲခြမ်းစိတ်ဖြာမှု',
      settings: 'စနစ် ဆက်တင်များ',
      description: 'အသုံးပြုသူ အပြန်အလှန် လုပ်ဆောင်မှုများနှင့် ဒေတာ စီးဆင်းမှုကို ပြသသော အပြည့်အစုံ စနစ် လုပ်ငန်းစဉ်'
    }
  }

  const text = t[language]

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {text.title}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {text.description}
        </p>
      </div>

      {/* System Flow Diagram */}
      <div className="relative">
        <svg
          viewBox="0 0 900 700"
          className="w-full h-auto border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900"
        >
          {/* User Login */}
          <rect x="400" y="30" width="100" height="60" fill="#EF4444" fillOpacity="0.1" stroke="#EF4444" strokeWidth="2" rx="8" />
          <text x="450" y="55" textAnchor="middle" className="fill-red-600 font-semibold text-sm">
            {text.userLogin}
          </text>
          <text x="450" y="70" textAnchor="middle" className="fill-red-500 text-xs">Authentication</text>

          {/* Dashboard */}
          <rect x="400" y="130" width="100" height="60" fill="#3B82F6" fillOpacity="0.1" stroke="#3B82F6" strokeWidth="2" rx="8" />
          <text x="450" y="155" textAnchor="middle" className="fill-blue-600 font-semibold text-sm">
            {text.dashboard}
          </text>
          <text x="450" y="170" textAnchor="middle" className="fill-blue-500 text-xs">Main Hub</text>

          {/* POS System */}
          <rect x="50" y="250" width="120" height="80" fill="#10B981" fillOpacity="0.1" stroke="#10B981" strokeWidth="2" rx="8" />
          <text x="110" y="275" textAnchor="middle" className="fill-green-600 font-semibold text-sm">
            {text.posSystem}
          </text>
          <text x="110" y="290" textAnchor="middle" className="fill-green-500 text-xs">Sales Processing</text>
          <text x="110" y="305" textAnchor="middle" className="fill-green-500 text-xs">Payment</text>
          <text x="110" y="320" textAnchor="middle" className="fill-green-500 text-xs">Receipt</text>

          {/* Product Management */}
          <rect x="200" y="250" width="120" height="80" fill="#8B5CF6" fillOpacity="0.1" stroke="#8B5CF6" strokeWidth="2" rx="8" />
          <text x="260" y="275" textAnchor="middle" className="fill-purple-600 font-semibold text-sm">
            {text.productMgmt}
          </text>
          <text x="260" y="290" textAnchor="middle" className="fill-purple-500 text-xs">Inventory</text>
          <text x="260" y="305" textAnchor="middle" className="fill-purple-500 text-xs">Categories</text>
          <text x="260" y="320" textAnchor="middle" className="fill-purple-500 text-xs">Stock Control</text>

          {/* Sales Management */}
          <rect x="350" y="250" width="120" height="80" fill="#F59E0B" fillOpacity="0.1" stroke="#F59E0B" strokeWidth="2" rx="8" />
          <text x="410" y="275" textAnchor="middle" className="fill-amber-600 font-semibold text-sm">
            {text.salesMgmt}
          </text>
          <text x="410" y="290" textAnchor="middle" className="fill-amber-500 text-xs">Transactions</text>
          <text x="410" y="305" textAnchor="middle" className="fill-amber-500 text-xs">History</text>
          <text x="410" y="320" textAnchor="middle" className="fill-amber-500 text-xs">Analytics</text>

          {/* Customer Management */}
          <rect x="500" y="250" width="120" height="80" fill="#EC4899" fillOpacity="0.1" stroke="#EC4899" strokeWidth="2" rx="8" />
          <text x="560" y="275" textAnchor="middle" className="fill-pink-600 font-semibold text-sm">
            {text.customerMgmt}
          </text>
          <text x="560" y="290" textAnchor="middle" className="fill-pink-500 text-xs">Profiles</text>
          <text x="560" y="305" textAnchor="middle" className="fill-pink-500 text-xs">Loyalty</text>
          <text x="560" y="320" textAnchor="middle" className="fill-pink-500 text-xs">Communication</text>

          {/* Reports & Analytics */}
          <rect x="650" y="250" width="120" height="80" fill="#06B6D4" fillOpacity="0.1" stroke="#06B6D4" strokeWidth="2" rx="8" />
          <text x="710" y="275" textAnchor="middle" className="fill-cyan-600 font-semibold text-sm">
            {text.reports}
          </text>
          <text x="710" y="290" textAnchor="middle" className="fill-cyan-500 text-xs">KPIs</text>
          <text x="710" y="305" textAnchor="middle" className="fill-cyan-500 text-xs">Forecasting</text>
          <text x="710" y="320" textAnchor="middle" className="fill-cyan-500 text-xs">Insights</text>

          {/* Settings */}
          <rect x="400" y="380" width="100" height="60" fill="#6B7280" fillOpacity="0.1" stroke="#6B7280" strokeWidth="2" rx="8" />
          <text x="450" y="405" textAnchor="middle" className="fill-gray-600 font-semibold text-sm">
            {text.settings}
          </text>
          <text x="450" y="420" textAnchor="middle" className="fill-gray-500 text-xs">Configuration</text>

          {/* Database */}
          <rect x="350" y="500" width="200" height="80" fill="#7C3AED" fillOpacity="0.1" stroke="#7C3AED" strokeWidth="2" rx="8" />
          <text x="450" y="525" textAnchor="middle" className="fill-violet-600 font-semibold text-sm">MongoDB Database</text>
          <text x="450" y="540" textAnchor="middle" className="fill-violet-500 text-xs">Collections: Products, Sales, Users,</text>
          <text x="450" y="555" textAnchor="middle" className="fill-violet-500 text-xs">Customers, Settings, Suppliers</text>
          <text x="450" y="570" textAnchor="middle" className="fill-violet-500 text-xs">Real-time Data Storage</text>

          {/* Connection Lines */}
          {/* Login to Dashboard */}
          <line x1="450" y1="90" x2="450" y2="130" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          
          {/* Dashboard to modules */}
          <line x1="400" y1="160" x2="110" y2="250" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="420" y1="190" x2="260" y2="250" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="450" y1="190" x2="410" y2="250" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="480" y1="190" x2="560" y2="250" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="500" y1="160" x2="710" y2="250" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          
          {/* Dashboard to Settings */}
          <line x1="450" y1="190" x2="450" y2="380" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          
          {/* Modules to Database */}
          <line x1="110" y1="330" x2="350" y2="500" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="260" y1="330" x2="400" y2="500" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="410" y1="330" x2="450" y2="500" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="560" y1="330" x2="500" y2="500" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="710" y1="330" x2="550" y2="500" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          
          {/* Settings to Database */}
          <line x1="450" y1="440" x2="450" y2="500" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />

          {/* Data Flow Indicators */}
          <circle cx="200" cy="400" r="3" fill="#10B981" />
          <text x="210" y="405" className="fill-green-600 text-xs">Real-time Updates</text>
          
          <circle cx="600" cy="400" r="3" fill="#EF4444" />
          <text x="610" y="405" className="fill-red-600 text-xs">Data Validation</text>

          {/* Arrow marker */}
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#6B7280" />
            </marker>
          </defs>
        </svg>
      </div>

      {/* Flow Description */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-red-600">Authentication Flow</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <p>1. User enters credentials</p>
            <p>2. JWT token generation</p>
            <p>3. Role-based access control</p>
            <p>4. Session management</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-blue-600">Main Navigation</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <p>1. Dashboard overview</p>
            <p>2. Module selection</p>
            <p>3. Feature access</p>
            <p>4. Real-time updates</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-purple-600">Data Processing</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2 text-sm">
            <p>1. Input validation</p>
            <p>2. Business logic</p>
            <p>3. Database operations</p>
            <p>4. Response handling</p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
