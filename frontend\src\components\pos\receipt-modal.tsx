'use client'

import React, { useRef } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ReceiptTemplate } from './receipt-template'
import apiClient from '@/lib/api'
import {
  Printer,
  Mail,
  Download,
  X,
  Check,
  Copy
} from 'lucide-react'

interface ReceiptModalProps {
  isOpen: boolean
  onClose: () => void
  saleData: any
  language?: 'en' | 'mm'
}

export function ReceiptModal({
  isOpen,
  onClose,
  saleData,
  language = 'en'
}: ReceiptModalProps) {
  const receiptRef = useRef<HTMLDivElement>(null)

  if (!isOpen || !saleData) return null

  // Format sale data for receipt template
  const receiptData = {
    saleNumber: saleData.saleNumber || 'N/A',
    date: saleData.createdAt ? new Date(saleData.createdAt).toLocaleDateString() : new Date().toLocaleDateString(),
    time: saleData.createdAt ? new Date(saleData.createdAt).toLocaleTimeString() : new Date().toLocaleTimeString(),
    cashier: {
      firstName: saleData.cashier?.split(' ')[0] || 'System',
      lastName: saleData.cashier?.split(' ')[1] || 'User',
      username: saleData.cashier || 'system'
    },
    customer: saleData.customer || { name: 'Walk-in Customer' },
    items: saleData.items?.map((item: any) => ({
      productName: item.productName || item.product?.name || 'Unknown Product',
      sku: item.productSku || item.product?.sku || 'N/A',
      quantity: item.quantity || 1,
      unitPrice: item.unitPrice || item.product?.price || 0,
      totalPrice: item.totalPrice || (item.quantity * (item.unitPrice || item.product?.price || 0)),
      discount: item.discountAmount || 0
    })) || [],
    subtotal: saleData.subtotal || 0,
    totalDiscount: saleData.totalDiscount || 0,
    totalTax: saleData.totalTax || 0,
    totalAmount: saleData.totalAmount || 0,
    currency: saleData.currency || 'MMK',
    paymentMethod: saleData.paymentMethod || 'cash',
    amountPaid: saleData.amountPaid || saleData.totalAmount || 0,
    changeAmount: saleData.changeAmount || 0,
    notes: saleData.notes || ''
  }

  const text = {
    en: {
      receipt: 'Receipt',
      print: 'Print Receipt',
      email: 'Email Receipt',
      download: 'Download PDF',
      copy: 'Copy Receipt Number',
      close: 'Close',
      printSuccess: 'Receipt sent to printer',
      emailSuccess: 'Receipt sent via email',
      copySuccess: 'Receipt number copied',
      actions: 'Receipt Actions'
    },
    mm: {
      receipt: 'ဘောင်ချာ',
      print: 'ပရင့်ထုတ်ရန်',
      email: 'အီးမေးလ်ပို့ရန်',
      download: 'PDF ဒေါင်းလုဒ်',
      copy: 'နံပါတ် ကော်ပီကူးရန်',
      close: 'ပိတ်ရန်',
      printSuccess: 'ဘောင်ချာကို ပရင့်တာသို့ ပို့ပြီးပြီ',
      emailSuccess: 'ဘောင်ချာကို အီးမေးလ်ပို့ပြီးပြီ',
      copySuccess: 'ဘောင်ချာ နံပါတ် ကော်ပီကူးပြီးပြီ',
      actions: 'ဘောင်ချာ လုပ်ဆောင်ချက်များ'
    }
  }

  const t = text[language]

  const handlePrint = () => {
    if (receiptRef.current) {
      const printWindow = window.open('', '_blank')
      if (printWindow) {
        printWindow.document.write(`
          <!DOCTYPE html>
          <html>
            <head>
              <title>Receipt - ${saleData.saleNumber}</title>
              <style>
                body {
                  margin: 0;
                  padding: 0;
                  font-family: 'Courier New', monospace;
                }
                @media print {
                  @page {
                    size: 80mm auto;
                    margin: 0;
                  }
                  body {
                    width: 80mm;
                  }
                }
              </style>
            </head>
            <body>
              ${receiptRef.current.innerHTML}
            </body>
          </html>
        `)
        printWindow.document.close()
        printWindow.focus()
        printWindow.print()
        printWindow.close()

        // Show success message
        alert(t.printSuccess)
      }
    }
  }

  const handleEmail = async () => {
    try {
      const email = saleData.customer?.email || prompt('Enter email address:')
      if (!email) return

      // Mock email sending - replace with actual API call when backend is ready
      console.log('Sending receipt email to:', email)
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulate API call
      alert(t.emailSuccess)
    } catch (error: any) {
      console.error('Error sending email:', error)
      alert(`Failed to send email: ${error.message || 'Please try again'}`)
    }
  }

  const handleDownloadPDF = () => {
    // TODO: Implement PDF generation
    // This would typically use a library like jsPDF or call a backend service
    console.log('Downloading PDF for sale:', saleData.saleNumber)
    alert('PDF download feature coming soon!')
  }

  const handleCopyReceiptNumber = () => {
    navigator.clipboard.writeText(saleData.saleNumber).then(() => {
      alert(t.copySuccess)
    }).catch(() => {
      alert('Failed to copy receipt number')
    })
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <h2 className="text-xl font-semibold">{t.receipt} #{saleData.saleNumber}</h2>
          <Button variant="ghost" size="icon" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Content */}
        <div className="flex flex-col lg:flex-row max-h-[calc(90vh-120px)]">
          {/* Receipt Preview */}
          <div className="flex-1 overflow-auto p-4 bg-gray-50">
            <div ref={receiptRef}>
              <ReceiptTemplate data={receiptData} language={language} />
            </div>
          </div>

          {/* Actions Panel */}
          <div className="w-full lg:w-80 p-4 border-l bg-white">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">{t.actions}</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {/* Print Button */}
                <Button
                  onClick={handlePrint}
                  className="w-full flex items-center gap-2"
                  variant="default"
                >
                  <Printer className="h-4 w-4" />
                  {t.print}
                </Button>

                {/* Email Button */}
                <Button
                  onClick={handleEmail}
                  className="w-full flex items-center gap-2"
                  variant="outline"
                  disabled={!saleData.customer?.email}
                >
                  <Mail className="h-4 w-4" />
                  {t.email}
                </Button>

                {/* Download PDF Button */}
                <Button
                  onClick={handleDownloadPDF}
                  className="w-full flex items-center gap-2"
                  variant="outline"
                >
                  <Download className="h-4 w-4" />
                  {t.download}
                </Button>

                {/* Copy Receipt Number */}
                <Button
                  onClick={handleCopyReceiptNumber}
                  className="w-full flex items-center gap-2"
                  variant="outline"
                >
                  <Copy className="h-4 w-4" />
                  {t.copy}
                </Button>

                {/* Sale Information */}
                <div className="mt-6 pt-4 border-t space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Sale Number:</span>
                    <span className="font-mono">{saleData.saleNumber}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Date:</span>
                    <span>{new Date(saleData.createdAt).toLocaleDateString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Time:</span>
                    <span>{new Date(saleData.createdAt).toLocaleTimeString()}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Total:</span>
                    <span className="font-semibold">
                      {saleData.totalAmount?.toLocaleString() || '0'} {saleData.currency || 'MMK'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Payment:</span>
                    <span className="uppercase">{saleData.paymentMethod?.replace('_', ' ') || 'CASH'}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status:</span>
                    <span className="flex items-center gap-1">
                      <Check className="h-3 w-3 text-green-600" />
                      <span className="text-green-600 capitalize">{saleData.status || 'completed'}</span>
                    </span>
                  </div>
                </div>

                {/* Customer Information */}
                {saleData.customer?.name && (
                  <div className="mt-4 pt-4 border-t">
                    <h4 className="font-medium mb-2">Customer Information</h4>
                    <div className="space-y-1 text-sm">
                      <div>{saleData.customer.name}</div>
                      {saleData.customer.email && (
                        <div className="text-gray-600">{saleData.customer.email}</div>
                      )}
                      {saleData.customer.phone && (
                        <div className="text-gray-600">{saleData.customer.phone}</div>
                      )}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Footer */}
        <div className="flex justify-end gap-2 p-4 border-t bg-gray-50">
          <Button variant="outline" onClick={onClose}>
            {t.close}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default ReceiptModal
