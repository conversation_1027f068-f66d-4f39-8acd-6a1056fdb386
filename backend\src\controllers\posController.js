const asyncHandler = require('../utils/asyncHandler');
const Product = require('../models/Product');
const Sale = require('../models/Sale');
const User = require('../models/User');
const ErrorResponse = require('../utils/errorResponse');

// In-memory cart storage (in production, use Redis or database)
const userCarts = new Map();

// @desc    Get user's cart
// @route   GET /api/pos/cart
// @access  Private
const getCart = asyncHandler(async (req, res) => {
    const userId = req.user.id;
    const cart = userCarts.get(userId) || { items: [], total: 0 };
    
    res.status(200).json({
        success: true,
        data: cart
    });
});

// @desc    Add item to cart
// @route   POST /api/pos/cart/add
// @access  Private
const addToCart = asyncHandler(async (req, res) => {
    const { productId, quantity = 1 } = req.body;
    const userId = req.user.id;

    if (!productId) {
        return res.status(400).json({
            success: false,
            error: 'Product ID is required'
        });
    }

    // Get product details
    const product = await Product.findById(productId).populate('category');
    if (!product) {
        return res.status(404).json({
            success: false,
            error: 'Product not found'
        });
    }

    if (!product.isActive) {
        return res.status(400).json({
            success: false,
            error: 'Product is not available'
        });
    }

    if (product.inventory.quantity < quantity) {
        return res.status(400).json({
            success: false,
            error: 'Insufficient stock'
        });
    }

    // Get or create cart
    let cart = userCarts.get(userId) || { items: [], total: 0 };

    // Check if item already exists in cart
    const existingItemIndex = cart.items.findIndex(item => 
        item.product._id.toString() === productId
    );

    if (existingItemIndex > -1) {
        // Update existing item
        const newQuantity = cart.items[existingItemIndex].quantity + quantity;
        
        if (newQuantity > product.inventory.quantity) {
            return res.status(400).json({
                success: false,
                error: 'Cannot add more items than available stock'
            });
        }

        cart.items[existingItemIndex].quantity = newQuantity;
        cart.items[existingItemIndex].totalPrice = newQuantity * product.price;
    } else {
        // Add new item
        cart.items.push({
            product: {
                _id: product._id,
                name: product.name,
                price: product.price,
                currency: product.currency,
                sku: product.sku,
                category: product.category,
                inventory: product.inventory,
                image: product.image
            },
            quantity: quantity,
            totalPrice: quantity * product.price
        });
    }

    // Recalculate total
    cart.total = cart.items.reduce((sum, item) => sum + item.totalPrice, 0);

    // Save cart
    userCarts.set(userId, cart);

    // Broadcast inventory update if WebSocket server is available
    if (global.wsServer && product.inventory.quantity <= product.inventory.minStock) {
        global.wsServer.broadcastLowStockAlert(
            product._id,
            product.name,
            product.inventory.quantity,
            product.inventory.minStock
        );
    }

    res.status(200).json({
        success: true,
        message: 'Item added to cart',
        data: cart
    });
});

// @desc    Update cart item quantity
// @route   PUT /api/pos/cart/update
// @access  Private
const updateCartItem = asyncHandler(async (req, res) => {
    const { productId, quantity } = req.body;
    const userId = req.user.id;

    if (!productId || quantity === undefined) {
        return res.status(400).json({
            success: false,
            error: 'Product ID and quantity are required'
        });
    }

    if (quantity < 0) {
        return res.status(400).json({
            success: false,
            error: 'Quantity cannot be negative'
        });
    }

    let cart = userCarts.get(userId);
    if (!cart) {
        return res.status(404).json({
            success: false,
            error: 'Cart not found'
        });
    }

    const itemIndex = cart.items.findIndex(item => 
        item.product._id.toString() === productId
    );

    if (itemIndex === -1) {
        return res.status(404).json({
            success: false,
            error: 'Item not found in cart'
        });
    }

    if (quantity === 0) {
        // Remove item from cart
        cart.items.splice(itemIndex, 1);
    } else {
        // Get product to check stock
        const product = await Product.findById(productId);
        if (!product) {
            return res.status(404).json({
                success: false,
                error: 'Product not found'
            });
        }

        if (quantity > product.inventory.quantity) {
            return res.status(400).json({
                success: false,
                error: 'Insufficient stock'
            });
        }

        // Update quantity
        cart.items[itemIndex].quantity = quantity;
        cart.items[itemIndex].totalPrice = quantity * cart.items[itemIndex].product.price;
    }

    // Recalculate total
    cart.total = cart.items.reduce((sum, item) => sum + item.totalPrice, 0);

    // Save cart
    userCarts.set(userId, cart);

    res.status(200).json({
        success: true,
        message: 'Cart updated',
        data: cart
    });
});

// @desc    Remove item from cart
// @route   DELETE /api/pos/cart/remove/:productId
// @access  Private
const removeFromCart = asyncHandler(async (req, res) => {
    const { productId } = req.params;
    const userId = req.user.id;

    let cart = userCarts.get(userId);
    if (!cart) {
        return res.status(404).json({
            success: false,
            error: 'Cart not found'
        });
    }

    const itemIndex = cart.items.findIndex(item => 
        item.product._id.toString() === productId
    );

    if (itemIndex === -1) {
        return res.status(404).json({
            success: false,
            error: 'Item not found in cart'
        });
    }

    // Remove item
    cart.items.splice(itemIndex, 1);

    // Recalculate total
    cart.total = cart.items.reduce((sum, item) => sum + item.totalPrice, 0);

    // Save cart
    userCarts.set(userId, cart);

    res.status(200).json({
        success: true,
        message: 'Item removed from cart',
        data: cart
    });
});

// @desc    Clear cart
// @route   DELETE /api/pos/cart/clear
// @access  Private
const clearCart = asyncHandler(async (req, res) => {
    const userId = req.user.id;

    // Clear cart
    userCarts.set(userId, { items: [], total: 0 });

    res.status(200).json({
        success: true,
        message: 'Cart cleared',
        data: { items: [], total: 0 }
    });
});

// @desc    Validate checkout data
// @route   POST /api/pos/checkout/validate
// @access  Private
const validateCheckout = asyncHandler(async (req, res) => {
    const { customer, paymentMethod, taxRate = 5, discountRate = 0, shippingCost = 0 } = req.body;
    const userId = req.user.id;

    // Get cart
    const cart = userCarts.get(userId);
    if (!cart || cart.items.length === 0) {
        return res.status(400).json({
            success: false,
            error: 'Cart is empty'
        });
    }

    // Validate customer data
    if (!customer || !customer.name || !customer.phone) {
        return res.status(400).json({
            success: false,
            error: 'Customer name and phone are required'
        });
    }

    // Validate payment method
    const validPaymentMethods = ['cash', 'card', 'kbz', 'wave', 'nug', 'bank'];
    if (!paymentMethod || !validPaymentMethods.includes(paymentMethod)) {
        return res.status(400).json({
            success: false,
            error: 'Valid payment method is required'
        });
    }

    // Check stock availability for all items
    for (const item of cart.items) {
        const product = await Product.findById(item.product._id);
        if (!product) {
            return res.status(404).json({
                success: false,
                error: `Product ${item.product.name} not found`
            });
        }

        if (product.inventory.quantity < item.quantity) {
            return res.status(400).json({
                success: false,
                error: `Insufficient stock for ${product.name}. Available: ${product.inventory.quantity}, Required: ${item.quantity}`
            });
        }
    }

    // Calculate totals
    const subtotal = cart.total;
    const tax = subtotal * (taxRate / 100);
    const discount = subtotal * (discountRate / 100);
    const shipping = shippingCost;
    const total = subtotal + tax - discount + shipping;

    res.status(200).json({
        success: true,
        message: 'Checkout validation successful',
        data: {
            subtotal,
            tax,
            discount,
            shipping,
            total,
            itemCount: cart.items.length,
            totalQuantity: cart.items.reduce((sum, item) => sum + item.quantity, 0)
        }
    });
});

// @desc    Process checkout
// @route   POST /api/pos/checkout
// @access  Private (Admin, Manager, Cashier)
const checkout = asyncHandler(async (req, res) => {
    const {
        customer,
        paymentMethod,
        paymentDetails = {},
        taxRate = 5,
        discountRate = 0,
        shippingCost = 0,
        notes = ''
    } = req.body;
    const userId = req.user.id;

    // Get cart
    const cart = userCarts.get(userId);
    if (!cart || cart.items.length === 0) {
        return res.status(400).json({
            success: false,
            error: 'Cart is empty'
        });
    }

    // Validate and update inventory
    const productUpdates = [];
    for (const item of cart.items) {
        const product = await Product.findById(item.product._id);
        if (!product) {
            return res.status(404).json({
                success: false,
                error: `Product ${item.product.name} not found`
            });
        }

        if (product.inventory.quantity < item.quantity) {
            return res.status(400).json({
                success: false,
                error: `Insufficient stock for ${product.name}`
            });
        }

        productUpdates.push({
            productId: product._id,
            newQuantity: product.inventory.quantity - item.quantity
        });
    }

    // Calculate totals
    const subtotal = cart.total;
    const tax = subtotal * (taxRate / 100);
    const discount = subtotal * (discountRate / 100);
    const shipping = shippingCost;
    const total = subtotal + tax - discount + shipping;

    // Generate sale number
    const saleNumber = `SALE-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

    // Create sale record
    const sale = await Sale.create({
        saleNumber,
        cashier: userId,
        customer: {
            name: customer.name,
            phone: customer.phone,
            email: customer.email || '',
            address: customer.address || ''
        },
        items: cart.items.map(item => ({
            product: item.product._id,
            productName: item.product.name,
            sku: item.product.sku || 'N/A',
            quantity: item.quantity,
            unitPrice: item.product.price,
            totalPrice: item.totalPrice
        })),
        subtotal,
        totalDiscount: discount,
        totalTax: tax,
        totalAmount: total,
        paymentMethod,
        paymentDetails,
        amountPaid: total, // Assuming full payment
        changeAmount: 0,
        notes,
        status: 'completed'
    });

    // Update product inventory
    for (const update of productUpdates) {
        await Product.findByIdAndUpdate(
            update.productId,
            { 'inventory.quantity': update.newQuantity }
        );
    }

    // Clear cart
    userCarts.set(userId, { items: [], total: 0 });

    // Broadcast real-time updates via WebSocket
    if (global.wsServer) {
        // Broadcast sales update
        global.wsServer.broadcastSalesUpdate({
            todaySales: {
                amount: sale.totalAmount,
                transactions: 1,
                change: 0 // Calculate based on previous data if needed
            },
            recentSales: [{
                id: sale.saleNumber,
                customer: sale.customer.name,
                amount: sale.totalAmount,
                time: new Date().toLocaleTimeString('en-US', {
                    hour: '2-digit',
                    minute: '2-digit'
                }),
                timestamp: sale.createdAt
            }]
        });

        // Broadcast inventory updates for sold products
        sale.items.forEach(item => {
            global.wsServer.broadcastProductSold(
                item.product,
                item.quantity,
                item.productName
            );
        });

        console.log('📡 Real-time updates broadcasted for sale:', sale.saleNumber);
    }

    res.status(201).json({
        success: true,
        message: 'Checkout completed successfully',
        data: {
            saleId: sale._id,
            saleNumber: sale.saleNumber,
            total: sale.totalAmount,
            timestamp: sale.createdAt
        }
    });
});

// @desc    Get receipt data
// @route   GET /api/pos/receipt/:saleId
// @access  Private
const getReceipt = asyncHandler(async (req, res) => {
    const { saleId } = req.params;

    const sale = await Sale.findById(saleId)
        .populate('cashier', 'firstName lastName email')
        .populate('items.product', 'name sku category');

    if (!sale) {
        return res.status(404).json({
            success: false,
            error: 'Sale not found'
        });
    }

    res.status(200).json({
        success: true,
        data: sale
    });
});

module.exports = {
    getCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    validateCheckout,
    checkout,
    getReceipt
};
