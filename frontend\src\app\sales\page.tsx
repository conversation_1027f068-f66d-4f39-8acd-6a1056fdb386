'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useSettings, useTranslation } from '@/contexts/settings-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  ShoppingCart,
  Search,
  Eye,
  Calendar,
  DollarSign,
  User,
  Receipt,
  TrendingUp,
  RefreshCw,
  FileText
} from 'lucide-react'

interface Sale {
  _id: string
  saleNumber: string
  cashier?: {
    _id: string
    firstName: string
    lastName: string
    username: string
  }
  customer?: {
    name?: string
    email?: string
    phone?: string
  }
  items: Array<{
    productName: string
    sku: string
    quantity: number
    unitPrice: number
    totalPrice: number
  }>
  subtotal: number
  totalDiscount: number
  totalTax: number
  totalAmount: number
  currency: string
  paymentMethod: string
  status: string
  totalItems: number
  createdAt: string
  updatedAt: string
}

interface SalesStats {
  totalSales: number
  totalRevenue: number
  totalItems: number
  averageOrderValue: number
}

export default function SalesPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const { t } = useTranslation()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [sales, setSales] = useState<Sale[]>([])
  const [stats, setStats] = useState<SalesStats | null>(null)
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [paymentFilter, setPaymentFilter] = useState('')
  const [dateFilter, setDateFilter] = useState('today')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Real-time WebSocket updates and currency sync
  useEffect(() => {
    const handleSalesUpdate = (event: CustomEvent) => {
      const data = event.detail
      console.log('💰 Sales update received:', data)

      if (data.type === 'sale_created' || data.type === 'sale_updated') {
        fetchSales() // Refresh sales list
        fetchStats() // Refresh stats
      }
    }

    const handleCurrencyChange = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      console.log('🔄 Sales currency changed to:', newCurrency)
      // Sales will automatically re-render with new currency formatting
    }

    window.addEventListener('ws-sales-update', handleSalesUpdate as EventListener)
    window.addEventListener('global-currency-sync', handleCurrencyChange as EventListener)

    return () => {
      window.removeEventListener('ws-sales-update', handleSalesUpdate as EventListener)
      window.removeEventListener('global-currency-sync', handleCurrencyChange as EventListener)
    }
  }, [])

  useEffect(() => {
    if (isAuthenticated) {
      fetchSales()
      fetchStats()
    }
  }, [isAuthenticated, searchQuery, statusFilter, paymentFilter, dateFilter])

  const fetchSales = async () => {
    try {
      setLoading(true)
      const params: any = {}

      if (searchQuery) params.search = searchQuery
      if (statusFilter) params.status = statusFilter
      if (paymentFilter) params.paymentMethod = paymentFilter

      // Date filtering
      if (dateFilter !== 'all') {
        const now = new Date()
        let startDate: Date

        switch (dateFilter) {
          case 'today':
            startDate = new Date(now.getFullYear(), now.getMonth(), now.getDate())
            params.startDate = startDate.toISOString()
            break
          case 'week':
            startDate = new Date(now.setDate(now.getDate() - 7))
            params.startDate = startDate.toISOString()
            break
          case 'month':
            startDate = new Date(now.getFullYear(), now.getMonth(), 1)
            params.startDate = startDate.toISOString()
            break
        }
      }

      const response = await apiClient.getSales(params)
      setSales(response.data)
    } catch (error) {
      console.error('Error fetching sales:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchStats = async () => {
    try {
      const data = await apiClient.getSalesStats(dateFilter)
      setStats(data)
    } catch (error) {
      console.error('Error fetching stats:', error)
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>
      case 'pending':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'cancelled':
        return <Badge variant="destructive">Cancelled</Badge>
      case 'refunded':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Refunded</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getPaymentMethodBadge = (method: string | undefined | null) => {
    if (!method) {
      return (
        <Badge variant="outline" className="bg-gray-100 text-gray-800">
          N/A
        </Badge>
      )
    }

    const colors = {
      cash: 'bg-green-100 text-green-800',
      card: 'bg-blue-100 text-blue-800',
      digital: 'bg-purple-100 text-purple-800',
      bank_transfer: 'bg-orange-100 text-orange-800',
      other: 'bg-gray-100 text-gray-800'
    }

    return (
      <Badge variant="outline" className={colors[method as keyof typeof colors] || colors.other}>
        {method.replace('_', ' ').toUpperCase()}
      </Badge>
    )
  }

  const formatPrice = (price: number | undefined | null) => {
    if (typeof price !== 'number' || isNaN(price) || price === null || price === undefined) {
      return formatCurrency(0)
    }
    return formatCurrency(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }



  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-emerald-600 to-teal-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <ShoppingCart className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'ရောင်းအား မှတ်တမ်းများ' : 'Sales History'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm'
                      ? 'ရောင်းချမှု မှတ်တမ်းများကို ကြည့်ရှုပါ'
                      : 'View and manage sales transactions'
                    }
                  </p>
                </div>
              </div>
              <Button
                onClick={fetchSales}
                className="bg-white/20 hover:bg-white/30 text-white border-white/30"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                {language === 'mm' ? 'ပြန်လည်ရယူရန်' : 'Refresh'}
              </Button>
            </div>
          </div>
        </div>

        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <Card className="border-l-4 border-l-emerald-500 hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'mm' ? 'စုစုပေါင်း ရောင်းအား' : 'Total Sales'}
                </CardTitle>
                <div className="p-2 bg-emerald-100 dark:bg-emerald-900/20 rounded-lg">
                  <ShoppingCart className="h-4 w-4 text-emerald-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-emerald-600">{stats.totalSales}</div>
                <p className="text-xs text-gray-500">
                  {dateFilter === 'today'
                    ? (language === 'mm' ? 'ယနေ့' : 'Today')
                    : dateFilter === 'week'
                    ? (language === 'mm' ? 'ဒီအပတ်' : 'This week')
                    : (language === 'mm' ? 'ဒီလ' : 'This month')
                  }
                </p>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-blue-500 hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'mm' ? 'စုစုပေါင်း ဝင်ငွေ' : 'Total Revenue'}
                </CardTitle>
                <div className="p-2 bg-blue-100 dark:bg-blue-900/20 rounded-lg">
                  <DollarSign className="h-4 w-4 text-blue-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-blue-600">{formatPrice(stats.totalRevenue)}</div>
                <p className="text-xs text-gray-500">
                  {dateFilter === 'today'
                    ? (language === 'mm' ? 'ယနေ့' : 'Today')
                    : dateFilter === 'week'
                    ? (language === 'mm' ? 'ဒီအပတ်' : 'This week')
                    : (language === 'mm' ? 'ဒီလ' : 'This month')
                  }
                </p>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-purple-500 hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'mm' ? 'စုစုပေါင်း ပစ္စည်းများ' : 'Total Items'}
                </CardTitle>
                <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg">
                  <Receipt className="h-4 w-4 text-purple-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-purple-600">{stats.totalItems}</div>
                <p className="text-xs text-gray-500">
                  {dateFilter === 'today'
                    ? (language === 'mm' ? 'ယနေ့' : 'Today')
                    : dateFilter === 'week'
                    ? (language === 'mm' ? 'ဒီအပတ်' : 'This week')
                    : (language === 'mm' ? 'ဒီလ' : 'This month')
                  }
                </p>
              </CardContent>
            </Card>

            <Card className="border-l-4 border-l-orange-500 hover:shadow-lg transition-shadow">
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {language === 'mm' ? 'ပျမ်းမျှ တန်ဖိုး' : 'Average Order'}
                </CardTitle>
                <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg">
                  <TrendingUp className="h-4 w-4 text-orange-600" />
                </div>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold text-orange-600">{formatPrice(stats.averageOrderValue)}</div>
                <p className="text-xs text-gray-500">
                  {dateFilter === 'today'
                    ? (language === 'mm' ? 'ယနေ့' : 'Today')
                    : dateFilter === 'week'
                    ? (language === 'mm' ? 'ဒီအပတ်' : 'This week')
                    : (language === 'mm' ? 'ဒီလ' : 'This month')
                  }
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Filters */}
        <Card className="border-0 shadow-lg bg-gradient-to-r from-gray-50 to-white dark:from-gray-800 dark:to-gray-900">
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={language === 'mm' ? 'ရောင်းအား ရှာရန်...' : 'Search sales...'}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10 border-gray-200 focus:border-emerald-500 focus:ring-emerald-500"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={dateFilter}
                  onChange={(e) => setDateFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:border-emerald-500 focus:ring-emerald-500 bg-white dark:bg-gray-800"
                  aria-label="Filter by date"
                  title="Filter by date"
                >
                  <option value="today">{language === 'mm' ? 'ယနေ့' : 'Today'}</option>
                  <option value="week">{language === 'mm' ? 'ဒီအပတ်' : 'This Week'}</option>
                  <option value="month">{language === 'mm' ? 'ဒီလ' : 'This Month'}</option>
                  <option value="all">{language === 'mm' ? 'အားလုံး' : 'All Time'}</option>
                </select>

                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:border-emerald-500 focus:ring-emerald-500 bg-white dark:bg-gray-800"
                  aria-label="Filter by status"
                  title="Filter by status"
                >
                  <option value="">{language === 'mm' ? 'အားလုံး' : 'All Status'}</option>
                  <option value="completed">{language === 'mm' ? 'ပြီးစီး' : 'Completed'}</option>
                  <option value="pending">{language === 'mm' ? 'စောင့်ဆိုင်း' : 'Pending'}</option>
                  <option value="cancelled">{language === 'mm' ? 'ပယ်ဖျက်' : 'Cancelled'}</option>
                  <option value="refunded">{language === 'mm' ? 'ပြန်အမ်း' : 'Refunded'}</option>
                </select>

                <select
                  value={paymentFilter}
                  onChange={(e) => setPaymentFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-200 rounded-lg text-sm focus:border-emerald-500 focus:ring-emerald-500 bg-white dark:bg-gray-800"
                  aria-label="Filter by payment method"
                  title="Filter by payment method"
                >
                  <option value="">{language === 'mm' ? 'အားလုံး' : 'All Payments'}</option>
                  <option value="cash">{language === 'mm' ? 'လက်ငင်း' : 'Cash'}</option>
                  <option value="card">{language === 'mm' ? 'ကတ်' : 'Card'}</option>
                  <option value="digital">{language === 'mm' ? 'ဒစ်ဂျစ်တယ်' : 'Digital'}</option>
                  <option value="bank_transfer">{language === 'mm' ? 'ဘဏ်လွှဲ' : 'Bank Transfer'}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sales List */}
        <div className="space-y-4">
          {sales.length === 0 ? (
            <Card className="border-0 shadow-lg">
              <CardContent className="text-center py-16">
                <div className="w-24 h-24 bg-gradient-to-br from-emerald-100 to-teal-100 dark:from-emerald-900/20 dark:to-teal-900/20 rounded-full flex items-center justify-center mx-auto mb-6">
                  <ShoppingCart className="h-12 w-12 text-emerald-600" />
                </div>
                <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                  {language === 'mm' ? 'ရောင်းအား မရှိပါ' : 'No sales found'}
                </h3>
                <p className="text-gray-500 mb-6 max-w-md mx-auto">
                  {language === 'mm'
                    ? 'ရွေးချယ်ထားသော filter များနှင့် ကိုက်ညီသော ရောင်းအား မရှိပါ'
                    : 'No sales match the selected filters'
                  }
                </p>
                <Button onClick={() => router.push('/pos')} className="bg-emerald-600 hover:bg-emerald-700">
                  <ShoppingCart className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ရောင်းချ စတင်ရန်' : 'Start Selling'}
                </Button>
              </CardContent>
            </Card>
          ) : (
            sales.map((sale) => (
              <Card key={sale._id} className="border-l-4 border-l-emerald-500 hover:shadow-lg transition-all duration-200 hover:border-l-emerald-600">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-4 mb-3">
                        <h3 className="text-lg font-bold text-emerald-700 dark:text-emerald-400">#{sale.saleNumber}</h3>
                        {getStatusBadge(sale.status)}
                        {getPaymentMethodBadge(sale.paymentMethod)}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm">
                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                          <div className="p-1 bg-blue-100 dark:bg-blue-900/20 rounded">
                            <User className="h-3 w-3 text-blue-600" />
                          </div>
                          <span>
                            {sale.cashier?.firstName || 'Unknown'} {sale.cashier?.lastName || 'User'}
                          </span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                          <div className="p-1 bg-purple-100 dark:bg-purple-900/20 rounded">
                            <Calendar className="h-3 w-3 text-purple-600" />
                          </div>
                          <span>{formatDate(sale.createdAt)}</span>
                        </div>
                        <div className="flex items-center gap-2 text-gray-600 dark:text-gray-400">
                          <div className="p-1 bg-orange-100 dark:bg-orange-900/20 rounded">
                            <Receipt className="h-3 w-3 text-orange-600" />
                          </div>
                          <span>{sale.totalItems} {language === 'mm' ? 'ပစ္စည်း' : 'items'}</span>
                        </div>
                        <div className="flex items-center gap-2">
                          <div className="p-1 bg-emerald-100 dark:bg-emerald-900/20 rounded">
                            <DollarSign className="h-3 w-3 text-emerald-600" />
                          </div>
                          <span className="font-bold text-lg text-emerald-700 dark:text-emerald-400">
                            {formatPrice(sale.totalAmount)}
                          </span>
                        </div>
                      </div>

                      {sale.customer?.name && (
                        <div className="mt-3 p-2 bg-gray-50 dark:bg-gray-800 rounded-lg">
                          <div className="text-sm text-gray-600 dark:text-gray-400">
                            <span className="font-medium">{language === 'mm' ? 'ဖောက်သည်:' : 'Customer:'}</span> {sale.customer.name}
                            {sale.customer?.phone && ` • ${sale.customer.phone}`}
                          </div>
                        </div>
                      )}
                    </div>

                    <div className="flex gap-2 ml-4">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/sales/${sale._id}`)}
                        className="border-emerald-200 text-emerald-700 hover:bg-emerald-50 hover:border-emerald-300"
                      >
                        <Eye className="h-4 w-4 mr-1" />
                        {language === 'mm' ? 'ကြည့်ရန်' : 'View'}
                      </Button>

                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => router.push(`/invoice?saleId=${sale._id}`)}
                        className="border-blue-200 text-blue-700 hover:bg-blue-50 hover:border-blue-300"
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        {language === 'mm' ? 'ဖိုင်ဝယ်စ်' : 'Invoice'}
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>
      </div>
    </MainLayout>
  )
}
