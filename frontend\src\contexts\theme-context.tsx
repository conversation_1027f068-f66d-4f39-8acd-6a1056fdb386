'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'
import { setCurrentCurrency as setCentralCurrency, getCurrentCurrency } from '@/lib/currency'

type Theme = 'light' | 'dark' | 'system'
type ColorScheme = 'blue' | 'green' | 'purple' | 'orange' | 'pink' | 'indigo'
type Language = 'en' | 'mm'
type Currency = 'MMK' | 'USD' | 'THB'

interface ExchangeRate {
  rate: number
  symbol: string
  name: string
  flag: string
}

interface ThemeContextType {
  theme: Theme
  colorScheme: ColorScheme
  setTheme: (theme: Theme) => void
  setColorScheme: (scheme: ColorScheme) => void
  actualTheme: 'light' | 'dark'
  toggleTheme: () => void

  // Language
  language: Language
  setLanguage: (language: Language) => void
  toggleLanguage: () => void

  // Currency
  currency: Currency
  setCurrency: (currency: Currency) => void
  toggleCurrency: () => void
  exchangeRates: Record<Currency, ExchangeRate>
  setExchangeRates: (rates: Record<Currency, ExchangeRate>) => void
  formatCurrency: (amount: number) => string
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined)

export function ThemeProvider({ children }: { children: React.ReactNode }) {
  // Check if we're on the client side
  const [mounted, setMounted] = useState(false)
  const [theme, setTheme] = useState<Theme>('light')
  const [colorScheme, setColorScheme] = useState<ColorScheme>('blue')
  const [actualTheme, setActualTheme] = useState<'light' | 'dark'>('light')
  const [language, setLanguage] = useState<Language>('mm')
  const [currency, setCurrency] = useState<Currency>('MMK')

  // Set mounted to true after component mounts
  useEffect(() => {
    setMounted(true)
  }, [])

  // Default exchange rates
  const [exchangeRates, setExchangeRates] = useState<Record<Currency, ExchangeRate>>({
    MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
    USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
    THB: { rate: 0.017, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
  })

  useEffect(() => {
    // Load saved preferences only on client side
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('bitstech-theme') as Theme
      const savedColorScheme = localStorage.getItem('bitstech-colorScheme') as ColorScheme
      const savedLanguage = localStorage.getItem('bitstech-language') as Language
      // Get currency from centralized system
      const currentCurrency = getCurrentCurrency()
      const savedCurrency = currentCurrency.code as Currency

      if (savedTheme && ['light', 'dark', 'system'].includes(savedTheme)) {
        setTheme(savedTheme)
      }

      if (savedColorScheme && ['blue', 'green', 'purple', 'orange', 'pink', 'indigo'].includes(savedColorScheme)) {
        setColorScheme(savedColorScheme)
      }

      if (savedLanguage && ['en', 'mm'].includes(savedLanguage)) {
        setLanguage(savedLanguage)
      }

      if (savedCurrency && ['MMK', 'USD', 'THB'].includes(savedCurrency)) {
        setCurrency(savedCurrency)
      }

      // Listen for currency changes from centralized system
      const handleCurrencyChange = (event: any) => {
        const newCurrency = event.detail.currency?.code || event.detail.currency
        if (newCurrency && newCurrency !== currency && ['MMK', 'USD', 'THB'].includes(newCurrency)) {
          console.log('🔄 Theme context received currency change:', newCurrency)
          // Don't update currency here to prevent loops
          // Let CurrencyProvider handle currency changes
        }
      }

      window.addEventListener('currencyChanged', handleCurrencyChange)

      // Load exchange rates
      const savedRates = localStorage.getItem('bitstech_exchange_rates')
      if (savedRates) {
        try {
          const parsedRates = JSON.parse(savedRates)
          setExchangeRates(parsedRates)
        } catch (error) {
          console.error('Error loading exchange rates:', error)
        }
      }

      // Cleanup listener on unmount
      return () => {
        window.removeEventListener('currencyChanged', handleCurrencyChange)
      }
    }
  }, [currency])

  // Save language preference
  useEffect(() => {
    if (typeof window !== 'undefined' && mounted) {
      localStorage.setItem('bitstech-language', language)
    }
  }, [language, mounted])

  // Save currency preference - NO SYNC TO PREVENT LOOPS
  useEffect(() => {
    if (typeof window !== 'undefined' && mounted) {
      localStorage.setItem('bitstech-currency', currency)
      console.log('🔄 Theme currency saved to localStorage:', currency)
    }
  }, [currency, mounted])

  // Real-time cross-tab synchronization
  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleStorageChange = (event: StorageEvent) => {
      try {
        if (event.key === 'bitstech_currency_sync' && event.newValue) {
          const data = JSON.parse(event.newValue)
          if (data.currency && data.currency !== currency && ['MMK', 'USD', 'THB'].includes(data.currency)) {
            setCurrency(data.currency)
            console.log('🔄 Currency synced from another tab:', data.currency)
          }
        }

        if (event.key === 'bitstech_language_sync' && event.newValue) {
          const data = JSON.parse(event.newValue)
          if (data.language && data.language !== language) {
            setLanguage(data.language)
            console.log('🔄 Language synced from another tab:', data.language)
          }
        }
      } catch (error) {
        console.error('Error parsing sync data:', error)
      }
    }

    // Listen for storage changes from other tabs
    window.addEventListener('storage', handleStorageChange)

    // Listen for custom events within the same tab
    const handleCurrencyChange = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      if (newCurrency && newCurrency !== currency && ['MMK', 'USD', 'THB'].includes(newCurrency)) {
        setCurrency(newCurrency)
      }
    }

    const handleLanguageChange = (event: CustomEvent) => {
      if (event.detail.language !== language) {
        // Use setTimeout to avoid setState during render
        setTimeout(() => {
          setLanguage(event.detail.language)
        }, 0)
      }
    }

    window.addEventListener('currency-changed', handleCurrencyChange as EventListener)
    window.addEventListener('language-changed', handleLanguageChange as EventListener)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
      window.removeEventListener('currency-changed', handleCurrencyChange as EventListener)
      window.removeEventListener('language-changed', handleLanguageChange as EventListener)
    }
  }, []) // Remove dependencies to prevent infinite loop

  // Listen for currency and exchange rate updates from settings
  useEffect(() => {
    const handleThemeCurrencyUpdate = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      if (newCurrency && ['MMK', 'USD', 'THB'].includes(newCurrency)) {
        setCurrency(newCurrency)
      }
    }

    const handleExchangeRatesUpdate = (event: CustomEvent) => {
      const newRates = event.detail.rates
      if (newRates) {
        setExchangeRates(newRates)
      }
    }

    if (typeof window !== 'undefined') {
      window.addEventListener('theme-currency-update', handleThemeCurrencyUpdate as EventListener)
      window.addEventListener('exchange-rates-updated', handleExchangeRatesUpdate as EventListener)

      return () => {
        window.removeEventListener('theme-currency-update', handleThemeCurrencyUpdate as EventListener)
        window.removeEventListener('exchange-rates-updated', handleExchangeRatesUpdate as EventListener)
      }
    }
  }, [])

  useEffect(() => {
    if (typeof window !== 'undefined' && mounted) {
      const root = window.document.documentElement

      // Remove previous theme classes
      root.classList.remove('light', 'dark')

      if (theme === 'system') {
        const systemTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
        root.classList.add(systemTheme)
        setActualTheme(systemTheme)
      } else {
        root.classList.add(theme)
        setActualTheme(theme)
      }

      // Save theme preference
      localStorage.setItem('bitstech-theme', theme)
    }
  }, [theme, mounted])

  useEffect(() => {
    if (typeof window !== 'undefined' && mounted) {
      const root = window.document.documentElement

      // Remove previous color scheme classes
      root.classList.remove('scheme-blue', 'scheme-green', 'scheme-purple', 'scheme-orange', 'scheme-pink', 'scheme-indigo')

      // Add new color scheme class
      root.classList.add(`scheme-${colorScheme}`)

      // Update CSS custom properties for the color scheme
      const colorSchemes = {
        blue: {
          primary: '59 130 246', // blue-500
          primaryForeground: '255 255 255',
          secondary: '239 246 255', // blue-50
          accent: '147 197 253', // blue-300
        },
        green: {
          primary: '34 197 94', // green-500
          primaryForeground: '255 255 255',
          secondary: '240 253 244', // green-50
          accent: '134 239 172', // green-300
        },
        purple: {
          primary: '147 51 234', // purple-500
          primaryForeground: '255 255 255',
          secondary: '250 245 255', // purple-50
          accent: '196 181 253', // purple-300
        },
        orange: {
          primary: '249 115 22', // orange-500
          primaryForeground: '255 255 255',
          secondary: '255 247 237', // orange-50
          accent: '253 186 116', // orange-300
        },
        pink: {
          primary: '236 72 153', // pink-500
          primaryForeground: '255 255 255',
          secondary: '253 242 248', // pink-50
          accent: '249 168 212', // pink-300
        },
        indigo: {
          primary: '99 102 241', // indigo-500
          primaryForeground: '255 255 255',
          secondary: '238 242 255', // indigo-50
          accent: '165 180 252', // indigo-300
        }
      }

      const colors = colorSchemes[colorScheme]

      root.style.setProperty('--primary', colors.primary)
      root.style.setProperty('--primary-foreground', colors.primaryForeground)
      root.style.setProperty('--secondary', colors.secondary)
      root.style.setProperty('--accent', colors.accent)

      // Save color scheme preference
      localStorage.setItem('bitstech-colorScheme', colorScheme)
    }
  }, [colorScheme, mounted])

  const toggleTheme = () => {
    setTheme(prev => prev === 'light' ? 'dark' : 'light')
  }

  const toggleLanguage = () => {
    setLanguage(prev => {
      const newLanguage = prev === 'en' ? 'mm' : 'en'

      // Broadcast language change to other tabs/components
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('language-changed', {
          detail: { language: newLanguage }
        }))

        // Broadcast via storage event for cross-tab sync
        localStorage.setItem('bitstech_language_sync', JSON.stringify({
          language: newLanguage,
          timestamp: Date.now()
        }))
      }

      return newLanguage
    })
  }

  const toggleCurrency = () => {
    setCurrency(prev => {
      const newCurrency = prev === 'MMK' ? 'USD' : prev === 'USD' ? 'THB' : 'MMK'

      // Broadcast currency change to CurrencyProvider
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('currency-toggle', {
          detail: { newCurrency }
        }))
      }

      return newCurrency
    })
  }

  const formatCurrency = (amount: number): string => {
    const rate = exchangeRates[currency]
    if (!rate) return `${amount} K`

    // Convert from MMK to target currency
    const convertedAmount = currency === 'MMK' ? amount : amount * rate.rate

    let formattedAmount: string
    if (currency === 'MMK') {
      // Format MMK without decimals
      formattedAmount = Math.round(convertedAmount).toLocaleString()
    } else {
      // Format other currencies with 2 decimals
      formattedAmount = convertedAmount.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    // Return formatted currency with symbol
    if (currency === 'MMK') {
      return `${formattedAmount} ${rate.symbol}`
    } else {
      return `${rate.symbol}${formattedAmount}`
    }
  }

  const value = {
    theme,
    colorScheme,
    setTheme,
    setColorScheme,
    actualTheme,
    toggleTheme,
    language,
    setLanguage,
    toggleLanguage,
    currency,
    setCurrency,
    toggleCurrency,
    exchangeRates,
    setExchangeRates,
    formatCurrency
  }

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  )
}

export function useTheme() {
  const context = useContext(ThemeContext)
  if (context === undefined) {
    // Return default values instead of throwing error during SSR
    if (typeof window === 'undefined') {
      return {
        theme: 'light' as Theme,
        colorScheme: 'blue' as ColorScheme,
        setTheme: () => {},
        setColorScheme: () => {},
        actualTheme: 'light' as 'light' | 'dark',
        toggleTheme: () => {},
        language: 'mm' as Language,
        setLanguage: () => {},
        toggleLanguage: () => {},
        currency: 'MMK' as Currency,
        setCurrency: () => {},
        toggleCurrency: () => {},
        exchangeRates: {
          MMK: { rate: 1, symbol: 'K', name: 'Myanmar Kyat', flag: '🇲🇲' },
          USD: { rate: 0.00048, symbol: '$', name: 'US Dollar', flag: '🇺🇸' },
          THB: { rate: 0.017, symbol: '฿', name: 'Thai Baht', flag: '🇹🇭' }
        },
        setExchangeRates: () => {},
        formatCurrency: (amount: number) => `${amount} K`
      }
    }
    throw new Error('useTheme must be used within a ThemeProvider')
  }
  return context
}
