'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import apiClient from '@/lib/api'
import {
  Package,
  Search,
  Plus,
  Edit,
  Eye,
  RefreshCw,
  Filter,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  XCircle,
  Clock,
  FileText
} from 'lucide-react'

interface StockAdjustment {
  _id: string
  adjustmentNumber: string
  type: 'increase' | 'decrease' | 'correction' | 'damage' | 'theft' | 'expired' | 'return' | 'transfer'
  reason: string
  status: 'draft' | 'pending' | 'approved' | 'applied' | 'rejected'
  totalItems: number
  totalCostImpact: number
  currency: string
  adjustmentDate: string
  createdBy: {
    _id: string
    firstName: string
    lastName: string
  }
  approvedBy?: {
    _id: string
    firstName: string
    lastName: string
  }
  items: Array<{
    product: {
      _id: string
      name: string
      sku: string
    }
    currentQuantity: number
    adjustmentQuantity: number
    newQuantity: number
    unitCost: number
    totalCost: number
  }>
}

export default function StockAdjustmentsPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const [adjustments, setAdjustments] = useState<StockAdjustment[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchAdjustments()
    }
  }, [isAuthenticated, searchQuery, typeFilter, statusFilter])

  const fetchAdjustments = async () => {
    try {
      setLoading(true)

      const params: any = {}
      if (searchQuery) params.search = searchQuery
      if (typeFilter) params.type = typeFilter
      if (statusFilter) params.status = statusFilter

      const response = await apiClient.getStockAdjustments(params)
      setAdjustments(response.data || [])
    } catch (error) {
      console.error('Error fetching adjustments:', error)

      // Fallback to mock data if API fails
      const mockAdjustments: StockAdjustment[] = [
        {
          _id: '1',
          adjustmentNumber: 'ADJ-240101-001',
          type: 'increase',
          reason: 'New stock received from supplier',
          status: 'applied',
          totalItems: 3,
          totalCostImpact: 450000,
          currency: 'MMK',
          adjustmentDate: '2024-01-01T10:00:00Z',
          createdBy: {
            _id: '1',
            firstName: 'Admin',
            lastName: 'User'
          },
          approvedBy: {
            _id: '2',
            firstName: 'Manager',
            lastName: 'User'
          },
          items: [
            {
              product: {
                _id: '1',
                name: 'ASUS VivoBook 15',
                sku: 'LAP001'
              },
              currentQuantity: 15,
              adjustmentQuantity: 10,
              newQuantity: 25,
              unitCost: 720000,
              totalCost: 7200000
            }
          ]
        },
        {
          _id: '2',
          adjustmentNumber: 'ADJ-240102-002',
          type: 'damage',
          reason: 'Damaged during shipping',
          status: 'pending',
          totalItems: 1,
          totalCostImpact: -120000,
          currency: 'MMK',
          adjustmentDate: '2024-01-02T14:30:00Z',
          createdBy: {
            _id: '1',
            firstName: 'Admin',
            lastName: 'User'
          },
          items: [
            {
              product: {
                _id: '2',
                name: 'Logitech MX Master 3S',
                sku: 'MOU001'
              },
              currentQuantity: 25,
              adjustmentQuantity: -1,
              newQuantity: 24,
              unitCost: 120000,
              totalCost: -120000
            }
          ]
        }
      ]

      // Apply filters to mock data
      let filteredAdjustments = mockAdjustments

      if (searchQuery) {
        filteredAdjustments = filteredAdjustments.filter(adj =>
          adj.adjustmentNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
          adj.reason.toLowerCase().includes(searchQuery.toLowerCase())
        )
      }

      if (typeFilter) {
        filteredAdjustments = filteredAdjustments.filter(adj => adj.type === typeFilter)
      }

      if (statusFilter) {
        filteredAdjustments = filteredAdjustments.filter(adj => adj.status === statusFilter)
      }

      setAdjustments(filteredAdjustments)
    } finally {
      setLoading(false)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'increase': return <TrendingUp className="h-4 w-4 text-green-600" />
      case 'decrease': return <TrendingDown className="h-4 w-4 text-red-600" />
      case 'damage': return <AlertTriangle className="h-4 w-4 text-orange-600" />
      case 'theft': return <XCircle className="h-4 w-4 text-red-600" />
      case 'expired': return <Clock className="h-4 w-4 text-gray-600" />
      case 'return': return <RefreshCw className="h-4 w-4 text-blue-600" />
      case 'transfer': return <Package className="h-4 w-4 text-purple-600" />
      default: return <Edit className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'approved':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Approved</Badge>
      case 'applied':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Applied</Badge>
      case 'rejected':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Rejected</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatPrice = (price: number, currency: string) => {
    return `${price.toLocaleString()} ${currency}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const text = {
    en: {
      title: 'Stock Adjustments',
      description: 'Manage inventory adjustments and stock corrections',
      newAdjustment: 'New Adjustment',
      refresh: 'Refresh',
      searchPlaceholder: 'Search adjustments...',
      allTypes: 'All Types',
      allStatuses: 'All Statuses',
      adjustmentNumber: 'Adjustment #',
      type: 'Type',
      reason: 'Reason',
      items: 'Items',
      impact: 'Cost Impact',
      status: 'Status',
      date: 'Date',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      noAdjustments: 'No adjustments found',
      noAdjustmentsDesc: 'No stock adjustments match the selected filters',
      types: {
        increase: 'Stock Increase',
        decrease: 'Stock Decrease',
        correction: 'Stock Correction',
        damage: 'Damage',
        theft: 'Theft',
        expired: 'Expired',
        return: 'Return',
        transfer: 'Transfer'
      }
    },
    mm: {
      title: 'စတော့ ပြင်ဆင်မှုများ',
      description: 'စတော့ ပြင်ဆင်မှု နှင့် ပြင်ဆင်ချက်များ စီမံခန့်ခွဲမှု',
      newAdjustment: 'ပြင်ဆင်မှု အသစ်',
      refresh: 'ပြန်လည်ရယူရန်',
      searchPlaceholder: 'ပြင်ဆင်မှုများ ရှာရန်...',
      allTypes: 'အမျိုးအစား အားလုံး',
      allStatuses: 'အခြေအနေ အားလုံး',
      adjustmentNumber: 'ပြင်ဆင်မှု နံပါတ်',
      type: 'အမျိုးအစား',
      reason: 'အကြောင်းပြချက်',
      items: 'ပစ္စည်းများ',
      impact: 'ကုန်ကျစရိတ် သက်ရောက်မှု',
      status: 'အခြေအနေ',
      date: 'ရက်စွဲ',
      actions: 'လုပ်ဆောင်ချက်များ',
      view: 'ကြည့်ရန်',
      edit: 'ပြင်ရန်',
      noAdjustments: 'ပြင်ဆင်မှု မရှိပါ',
      noAdjustmentsDesc: 'ရွေးချယ်ထားသော filter များနှင့် ကိုက်ညီသော ပြင်ဆင်မှု မရှိပါ',
      types: {
        increase: 'စတော့ တိုးခြင်း',
        decrease: 'စတော့ လျှော့ခြင်း',
        correction: 'စတော့ ပြင်ဆင်ခြင်း',
        damage: 'ပျက်စီးမှု',
        theft: 'ခိုးယူမှု',
        expired: 'သက်တမ်းကုန်',
        return: 'ပြန်အမ်း',
        transfer: 'လွှဲပြောင်း'
      }
    }
  }

  const t = text[language]

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t.title}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t.description}
            </p>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchAdjustments}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {t.refresh}
            </Button>
            <Button onClick={() => router.push('/inventory/adjustments/new')}>
              <Plus className="h-4 w-4 mr-2" />
              {t.newAdjustment}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={t.searchPlaceholder}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={typeFilter}
                  onChange={(e) => setTypeFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  aria-label="Filter by type"
                  title="Filter by type"
                >
                  <option value="">{t.allTypes}</option>
                  <option value="increase">{t.types.increase}</option>
                  <option value="decrease">{t.types.decrease}</option>
                  <option value="correction">{t.types.correction}</option>
                  <option value="damage">{t.types.damage}</option>
                  <option value="theft">{t.types.theft}</option>
                  <option value="expired">{t.types.expired}</option>
                  <option value="return">{t.types.return}</option>
                  <option value="transfer">{t.types.transfer}</option>
                </select>

                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  aria-label="Filter by status"
                  title="Filter by status"
                >
                  <option value="">{t.allStatuses}</option>
                  <option value="draft">Draft</option>
                  <option value="pending">Pending</option>
                  <option value="approved">Approved</option>
                  <option value="applied">Applied</option>
                  <option value="rejected">Rejected</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Adjustments Table */}
        <Card>
          <CardContent className="p-0">
            {!adjustments || adjustments.length === 0 ? (
              <div className="text-center py-12">
                <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {t.noAdjustments}
                </h3>
                <p className="text-gray-500">
                  {t.noAdjustmentsDesc}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.adjustmentNumber}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.type}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.reason}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.items}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.impact}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.status}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.date}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.actions}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {adjustments && adjustments.map((adjustment) => (
                      <tr key={adjustment._id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-6 py-4">
                          <div className="text-sm font-medium text-gray-900 dark:text-white">
                            {adjustment.adjustmentNumber}
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            {getTypeIcon(adjustment.type)}
                            <span className="text-sm text-gray-900 dark:text-white capitalize">
                              {t.types[adjustment.type as keyof typeof t.types] || adjustment.type}
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div className="text-sm text-gray-900 dark:text-white max-w-xs truncate">
                            {adjustment.reason}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <div className="text-sm font-medium">
                            {adjustment.totalItems}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-right">
                          <div className={`text-sm font-medium ${
                            adjustment.totalCostImpact >= 0 ? 'text-green-600' : 'text-red-600'
                          }`}>
                            {adjustment.totalCostImpact >= 0 ? '+' : ''}
                            {formatPrice(adjustment.totalCostImpact, adjustment.currency)}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-center">
                          {getStatusBadge(adjustment.status)}
                        </td>
                        <td className="px-6 py-4 text-center">
                          <div className="text-sm text-gray-500">
                            {formatDate(adjustment.adjustmentDate)}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <div className="flex justify-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => router.push(`/inventory/adjustments/${adjustment._id}`)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {adjustment.status === 'draft' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => router.push(`/inventory/adjustments/${adjustment._id}/edit`)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
