const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

const connectDB = async () => {
    try {
        const conn = await mongoose.connect(process.env.MONGODB_URI);
        console.log(`MongoDB Connected: ${conn.connection.host}`);
    } catch (error) {
        console.error('Database connection error:', error);
        process.exit(1);
    }
};

const seedUsers = async () => {
    try {
        console.log('🌱 Starting users seeding...');

        // Clear existing users (except keep admin if exists)
        const existingAdmin = await User.findOne({ email: '<EMAIL>' });
        if (!existingAdmin) {
            await User.deleteMany({});
            console.log('✅ Cleared existing users');
        } else {
            await User.deleteMany({ email: { $ne: '<EMAIL>' } });
            console.log('✅ Cleared existing users (kept admin)');
        }

        const defaultUsers = [
            {
                username: 'admin',
                email: '<EMAIL>',
                password: 'admin123',
                firstName: 'System',
                lastName: 'Administrator',
                role: 'admin',
                phone: '+95 9 111 111 111',
                address: 'Yangon, Myanmar',
                isActive: true,
                preferences: {
                    language: 'en',
                    theme: 'light',
                    currency: 'MMK',
                    notifications: {
                        email: true,
                        push: true,
                        sms: false
                    }
                }
            },
            {
                username: 'manager',
                email: '<EMAIL>',
                password: 'manager123',
                firstName: 'Store',
                lastName: 'Manager',
                role: 'manager',
                phone: '+95 9 222 222 222',
                address: 'Yangon, Myanmar',
                isActive: true,
                preferences: {
                    language: 'en',
                    theme: 'light',
                    currency: 'MMK',
                    notifications: {
                        email: true,
                        push: true,
                        sms: false
                    }
                }
            },
            {
                username: 'cashier1',
                email: '<EMAIL>',
                password: 'cashier123',
                firstName: 'Main',
                lastName: 'Cashier',
                role: 'cashier',
                phone: '+95 9 333 333 333',
                address: 'Yangon, Myanmar',
                isActive: true,
                preferences: {
                    language: 'mm',
                    theme: 'light',
                    currency: 'MMK',
                    notifications: {
                        email: false,
                        push: true,
                        sms: false
                    }
                }
            },
            {
                username: 'cashier2',
                email: '<EMAIL>',
                password: 'cashier123',
                firstName: 'Part Time',
                lastName: 'Cashier',
                role: 'cashier',
                phone: '+95 9 444 444 444',
                address: 'Yangon, Myanmar',
                isActive: false,
                preferences: {
                    language: 'mm',
                    theme: 'dark',
                    currency: 'MMK',
                    notifications: {
                        email: false,
                        push: false,
                        sms: false
                    }
                }
            },
            {
                username: 'supervisor',
                email: '<EMAIL>',
                password: 'supervisor123',
                firstName: 'Floor',
                lastName: 'Supervisor',
                role: 'manager',
                phone: '+95 9 555 555 555',
                address: 'Yangon, Myanmar',
                isActive: true,
                preferences: {
                    language: 'en',
                    theme: 'auto',
                    currency: 'USD',
                    notifications: {
                        email: true,
                        push: true,
                        sms: true
                    }
                }
            }
        ];

        const createdUsers = [];

        for (const userData of defaultUsers) {
            // Check if user already exists
            const existingUser = await User.findOne({ 
                $or: [
                    { email: userData.email },
                    { username: userData.username }
                ]
            });

            if (!existingUser) {
                const user = await User.create(userData);
                createdUsers.push(user);
                console.log(`✅ Created user: ${user.email} (${user.role})`);
            } else {
                console.log(`⚠️  User already exists: ${userData.email}`);
            }
        }

        console.log('🎉 Users seeding completed successfully!');
        
        // Display user statistics
        const totalUsers = await User.countDocuments();
        const activeUsers = await User.countDocuments({ isActive: true });
        const usersByRole = await User.aggregate([
            {
                $group: {
                    _id: '$role',
                    count: { $sum: 1 }
                }
            }
        ]);

        console.log('\n📊 User Statistics:');
        console.log(`- Total Users: ${totalUsers}`);
        console.log(`- Active Users: ${activeUsers}`);
        console.log(`- Inactive Users: ${totalUsers - activeUsers}`);
        
        console.log('\n👥 Users by Role:');
        usersByRole.forEach(role => {
            console.log(`- ${role._id}: ${role.count}`);
        });

        console.log('\n🔑 Default Login Credentials:');
        console.log('- Admin: <EMAIL> / admin123');
        console.log('- Manager: <EMAIL> / manager123');
        console.log('- Cashier: <EMAIL> / cashier123');
        console.log('- Supervisor: <EMAIL> / supervisor123');

    } catch (error) {
        console.error('❌ Error seeding users:', error);
        throw error;
    }
};

const runSeed = async () => {
    try {
        await connectDB();
        await seedUsers();
        console.log('\n✨ User seeding process completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('❌ User seeding failed:', error);
        process.exit(1);
    }
};

// Run if called directly
if (require.main === module) {
    runSeed();
}

module.exports = { seedUsers };
