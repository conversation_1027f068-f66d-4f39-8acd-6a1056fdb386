const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const {
    getCart,
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,
    checkout,
    getReceipt,
    validateCheckout
} = require('../controllers/posController');

const router = express.Router();

// Cart management routes
router.get('/cart', protect, getCart);
router.post('/cart/add', protect, addToCart);
router.put('/cart/update', protect, updateCartItem);
router.delete('/cart/remove/:productId', protect, removeFromCart);
router.delete('/cart/clear', protect, clearCart);

// Checkout routes
router.post('/checkout/validate', protect, validateCheckout);
router.post('/checkout', protect, authorize('admin', 'manager', 'cashier'), checkout);

// Receipt routes
router.get('/receipt/:saleId', protect, getReceipt);

module.exports = router;
