// Role-Based Permission System for BitsTech POS

export interface Permission {
  id: string
  name: string
  description: string
  category: string
}

export interface Role {
  id: string
  name: string
  description: string
  permissions: string[]
  level: number
}

export interface User {
  id: string
  email: string
  name: string
  role: string
  permissions?: string[]
  isActive: boolean
}

// Define all available permissions
export const PERMISSIONS: Record<string, Permission> = {
  // POS Operations
  'pos.view': {
    id: 'pos.view',
    name: 'View POS',
    description: 'Access POS interface',
    category: 'POS'
  },
  'pos.sell': {
    id: 'pos.sell',
    name: 'Process Sales',
    description: 'Process sales transactions',
    category: 'POS'
  },
  'pos.refund': {
    id: 'pos.refund',
    name: 'Process Refunds',
    description: 'Process refunds and returns',
    category: 'POS'
  },
  'pos.discount': {
    id: 'pos.discount',
    name: 'Apply Discounts',
    description: 'Apply discounts to sales',
    category: 'POS'
  },
  'pos.void': {
    id: 'pos.void',
    name: 'Void Transactions',
    description: 'Void sales transactions',
    category: 'POS'
  },

  // Product Management
  'products.view': {
    id: 'products.view',
    name: 'View Products',
    description: 'View product catalog',
    category: 'Products'
  },
  'products.create': {
    id: 'products.create',
    name: 'Create Products',
    description: 'Add new products',
    category: 'Products'
  },
  'products.edit': {
    id: 'products.edit',
    name: 'Edit Products',
    description: 'Modify existing products',
    category: 'Products'
  },
  'products.delete': {
    id: 'products.delete',
    name: 'Delete Products',
    description: 'Remove products from catalog',
    category: 'Products'
  },
  'products.pricing': {
    id: 'products.pricing',
    name: 'Manage Pricing',
    description: 'Update product prices',
    category: 'Products'
  },

  // Inventory Management
  'inventory.view': {
    id: 'inventory.view',
    name: 'View Inventory',
    description: 'View inventory levels',
    category: 'Inventory'
  },
  'inventory.adjust': {
    id: 'inventory.adjust',
    name: 'Adjust Inventory',
    description: 'Make inventory adjustments',
    category: 'Inventory'
  },
  'inventory.transfer': {
    id: 'inventory.transfer',
    name: 'Transfer Inventory',
    description: 'Transfer inventory between locations',
    category: 'Inventory'
  },

  // Customer Management
  'customers.view': {
    id: 'customers.view',
    name: 'View Customers',
    description: 'View customer information',
    category: 'Customers'
  },
  'customers.create': {
    id: 'customers.create',
    name: 'Create Customers',
    description: 'Add new customers',
    category: 'Customers'
  },
  'customers.edit': {
    id: 'customers.edit',
    name: 'Edit Customers',
    description: 'Modify customer information',
    category: 'Customers'
  },
  'customers.delete': {
    id: 'customers.delete',
    name: 'Delete Customers',
    description: 'Remove customers',
    category: 'Customers'
  },

  // Sales & Reports
  'sales.view': {
    id: 'sales.view',
    name: 'View Sales',
    description: 'View sales transactions',
    category: 'Sales'
  },
  'reports.view': {
    id: 'reports.view',
    name: 'View Reports',
    description: 'Access reports and analytics',
    category: 'Reports'
  },
  'reports.export': {
    id: 'reports.export',
    name: 'Export Reports',
    description: 'Export reports and data',
    category: 'Reports'
  },
  'analytics.view': {
    id: 'analytics.view',
    name: 'View Analytics',
    description: 'Access advanced analytics',
    category: 'Analytics'
  },
  'forecasting.view': {
    id: 'forecasting.view',
    name: 'View Forecasting',
    description: 'Access AI forecasting features',
    category: 'Analytics'
  },

  // System Administration
  'users.view': {
    id: 'users.view',
    name: 'View Users',
    description: 'View user accounts',
    category: 'Administration'
  },
  'users.create': {
    id: 'users.create',
    name: 'Create Users',
    description: 'Create new user accounts',
    category: 'Administration'
  },
  'users.edit': {
    id: 'users.edit',
    name: 'Edit Users',
    description: 'Modify user accounts',
    category: 'Administration'
  },
  'users.delete': {
    id: 'users.delete',
    name: 'Delete Users',
    description: 'Remove user accounts',
    category: 'Administration'
  },
  'settings.view': {
    id: 'settings.view',
    name: 'View Settings',
    description: 'View system settings',
    category: 'Administration'
  },
  'settings.edit': {
    id: 'settings.edit',
    name: 'Edit Settings',
    description: 'Modify system settings',
    category: 'Administration'
  },
  'hardware.manage': {
    id: 'hardware.manage',
    name: 'Manage Hardware',
    description: 'Configure hardware devices',
    category: 'Administration'
  },
  'payments.configure': {
    id: 'payments.configure',
    name: 'Configure Payments',
    description: 'Configure payment gateways',
    category: 'Administration'
  }
}

// Define roles with their permissions
export const ROLES: Record<string, Role> = {
  'admin': {
    id: 'admin',
    name: 'Administrator',
    description: 'Full system access with all permissions',
    level: 100,
    permissions: Object.keys(PERMISSIONS) // All permissions
  },
  'manager': {
    id: 'manager',
    name: 'Manager',
    description: 'Management access with most permissions except user management',
    level: 80,
    permissions: [
      // POS Operations
      'pos.view', 'pos.sell', 'pos.refund', 'pos.discount', 'pos.void',
      // Product Management
      'products.view', 'products.create', 'products.edit', 'products.pricing',
      // Inventory Management
      'inventory.view', 'inventory.adjust', 'inventory.transfer',
      // Customer Management
      'customers.view', 'customers.create', 'customers.edit',
      // Sales & Reports
      'sales.view', 'reports.view', 'reports.export', 'analytics.view', 'forecasting.view',
      // Limited Administration
      'settings.view', 'hardware.manage'
    ]
  },
  'supervisor': {
    id: 'supervisor',
    name: 'Supervisor',
    description: 'Supervisory access with sales and basic management permissions',
    level: 60,
    permissions: [
      // POS Operations
      'pos.view', 'pos.sell', 'pos.refund', 'pos.discount',
      // Product Management
      'products.view', 'products.edit',
      // Inventory Management
      'inventory.view', 'inventory.adjust',
      // Customer Management
      'customers.view', 'customers.create', 'customers.edit',
      // Sales & Reports
      'sales.view', 'reports.view', 'analytics.view'
    ]
  },
  'cashier': {
    id: 'cashier',
    name: 'Cashier',
    description: 'Basic POS operations and customer service',
    level: 40,
    permissions: [
      // POS Operations
      'pos.view', 'pos.sell', 'pos.discount',
      // Product Management
      'products.view',
      // Customer Management
      'customers.view', 'customers.create',
      // Sales
      'sales.view'
    ]
  },
  'sales_associate': {
    id: 'sales_associate',
    name: 'Sales Associate',
    description: 'Basic sales operations with limited access',
    level: 20,
    permissions: [
      // POS Operations
      'pos.view', 'pos.sell',
      // Product Management
      'products.view',
      // Customer Management
      'customers.view'
    ]
  }
}

// Permission checking functions
export class PermissionManager {
  private user: User | null = null

  setUser(user: User | null) {
    this.user = user
  }

  hasPermission(permission: string): boolean {
    if (!this.user || !this.user.isActive) return false
    
    // Get user's role
    const role = ROLES[this.user.role]
    if (!role) return false
    
    // Check if user has the permission through their role
    return role.permissions.includes(permission)
  }

  hasAnyPermission(permissions: string[]): boolean {
    return permissions.some(permission => this.hasPermission(permission))
  }

  hasAllPermissions(permissions: string[]): boolean {
    return permissions.every(permission => this.hasPermission(permission))
  }

  getUserPermissions(): string[] {
    if (!this.user) return []
    
    const role = ROLES[this.user.role]
    return role ? role.permissions : []
  }

  canAccessRoute(route: string): boolean {
    const routePermissions: Record<string, string[]> = {
      '/pos': ['pos.view'],
      '/dashboard': ['sales.view', 'reports.view'],
      '/dashboard/analytics': ['analytics.view'],
      '/dashboard/forecasting': ['forecasting.view'],
      '/products': ['products.view'],
      '/inventory': ['inventory.view'],
      '/customers': ['customers.view'],
      '/sales': ['sales.view'],
      '/reports': ['reports.view'],
      '/users': ['users.view'],
      '/settings': ['settings.view']
    }

    const requiredPermissions = routePermissions[route]
    if (!requiredPermissions) return true // No specific permissions required
    
    return this.hasAnyPermission(requiredPermissions)
  }

  getRoleLevel(): number {
    if (!this.user) return 0
    
    const role = ROLES[this.user.role]
    return role ? role.level : 0
  }

  canManageUser(targetUser: User): boolean {
    if (!this.user) return false
    
    const currentUserLevel = this.getRoleLevel()
    const targetRole = ROLES[targetUser.role]
    const targetUserLevel = targetRole ? targetRole.level : 0
    
    // Can only manage users with lower role level
    return currentUserLevel > targetUserLevel && this.hasPermission('users.edit')
  }
}

// Global permission manager instance
export const permissionManager = new PermissionManager()

// Helper function to check permissions in components
export function usePermissions() {
  return {
    hasPermission: (permission: string) => permissionManager.hasPermission(permission),
    hasAnyPermission: (permissions: string[]) => permissionManager.hasAnyPermission(permissions),
    hasAllPermissions: (permissions: string[]) => permissionManager.hasAllPermissions(permissions),
    canAccessRoute: (route: string) => permissionManager.canAccessRoute(route),
    getUserPermissions: () => permissionManager.getUserPermissions(),
    getRoleLevel: () => permissionManager.getRoleLevel()
  }
}
