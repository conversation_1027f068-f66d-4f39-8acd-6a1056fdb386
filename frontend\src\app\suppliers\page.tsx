'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  Building2,
  Search,
  Plus,
  Edit,
  Eye,
  RefreshCw,
  Filter,
  Phone,
  Mail,
  MapPin,
  Star,
  Package,
  Calendar,
  DollarSign,
  Users,
  BarChart3,
  Award,
  Trash2,
  TrendingUp
} from 'lucide-react'

interface Supplier {
  _id: string
  name: string
  code: string
  contactPerson: {
    name: string
    title?: string
    email?: string
    phone?: string
    mobile?: string
  }
  company: {
    address: {
      street?: string
      city?: string
      state?: string
      country?: string
      zipCode?: string
    }
    phone?: string
    website?: string
  }
  paymentTerms: string
  creditLimit: number
  currency: string
  categories: Array<{
    _id: string
    name: string
    color: string
  }>
  rating: number
  isActive: boolean
  lastOrderDate?: string
  totalOrders: number
  totalOrderValue: number
  createdAt: string
}

export default function SuppliersPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [suppliers, setSuppliers] = useState<Supplier[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [showComparison, setShowComparison] = useState(false)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchSuppliers()
    }
  }, [isAuthenticated, searchQuery, statusFilter])

  // Listen for currency changes and refresh data
  useEffect(() => {
    const handleCurrencyChange = () => {
      console.log('📊 Suppliers currency change detected, refreshing data')
      // No need to refetch data, just trigger re-render with new currency formatting
      // The formatCurrency function will handle the new currency
    }

    // Listen for currency change events
    window.addEventListener('currency-changed', handleCurrencyChange)
    window.addEventListener('currencyChanged', handleCurrencyChange)

    return () => {
      window.removeEventListener('currency-changed', handleCurrencyChange)
      window.removeEventListener('currencyChanged', handleCurrencyChange)
    }
  }, [])

  const fetchSuppliers = async () => {
    try {
      setLoading(true)
      console.log('🔄 Loading suppliers from real database...')

      // Build API parameters
      const params: any = {}
      if (searchQuery) params.search = searchQuery
      if (statusFilter) params.status = statusFilter

      // Fetch real suppliers from MongoDB API
      const response = await apiClient.getSuppliers(params)

      if (response.success) {
        const suppliersData = Array.isArray(response.data) ? response.data : []
        setSuppliers(suppliersData)
        console.log('✅ Real suppliers loaded:', suppliersData.length, 'suppliers')
      } else {
        console.error('❌ Failed to load suppliers:', response.error)
        setSuppliers([])
      }
    } catch (error) {
      console.error('❌ Error loading suppliers from database:', error)
      // No fallback to mock data - force real database usage
      setSuppliers([])
    } finally {
      setLoading(false)
    }
  }

  const getPaymentTermsText = (terms: string) => {
    const termsMap: Record<string, string> = {
      'cash': 'Cash',
      'net_15': 'Net 15 Days',
      'net_30': 'Net 30 Days',
      'net_45': 'Net 45 Days',
      'net_60': 'Net 60 Days',
      'net_90': 'Net 90 Days',
      'custom': 'Custom Terms'
    }
    return termsMap[terms] || terms
  }

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
      />
    ))
  }

  const formatPrice = (price: number | undefined | null) => {
    if (typeof price !== 'number' || isNaN(price) || price === null || price === undefined) {
      return formatCurrency(0)
    }
    return formatCurrency(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  // Calculate dynamic rating based on performance
  const calculateDynamicRating = (supplier: any) => {
    let rating = 3; // Base rating

    // Order frequency bonus (max +1.5)
    if (supplier.totalOrders >= 50) rating += 1.5;
    else if (supplier.totalOrders >= 30) rating += 1.0;
    else if (supplier.totalOrders >= 15) rating += 0.5;

    // Order value bonus (max +0.5)
    if (supplier.totalOrderValue >= 100000000) rating += 0.5; // 100M MMK
    else if (supplier.totalOrderValue >= 50000000) rating += 0.3;
    else if (supplier.totalOrderValue >= 20000000) rating += 0.1;

    // Recent activity bonus/penalty
    if (supplier.lastOrderDate) {
      const daysSinceLastOrder = Math.floor(
        (new Date().getTime() - new Date(supplier.lastOrderDate).getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysSinceLastOrder <= 30) rating += 0.3; // Recent orders
      else if (daysSinceLastOrder <= 90) rating += 0.1;
      else if (daysSinceLastOrder > 365) rating -= 0.5; // Very old
    }

    // Status penalty
    if (!supplier.isActive) rating -= 1.0;

    return Math.max(1, Math.min(5, Math.round(rating * 2) / 2)); // Round to nearest 0.5
  }

  // View supplier details function
  const handleViewSupplier = (supplierId: string) => {
    router.push(`/suppliers/${supplierId}`)
  }

  // Edit supplier function
  const handleEditSupplier = (supplierId: string) => {
    router.push(`/suppliers/${supplierId}/edit`)
  }

  // Delete supplier function
  const handleDeleteSupplier = async (supplierId: string) => {
    const supplier = suppliers.find(s => s._id === supplierId);
    if (!supplier) return;

    const confirmMessage = language === 'mm'
      ? `${supplier.name} ကို ဖျက်မည်လား? ဤလုပ်ဆောင်ချက်ကို ပြန်ပြင်၍မရပါ။`
      : `Delete ${supplier.name}? This action cannot be undone.`;

    if (window.confirm(confirmMessage)) {
      try {
        const response = await apiClient.deleteSupplier(supplierId);

        if (response.success) {
          // Refresh suppliers list
          await fetchSuppliers();
          alert(language === 'mm' ? 'ပေးသွင်းသူ ဖျက်ပြီးပါပြီ' : 'Supplier deleted successfully!');
        } else {
          alert('Failed to delete supplier: ' + (response.error || 'Unknown error'));
        }
      } catch (error) {
        console.error('Error deleting supplier:', error);
        alert('Error deleting supplier. Please try again.');
      }
    }
  }

  // Compare suppliers function
  const getSupplierComparison = () => {
    if (!suppliers || suppliers.length === 0) return [];

    return suppliers.map(supplier => ({
      ...supplier,
      dynamicRating: calculateDynamicRating(supplier),
      performanceScore: (supplier.totalOrders * 0.3) +
                       (supplier.totalOrderValue / 1000000 * 0.4) +
                       (supplier.isActive ? 30 : 0),
      avgOrderValue: supplier.totalOrders > 0 ? supplier.totalOrderValue / supplier.totalOrders : 0
    })).sort((a, b) => b.performanceScore - a.performanceScore);
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const text = {
    en: {
      title: 'Suppliers',
      description: 'Manage your suppliers and vendor relationships',
      newSupplier: 'New Supplier',
      refresh: 'Refresh',
      searchPlaceholder: 'Search suppliers...',
      allStatuses: 'All Statuses',
      active: 'Active',
      inactive: 'Inactive',
      supplierCode: 'Code',
      supplierName: 'Supplier',
      contact: 'Contact',
      categories: 'Categories',
      paymentTerms: 'Payment Terms',
      rating: 'Rating',
      lastOrder: 'Last Order',
      totalOrders: 'Orders',
      totalValue: 'Total Value',
      status: 'Status',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      delete: 'Delete',
      compare: 'Compare',
      performance: 'Performance',
      dynamicRating: 'Smart Rating',
      avgOrderValue: 'Avg Order',
      noSuppliers: 'No suppliers found',
      noSuppliersDesc: 'No suppliers match the selected filters',
      orders: 'orders',
      creditLimit: 'Credit Limit'
    },
    mm: {
      title: 'ပေးသွင်းသူများ',
      description: 'ပေးသွင်းသူများ နှင့် ရောင်းချသူ ဆက်ဆံရေး စီမံခန့်ခွဲမှု',
      newSupplier: 'ပေးသွင်းသူ အသစ်',
      refresh: 'ပြန်လည်ရယူရန်',
      searchPlaceholder: 'ပေးသွင်းသူများ ရှာရန်...',
      allStatuses: 'အခြေအနေ အားလုံး',
      active: 'အသုံးပြုနေသော',
      inactive: 'အသုံးမပြုတော့သော',
      supplierCode: 'ကုဒ်',
      supplierName: 'ပေးသွင်းသူ',
      contact: 'ဆက်သွယ်ရန်',
      categories: 'အမျိုးအစားများ',
      paymentTerms: 'ငွေပေးချေမှု စည်းကမ်းများ',
      rating: 'အဆင့်သတ်မှတ်ချက်',
      lastOrder: 'နောက်ဆုံး အမှာစာ',
      totalOrders: 'အမှာစာများ',
      totalValue: 'စုစုပေါင်း တန်ဖိုး',
      status: 'အခြေအနေ',
      actions: 'လုပ်ဆောင်ချက်များ',
      view: 'ကြည့်ရန်',
      edit: 'ပြင်ရန်',
      delete: 'ဖျက်ရန်',
      compare: 'နှိုင်းယှဉ်ရန်',
      performance: 'စွမ်းဆောင်ရည်',
      dynamicRating: 'စမတ် အဆင့်',
      avgOrderValue: 'ပျမ်းမျှ အမှာ',
      noSuppliers: 'ပေးသွင်းသူ မရှိပါ',
      noSuppliersDesc: 'ရွေးချယ်ထားသော filter များနှင့် ကိုက်ညီသော ပေးသွင်းသူ မရှိပါ',
      orders: 'အမှာစာများ',
      creditLimit: 'ခရက်ဒစ် ကန့်သတ်ချက်'
    }
  }

  const t = text[language]



  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t.title}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t.description}
            </p>
          </div>
          
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchSuppliers}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {t.refresh}
            </Button>
            <Button
              variant="outline"
              onClick={() => setShowComparison(!showComparison)}
              className={showComparison ? 'bg-blue-50 border-blue-200' : ''}
            >
              <BarChart3 className="h-4 w-4 mr-2" />
              {t.compare}
            </Button>
            <Button onClick={() => router.push('/suppliers/new')}>
              <Plus className="h-4 w-4 mr-2" />
              {t.newSupplier}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={t.searchPlaceholder}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                  aria-label="Filter by status"
                  title="Filter by status"
                >
                  <option value="">{t.allStatuses}</option>
                  <option value="active">{t.active}</option>
                  <option value="inactive">{t.inactive}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Comparison */}
        {showComparison && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                {t.performance} {t.compare}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ပေးသွင်းသူများ၏ စွမ်းဆောင်ရည် နှိုင်းယှဉ်ချက် (အမှာစာ အရေအတွက်၊ တန်ဖိုး၊ လတ်တလော လုပ်ဆောင်မှုများ အပေါ် အခြေခံ၍)'
                  : 'Supplier performance comparison based on order frequency, value, and recent activity'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-4 py-2 text-left font-medium text-gray-500">
                        {language === 'mm' ? 'အဆင့်' : 'Rank'}
                      </th>
                      <th className="px-4 py-2 text-left font-medium text-gray-500">
                        {t.supplierName}
                      </th>
                      <th className="px-4 py-2 text-center font-medium text-gray-500">
                        {t.dynamicRating}
                      </th>
                      <th className="px-4 py-2 text-center font-medium text-gray-500">
                        {t.totalOrders}
                      </th>
                      <th className="px-4 py-2 text-right font-medium text-gray-500">
                        {t.totalValue}
                      </th>
                      <th className="px-4 py-2 text-right font-medium text-gray-500">
                        {t.avgOrderValue}
                      </th>
                      <th className="px-4 py-2 text-center font-medium text-gray-500">
                        {language === 'mm' ? 'စွမ်းဆောင်ရည်' : 'Score'}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {getSupplierComparison().slice(0, 10).map((supplier, index) => (
                      <tr key={supplier._id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-4 py-3">
                          <div className="flex items-center gap-2">
                            <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                              index === 0 ? 'bg-yellow-100 text-yellow-800' :
                              index === 1 ? 'bg-gray-100 text-gray-800' :
                              index === 2 ? 'bg-orange-100 text-orange-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {index + 1}
                            </span>
                            {index < 3 && (
                              <Award className={`h-4 w-4 ${
                                index === 0 ? 'text-yellow-500' :
                                index === 1 ? 'text-gray-500' :
                                'text-orange-500'
                              }`} />
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div>
                            <div className="font-medium text-gray-900 dark:text-white">
                              {supplier.name}
                            </div>
                            <div className="text-xs text-gray-500">{supplier.code}</div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <div className="flex justify-center items-center gap-1">
                            <span className="font-bold text-blue-600">{supplier.dynamicRating}</span>
                            <div className="flex gap-0.5">
                              {Array.from({ length: 5 }, (_, i) => (
                                <Star
                                  key={i}
                                  className={`h-3 w-3 ${
                                    i < Math.floor(supplier.dynamicRating)
                                      ? 'text-yellow-400 fill-current'
                                      : i < supplier.dynamicRating
                                        ? 'text-yellow-400 fill-current opacity-50'
                                        : 'text-gray-300'
                                  }`}
                                />
                              ))}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-center font-medium">
                          {supplier.totalOrders}
                        </td>
                        <td className="px-4 py-3 text-right font-medium">
                          {formatPrice(supplier.totalOrderValue)}
                        </td>
                        <td className="px-4 py-3 text-right font-medium">
                          {formatPrice(supplier.avgOrderValue)}
                        </td>
                        <td className="px-4 py-3 text-center">
                          <div className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${
                            supplier.performanceScore >= 80 ? 'bg-green-100 text-green-800' :
                            supplier.performanceScore >= 60 ? 'bg-yellow-100 text-yellow-800' :
                            supplier.performanceScore >= 40 ? 'bg-orange-100 text-orange-800' :
                            'bg-red-100 text-red-800'
                          }`}>
                            {Math.round(supplier.performanceScore)}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Suppliers Table */}
        <Card>
          <CardContent className="p-0">
            {!suppliers || suppliers.length === 0 ? (
              <div className="text-center py-12">
                <Building2 className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {t.noSuppliers}
                </h3>
                <p className="text-gray-500">
                  {t.noSuppliersDesc}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.supplierName}
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.contact}
                      </th>
                      <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.categories}
                      </th>
                      <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.rating}
                      </th>
                      <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.totalOrders}
                      </th>
                      <th className="px-3 py-2 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.totalValue}
                      </th>
                      <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.status}
                      </th>
                      <th className="px-3 py-2 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.actions}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {suppliers && suppliers.map((supplier) => (
                      <tr key={supplier._id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-3 py-2">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {supplier.name}
                            </div>
                            <div className="text-xs text-gray-500">
                              {supplier.code}
                            </div>
                          </div>
                        </td>
                        <td className="px-3 py-2">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {supplier.contactPerson.name}
                            </div>
                            {supplier.contactPerson.phone && (
                              <div className="text-xs text-gray-400">
                                {supplier.contactPerson.phone}
                              </div>
                            )}
                          </div>
                        </td>
                        <td className="px-3 py-2">
                          <div className="flex flex-wrap gap-1">
                            {supplier.categories.slice(0, 1).map((category) => (
                              <Badge
                                key={category._id}
                                variant="outline"
                                className="text-xs"
                                title={category.name}
                              >
                                {category.name}
                              </Badge>
                            ))}
                            {supplier.categories.length > 1 && (
                              <Badge variant="outline" className="text-xs">
                                +{supplier.categories.length - 1}
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className="px-3 py-2 text-center">
                          <div className="flex justify-center gap-1">
                            {getRatingStars(calculateDynamicRating(supplier))}
                          </div>
                          <div className="text-xs text-gray-500">
                            {calculateDynamicRating(supplier)}
                          </div>
                        </td>
                        <td className="px-3 py-2 text-center">
                          <div className="text-sm font-medium">
                            {supplier.totalOrders}
                          </div>
                        </td>
                        <td className="px-3 py-2 text-right">
                          <div className="text-sm font-medium">
                            {formatPrice(supplier.totalOrderValue)}
                          </div>
                        </td>
                        <td className="px-3 py-2 text-center">
                          <Badge
                            variant="outline"
                            className={supplier.isActive ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}
                          >
                            {supplier.isActive ? t.active : t.inactive}
                          </Badge>
                        </td>
                        <td className="px-3 py-2 text-center">
                          <div className="flex justify-center gap-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewSupplier(supplier._id)}
                              title={t.view}
                              className="hover:bg-blue-50"
                            >
                              <Eye className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleEditSupplier(supplier._id)}
                              title={t.edit}
                              className="hover:bg-green-50"
                            >
                              <Edit className="h-3 w-3" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteSupplier(supplier._id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                              title={t.delete}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
