'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { 
  WifiOff, 
  RefreshCw, 
  ShoppingCart, 
  Package, 
  AlertCircle,
  CheckCircle,
  Clock
} from 'lucide-react'
import { useTheme } from '@/contexts/theme-context'

export default function OfflinePage() {
  const router = useRouter()
  const { language } = useTheme()
  const [isOnline, setIsOnline] = useState(false)
  const [retryCount, setRetryCount] = useState(0)
  const [lastSync, setLastSync] = useState<Date | null>(null)

  // Check online status
  useEffect(() => {
    const updateOnlineStatus = () => {
      setIsOnline(navigator.onLine)
      if (navigator.onLine) {
        setLastSync(new Date())
      }
    }

    window.addEventListener('online', updateOnlineStatus)
    window.addEventListener('offline', updateOnlineStatus)
    updateOnlineStatus()

    return () => {
      window.removeEventListener('online', updateOnlineStatus)
      window.removeEventListener('offline', updateOnlineStatus)
    }
  }, [])

  const handleRetry = async () => {
    setRetryCount(prev => prev + 1)
    
    try {
      const response = await fetch('/api/health', { 
        method: 'GET',
        cache: 'no-cache'
      })
      
      if (response.ok) {
        router.push('/')
      }
    } catch (error) {
      console.log('Still offline, retry failed')
    }
  }

  const translations = {
    en: {
      title: 'You\'re Offline',
      subtitle: 'No internet connection detected',
      description: 'Don\'t worry! You can still use some features of BitsTech POS while offline.',
      retry: 'Try Again',
      offlineFeatures: 'Available Offline Features',
      pos: {
        title: 'POS Terminal',
        desc: 'Process sales and transactions (will sync when online)'
      },
      inventory: {
        title: 'View Inventory',
        desc: 'Check product stock levels and information'
      },
      reports: {
        title: 'Cached Reports',
        desc: 'View previously loaded reports and data'
      },
      status: {
        online: 'Back Online!',
        offline: 'Offline Mode',
        syncing: 'Syncing data...',
        lastSync: 'Last synced'
      },
      tips: {
        title: 'Offline Tips',
        items: [
          'Sales transactions will be saved and synced when connection returns',
          'Product information is cached for offline viewing',
          'Reports data is available from your last sync',
          'All offline actions will be processed when you\'re back online'
        ]
      }
    },
    mm: {
      title: 'အင်တာနက် မရှိပါ',
      subtitle: 'အင်တာနက် ချိတ်ဆက်မှု မတွေ့ရှိပါ',
      description: 'စိတ်မပူပါနှင့်! အင်တာနက် မရှိချိန်မှာလည်း BitsTech POS ရဲ့ အချို့လုပ်ဆောင်ချက်များကို အသုံးပြုနိုင်ပါသည်။',
      retry: 'ပြန်လည်ကြိုးစားရန်',
      offlineFeatures: 'အင်တာနက်မရှိချိန် အသုံးပြုနိုင်သော လုပ်ဆောင်ချက်များ',
      pos: {
        title: 'POS စနစ်',
        desc: 'ရောင်းချမှုများ လုပ်ဆောင်နိုင်သည် (အင်တာနက်ပြန်ရရှိသောအခါ sync လုပ်မည်)'
      },
      inventory: {
        title: 'ကုန်ပစ္စည်းများ ကြည့်ရှုရန်',
        desc: 'ကုန်ပစ္စည်း အရေအတွက်နှင့် အချက်အလက်များ စစ်ဆေးနိုင်သည်'
      },
      reports: {
        title: 'သိမ်းဆည်းထားသော အစီရင်ခံစာများ',
        desc: 'ယခင်က ဖွင့်ထားသော အစီရင်ခံစာများနှင့် ဒေတာများ ကြည့်ရှုနိုင်သည်'
      },
      status: {
        online: 'အင်တာနက် ပြန်ရရှိပြီ!',
        offline: 'အင်တာနက်မရှိ အခြေအနေ',
        syncing: 'ဒေတာ sync လုပ်နေသည်...',
        lastSync: 'နောက်ဆုံး sync လုပ်ခဲ့သည်'
      },
      tips: {
        title: 'အင်တာနက်မရှိချိန် အကြံပြုချက်များ',
        items: [
          'ရောင်းချမှုများကို သိမ်းဆည်းပြီး အင်တာနက်ပြန်ရရှိသောအခါ sync လုပ်မည်',
          'ကုန်ပစ္စည်း အချက်အလက်များကို အင်တာနက်မရှိချိန်မှာ ကြည့်ရှုနိုင်သည်',
          'နောက်ဆုံး sync လုပ်ခဲ့သော အစီရင်ခံစာ ဒေတာများ ရရှိနိုင်သည်',
          'အင်တာနက်မရှိချိန်က လုပ်ဆောင်ချက်များကို အင်တာနက်ပြန်ရရှိသောအခါ လုပ်ဆောင်မည်'
        ]
      }
    }
  }

  const t = translations[language] || translations.en

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="max-w-2xl w-full space-y-8">
        {/* Status Header */}
        <div className="text-center">
          <div className="mx-auto mb-6 w-24 h-24 bg-muted rounded-full flex items-center justify-center">
            {isOnline ? (
              <CheckCircle className="h-12 w-12 text-green-600" />
            ) : (
              <WifiOff className="h-12 w-12 text-muted-foreground" />
            )}
          </div>
          
          <h1 className={`text-3xl font-bold text-foreground mb-2 ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
            {isOnline ? t.status.online : t.title}
          </h1>
          
          <p className={`text-lg text-muted-foreground mb-6 ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
            {isOnline ? t.status.syncing : t.subtitle}
          </p>

          {!isOnline && (
            <p className={`text-muted-foreground mb-8 ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
              {t.description}
            </p>
          )}

          {/* Retry Button */}
          <Button 
            onClick={handleRetry}
            disabled={isOnline}
            className="mb-8"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${retryCount > 0 ? 'animate-spin' : ''}`} />
            <span className={language === 'mm' ? 'font-myanmar' : ''}>{t.retry}</span>
          </Button>

          {/* Last Sync Info */}
          {lastSync && (
            <div className="flex items-center justify-center text-sm text-muted-foreground mb-8">
              <Clock className="h-4 w-4 mr-2" />
              <span className={language === 'mm' ? 'font-myanmar' : ''}>
                {t.status.lastSync}: {lastSync.toLocaleTimeString()}
              </span>
            </div>
          )}
        </div>

        {/* Offline Features */}
        {!isOnline && (
          <Card>
            <CardHeader>
              <CardTitle className={`text-center ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
                {t.offlineFeatures}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4">
                <div className="flex items-start space-x-3 p-4 bg-muted/50 rounded-lg">
                  <ShoppingCart className="h-6 w-6 text-blue-600 mt-1" />
                  <div>
                    <h3 className={`font-semibold text-foreground ${language === 'mm' ? 'font-myanmar' : ''}`}>
                      {t.pos.title}
                    </h3>
                    <p className={`text-sm text-muted-foreground ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
                      {t.pos.desc}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-4 bg-muted/50 rounded-lg">
                  <Package className="h-6 w-6 text-green-600 mt-1" />
                  <div>
                    <h3 className={`font-semibold text-foreground ${language === 'mm' ? 'font-myanmar' : ''}`}>
                      {t.inventory.title}
                    </h3>
                    <p className={`text-sm text-muted-foreground ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
                      {t.inventory.desc}
                    </p>
                  </div>
                </div>

                <div className="flex items-start space-x-3 p-4 bg-muted/50 rounded-lg">
                  <AlertCircle className="h-6 w-6 text-orange-600 mt-1" />
                  <div>
                    <h3 className={`font-semibold text-foreground ${language === 'mm' ? 'font-myanmar' : ''}`}>
                      {t.reports.title}
                    </h3>
                    <p className={`text-sm text-muted-foreground ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
                      {t.reports.desc}
                    </p>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Tips */}
        <Card>
          <CardHeader>
            <CardTitle className={`text-center ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
              {t.tips.title}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="space-y-3">
              {t.tips.items.map((tip, index) => (
                <li key={index} className="flex items-start space-x-3">
                  <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                  <span className={`text-sm text-muted-foreground ${language === 'mm' ? 'font-myanmar leading-relaxed' : ''}`}>
                    {tip}
                  </span>
                </li>
              ))}
            </ul>
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-center space-x-4">
          <Button variant="outline" onClick={() => router.push('/pos')}>
            <ShoppingCart className="h-4 w-4 mr-2" />
            <span className={language === 'mm' ? 'font-myanmar' : ''}>
              {language === 'mm' ? 'POS သို့' : 'Go to POS'}
            </span>
          </Button>
          <Button variant="outline" onClick={() => router.push('/dashboard')}>
            <Package className="h-4 w-4 mr-2" />
            <span className={language === 'mm' ? 'font-myanmar' : ''}>
              {language === 'mm' ? 'ဒက်ရှ်ဘုတ်သို့' : 'Dashboard'}
            </span>
          </Button>
        </div>
      </div>
    </div>
  )
}
