// Myanmar number and date formatting utilities

// Myanmar numerals mapping
const myanmarNumerals: { [key: string]: string } = {
  '0': '၀',
  '1': '၁',
  '2': '၂',
  '3': '၃',
  '4': '၄',
  '5': '၅',
  '6': '၆',
  '7': '၇',
  '8': '၈',
  '9': '၉'
}

const englishNumerals: { [key: string]: string } = {
  '၀': '0',
  '၁': '1',
  '၂': '2',
  '၃': '3',
  '၄': '4',
  '၅': '5',
  '၆': '6',
  '၇': '7',
  '၈': '8',
  '၉': '9'
}

// Myanmar month names
const myanmarMonths = [
  'တန်ခူး', 'ကဆုန်', 'နယုန်', 'ဝါဆို', 'ဝါခေါင်', 'တော်သလင်း',
  'တန်ဆောင်မုန်း', 'တပေါင်း', 'တပေါင်းလဆန်း', 'နတ်တော်', 'ပြာသို', 'တပို့တွဲ'
]

const myanmarDays = [
  'တနင်္ဂနွေ', 'တနင်္လာ', 'အင်္ဂါ', 'ဗုဒ္ဓဟူး', 'ကြာသပတေး', 'သောကြာ', 'စနေ'
]

// Convert English numerals to Myanmar numerals
export function toMyanmarNumerals(text: string | number): string {
  const str = text.toString()
  return str.replace(/[0-9]/g, (digit) => myanmarNumerals[digit] || digit)
}

// Convert Myanmar numerals to English numerals
export function toEnglishNumerals(text: string): string {
  return text.replace(/[၀-၉]/g, (digit) => englishNumerals[digit] || digit)
}

// Format number with Myanmar numerals and proper separators
export function formatMyanmarNumber(
  number: number,
  options: {
    useMyanmar?: boolean
    decimals?: number
    currency?: string
    showCurrency?: boolean
  } = {}
): string {
  const {
    useMyanmar = true,
    decimals = 0,
    currency = 'MMK',
    showCurrency = false
  } = options

  // Format with commas
  const formatted = new Intl.NumberFormat('en-US', {
    minimumFractionDigits: decimals,
    maximumFractionDigits: decimals
  }).format(number)

  // Convert to Myanmar numerals if requested
  const result = useMyanmar ? toMyanmarNumerals(formatted) : formatted

  // Add currency if requested
  if (showCurrency) {
    if (useMyanmar) {
      switch (currency) {
        case 'MMK':
          return `${result} ကျပ်`
        case 'USD':
          return `${result} ဒေါ်လာ`
        case 'THB':
          return `${result} ဘတ်`
        default:
          return `${result} ${currency}`
      }
    } else {
      return `${result} ${currency}`
    }
  }

  return result
}

// Format currency specifically
export function formatMyanmarCurrency(
  amount: number,
  currency: string = 'MMK',
  useMyanmar: boolean = true
): string {
  return formatMyanmarNumber(amount, {
    useMyanmar,
    decimals: currency === 'MMK' ? 0 : 2,
    currency,
    showCurrency: true
  })
}

// Format date in Myanmar style
export function formatMyanmarDate(
  date: Date,
  options: {
    useMyanmar?: boolean
    format?: 'short' | 'medium' | 'long' | 'full'
    showDay?: boolean
  } = {}
): string {
  const {
    useMyanmar = true,
    format = 'medium',
    showDay = false
  } = options

  if (!useMyanmar) {
    return new Intl.DateTimeFormat('en-US', {
      year: 'numeric',
      month: format === 'short' ? 'numeric' : 'long',
      day: 'numeric',
      weekday: showDay ? 'long' : undefined
    }).format(date)
  }

  const day = date.getDate()
  const month = date.getMonth()
  const year = date.getFullYear()
  const dayOfWeek = date.getDay()

  let result = ''

  // Add day of week if requested
  if (showDay) {
    result += myanmarDays[dayOfWeek] + '၊ '
  }

  // Format based on style
  switch (format) {
    case 'short':
      result += `${toMyanmarNumerals(day)}/${toMyanmarNumerals(month + 1)}/${toMyanmarNumerals(year)}`
      break
    case 'medium':
      result += `${toMyanmarNumerals(day)} ${myanmarMonths[month]} ${toMyanmarNumerals(year)}`
      break
    case 'long':
      result += `${toMyanmarNumerals(day)} ${myanmarMonths[month]} ${toMyanmarNumerals(year)} ခုနှစ်`
      break
    case 'full':
      result += `${myanmarDays[dayOfWeek]}၊ ${toMyanmarNumerals(day)} ${myanmarMonths[month]} ${toMyanmarNumerals(year)} ခုနှစ်`
      break
  }

  return result
}

// Format time in Myanmar style
export function formatMyanmarTime(
  date: Date,
  useMyanmar: boolean = true,
  use24Hour: boolean = false
): string {
  if (!useMyanmar) {
    return new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: !use24Hour
    }).format(date)
  }

  const hours = date.getHours()
  const minutes = date.getMinutes()

  if (use24Hour) {
    return `${toMyanmarNumerals(hours.toString().padStart(2, '0'))}:${toMyanmarNumerals(minutes.toString().padStart(2, '0'))}`
  } else {
    const hour12 = hours % 12 || 12
    const period = hours >= 12 ? 'ညနေ' : 'နံနက်'
    return `${period} ${toMyanmarNumerals(hour12)}:${toMyanmarNumerals(minutes.toString().padStart(2, '0'))}`
  }
}

// Format datetime in Myanmar style
export function formatMyanmarDateTime(
  date: Date,
  options: {
    useMyanmar?: boolean
    dateFormat?: 'short' | 'medium' | 'long'
    showTime?: boolean
    use24Hour?: boolean
  } = {}
): string {
  const {
    useMyanmar = true,
    dateFormat = 'medium',
    showTime = true,
    use24Hour = false
  } = options

  let result = formatMyanmarDate(date, { useMyanmar, format: dateFormat })

  if (showTime) {
    const timeStr = formatMyanmarTime(date, useMyanmar, use24Hour)
    result += useMyanmar ? `၊ ${timeStr}` : `, ${timeStr}`
  }

  return result
}

// Parse Myanmar numerals to number
export function parseMyanmarNumber(text: string): number {
  const englishText = toEnglishNumerals(text.replace(/,/g, ''))
  return parseFloat(englishText) || 0
}

// Format percentage
export function formatMyanmarPercentage(
  value: number,
  useMyanmar: boolean = true,
  decimals: number = 1
): string {
  const formatted = (value * 100).toFixed(decimals)
  const result = useMyanmar ? toMyanmarNumerals(formatted) : formatted
  return useMyanmar ? `${result}%` : `${result}%`
}

// Format file size
export function formatMyanmarFileSize(
  bytes: number,
  useMyanmar: boolean = true
): string {
  const units = useMyanmar 
    ? ['ဘိုက်', 'ကေဘီ', 'မေဘီ', 'ဂီဘီ', 'တီဘီ']
    : ['B', 'KB', 'MB', 'GB', 'TB']
  
  let size = bytes
  let unitIndex = 0
  
  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024
    unitIndex++
  }
  
  const formatted = size.toFixed(unitIndex === 0 ? 0 : 1)
  const result = useMyanmar ? toMyanmarNumerals(formatted) : formatted
  
  return `${result} ${units[unitIndex]}`
}

// Format duration (in seconds)
export function formatMyanmarDuration(
  seconds: number,
  useMyanmar: boolean = true
): string {
  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)
  const secs = seconds % 60

  if (useMyanmar) {
    if (hours > 0) {
      return `${toMyanmarNumerals(hours)} နာရီ ${toMyanmarNumerals(minutes)} မိနစ်`
    } else if (minutes > 0) {
      return `${toMyanmarNumerals(minutes)} မိနစ် ${toMyanmarNumerals(secs)} စက္ကန့်`
    } else {
      return `${toMyanmarNumerals(secs)} စက္ကန့်`
    }
  } else {
    if (hours > 0) {
      return `${hours}h ${minutes}m`
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`
    } else {
      return `${secs}s`
    }
  }
}

// Utility to check if text contains Myanmar numerals
export function containsMyanmarNumerals(text: string): boolean {
  return /[၀-၉]/.test(text)
}

// Utility to check if text contains English numerals
export function containsEnglishNumerals(text: string): boolean {
  return /[0-9]/.test(text)
}

// Mixed numeral converter (auto-detect and convert)
export function convertNumerals(
  text: string,
  targetType: 'myanmar' | 'english' = 'myanmar'
): string {
  if (targetType === 'myanmar') {
    return toMyanmarNumerals(text)
  } else {
    return toEnglishNumerals(text)
  }
}
