'use client'

import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'

interface SystemArchitectureProps {
  language?: 'en' | 'mm'
}

export function SystemArchitecture({ language = 'en' }: SystemArchitectureProps) {
  const t = {
    en: {
      title: 'BitsTech POS System Architecture',
      frontend: 'Frontend Layer',
      backend: 'Backend Layer',
      database: 'Database Layer',
      external: 'External Services',
      components: 'Components',
      technologies: 'Technologies',
      description: 'System architecture overview showing all layers and components'
    },
    mm: {
      title: 'BitsTech POS စနစ် ဗိသုကာ',
      frontend: 'Frontend အလွှာ',
      backend: 'Backend အလွှာ',
      database: 'Database အလွှာ',
      external: 'ပြင်ပ ဝန်ဆောင်မှုများ',
      components: 'အစိတ်အပိုင်းများ',
      technologies: 'နည်းပညာများ',
      description: 'အလွှာများနှင့် အစိတ်အပိုင်းများ အားလုံးကို ပြသသော စနစ် ဗိသုကာ ခြုံငုံသုံးသပ်ချက်'
    }
  }

  const text = t[language]

  return (
    <div className="space-y-6">
      <div className="text-center">
        <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
          {text.title}
        </h2>
        <p className="text-gray-600 dark:text-gray-400">
          {text.description}
        </p>
      </div>

      {/* Architecture Diagram */}
      <div className="relative">
        <svg
          viewBox="0 0 800 600"
          className="w-full h-auto border border-gray-200 dark:border-gray-700 rounded-lg bg-white dark:bg-gray-900"
        >
          {/* Frontend Layer */}
          <rect x="50" y="50" width="700" height="120" fill="#3B82F6" fillOpacity="0.1" stroke="#3B82F6" strokeWidth="2" rx="8" />
          <text x="400" y="75" textAnchor="middle" className="fill-blue-600 font-semibold text-lg">
            {text.frontend}
          </text>
          
          {/* Frontend Components */}
          <rect x="70" y="90" width="120" height="60" fill="#EFF6FF" stroke="#3B82F6" strokeWidth="1" rx="4" />
          <text x="130" y="115" textAnchor="middle" className="fill-blue-800 text-sm font-medium">React/Next.js</text>
          <text x="130" y="130" textAnchor="middle" className="fill-blue-600 text-xs">UI Components</text>
          
          <rect x="210" y="90" width="120" height="60" fill="#EFF6FF" stroke="#3B82F6" strokeWidth="1" rx="4" />
          <text x="270" y="115" textAnchor="middle" className="fill-blue-800 text-sm font-medium">State Management</text>
          <text x="270" y="130" textAnchor="middle" className="fill-blue-600 text-xs">Context API</text>
          
          <rect x="350" y="90" width="120" height="60" fill="#EFF6FF" stroke="#3B82F6" strokeWidth="1" rx="4" />
          <text x="410" y="115" textAnchor="middle" className="fill-blue-800 text-sm font-medium">Routing</text>
          <text x="410" y="130" textAnchor="middle" className="fill-blue-600 text-xs">App Router</text>
          
          <rect x="490" y="90" width="120" height="60" fill="#EFF6FF" stroke="#3B82F6" strokeWidth="1" rx="4" />
          <text x="550" y="115" textAnchor="middle" className="fill-blue-800 text-sm font-medium">Styling</text>
          <text x="550" y="130" textAnchor="middle" className="fill-blue-600 text-xs">Tailwind CSS</text>
          
          <rect x="630" y="90" width="120" height="60" fill="#EFF6FF" stroke="#3B82F6" strokeWidth="1" rx="4" />
          <text x="690" y="115" textAnchor="middle" className="fill-blue-800 text-sm font-medium">Authentication</text>
          <text x="690" y="130" textAnchor="middle" className="fill-blue-600 text-xs">JWT Tokens</text>

          {/* Backend Layer */}
          <rect x="50" y="200" width="700" height="120" fill="#10B981" fillOpacity="0.1" stroke="#10B981" strokeWidth="2" rx="8" />
          <text x="400" y="225" textAnchor="middle" className="fill-green-600 font-semibold text-lg">
            {text.backend}
          </text>
          
          {/* Backend Components */}
          <rect x="70" y="240" width="120" height="60" fill="#F0FDF4" stroke="#10B981" strokeWidth="1" rx="4" />
          <text x="130" y="265" textAnchor="middle" className="fill-green-800 text-sm font-medium">API Server</text>
          <text x="130" y="280" textAnchor="middle" className="fill-green-600 text-xs">Express.js</text>
          
          <rect x="210" y="240" width="120" height="60" fill="#F0FDF4" stroke="#10B981" strokeWidth="1" rx="4" />
          <text x="270" y="265" textAnchor="middle" className="fill-green-800 text-sm font-medium">Authentication</text>
          <text x="270" y="280" textAnchor="middle" className="fill-green-600 text-xs">JWT/Passport</text>
          
          <rect x="350" y="240" width="120" height="60" fill="#F0FDF4" stroke="#10B981" strokeWidth="1" rx="4" />
          <text x="410" y="265" textAnchor="middle" className="fill-green-800 text-sm font-medium">Business Logic</text>
          <text x="410" y="280" textAnchor="middle" className="fill-green-600 text-xs">Controllers</text>
          
          <rect x="490" y="240" width="120" height="60" fill="#F0FDF4" stroke="#10B981" strokeWidth="1" rx="4" />
          <text x="550" y="265" textAnchor="middle" className="fill-green-800 text-sm font-medium">Data Validation</text>
          <text x="550" y="280" textAnchor="middle" className="fill-green-600 text-xs">Joi/Yup</text>
          
          <rect x="630" y="240" width="120" height="60" fill="#F0FDF4" stroke="#10B981" strokeWidth="1" rx="4" />
          <text x="690" y="265" textAnchor="middle" className="fill-green-800 text-sm font-medium">File Upload</text>
          <text x="690" y="280" textAnchor="middle" className="fill-green-600 text-xs">Multer</text>

          {/* Database Layer */}
          <rect x="50" y="350" width="700" height="120" fill="#8B5CF6" fillOpacity="0.1" stroke="#8B5CF6" strokeWidth="2" rx="8" />
          <text x="400" y="375" textAnchor="middle" className="fill-purple-600 font-semibold text-lg">
            {text.database}
          </text>
          
          {/* Database Components */}
          <rect x="70" y="390" width="150" height="60" fill="#FAF5FF" stroke="#8B5CF6" strokeWidth="1" rx="4" />
          <text x="145" y="415" textAnchor="middle" className="fill-purple-800 text-sm font-medium">MongoDB</text>
          <text x="145" y="430" textAnchor="middle" className="fill-purple-600 text-xs">Primary Database</text>
          
          <rect x="240" y="390" width="150" height="60" fill="#FAF5FF" stroke="#8B5CF6" strokeWidth="1" rx="4" />
          <text x="315" y="415" textAnchor="middle" className="fill-purple-800 text-sm font-medium">Collections</text>
          <text x="315" y="430" textAnchor="middle" className="fill-purple-600 text-xs">Products, Sales, Users</text>
          
          <rect x="410" y="390" width="150" height="60" fill="#FAF5FF" stroke="#8B5CF6" strokeWidth="1" rx="4" />
          <text x="485" y="415" textAnchor="middle" className="fill-purple-800 text-sm font-medium">Indexing</text>
          <text x="485" y="430" textAnchor="middle" className="fill-purple-600 text-xs">Performance</text>
          
          <rect x="580" y="390" width="150" height="60" fill="#FAF5FF" stroke="#8B5CF6" strokeWidth="1" rx="4" />
          <text x="655" y="415" textAnchor="middle" className="fill-purple-800 text-sm font-medium">Backup</text>
          <text x="655" y="430" textAnchor="middle" className="fill-purple-600 text-xs">Data Safety</text>

          {/* External Services */}
          <rect x="50" y="500" width="700" height="80" fill="#F59E0B" fillOpacity="0.1" stroke="#F59E0B" strokeWidth="2" rx="8" />
          <text x="400" y="525" textAnchor="middle" className="fill-amber-600 font-semibold text-lg">
            {text.external}
          </text>
          
          <rect x="100" y="540" width="120" height="30" fill="#FEF3C7" stroke="#F59E0B" strokeWidth="1" rx="4" />
          <text x="160" y="558" textAnchor="middle" className="fill-amber-800 text-sm">Email Service</text>
          
          <rect x="240" y="540" width="120" height="30" fill="#FEF3C7" stroke="#F59E0B" strokeWidth="1" rx="4" />
          <text x="300" y="558" textAnchor="middle" className="fill-amber-800 text-sm">Payment Gateway</text>
          
          <rect x="380" y="540" width="120" height="30" fill="#FEF3C7" stroke="#F59E0B" strokeWidth="1" rx="4" />
          <text x="440" y="558" textAnchor="middle" className="fill-amber-800 text-sm">Cloud Storage</text>
          
          <rect x="520" y="540" width="120" height="30" fill="#FEF3C7" stroke="#F59E0B" strokeWidth="1" rx="4" />
          <text x="580" y="558" textAnchor="middle" className="fill-amber-800 text-sm">Analytics</text>

          {/* Connection Lines */}
          <line x1="400" y1="170" x2="400" y2="200" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="400" y1="320" x2="400" y2="350" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />
          <line x1="400" y1="470" x2="400" y2="500" stroke="#6B7280" strokeWidth="2" markerEnd="url(#arrowhead)" />

          {/* Arrow marker */}
          <defs>
            <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
              <polygon points="0 0, 10 3.5, 0 7" fill="#6B7280" />
            </marker>
          </defs>
        </svg>
      </div>

      {/* Technology Stack */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-blue-600">{text.frontend}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Badge variant="outline" className="text-xs">React 18</Badge>
            <Badge variant="outline" className="text-xs">Next.js 14</Badge>
            <Badge variant="outline" className="text-xs">TypeScript</Badge>
            <Badge variant="outline" className="text-xs">Tailwind CSS</Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-green-600">{text.backend}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Badge variant="outline" className="text-xs">Node.js</Badge>
            <Badge variant="outline" className="text-xs">Express.js</Badge>
            <Badge variant="outline" className="text-xs">JWT</Badge>
            <Badge variant="outline" className="text-xs">Multer</Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-purple-600">{text.database}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Badge variant="outline" className="text-xs">MongoDB</Badge>
            <Badge variant="outline" className="text-xs">Mongoose</Badge>
            <Badge variant="outline" className="text-xs">Aggregation</Badge>
            <Badge variant="outline" className="text-xs">Indexing</Badge>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm text-amber-600">{text.external}</CardTitle>
          </CardHeader>
          <CardContent className="space-y-2">
            <Badge variant="outline" className="text-xs">SMTP</Badge>
            <Badge variant="outline" className="text-xs">Stripe</Badge>
            <Badge variant="outline" className="text-xs">AWS S3</Badge>
            <Badge variant="outline" className="text-xs">Analytics</Badge>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
