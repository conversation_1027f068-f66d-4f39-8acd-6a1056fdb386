{"name": "bitestech-pos-diagrams", "version": "1.0.0", "description": "PDF generation for BitesTech POS System diagrams", "main": "generate-pdf.js", "scripts": {"generate-pdf": "node generate-pdf.js", "install-deps": "npm install puppeteer", "setup": "npm install && npm run generate-pdf", "clean": "rm -f *.pdf", "help": "echo 'Available commands: npm run generate-pdf, npm run setup, npm run clean'"}, "keywords": ["pos", "diagrams", "pdf", "mermaid", "documentation"], "author": "BitesTech Development Team", "license": "MIT", "dependencies": {"puppeteer": "^21.0.0"}, "devDependencies": {}, "engines": {"node": ">=16.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-org/bites-tech-pos.git"}, "bugs": {"url": "https://github.com/your-org/bites-tech-pos/issues"}, "homepage": "https://github.com/your-org/bites-tech-pos#readme"}