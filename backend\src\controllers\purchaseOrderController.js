const PurchaseOrder = require('../models/PurchaseOrder');
const Product = require('../models/Product');
const Supplier = require('../models/Supplier');
const User = require('../models/User');
const { validationResult } = require('express-validator');

// Get all purchase orders
const getPurchaseOrders = async (req, res) => {
    try {
        const {
            page = 1,
            limit = 10,
            status,
            supplier,
            search,
            startDate,
            endDate,
            priority
        } = req.query;

        // Build filter object
        const filter = {};

        if (status) filter.status = status;
        if (supplier) filter.supplier = supplier;
        if (priority) filter.priority = priority;

        if (search) {
            filter.$or = [
                { orderNumber: { $regex: search, $options: 'i' } },
                { notes: { $regex: search, $options: 'i' } }
            ];
        }

        if (startDate || endDate) {
            filter.orderDate = {};
            if (startDate) filter.orderDate.$gte = new Date(startDate);
            if (endDate) filter.orderDate.$lte = new Date(endDate);
        }

        // Calculate pagination
        const skip = (parseInt(page) - 1) * parseInt(limit);

        // Get purchase orders with pagination
        const purchaseOrders = await PurchaseOrder.find(filter)
            .populate('supplier', 'name code contactPerson')
            .populate('createdBy', 'firstName lastName email')
            .populate('approvedBy', 'firstName lastName email')
            .populate('items.product', 'name sku category')
            .sort({ orderDate: -1 })
            .skip(skip)
            .limit(parseInt(limit));

        // Get total count for pagination
        const total = await PurchaseOrder.countDocuments(filter);

        res.json({
            success: true,
            data: purchaseOrders,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total,
                pages: Math.ceil(total / parseInt(limit))
            }
        });
    } catch (error) {
        console.error('Error fetching purchase orders:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching purchase orders',
            error: error.message
        });
    }
};

// Get single purchase order
const getPurchaseOrder = async (req, res) => {
    try {
        const { id } = req.params;

        const purchaseOrder = await PurchaseOrder.findById(id)
            .populate('supplier', 'name code contactPerson company paymentTerms')
            .populate('createdBy', 'firstName lastName email')
            .populate('approvedBy', 'firstName lastName email')
            .populate('receivedBy', 'firstName lastName email')
            .populate('items.product', 'name sku category inventory');

        if (!purchaseOrder) {
            return res.status(404).json({
                success: false,
                message: 'Purchase order not found'
            });
        }

        res.json({
            success: true,
            data: purchaseOrder
        });
    } catch (error) {
        console.error('Error fetching purchase order:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching purchase order',
            error: error.message
        });
    }
};

// Create new purchase order
const createPurchaseOrder = async (req, res) => {
    try {
        // Check validation errors
        const errors = validationResult(req);
        if (!errors.isEmpty()) {
            return res.status(400).json({
                success: false,
                message: 'Validation failed',
                errors: errors.array()
            });
        }

        const {
            supplier,
            expectedDeliveryDate,
            priority = 'normal',
            items,
            taxRate = 0,
            shippingCost = 0,
            discountAmount = 0,
            paymentTerms,
            deliveryAddress,
            shippingMethod,
            notes,
            internalNotes,
            status = 'draft'
        } = req.body;

        // Validate supplier exists
        const supplierDoc = await Supplier.findById(supplier);
        if (!supplierDoc) {
            return res.status(400).json({
                success: false,
                message: 'Supplier not found'
            });
        }

        // Validate and prepare items
        const orderItems = [];
        let subtotal = 0;

        for (const item of items) {
            const product = await Product.findById(item.product);

            if (!product) {
                return res.status(400).json({
                    success: false,
                    message: `Product ${item.product} not found`
                });
            }

            const unitCost = item.unitCost || product.cost;
            const totalCost = item.quantity * unitCost;
            subtotal += totalCost;

            orderItems.push({
                product: product._id,
                productName: product.name,
                sku: product.sku,
                description: item.description || product.description,
                quantity: item.quantity,
                unitCost,
                totalCost,
                notes: item.notes || ''
            });
        }

        // Generate order number
        const today = new Date();
        const year = today.getFullYear();
        const month = String(today.getMonth() + 1).padStart(2, '0');
        const day = String(today.getDate()).padStart(2, '0');
        const timestamp = Date.now().toString().slice(-6);
        const orderNumber = `PO-${year}${month}${day}-${timestamp}`;

        // Calculate totals
        const taxAmount = (subtotal * taxRate) / 100;
        const totalAmount = subtotal + taxAmount + shippingCost - discountAmount;

        // Create purchase order
        const purchaseOrder = new PurchaseOrder({
            orderNumber,
            supplier,
            expectedDeliveryDate,
            priority,
            items: orderItems,
            subtotal,
            taxRate,
            taxAmount,
            shippingCost,
            discountAmount,
            totalAmount,
            currency: supplierDoc.currency || 'MMK',
            paymentTerms: paymentTerms || supplierDoc.paymentTerms || 'net_30',
            deliveryAddress,
            shippingMethod,
            notes,
            internalNotes,
            status,
            requestedBy: req.user.id,
            createdBy: req.user.id
        });

        await purchaseOrder.save();

        // Populate the created purchase order
        await purchaseOrder.populate('supplier', 'name code contactPerson');
        await purchaseOrder.populate('createdBy', 'firstName lastName email');
        await purchaseOrder.populate('items.product', 'name sku category');

        res.status(201).json({
            success: true,
            message: 'Purchase order created successfully',
            data: purchaseOrder
        });
    } catch (error) {
        console.error('Error creating purchase order:', error);
        res.status(500).json({
            success: false,
            message: 'Error creating purchase order',
            error: error.message
        });
    }
};

// Update purchase order
const updatePurchaseOrder = async (req, res) => {
    try {
        const { id } = req.params;
        const updates = req.body;

        const purchaseOrder = await PurchaseOrder.findById(id);

        if (!purchaseOrder) {
            return res.status(404).json({
                success: false,
                message: 'Purchase order not found'
            });
        }

        // Only allow updates if status is draft or pending
        if (!['draft', 'pending'].includes(purchaseOrder.status)) {
            return res.status(400).json({
                success: false,
                message: 'Only draft or pending purchase orders can be updated'
            });
        }

        // Update purchase order
        Object.assign(purchaseOrder, updates);
        purchaseOrder.updatedBy = req.user.id;
        await purchaseOrder.save();

        // Populate the updated purchase order
        await purchaseOrder.populate('supplier', 'name code contactPerson');
        await purchaseOrder.populate('createdBy', 'firstName lastName email');
        await purchaseOrder.populate('items.product', 'name sku category');

        res.json({
            success: true,
            message: 'Purchase order updated successfully',
            data: purchaseOrder
        });
    } catch (error) {
        console.error('Error updating purchase order:', error);
        res.status(500).json({
            success: false,
            message: 'Error updating purchase order',
            error: error.message
        });
    }
};

// Approve purchase order
const approvePurchaseOrder = async (req, res) => {
    try {
        const { id } = req.params;
        const { approved, comments } = req.body;

        const purchaseOrder = await PurchaseOrder.findById(id);

        if (!purchaseOrder) {
            return res.status(404).json({
                success: false,
                message: 'Purchase order not found'
            });
        }

        if (purchaseOrder.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: 'Only pending purchase orders can be approved or rejected'
            });
        }

        if (approved) {
            purchaseOrder.status = 'approved';
            purchaseOrder.approvedBy = req.user.id;
            purchaseOrder.approvalDate = new Date();
        } else {
            purchaseOrder.status = 'cancelled';
            purchaseOrder.cancellationReason = comments || 'No reason provided';
        }

        await purchaseOrder.save();

        res.json({
            success: true,
            message: `Purchase order ${approved ? 'approved' : 'rejected'} successfully`,
            data: purchaseOrder
        });
    } catch (error) {
        console.error('Error approving purchase order:', error);
        res.status(500).json({
            success: false,
            message: 'Error processing approval',
            error: error.message
        });
    }
};

// Send order to supplier
const sendOrderToSupplier = async (req, res) => {
    try {
        const { id } = req.params;

        const purchaseOrder = await PurchaseOrder.findById(id);

        if (!purchaseOrder) {
            return res.status(404).json({
                success: false,
                message: 'Purchase order not found'
            });
        }

        if (purchaseOrder.status !== 'approved') {
            return res.status(400).json({
                success: false,
                message: 'Only approved purchase orders can be sent to supplier'
            });
        }

        purchaseOrder.status = 'ordered';
        await purchaseOrder.save();

        res.json({
            success: true,
            message: 'Purchase order sent to supplier successfully',
            data: purchaseOrder
        });
    } catch (error) {
        console.error('Error sending order to supplier:', error);
        res.status(500).json({
            success: false,
            message: 'Error sending order to supplier',
            error: error.message
        });
    }
};

// Receive items
const receiveItems = async (req, res) => {
    try {
        const { id } = req.params;
        const { receivedItems, notes } = req.body;

        const purchaseOrder = await PurchaseOrder.findById(id);

        if (!purchaseOrder) {
            return res.status(404).json({
                success: false,
                message: 'Purchase order not found'
            });
        }

        if (!['ordered', 'partially_received'].includes(purchaseOrder.status)) {
            return res.status(400).json({
                success: false,
                message: 'Only ordered or partially received purchase orders can receive items'
            });
        }

        // Update received quantities
        receivedItems.forEach(receivedItem => {
            const item = purchaseOrder.items.id(receivedItem.itemId);
            if (item) {
                item.receivedQuantity = Math.min(
                    item.receivedQuantity + receivedItem.quantity,
                    item.quantity
                );
            }
        });

        purchaseOrder.receivedBy = req.user.id;
        purchaseOrder.receivingNotes = notes;

        await purchaseOrder.save();

        res.json({
            success: true,
            message: 'Items received successfully',
            data: purchaseOrder
        });
    } catch (error) {
        console.error('Error receiving items:', error);
        res.status(500).json({
            success: false,
            message: 'Error receiving items',
            error: error.message
        });
    }
};

// Delete purchase order
const deletePurchaseOrder = async (req, res) => {
    try {
        const { id } = req.params;

        const purchaseOrder = await PurchaseOrder.findById(id);

        if (!purchaseOrder) {
            return res.status(404).json({
                success: false,
                message: 'Purchase order not found'
            });
        }

        // Only allow deletion if status is draft
        if (purchaseOrder.status !== 'draft') {
            return res.status(400).json({
                success: false,
                message: 'Only draft purchase orders can be deleted'
            });
        }

        await PurchaseOrder.findByIdAndDelete(id);

        res.json({
            success: true,
            message: 'Purchase order deleted successfully'
        });
    } catch (error) {
        console.error('Error deleting purchase order:', error);
        res.status(500).json({
            success: false,
            message: 'Error deleting purchase order',
            error: error.message
        });
    }
};

// Get purchase order statistics
const getPurchaseOrderStats = async (req, res) => {
    try {
        const { startDate, endDate } = req.query;

        const matchStage = {};
        if (startDate || endDate) {
            matchStage.orderDate = {};
            if (startDate) matchStage.orderDate.$gte = new Date(startDate);
            if (endDate) matchStage.orderDate.$lte = new Date(endDate);
        }

        const stats = await PurchaseOrder.aggregate([
            { $match: matchStage },
            {
                $group: {
                    _id: null,
                    totalOrders: { $sum: 1 },
                    totalAmount: { $sum: '$totalAmount' },
                    averageOrderValue: { $avg: '$totalAmount' },
                    totalItems: { $sum: '$totalItems' },
                    byStatus: {
                        $push: {
                            status: '$status',
                            count: 1,
                            amount: '$totalAmount'
                        }
                    },
                    bySupplier: {
                        $push: {
                            supplier: '$supplier',
                            count: 1,
                            amount: '$totalAmount'
                        }
                    }
                }
            }
        ]);

        res.json({
            success: true,
            data: stats[0] || {
                totalOrders: 0,
                totalAmount: 0,
                averageOrderValue: 0,
                totalItems: 0,
                byStatus: [],
                bySupplier: []
            }
        });
    } catch (error) {
        console.error('Error fetching purchase order stats:', error);
        res.status(500).json({
            success: false,
            message: 'Error fetching purchase order statistics',
            error: error.message
        });
    }
};

module.exports = {
    getPurchaseOrders,
    getPurchaseOrder,
    createPurchaseOrder,
    updatePurchaseOrder,
    approvePurchaseOrder,
    sendOrderToSupplier,
    receiveItems,
    deletePurchaseOrder,
    getPurchaseOrderStats
};
