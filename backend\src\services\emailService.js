const nodemailer = require('nodemailer');

class EmailService {
    constructor() {
        this.transporter = nodemailer.createTransport({
            host: process.env.SMTP_HOST || 'smtp.gmail.com',
            port: process.env.SMTP_PORT || 587,
            secure: false, // true for 465, false for other ports
            auth: {
                user: process.env.SMTP_USER,
                pass: process.env.SMTP_PASS
            }
        });
    }

    async sendEmail(to, subject, html, text = null) {
        try {
            const mailOptions = {
                from: `"BitsTech POS" <${process.env.SMTP_USER}>`,
                to,
                subject,
                html,
                text: text || this.stripHtml(html)
            };

            const result = await this.transporter.sendMail(mailOptions);
            console.log('Email sent successfully:', result.messageId);
            return result;
        } catch (error) {
            console.error('Error sending email:', error);
            throw error;
        }
    }

    stripHtml(html) {
        return html.replace(/<[^>]*>/g, '');
    }

    // Stock Adjustment Notifications
    async sendAdjustmentSubmittedNotification(adjustment, managers) {
        const subject = `Stock Adjustment Submitted for Approval - ${adjustment.adjustmentNumber}`;

        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px;">
                    <h2 style="color: #333; margin-bottom: 20px;">Stock Adjustment Submitted</h2>

                    <div style="background-color: white; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
                        <h3 style="color: #495057; margin-bottom: 15px;">Adjustment Details</h3>

                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Adjustment Number:</td>
                                <td style="padding: 8px 0;">${adjustment.adjustmentNumber}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Type:</td>
                                <td style="padding: 8px 0; text-transform: capitalize;">${adjustment.type}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Reason:</td>
                                <td style="padding: 8px 0;">${adjustment.reason}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Total Items:</td>
                                <td style="padding: 8px 0;">${adjustment.totalItems}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Cost Impact:</td>
                                <td style="padding: 8px 0; color: ${adjustment.totalCostImpact >= 0 ? '#28a745' : '#dc3545'};">
                                    ${adjustment.totalCostImpact >= 0 ? '+' : ''}${adjustment.totalCostImpact.toLocaleString()} ${adjustment.currency}
                                </td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Created By:</td>
                                <td style="padding: 8px 0;">${adjustment.createdBy.firstName} ${adjustment.createdBy.lastName}</td>
                            </tr>
                        </table>
                    </div>

                    <div style="background-color: white; padding: 20px; border-radius: 6px; margin-bottom: 20px;">
                        <h3 style="color: #495057; margin-bottom: 15px;">Affected Products</h3>

                        <table style="width: 100%; border-collapse: collapse; border: 1px solid #dee2e6;">
                            <thead>
                                <tr style="background-color: #f8f9fa;">
                                    <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Product</th>
                                    <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">Current</th>
                                    <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">Adjustment</th>
                                    <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">New</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${adjustment.items.map(item => `
                                    <tr>
                                        <td style="padding: 12px; border: 1px solid #dee2e6;">
                                            <strong>${item.productName}</strong><br>
                                            <small style="color: #6c757d;">SKU: ${item.sku}</small>
                                        </td>
                                        <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">${item.currentQuantity}</td>
                                        <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6; color: ${item.adjustmentQuantity >= 0 ? '#28a745' : '#dc3545'};">
                                            ${item.adjustmentQuantity >= 0 ? '+' : ''}${item.adjustmentQuantity}
                                        </td>
                                        <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">${item.newQuantity}</td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>
                    </div>

                    <div style="text-align: center; margin-top: 30px;">
                        <a href="${process.env.FRONTEND_URL}/inventory/adjustments/${adjustment._id}"
                           style="background-color: #007bff; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                            Review Adjustment
                        </a>
                    </div>

                    <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #dee2e6; color: #6c757d; font-size: 14px;">
                        <p>This adjustment requires your approval before it can be applied to inventory.</p>
                        <p>Please review the details and approve or reject as appropriate.</p>
                    </div>
                </div>
            </div>
        `;

        // Send to all managers
        for (const manager of managers) {
            await this.sendEmail(manager.email, subject, html);
        }
    }

    async sendAdjustmentApprovedNotification(adjustment, creator) {
        const subject = `Stock Adjustment Approved - ${adjustment.adjustmentNumber}`;

        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #d4edda; padding: 20px; border-radius: 8px; border-left: 4px solid #28a745;">
                    <h2 style="color: #155724; margin-bottom: 20px;">✅ Stock Adjustment Approved</h2>

                    <div style="background-color: white; padding: 20px; border-radius: 6px;">
                        <p style="color: #333; margin-bottom: 15px;">
                            Your stock adjustment <strong>${adjustment.adjustmentNumber}</strong> has been approved.
                        </p>

                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Approved By:</td>
                                <td style="padding: 8px 0;">${adjustment.approvedBy.firstName} ${adjustment.approvedBy.lastName}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Approval Date:</td>
                                <td style="padding: 8px 0;">${new Date(adjustment.approvalDate).toLocaleString()}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Type:</td>
                                <td style="padding: 8px 0; text-transform: capitalize;">${adjustment.type}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Total Items:</td>
                                <td style="padding: 8px 0;">${adjustment.totalItems}</td>
                            </tr>
                        </table>

                        <div style="text-align: center; margin-top: 20px;">
                            <a href="${process.env.FRONTEND_URL}/inventory/adjustments/${adjustment._id}"
                               style="background-color: #28a745; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        await this.sendEmail(creator.email, subject, html);
    }

    async sendAdjustmentRejectedNotification(adjustment, creator) {
        const subject = `Stock Adjustment Rejected - ${adjustment.adjustmentNumber}`;

        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #f8d7da; padding: 20px; border-radius: 8px; border-left: 4px solid #dc3545;">
                    <h2 style="color: #721c24; margin-bottom: 20px;">❌ Stock Adjustment Rejected</h2>

                    <div style="background-color: white; padding: 20px; border-radius: 6px;">
                        <p style="color: #333; margin-bottom: 15px;">
                            Your stock adjustment <strong>${adjustment.adjustmentNumber}</strong> has been rejected.
                        </p>

                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Rejected By:</td>
                                <td style="padding: 8px 0;">${adjustment.approvedBy?.firstName} ${adjustment.approvedBy?.lastName}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Rejection Date:</td>
                                <td style="padding: 8px 0;">${new Date().toLocaleString()}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Reason:</td>
                                <td style="padding: 8px 0;">${adjustment.rejectionReason || 'No reason provided'}</td>
                            </tr>
                        </table>

                        <div style="background-color: #f8f9fa; padding: 15px; border-radius: 6px; margin-top: 20px;">
                            <p style="color: #495057; margin: 0;">
                                You can review the feedback and create a new adjustment if needed.
                            </p>
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <a href="${process.env.FRONTEND_URL}/inventory/adjustments/${adjustment._id}"
                               style="background-color: #6c757d; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        await this.sendEmail(creator.email, subject, html);
    }

    async sendAdjustmentAppliedNotification(adjustment, stakeholders) {
        const subject = `Stock Adjustment Applied - ${adjustment.adjustmentNumber}`;

        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #d1ecf1; padding: 20px; border-radius: 8px; border-left: 4px solid #17a2b8;">
                    <h2 style="color: #0c5460; margin-bottom: 20px;">📦 Stock Adjustment Applied</h2>

                    <div style="background-color: white; padding: 20px; border-radius: 6px;">
                        <p style="color: #333; margin-bottom: 15px;">
                            Stock adjustment <strong>${adjustment.adjustmentNumber}</strong> has been applied to inventory.
                        </p>

                        <table style="width: 100%; border-collapse: collapse;">
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Applied Date:</td>
                                <td style="padding: 8px 0;">${new Date(adjustment.appliedDate).toLocaleString()}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Type:</td>
                                <td style="padding: 8px 0; text-transform: capitalize;">${adjustment.type}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Total Items Affected:</td>
                                <td style="padding: 8px 0;">${adjustment.totalItems}</td>
                            </tr>
                            <tr>
                                <td style="padding: 8px 0; font-weight: bold; color: #6c757d;">Inventory Impact:</td>
                                <td style="padding: 8px 0; color: ${adjustment.totalCostImpact >= 0 ? '#28a745' : '#dc3545'};">
                                    ${adjustment.totalCostImpact >= 0 ? '+' : ''}${adjustment.totalCostImpact.toLocaleString()} ${adjustment.currency}
                                </td>
                            </tr>
                        </table>

                        <div style="background-color: #d4edda; padding: 15px; border-radius: 6px; margin-top: 20px;">
                            <p style="color: #155724; margin: 0;">
                                ✅ All product quantities have been updated in the system.
                            </p>
                        </div>

                        <div style="text-align: center; margin-top: 20px;">
                            <a href="${process.env.FRONTEND_URL}/inventory/adjustments/${adjustment._id}"
                               style="background-color: #17a2b8; color: white; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                                View Details
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Send to all stakeholders (creator, approver, managers)
        for (const stakeholder of stakeholders) {
            await this.sendEmail(stakeholder.email, subject, html);
        }
    }

    // Low Stock Alert
    async sendLowStockAlert(products, managers) {
        const subject = `Low Stock Alert - ${products.length} Products Need Attention`;

        const html = `
            <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                <div style="background-color: #fff3cd; padding: 20px; border-radius: 8px; border-left: 4px solid #ffc107;">
                    <h2 style="color: #856404; margin-bottom: 20px;">⚠️ Low Stock Alert</h2>

                    <div style="background-color: white; padding: 20px; border-radius: 6px;">
                        <p style="color: #333; margin-bottom: 15px;">
                            The following products are running low on stock and may need replenishment:
                        </p>

                        <table style="width: 100%; border-collapse: collapse; border: 1px solid #dee2e6;">
                            <thead>
                                <tr style="background-color: #f8f9fa;">
                                    <th style="padding: 12px; text-align: left; border: 1px solid #dee2e6;">Product</th>
                                    <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">Current Stock</th>
                                    <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">Min Level</th>
                                    <th style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">Status</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${products.map(product => `
                                    <tr>
                                        <td style="padding: 12px; border: 1px solid #dee2e6;">
                                            <strong>${product.name}</strong><br>
                                            <small style="color: #6c757d;">SKU: ${product.sku}</small>
                                        </td>
                                        <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">${product.inventory.quantity}</td>
                                        <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">${product.inventory.minQuantity}</td>
                                        <td style="padding: 12px; text-align: center; border: 1px solid #dee2e6;">
                                            <span style="color: ${product.inventory.quantity === 0 ? '#dc3545' : '#ffc107'};">
                                                ${product.inventory.quantity === 0 ? 'Out of Stock' : 'Low Stock'}
                                            </span>
                                        </td>
                                    </tr>
                                `).join('')}
                            </tbody>
                        </table>

                        <div style="text-align: center; margin-top: 20px;">
                            <a href="${process.env.FRONTEND_URL}/inventory"
                               style="background-color: #ffc107; color: #212529; padding: 12px 24px; text-decoration: none; border-radius: 6px; display: inline-block;">
                                View Inventory
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Send to all managers
        for (const manager of managers) {
            await this.sendEmail(manager.email, subject, html);
        }
    }
}

module.exports = new EmailService();
