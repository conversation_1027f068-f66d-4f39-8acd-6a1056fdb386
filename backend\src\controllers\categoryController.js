const Category = require('../models/Category');
const Product = require('../models/Product');
const asyncHandler = require('../utils/asyncHandler');
const ErrorResponse = require('../utils/errorResponse');

// Helper function to generate slug
const generateSlug = (name) => {
    return name
        .toLowerCase()
        .replace(/[^a-z0-9 -]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .trim('-');
};

// @desc    Get all categories
// @route   GET /api/categories
// @access  Private
const getCategories = asyncHandler(async (req, res, next) => {
    let query = {};
    
    // Filter by parent category
    if (req.query.parent !== undefined) {
        query.parent = req.query.parent === 'null' ? null : req.query.parent;
    }
    
    // Filter by active status
    if (req.query.isActive !== undefined) {
        query.isActive = req.query.isActive === 'true';
    }
    
    // Sort
    let sort = { sortOrder: 1, name: 1 };
    if (req.query.sortBy) {
        const parts = req.query.sortBy.split(':');
        sort = {};
        sort[parts[0]] = parts[1] === 'desc' ? -1 : 1;
    }
    
    try {
        const categories = await Category.find(query)
            .populate('parent', 'name slug color icon')
            .sort(sort);
        
        // Add products count for each category
        const categoriesWithCount = await Promise.all(
            categories.map(async (category) => {
                const productsCount = await Product.countDocuments({ 
                    category: category._id,
                    isActive: true 
                });
                
                return {
                    ...category.toObject(),
                    productsCount
                };
            })
        );
        
        res.status(200).json({
            success: true,
            count: categoriesWithCount.length,
            data: categoriesWithCount
        });
    } catch (error) {
        return next(new ErrorResponse('Error fetching categories', 500));
    }
});

// @desc    Get single category
// @route   GET /api/categories/:id
// @access  Private
const getCategory = asyncHandler(async (req, res, next) => {
    const category = await Category.findById(req.params.id)
        .populate('parent', 'name slug color icon');
        
    if (!category) {
        return next(new ErrorResponse(`Category not found with id of ${req.params.id}`, 404));
    }
    
    // Get subcategories
    const subcategories = await Category.find({ parent: category._id, isActive: true })
        .sort({ sortOrder: 1, name: 1 });
    
    // Get products count
    const productsCount = await Product.countDocuments({ 
        category: category._id,
        isActive: true 
    });
    
    const categoryData = {
        ...category.toObject(),
        subcategories,
        productsCount
    };
    
    res.status(200).json({
        success: true,
        data: categoryData
    });
});

// @desc    Create new category
// @route   POST /api/categories
// @access  Private (Admin, Manager)
const createCategory = asyncHandler(async (req, res, next) => {
    // Validate required fields
    if (!req.body.name || !req.body.name.trim()) {
        return next(new ErrorResponse('Category name is required', 400));
    }

    // Validate name length
    if (req.body.name.trim().length < 2) {
        return next(new ErrorResponse('Category name must be at least 2 characters', 400));
    }

    // Generate slug from name
    const slug = generateSlug(req.body.name.trim());

    // Check if slug already exists
    const existingCategory = await Category.findOne({ slug });
    if (existingCategory) {
        return next(new ErrorResponse('Category with this name already exists', 400));
    }

    // Check if parent category exists (if provided)
    if (req.body.parent) {
        const parentCategory = await Category.findById(req.body.parent);
        if (!parentCategory) {
            return next(new ErrorResponse('Parent category not found', 404));
        }
    }

    // Prepare category data with defaults
    const categoryData = {
        name: req.body.name.trim(),
        description: req.body.description?.trim() || '',
        color: req.body.color || '#3B82F6',
        icon: req.body.icon || 'folder',
        isActive: req.body.isActive !== undefined ? req.body.isActive : true,
        parent: req.body.parent || null,
        sortOrder: req.body.sortOrder || 0,
        slug
    };

    console.log('Creating category with data:', categoryData);

    const category = await Category.create(categoryData);

    // Populate parent information
    await category.populate('parent', 'name slug color icon');

    console.log('Category created successfully:', category);

    res.status(201).json({
        success: true,
        data: category,
        message: 'Category created successfully'
    });
});

// @desc    Update category
// @route   PUT /api/categories/:id
// @access  Private (Admin, Manager)
const updateCategory = asyncHandler(async (req, res, next) => {
    let category = await Category.findById(req.params.id);
    
    if (!category) {
        return next(new ErrorResponse(`Category not found with id of ${req.params.id}`, 404));
    }
    
    // Generate new slug if name is being updated
    if (req.body.name && req.body.name !== category.name) {
        const slug = generateSlug(req.body.name);
        
        // Check if new slug already exists
        const existingCategory = await Category.findOne({ slug, _id: { $ne: req.params.id } });
        if (existingCategory) {
            return next(new ErrorResponse('Category with this name already exists', 400));
        }
        
        req.body.slug = slug;
    }
    
    // Check if parent category exists (if being updated)
    if (req.body.parent) {
        const parentCategory = await Category.findById(req.body.parent);
        if (!parentCategory) {
            return next(new ErrorResponse('Parent category not found', 404));
        }
        
        // Prevent circular reference
        if (req.body.parent === req.params.id) {
            return next(new ErrorResponse('Category cannot be its own parent', 400));
        }
    }
    
    category = await Category.findByIdAndUpdate(req.params.id, req.body, {
        new: true,
        runValidators: true
    }).populate('parent', 'name slug color icon');
    
    res.status(200).json({
        success: true,
        data: category
    });
});

// @desc    Delete category
// @route   DELETE /api/categories/:id
// @access  Private (Admin, Manager)
const deleteCategory = asyncHandler(async (req, res, next) => {
    const category = await Category.findById(req.params.id);
    
    if (!category) {
        return next(new ErrorResponse(`Category not found with id of ${req.params.id}`, 404));
    }
    
    // Check if category has products
    const productsCount = await Product.countDocuments({ category: req.params.id });
    if (productsCount > 0) {
        return next(new ErrorResponse('Cannot delete category that has products. Please move or delete products first.', 400));
    }
    
    // Check if category has subcategories
    const subcategoriesCount = await Category.countDocuments({ parent: req.params.id });
    if (subcategoriesCount > 0) {
        return next(new ErrorResponse('Cannot delete category that has subcategories. Please move or delete subcategories first.', 400));
    }
    
    await category.deleteOne();
    
    res.status(200).json({
        success: true,
        data: {}
    });
});

// @desc    Get category tree (hierarchical structure)
// @route   GET /api/categories/tree
// @access  Private
const getCategoryTree = asyncHandler(async (req, res, next) => {
    try {
        // Get all active categories
        const categories = await Category.find({ isActive: true })
            .sort({ sortOrder: 1, name: 1 });
        
        // Build tree structure
        const categoryMap = {};
        const tree = [];
        
        // First pass: create map of all categories
        categories.forEach(category => {
            categoryMap[category._id] = {
                ...category.toObject(),
                children: []
            };
        });
        
        // Second pass: build tree structure
        categories.forEach(category => {
            if (category.parent) {
                if (categoryMap[category.parent]) {
                    categoryMap[category.parent].children.push(categoryMap[category._id]);
                }
            } else {
                tree.push(categoryMap[category._id]);
            }
        });
        
        res.status(200).json({
            success: true,
            data: tree
        });
    } catch (error) {
        return next(new ErrorResponse('Error building category tree', 500));
    }
});

module.exports = {
    getCategories,
    getCategory,
    createCategory,
    updateCategory,
    deleteCategory,
    getCategoryTree
};
