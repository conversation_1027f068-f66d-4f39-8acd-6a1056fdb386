'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  ShoppingCart,
  Search,
  Plus,
  Edit,
  Eye,
  RefreshCw,
  Filter,
  Calendar,
  Package,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Truck,
  FileText
} from 'lucide-react'

interface PurchaseOrder {
  _id: string
  orderNumber: string
  supplier: {
    _id: string
    name: string
    code: string
    contactPerson: {
      name: string
      email?: string
      phone?: string
    }
  }
  orderDate: string
  expectedDeliveryDate: string
  actualDeliveryDate?: string
  status: 'draft' | 'pending' | 'approved' | 'ordered' | 'partially_received' | 'received' | 'cancelled'
  priority: 'low' | 'normal' | 'high' | 'urgent'
  totalItems: number
  totalAmount: number
  currency: string
  completionPercentage: number
  createdBy: {
    _id: string
    firstName: string
    lastName: string
  }
  approvedBy?: {
    _id: string
    firstName: string
    lastName: string
  }
  items: Array<{
    _id: string
    product: {
      _id: string
      name: string
      sku: string
    }
    productName: string
    quantity: number
    receivedQuantity: number
    unitCost: number
    totalCost: number
  }>
}

export default function PurchaseOrdersPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [purchaseOrders, setPurchaseOrders] = useState<PurchaseOrder[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('')
  const [priorityFilter, setPriorityFilter] = useState('')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated) {
      fetchPurchaseOrders()
    }
  }, [isAuthenticated, searchQuery, statusFilter, priorityFilter])

  // Listen for currency changes and refresh data
  useEffect(() => {
    const handleCurrencyChange = () => {
      console.log('📊 Purchase Orders currency change detected, refreshing data')
      // No need to refetch data, just trigger re-render with new currency formatting
      // The formatCurrency function will handle the new currency
    }

    // Listen for currency change events
    window.addEventListener('currency-changed', handleCurrencyChange)
    window.addEventListener('currencyChanged', handleCurrencyChange)

    return () => {
      window.removeEventListener('currency-changed', handleCurrencyChange)
      window.removeEventListener('currencyChanged', handleCurrencyChange)
    }
  }, [])

  const fetchPurchaseOrders = async () => {
    try {
      setLoading(true)

      // Build query parameters
      const params: any = {}

      if (searchQuery) {
        params.search = searchQuery
      }

      if (statusFilter) {
        params.status = statusFilter
      }

      if (priorityFilter) {
        params.priority = priorityFilter
      }

      // Fetch from real API
      const response = await apiClient.getPurchaseOrders(params)

      if (response.success) {
        // Ensure response.data is an array
        const ordersData = Array.isArray(response.data) ? response.data : []
        setPurchaseOrders(ordersData)
        console.log('✅ Purchase orders loaded:', ordersData.length, 'orders')
      } else {
        console.error('Failed to fetch purchase orders:', response.error)
        setPurchaseOrders([])
      }
    } catch (error) {
      console.error('Error fetching purchase orders:', error)
      setPurchaseOrders([])
    } finally {
      setLoading(false)
    }
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'draft': return <FileText className="h-4 w-4 text-gray-600" />
      case 'pending': return <Clock className="h-4 w-4 text-yellow-600" />
      case 'approved': return <CheckCircle className="h-4 w-4 text-blue-600" />
      case 'ordered': return <ShoppingCart className="h-4 w-4 text-purple-600" />
      case 'partially_received': return <Package className="h-4 w-4 text-orange-600" />
      case 'received': return <CheckCircle className="h-4 w-4 text-green-600" />
      case 'cancelled': return <XCircle className="h-4 w-4 text-red-600" />
      default: return <FileText className="h-4 w-4 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'approved':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Approved</Badge>
      case 'ordered':
        return <Badge variant="outline" className="bg-purple-100 text-purple-800">Ordered</Badge>
      case 'partially_received':
        return <Badge variant="outline" className="bg-orange-100 text-orange-800">Partial</Badge>
      case 'received':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Received</Badge>
      case 'cancelled':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Cancelled</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case 'low':
        return <Badge variant="outline" className="bg-gray-100 text-gray-600">Low</Badge>
      case 'normal':
        return <Badge variant="outline" className="bg-blue-100 text-blue-600">Normal</Badge>
      case 'high':
        return <Badge variant="outline" className="bg-orange-100 text-orange-600">High</Badge>
      case 'urgent':
        return <Badge variant="outline" className="bg-red-100 text-red-600">Urgent</Badge>
      default:
        return <Badge variant="outline">Normal</Badge>
    }
  }

  const formatPrice = (price: number | undefined | null) => {
    if (typeof price !== 'number' || isNaN(price) || price === null || price === undefined) {
      return formatCurrency(0)
    }
    return formatCurrency(price)
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const text = {
    en: {
      title: 'Purchase Orders',
      description: 'Manage purchase orders and supplier orders',
      newPurchaseOrder: 'New Purchase Order',
      refresh: 'Refresh',
      searchPlaceholder: 'Search purchase orders...',
      allStatuses: 'All Statuses',
      allPriorities: 'All Priorities',
      orderNumber: 'Order #',
      supplier: 'Supplier',
      orderDate: 'Order Date',
      expectedDelivery: 'Expected Delivery',
      status: 'Status',
      priority: 'Priority',
      items: 'Items',
      progress: 'Progress',
      totalAmount: 'Total Amount',
      actions: 'Actions',
      view: 'View',
      edit: 'Edit',
      noPurchaseOrders: 'No purchase orders found',
      noPurchaseOrdersDesc: 'No purchase orders match the selected filters',
      priorities: {
        low: 'Low',
        normal: 'Normal',
        high: 'High',
        urgent: 'Urgent'
      },
      statuses: {
        draft: 'Draft',
        pending: 'Pending',
        approved: 'Approved',
        ordered: 'Ordered',
        partially_received: 'Partially Received',
        received: 'Received',
        cancelled: 'Cancelled'
      }
    },
    mm: {
      title: 'ဝယ်ယူမှု အမှာစာများ',
      description: 'ဝယ်ယူမှု အမှာစာများ နှင့် ပေးသွင်းသူ အမှာစာများ စီမံခန့်ခွဲမှု',
      newPurchaseOrder: 'ဝယ်ယူမှု အမှာစာ အသစ်',
      refresh: 'ပြန်လည်ရယူရန်',
      searchPlaceholder: 'ဝယ်ယူမှု အမှာစာများ ရှာရန်...',
      allStatuses: 'အခြေအနေ အားလုံး',
      allPriorities: 'ဦးစားပေးမှု အားလုံး',
      orderNumber: 'အမှာစာ နံပါတ်',
      supplier: 'ပေးသွင်းသူ',
      orderDate: 'အမှာစာ ရက်စွဲ',
      expectedDelivery: 'မျှော်လင့်ထားသော ပေးပို့မှု',
      status: 'အခြေအနေ',
      priority: 'ဦးစားပေးမှု',
      items: 'ပစ္စည်းများ',
      progress: 'တိုးတက်မှု',
      totalAmount: 'စုစုပေါင်း ပမာဏ',
      actions: 'လုပ်ဆောင်ချက်များ',
      view: 'ကြည့်ရန်',
      edit: 'ပြင်ရန်',
      noPurchaseOrders: 'ဝယ်ယူမှု အမှာစာ မရှိပါ',
      noPurchaseOrdersDesc: 'ရွေးချယ်ထားသော filter များနှင့် ကိုက်ညီသော ဝယ်ယူမှု အမှာစာ မရှိပါ',
      priorities: {
        low: 'နိမ့်',
        normal: 'ပုံမှန်',
        high: 'မြင့်',
        urgent: 'အရေးပေါ်'
      },
      statuses: {
        draft: 'မူကြမ်း',
        pending: 'စောင့်ဆိုင်းနေသော',
        approved: 'အတည်ပြုပြီး',
        ordered: 'အမှာစာပေး ပြီး',
        partially_received: 'တစ်စိတ်တစ်ပိုင်း ရရှိပြီး',
        received: 'ရရှိပြီး',
        cancelled: 'ပယ်ဖျက်ပြီး'
      }
    }
  }

  const t = text[language]



  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              {t.title}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mt-1">
              {t.description}
            </p>
          </div>

          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchPurchaseOrders}>
              <RefreshCw className="h-4 w-4 mr-2" />
              {t.refresh}
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push('/purchase-orders/receive')}
              className="bg-green-50 hover:bg-green-100 text-green-700 border-green-200"
            >
              <Package className="h-4 w-4 mr-2" />
              {language === 'mm' ? 'ကုန်ပစ္စည်း လက်ခံရန်' : 'Receive Items'}
            </Button>
            <Button onClick={() => router.push('/purchase-orders/new')}>
              <Plus className="h-4 w-4 mr-2" />
              {t.newPurchaseOrder}
            </Button>
          </div>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={t.searchPlaceholder}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex gap-2">
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  aria-label="Filter by status"
                  title="Filter by status"
                >
                  <option value="">{t.allStatuses}</option>
                  <option value="draft">{t.statuses.draft}</option>
                  <option value="pending">{t.statuses.pending}</option>
                  <option value="approved">{t.statuses.approved}</option>
                  <option value="ordered">{t.statuses.ordered}</option>
                  <option value="partially_received">{t.statuses.partially_received}</option>
                  <option value="received">{t.statuses.received}</option>
                  <option value="cancelled">{t.statuses.cancelled}</option>
                </select>

                <select
                  value={priorityFilter}
                  onChange={(e) => setPriorityFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100"
                  aria-label="Filter by priority"
                  title="Filter by priority"
                >
                  <option value="">{t.allPriorities}</option>
                  <option value="low">{t.priorities.low}</option>
                  <option value="normal">{t.priorities.normal}</option>
                  <option value="high">{t.priorities.high}</option>
                  <option value="urgent">{t.priorities.urgent}</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Purchase Orders Table */}
        <Card>
          <CardContent className="p-0">
            {!purchaseOrders || purchaseOrders.length === 0 ? (
              <div className="text-center py-12">
                <ShoppingCart className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {t.noPurchaseOrders}
                </h3>
                <p className="text-gray-500">
                  {t.noPurchaseOrdersDesc}
                </p>
              </div>
            ) : (
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.orderNumber}
                      </th>
                      <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.supplier}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.orderDate}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.status}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.priority}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.items}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.progress}
                      </th>
                      <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.totalAmount}
                      </th>
                      <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                        {t.actions}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                    {purchaseOrders && purchaseOrders.map((order) => (
                      <tr key={order._id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-6 py-4">
                          <div className="flex items-center gap-2">
                            {getStatusIcon(order.status)}
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {order.orderNumber}
                              </div>
                              <div className="text-sm text-gray-500">
                                {formatDate(order.orderDate)}
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4">
                          <div>
                            <div className="text-sm font-medium text-gray-900 dark:text-white">
                              {order.supplier.name}
                            </div>
                            <div className="text-sm text-gray-500 dark:text-gray-400">
                              {order.supplier.code}
                            </div>
                            <div className="text-xs text-gray-400 dark:text-gray-500">
                              {order.supplier.contactPerson.name}
                            </div>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <div className="text-sm">
                            {formatDate(order.expectedDeliveryDate)}
                          </div>
                          {order.actualDeliveryDate && (
                            <div className="text-xs text-green-600">
                              Delivered: {formatDate(order.actualDeliveryDate)}
                            </div>
                          )}
                        </td>
                        <td className="px-6 py-4 text-center">
                          {getStatusBadge(order.status)}
                        </td>
                        <td className="px-6 py-4 text-center">
                          {getPriorityBadge(order.priority)}
                        </td>
                        <td className="px-6 py-4 text-center">
                          <div className="text-sm font-medium">
                            {order.totalItems}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <div className="flex items-center justify-center gap-2">
                            <div className="w-16 bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${
                                  order.completionPercentage === 100 ? 'bg-green-500' :
                                  order.completionPercentage > 0 ? 'bg-orange-500' : 'bg-gray-300'
                                }`}
                                style={{ width: `${order.completionPercentage}%` }}
                              ></div>
                            </div>
                            <span className="text-xs text-gray-500 dark:text-gray-400">
                              {order.completionPercentage}%
                            </span>
                          </div>
                        </td>
                        <td className="px-6 py-4 text-right">
                          <div className="text-sm font-medium">
                            {formatPrice(order.totalAmount)}
                          </div>
                        </td>
                        <td className="px-6 py-4 text-center">
                          <div className="flex justify-center gap-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => router.push(`/purchase-orders/${order._id}`)}
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            {order.status === 'draft' && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => router.push(`/purchase-orders/${order._id}/edit`)}
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                            )}
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </MainLayout>
  )
}
