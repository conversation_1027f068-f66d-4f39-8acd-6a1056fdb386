'use client'

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { useSettings } from '@/contexts/settings-context'
import { useTheme } from '@/contexts/theme-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  Download,
  ArrowLeft,
  Printer,
  Receipt,
  Sun,
  Moon,
  Globe,
  DollarSign,
  Palette,
  Phone,
  Mail,
  MapPin,
  Award,
  Crown,
  Gem,
  Shield,
  Zap,
  Sparkles,
  Trophy,
  Diamond,
  Flame,
  Target,
  Briefcase,
  Users,
  Building,
  FileText,
  Layers,
  Star,
  QrCode,
  Upload,
  Palette as PaletteIcon,
  Type,
  Hash,
  Send,
  Save,
  History,
  FileSpreadsheet,
  ShoppingCart
} from 'lucide-react'

interface InvoiceData {
  saleNumber: string
  timestamp: Date
  customer: {
    name: string
    phone: string
    address: string
  }
  items: Array<{
    product: {
      id: string
      name: string
      price: number
      sku: string
    }
    quantity: number
    totalPrice: number
  }>
  subtotal: number
  tax: number
  shipping: number
  discount: number
  total: number
  paymentMethod: string
  currency?: string
}

// Translation object
const translations = {
  en: {
    invoice: "INVOICE",
    billTo: "BILL TO",
    subtotal: "Subtotal",
    tax: "Tax",
    total: "Total",
    print: "Print Invoice",
    download: "Download PDF",
    customer: "Valued Customer",
    items: "Premium Items",
    summary: "Invoice Summary",
    discount: "Discount",
    shipping: "Shipping",
    grandTotal: "Grand Total",
    paymentTerms: "Payment Terms",
    notes: "Notes",
    signature: "Authorized Signature",
    thankYou: "Thank you for your business",
    companyName: "BitsTech",
    companyTagline: "Premium Technology Solutions",
    templates: "Invoice Templates",
    chooseDesigns: "Choose from 10 designs"
  },
  mm: {
    invoice: "ငွေတောင်းခံလွှာ",
    billTo: "ငွေပေးရမည့်သူ",
    subtotal: "စုစုပေါင်း",
    tax: "အခွန်",
    total: "စုစုပေါင်းကျသင့်ငွေ",
    print: "ပရင့်ထုတ်မည်",
    download: "PDF ဒေါင်းလုဒ်",
    customer: "တန်ဖိုးရှိသော ဖောက်သည်",
    items: "ပစ္စည်းများ",
    summary: "ငွေတောင်းခံလွှာ အနှစ်ချုပ်",
    discount: "လျှော့စျေး",
    shipping: "ပို့ဆောင်ခ",
    grandTotal: "စုစုပေါင်းကျသင့်ငွေ",
    paymentTerms: "ငွေပေးချေမှု စည်းကမ်းများ",
    notes: "မှတ်ချက်များ",
    signature: "တရားဝင် လက်မှတ်",
    thankYou: "သင့်ကို ကျေးဇူးတင်ပါသည်",
    companyName: "ဘစ်စ်တက်",
    companyTagline: "အဆင့်မြင့် နည်းပညာ ဖြေရှင်းချက်များ",
    templates: "ငွေတောင်းခံလွှာ ပုံစံများ",
    chooseDesigns: "၁၀ မျိုးမှ ရွေးချယ်ပါ"
  }
}

export default function InvoicePage() {
  const router = useRouter()
  const {
    actualTheme,
    toggleTheme,
    toggleLanguage,
    language
  } = useTheme()
  const { currentCurrency, formatCurrency, setCurrency, currentCurrencyInfo } = useCurrency()
  const [loading, setLoading] = useState(true)
  const [invoiceData, setInvoiceData] = useState<InvoiceData | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState(1)
  const [isRealTimeData, setIsRealTimeData] = useState(false)
  const [invoiceHistory, setInvoiceHistory] = useState<any[]>([])
  const [historyRefresh, setHistoryRefresh] = useState(0)

  // Template-specific customizations
  const [templateCustomizations, setTemplateCustomizations] = useState<{[key: number]: any}>({})

  // Get current template customization
  const getCurrentTemplateCustomization = () => {
    return templateCustomizations[selectedTemplate] || customization
  }

  // Load invoice history from localStorage and database
  const loadInvoiceHistory = async () => {
    try {
      // Load from localStorage first
      const localHistory = JSON.parse(localStorage.getItem('invoiceHistory') || '[]')

      // Try to load from database for real-time data
      try {
        const response = await fetch('/api/pos/sales', {
          headers: {
            'Authorization': `Bearer ${localStorage.getItem('token')}`,
            'Content-Type': 'application/json'
          }
        })

        if (response.ok) {
          const result = await response.json()
          if (result.success && result.data) {
            const dbInvoices = result.data.slice(0, 10).map((sale: any) => ({
              saleNumber: sale.saleNumber,
              timestamp: new Date(sale.createdAt),
              customer: {
                name: sale.customer.name,
                phone: sale.customer.phone,
                address: sale.customer.address || 'Yangon, Myanmar'
              },
              items: sale.items.map((item: any) => ({
                product: {
                  id: item.product._id || item.product,
                  name: item.productName,
                  price: item.unitPrice,
                  sku: item.productSku || 'N/A'
                },
                quantity: item.quantity,
                totalPrice: item.totalPrice
              })),
              subtotal: sale.subtotal,
              discount: sale.discount || 0,
              tax: sale.tax || 0,
              shipping: sale.shipping || 0,
              total: sale.total,
              paymentMethod: sale.paymentMethod,
              currency: sale.currency || 'MMK',
              template: 1, // Default template
              savedAt: sale.createdAt,
              isRealTime: true // Mark as real-time data
            }))

            // Combine local and database invoices, prioritize database
            const combinedHistory = [...dbInvoices, ...localHistory.filter((local: any) =>
              !dbInvoices.some((db: any) => db.saleNumber === local.saleNumber)
            )]

            setInvoiceHistory(combinedHistory)
            console.log('📊 Invoice history loaded:', combinedHistory.length, 'invoices')
            return
          }
        }
      } catch (dbError) {
        console.warn('Database history load failed, using localStorage only:', dbError)
      }

      // Fallback to localStorage only
      setInvoiceHistory(localHistory)
      console.log('📁 Local invoice history loaded:', localHistory.length, 'invoices')
    } catch (error) {
      console.error('Error loading invoice history:', error)
      setInvoiceHistory([])
    }
  }

  // Check if current invoice is real-time data
  const checkIsRealTimeData = (invoice: any) => {
    return invoice?.isRealTime ||
           (invoice?.saleNumber && !invoice.saleNumber.startsWith('mock_sale_'))
  }

  // Update current template customization
  const updateTemplateCustomization = (updates: any) => {
    setTemplateCustomizations(prev => ({
      ...prev,
      [selectedTemplate]: {
        ...getCurrentTemplateCustomization(),
        ...updates
      }
    }))
  }

  // Company settings state
  const [companySettings, setCompanySettings] = useState({
    name: 'BitsTech Solutions',
    address: 'No. 456, Technology Street, Yangon, Myanmar',
    phone: '+95 9 ***********',
    email: '<EMAIL>',
    website: 'www.bitstech.com',
    logo: '',
    taxId: 'TAX-*********',
    businessLicense: 'BL-*********'
  })

  // Customization states with localStorage support
  const [customization, setCustomization] = useState({
    companyLogo: null as string | null,
    companyName: 'BitsTech',
    customColors: {
      primary: '#f97316', // orange-500
      secondary: '#1f2937', // gray-800
      accent: '#059669' // green-600
    },
    customFonts: {
      heading: 'Arial',
      body: 'Arial'
    },
    invoiceNumberFormat: 'INV-{YYYY}-{###}',
    showQRCode: true,
    qrCodeData: 'Invoice: ',
    customNotes: 'Thank you for your business!'
  })

  // Load company settings from localStorage on mount
  useEffect(() => {
    // Try multiple localStorage keys for company settings
    const loadCompanySettings = () => {
      // Try the main company settings key first
      let savedSettings = localStorage.getItem('company-settings')
      if (!savedSettings) {
        // Try alternative keys
        savedSettings = localStorage.getItem('bitstech_company_info')
      }

      if (savedSettings) {
        try {
          const parsed = JSON.parse(savedSettings)

          // Map different field names to our standard format
          const mappedSettings = {
            name: parsed.name || parsed.storeName || 'BitsTech Solutions',
            address: parsed.address || parsed.storeAddress || 'No. 456, Technology Street, Yangon, Myanmar',
            phone: parsed.phone || parsed.storePhone || '+95 9 ***********',
            email: parsed.email || parsed.storeEmail || '<EMAIL>',
            website: parsed.website || 'www.bitstech.com',
            logo: parsed.logo || parsed.logoUrl || '',
            taxId: parsed.taxId || parsed.taxNumber || parsed.registrationNumber || 'TAX-*********',
            businessLicense: parsed.businessLicense || parsed.registrationNumber || 'BL-*********'
          }

          setCompanySettings(prev => ({ ...prev, ...mappedSettings }))
          console.log('✅ Company settings loaded:', mappedSettings)
          return true
        } catch (error) {
          console.error('Error parsing company settings:', error)
        }
      }
      return false
    }

    // Load settings
    const loaded = loadCompanySettings()
    if (!loaded) {
      console.log('ℹ️ No saved company settings found, using defaults')
    }

    // Also sync with customization company name
    const savedCustomization = localStorage.getItem('invoice-customization')
    if (savedCustomization) {
      try {
        const customizationData = JSON.parse(savedCustomization)
        if (customizationData.companyName) {
          setCompanySettings(prev => ({ ...prev, name: customizationData.companyName }))
        }
      } catch (error) {
        console.error('Error syncing customization data:', error)
      }
    }
  }, [])

  // Load customization from localStorage on mount
  useEffect(() => {
    const savedCustomization = localStorage.getItem('invoice-customization')
    if (savedCustomization) {
      try {
        const parsed = JSON.parse(savedCustomization)
        setCustomization(prev => ({ ...prev, ...parsed }))
      } catch (error) {
        console.error('Error loading customization:', error)
      }
    }
  }, [])

  // Save customization to localStorage whenever it changes
  useEffect(() => {
    try {
      localStorage.setItem('invoice-customization', JSON.stringify(customization))
    } catch (error) {
      console.warn('Failed to save customization to localStorage:', error)
    }
  }, [customization])

  // Load template-specific customizations from localStorage
  useEffect(() => {
    const savedTemplateCustomizations = localStorage.getItem('template-customizations')
    if (savedTemplateCustomizations) {
      try {
        const parsed = JSON.parse(savedTemplateCustomizations)
        setTemplateCustomizations(parsed)
      } catch (error) {
        console.error('Error loading template customizations:', error)
      }
    }
  }, [])

  // Save template-specific customizations to localStorage
  useEffect(() => {
    try {
      localStorage.setItem('template-customizations', JSON.stringify(templateCustomizations))
    } catch (error) {
      console.warn('Failed to save template customizations to localStorage:', error)
      // If storage is full, try to clear some space
      try {
        localStorage.removeItem('invoiceHistory')
        localStorage.setItem('template-customizations', JSON.stringify(templateCustomizations))
      } catch (e) {
        console.error('Storage quota exceeded even after cleanup')
      }
    }
  }, [templateCustomizations])

  // Get current translations
  const t = translations[language as keyof typeof translations] || translations.en

  // Real QR Code generator function using QR Server API
  const generateQRCode = (data: string) => {
    if (!data || data.trim() === '') {
      data = 'BitsTech Invoice System'
    }

    // Use QR Server API for real QR code generation with smaller size
    const encodedData = encodeURIComponent(data.trim())
    const qrCodeUrl = `https://api.qrserver.com/v1/create-qr-code/?size=120x120&data=${encodedData}&format=png&ecc=M&margin=5`

    return qrCodeUrl
  }

  // Excel Export function
  const exportToExcel = () => {
    if (!invoiceData) return

    const csvContent = [
      ['Invoice Number', invoiceData.saleNumber],
      ['Date', invoiceData.timestamp.toLocaleDateString()],
      ['Customer', invoiceData.customer.name],
      ['Phone', invoiceData.customer.phone],
      ['Address', invoiceData.customer.address],
      [''],
      ['Item', 'SKU', 'Quantity', 'Unit Price', 'Total'],
      ...(invoiceData.items && invoiceData.items.length > 0
        ? invoiceData.items.map(item => [
            item.product.name,
            item.product.sku,
            item.quantity.toString(),
            item.product.price.toString(),
            item.totalPrice.toString()
          ])
        : [['No items', 'N/A', '0', '0', '0']]
      ),
      [''],
      ['Subtotal', '', '', '', (invoiceData.subtotal || 0).toString()],
      ['Discount', '', '', '', (invoiceData.discount || 0).toString()],
      ['Tax', '', '', '', (invoiceData.tax || 0).toString()],
      ['Shipping', '', '', '', (invoiceData.shipping || 0).toString()],
      ['Total', '', '', '', (invoiceData.total || 0).toString()]
    ]

    const csvString = csvContent.map(row => row.join(',')).join('\n')
    const blob = new Blob([csvString], { type: 'text/csv' })
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `invoice-${invoiceData.saleNumber}.csv`
    a.click()
    window.URL.revokeObjectURL(url)
  }

  // Load invoice data (real or sample)
  useEffect(() => {
    const loadInvoiceData = async () => {
      console.log('Loading invoice data...')
      setLoading(true) // Ensure loading state is set

      // Check for saleId in URL parameters
      const urlParams = new URLSearchParams(window.location.search)
      const saleId = urlParams.get('saleId')

      // First, try to load from localStorage (recent checkout)
      const lastCheckoutData = localStorage.getItem('lastCheckoutData')
      console.log('🔍 Checking for lastCheckoutData:', lastCheckoutData ? 'Found' : 'Not found')

      if (lastCheckoutData) {
        try {
          const checkoutData = JSON.parse(lastCheckoutData)
          console.log('📦 Found checkout data in localStorage:', checkoutData)
          console.log('📦 Customer Details:', checkoutData.customerDetails)
          console.log('📦 Cart Items:', checkoutData.cartItems)
          console.log('📦 Payment Method:', checkoutData.paymentMethod)

          // Always use the checkout data if it exists (remove time restriction for debugging)
          // Validate that we have the required data
          if (checkoutData.customerDetails && checkoutData.cartItems && checkoutData.saleNumber) {
            console.log('✅ Checkout data validation passed')

            // Transform checkout data to InvoiceData format
            const invoiceData: InvoiceData = {
              saleNumber: checkoutData.saleNumber,
              timestamp: new Date(checkoutData.timestamp || new Date()),
              customer: {
                name: checkoutData.customerDetails.name || 'Unknown Customer',
                phone: checkoutData.customerDetails.phone || 'N/A',
                address: checkoutData.customerDetails.address || 'Yangon, Myanmar'
              },
              items: (checkoutData.cartItems || []).map((item: any) => ({
                product: {
                  id: item.productId || 'unknown',
                  name: item.productName || 'Unknown Product',
                  price: item.unitPrice || 0,
                  sku: item.productSku || 'N/A'
                },
                quantity: item.quantity || 1,
                totalPrice: item.totalPrice || 0
              })),
              subtotal: checkoutData.subtotal || 0,
              discount: checkoutData.discount || 0,
              tax: checkoutData.tax || 0,
              shipping: checkoutData.shipping || 0,
              total: checkoutData.total || 0,
              paymentMethod: checkoutData.paymentMethod || 'Cash',
              currency: checkoutData.currency || currentCurrency
            }

            setInvoiceData(invoiceData)
            setIsRealTimeData(true)
            setLoading(false)
            console.log('✅ Real checkout data loaded successfully:', invoiceData)
            console.log('✅ Customer:', invoiceData.customer)
            console.log('✅ Items count:', invoiceData.items.length)
            console.log('✅ Items details:', invoiceData.items)
            console.log('✅ Payment Method:', invoiceData.paymentMethod)
            console.log('✅ Total Amount:', invoiceData.total)
            return
          } else {
            console.warn('❌ Incomplete checkout data found, missing required fields:')
            console.warn('- customerDetails:', !!checkoutData.customerDetails)
            console.warn('- cartItems:', !!checkoutData.cartItems)
            console.warn('- saleNumber:', !!checkoutData.saleNumber)
          }
        } catch (error) {
          console.error('❌ Error parsing checkout data:', error)
        }
      } else {
        console.log('ℹ️ No lastCheckoutData found in localStorage')
      }

      if (saleId) {
        console.log('Sale ID found:', saleId)

        // Check if it's a mock sale ID first
        if (saleId.startsWith('mock_sale_')) {
          console.log('❌ Mock sale ID detected, showing empty invoice instead')
          const emptyMockData: InvoiceData = {
            saleNumber: `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
            timestamp: new Date(),
            customer: {
              name: language === 'mm' ? 'ဖောက်သည်မရှိပါ' : 'No Customer',
              phone: 'N/A',
              address: language === 'mm' ? 'POS မှ စစ်မှန်သော ငွေရှင်းမှုပြုလုပ်ပါ' : 'Please complete a real POS checkout'
            },
            items: [], // Empty items for mock data
            subtotal: 0,
            discount: 0,
            tax: 0,
            shipping: 0,
            total: 0,
            paymentMethod: language === 'mm' ? 'ငွေပေးချေမှုမရှိပါ' : 'No Payment Method',
            currency: currentCurrency
          }

          setInvoiceData(emptyMockData)
          setIsRealTimeData(false)
          setLoading(false)
          console.log('Empty mock invoice data loaded:', emptyMockData)
          return
        }

        // Try to fetch real sale data from API using apiClient
        try {
          console.log('Fetching real sale data for ID:', saleId)

          const response = await apiClient.getSale(saleId)
          if (response.success && response.data) {
            const saleData = response.data

            // Transform API data to InvoiceData format
            const invoiceData: InvoiceData = {
              saleNumber: saleData.saleNumber || `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
              timestamp: new Date(saleData.createdAt || new Date()),
              customer: {
                name: saleData.customer?.name || saleData.customer?.firstName + ' ' + saleData.customer?.lastName || 'Walk-in Customer',
                phone: saleData.customer?.phone || 'N/A',
                address: saleData.customer?.address || 'Yangon, Myanmar'
              },
              items: saleData.items?.map((item: any) => ({
                name: item.product?.name || item.productName || 'Unknown Product',
                quantity: item.quantity || 1,
                price: item.price || item.unitPrice || 0,
                total: item.total || item.totalPrice || (item.quantity * item.price) || 0
              })) || [],
              subtotal: saleData.subtotal || 0,
              discount: saleData.discount || 0,
              tax: saleData.tax || 0,
              shipping: saleData.shipping || 0,
              total: saleData.total || 0,
              paymentMethod: saleData.paymentMethod || 'Cash',
              currency: saleData.currency || currentCurrency
            }

            setInvoiceData(invoiceData)
            setIsRealTimeData(true)
            setLoading(false)
            console.log('✅ Real sale data loaded:', invoiceData)
            return
          } else {
            console.warn('Sale not found in database, falling back to empty invoice')
          }
        } catch (error) {
          console.warn('API call failed, falling back to empty invoice:', error)
        }
      }

      // Fallback to empty invoice data (no real data found)
      console.log('⚠️ No real checkout data found - showing empty invoice template')
      const emptyData: InvoiceData = {
        saleNumber: `INV-${new Date().getFullYear()}-${String(Date.now()).slice(-6)}`,
        timestamp: new Date(),
        customer: {
          name: 'No Customer Selected',
          phone: 'N/A',
          address: 'Please complete a POS checkout to see real data'
        },
        items: [], // Empty items array
        subtotal: 0,
        discount: 0,
        tax: 0,
        shipping: 0,
        total: 0,
        paymentMethod: 'No Payment Method',
        currency: currentCurrency
      }

      setInvoiceData(emptyData)
      setIsRealTimeData(false)
      setLoading(false)
      console.log('Empty invoice template loaded:', emptyData)
      console.log('💡 Complete a POS checkout to see real invoice data')
    }

    // Add timeout to prevent infinite loading
    const timeoutId = setTimeout(() => {
      if (loading) {
        console.warn('⚠️ Invoice loading timeout - forcing completion')
        setLoading(false)
      }
    }, 5000) // 5 second timeout

    loadInvoiceData().finally(() => {
      clearTimeout(timeoutId)
    })

    // Load invoice history
    loadInvoiceHistory()

    return () => {
      clearTimeout(timeoutId)
    }
  }, []) // Remove all dependencies to prevent re-runs

  // Load invoice history when historyRefresh changes
  useEffect(() => {
    loadInvoiceHistory()
  }, [historyRefresh])

  // Update invoice currency when currency context changes
  useEffect(() => {
    if (invoiceData && invoiceData.currency !== currentCurrency) {
      setInvoiceData(prev => {
        if (prev) {
          return { ...prev, currency: currentCurrency }
        }
        return prev
      })
      console.log('💰 Invoice currency updated to:', currentCurrency)
    }
  }, [currentCurrency, invoiceData?.currency])

  // Listen for currency changes from other components
  useEffect(() => {
    const handleCurrencyChange = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      if (newCurrency && ['MMK', 'USD', 'THB'].includes(newCurrency)) {
        console.log('🔄 Invoice received currency change:', newCurrency)
        // Currency context will handle the update automatically
      }
    }

    window.addEventListener('currency-changed', handleCurrencyChange as EventListener)
    return () => {
      window.removeEventListener('currency-changed', handleCurrencyChange as EventListener)
    }
  }, [])

  // Real-time WebSocket connection for instant updates
  useEffect(() => {
    // Handle invoice updates
    const handleInvoiceUpdate = (event: CustomEvent) => {
      const data = event.detail
      console.log('📡 Invoice update received:', data)

      if (data.saleId && invoiceData?.saleNumber === data.saleNumber) {
        setInvoiceData(prev => prev ? { ...prev, ...data.updates } : prev)
      }
    }

    // Handle company settings updates
    const handleCompanySettingsUpdate = (event: CustomEvent) => {
      const data = event.detail
      console.log('🏢 Company settings update received:', data)
      setCompanySettings(prev => ({ ...prev, ...data.settings }))
    }

    // Handle customization updates
    const handleCustomizationUpdate = (event: CustomEvent) => {
      const data = event.detail
      console.log('🎨 Customization update received:', data)
      setCustomization(prev => ({ ...prev, ...data.customization }))
    }

    // Add event listeners
    window.addEventListener('ws-invoice-update', handleInvoiceUpdate as EventListener)
    window.addEventListener('ws-company-settings-update', handleCompanySettingsUpdate as EventListener)
    window.addEventListener('ws-customization-update', handleCustomizationUpdate as EventListener)

    // Initialize WebSocket connection
    const initWebSocket = async () => {
      try {
        const { default: wsClient, subscribeToInvoiceUpdates } = await import('@/lib/websocket')
        subscribeToInvoiceUpdates()
        console.log('✅ Invoice WebSocket subscribed')
      } catch (error) {
        console.warn('WebSocket not available:', error)
      }
    }

    initWebSocket()

    // Cleanup function
    return () => {
      window.removeEventListener('ws-invoice-update', handleInvoiceUpdate as EventListener)
      window.removeEventListener('ws-company-settings-update', handleCompanySettingsUpdate as EventListener)
      window.removeEventListener('ws-customization-update', handleCustomizationUpdate as EventListener)
    }

    // Fallback polling for when WebSocket is not available
    const interval = setInterval(async () => {
      if (!isRealTimeData) return

      const urlParams = new URLSearchParams(window.location.search)
      const saleId = urlParams.get('saleId')

      if (saleId && !saleId.startsWith('mock_sale_')) {
        try {
          const response = await fetch(`/api/pos/receipt/${saleId}`, {
            headers: {
              'Authorization': `Bearer ${localStorage.getItem('token')}`,
              'Content-Type': 'application/json'
            }
          })

          if (response.ok) {
            const result = await response.json()
            if (result.success && result.data) {
              const saleData = result.data
              const updatedInvoiceData: InvoiceData = {
                saleNumber: saleData.saleNumber,
                timestamp: new Date(saleData.createdAt),
                customer: {
                  name: saleData.customer.name,
                  phone: saleData.customer.phone,
                  address: saleData.customer.address || 'Yangon, Myanmar'
                },
                items: saleData.items.map((item: any) => ({
                  product: {
                    id: item.product._id || item.product,
                    name: item.productName,
                    price: item.unitPrice,
                    sku: item.productSku || 'N/A'
                  },
                  quantity: item.quantity,
                  totalPrice: item.totalPrice
                })),
                subtotal: saleData.subtotal,
                discount: saleData.discount || 0,
                tax: saleData.tax || 0,
                shipping: saleData.shipping || 0,
                total: saleData.total,
                paymentMethod: saleData.paymentMethod,
                currency: invoiceData?.currency || 'MMK'
              }
              setInvoiceData(updatedInvoiceData)
              console.log('📊 Polling fallback update:', updatedInvoiceData)
            }
          }
        } catch (error) {
          console.warn('Polling error:', error)
        }
      }
    }, 30000) // Poll every 30 seconds as fallback

    return () => clearInterval(interval)
  }, [isRealTimeData])

  // Real-time settings synchronization
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === 'company-settings' && event.newValue) {
        try {
          const newSettings = JSON.parse(event.newValue)
          setCompanySettings(prev => ({ ...prev, ...newSettings }))
          console.log('🔄 Company settings synced from storage:', newSettings)
        } catch (error) {
          console.error('Error syncing company settings:', error)
        }
      }

      if (event.key === 'invoice-customization' && event.newValue) {
        try {
          const newCustomization = JSON.parse(event.newValue)
          setCustomization(prev => ({ ...prev, ...newCustomization }))
          console.log('🎨 Customization synced from storage:', newCustomization)
        } catch (error) {
          console.error('Error syncing customization:', error)
        }
      }
    }

    // Listen for storage changes from other tabs
    window.addEventListener('storage', handleStorageChange)

    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  // Handle direct PDF download using html-to-image + jsPDF
  const handleDownload = async () => {
    if (!invoiceData) return

    try {
      console.log('Starting high-quality PDF download...')

      // Show loading
      const loadingDiv = document.createElement('div')
      loadingDiv.style.cssText = `
        position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.95); color: white; padding: 30px 40px;
        border-radius: 15px; z-index: 10000; text-align: center;
        font-family: Arial, sans-serif; font-size: 16px; box-shadow: 0 15px 35px rgba(0,0,0,0.5);
        max-width: 400px; line-height: 1.5;
      `
      loadingDiv.innerHTML = `
        <div style="margin-bottom: 20px; font-size: 32px;">📄</div>
        <div style="font-weight: bold; margin-bottom: 15px; font-size: 18px;">Generating High-Quality PDF</div>
        <div style="font-size: 14px; margin-bottom: 15px; opacity: 0.9;">
          Converting template to image...<br>
          <strong>Please wait</strong>
        </div>
        <div style="font-size: 12px; opacity: 0.7; border-top: 1px solid rgba(255,255,255,0.2); padding-top: 15px; margin-top: 15px;">
          ⚡ This preserves all colors and styling
        </div>
      `
      document.body.appendChild(loadingDiv)

      // Dynamic imports for PDF libraries
      const [{ default: jsPDF }, { toPng }] = await Promise.all([
        import('jspdf'),
        import('html-to-image')
      ])

      // Get the invoice element
      const invoiceElement = document.querySelector('.invoice-container') as HTMLElement
      if (!invoiceElement) {
        alert('Invoice not found. Please try again.')
        document.body.removeChild(loadingDiv)
        return
      }

      // Store current theme state and force light mode
      const isDarkMode = document.documentElement.classList.contains('dark')
      const bodyHasDark = document.body.classList.contains('dark')

      if (isDarkMode) document.documentElement.classList.remove('dark')
      if (bodyHasDark) document.body.classList.remove('dark')

      // Wait for theme change to apply
      await new Promise(resolve => setTimeout(resolve, 500))

      // Generate filename for the PDF
      const filename = `Invoice-${invoiceData?.saleNumber || 'Unknown'}-${new Date().toISOString().split('T')[0]}.pdf`

      // Configure html-to-image options for high quality with proper sizing
      const imageOptions = {
        quality: 1.0,
        pixelRatio: 2,
        width: 850,
        height: 1200,
        backgroundColor: '#ffffff',
        style: {
          transform: 'scale(1)',
          transformOrigin: 'top left',
          width: '850px',
          height: 'auto',
          minHeight: '1200px',
          margin: '0',
          padding: '25px',
          overflow: 'visible',
          boxSizing: 'border-box'
        },
        canvasWidth: 850,
        canvasHeight: 1200
      }

      // Convert HTML to high-quality PNG image
      const imageDataUrl = await toPng(invoiceElement, imageOptions)

      // Create PDF with exact A4 dimensions with proper margins
      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
        compress: true,
        putOnlyUsedFonts: true
      })

      // Calculate dimensions to fit A4 with proper margins
      const imgWidth = 200 // A4 width with small margins
      const imgHeight = 285 // A4 height with small margins

      // Add the image to PDF with 5mm margins on all sides
      pdf.addImage(imageDataUrl, 'PNG', 5, 6, imgWidth, imgHeight, '', 'FAST')

      // Download the PDF
      pdf.save(filename)

      // Restore theme
      setTimeout(() => {
        if (isDarkMode) document.documentElement.classList.add('dark')
        if (bodyHasDark) document.body.classList.add('dark')
      }, 500)

      // Remove loading and show success
      document.body.removeChild(loadingDiv)

      const successDiv = document.createElement('div')
      successDiv.style.cssText = `
        position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
        background: #10b981; color: white; padding: 25px 35px;
        border-radius: 15px; z-index: 10000; text-align: center;
        font-family: Arial, sans-serif; box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        max-width: 350px; line-height: 1.4;
      `
      successDiv.innerHTML = `
        <div style="font-size: 28px; margin-bottom: 12px;">✅</div>
        <div style="font-weight: bold; margin-bottom: 8px; font-size: 16px;">PDF Downloaded Successfully!</div>
        <div style="font-size: 14px; opacity: 0.9; margin-bottom: 8px;">High-quality PDF with all styling preserved</div>
        <div style="font-size: 12px; opacity: 0.7;">${filename}</div>
      `
      document.body.appendChild(successDiv)

      setTimeout(() => {
        if (document.body.contains(successDiv)) {
          document.body.removeChild(successDiv)
        }
      }, 5000)

    } catch (error) {
      console.error('PDF generation error:', error)

      // Remove loading if still present
      const loadingDiv = document.querySelector('[style*="Generating High-Quality PDF"]')
      if (loadingDiv && document.body.contains(loadingDiv)) {
        document.body.removeChild(loadingDiv)
      }

      alert('PDF generation failed. Please try again.')
    }
  }

  // Handle print function (existing print functionality)
  const handlePrint = () => {
    if (!invoiceData) return

    try {
      console.log('Starting print...')

      // Show loading
      const loadingDiv = document.createElement('div')
      loadingDiv.style.cssText = `
        position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
        background: rgba(0,0,0,0.8); color: white; padding: 20px 30px;
        border-radius: 10px; z-index: 10000; text-align: center;
        font-family: Arial, sans-serif; font-size: 16px;
      `
      loadingDiv.innerHTML = `
        <div style="margin-bottom: 10px;">🖨️</div>
        <div>Preparing for print...</div>
      `
      document.body.appendChild(loadingDiv)

      // Create A4 print-optimized styles with enhanced color preservation
      const printStyles = `
        <style>
          @media print {
            @page {
              size: A4 portrait;
              margin: 10mm 8mm;
              -webkit-print-color-adjust: exact;
              color-adjust: exact;
              print-color-adjust: exact;
            }

            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }

            html {
              width: 210mm !important;
              height: 297mm !important;
              margin: 0 !important;
              padding: 0 !important;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }

            body {
              font-family: 'Arial', 'Helvetica', sans-serif !important;
              font-size: 11px !important;
              line-height: 1.3 !important;
              background: white !important;
              color: black !important;
              width: 210mm !important;
              height: 297mm !important;
              margin: 0 !important;
              padding: 0 !important;
              box-sizing: border-box !important;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
              overflow: hidden !important;
            }

            /* Force light mode for printing */
            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }

            /* Override dark mode styles for print */
            .dark\\:bg-gray-800,
            .dark\\:bg-gray-900,
            .dark\\:text-white,
            .dark\\:text-gray-300,
            .dark\\:text-gray-400,
            .dark\\:border-gray-700 {
              background-color: white !important;
              color: black !important;
              border-color: #e5e7eb !important;
            }

            /* Ensure invoice container takes exact A4 dimensions */
            .invoice-container {
              width: 210mm !important;
              height: 297mm !important;
              max-width: 210mm !important;
              max-height: 297mm !important;
              margin: 0 !important;
              padding: 15mm !important;
              box-sizing: border-box !important;
              background: white !important;
              color: black !important;
              overflow: hidden !important;
              page-break-inside: avoid !important;
            }

            .invoice-container {
              width: 100% !important;
              background: white !important;
              color: black !important;
              padding: 10px !important;
              margin: 0 !important;
              max-width: 100% !important;
            }

            /* Compact spacing for A4 format */
            .mb-12 { margin-bottom: 12px !important; }
            .mb-8 { margin-bottom: 8px !important; }
            .mb-6 { margin-bottom: 6px !important; }
            .mb-4 { margin-bottom: 4px !important; }
            .mb-3 { margin-bottom: 3px !important; }
            .mb-2 { margin-bottom: 2px !important; }
            .p-10 { padding: 8px !important; }
            .p-8 { padding: 6px !important; }
            .p-6 { padding: 4px !important; }
            .p-4 { padding: 3px !important; }
            .p-3 { padding: 2px !important; }
            .px-8 { padding-left: 6px !important; padding-right: 6px !important; }
            .px-6 { padding-left: 4px !important; padding-right: 4px !important; }
            .px-4 { padding-left: 3px !important; padding-right: 3px !important; }
            .py-5 { padding-top: 4px !important; padding-bottom: 4px !important; }
            .py-4 { padding-top: 3px !important; padding-bottom: 3px !important; }
            .py-3 { padding-top: 2px !important; padding-bottom: 2px !important; }
            .py-2 { padding-top: 1px !important; padding-bottom: 1px !important; }
            .py-1 { padding-top: 1px !important; padding-bottom: 1px !important; }
            .px-16 { padding-left: 12px !important; padding-right: 12px !important; }
            .py-12 { padding-top: 8px !important; padding-bottom: 8px !important; }

            .print\\:hidden { display: none !important; }
            .print\\:shadow-none { box-shadow: none !important; }
            .print\\:rounded-none { border-radius: 0 !important; }
            .print\\:bg-white { background: white !important; }

            /* Enhanced color preservation for all templates */
            .bg-gradient-to-r,
            .bg-gradient-to-br,
            .bg-gradient-to-l,
            .bg-gradient-to-t,
            .bg-gradient-to-b {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }

            /* Template 1 - Red Corporate */
            .from-red-600 { --tw-gradient-from: #dc2626 !important; background-color: #dc2626 !important; }
            .to-red-700 { --tw-gradient-to: #b91c1c !important; background-color: #b91c1c !important; }
            .to-red-900 { --tw-gradient-to: #7f1d1d !important; background-color: #7f1d1d !important; }
            .from-red-500 { --tw-gradient-from: #ef4444 !important; background-color: #ef4444 !important; }
            .to-red-600 { --tw-gradient-to: #dc2626 !important; background-color: #dc2626 !important; }
            .bg-red-500 { background-color: #ef4444 !important; }
            .bg-red-600 { background-color: #dc2626 !important; }
            .bg-red-700 { background-color: #b91c1c !important; }
            .bg-red-900 { background-color: #7f1d1d !important; }

            /* Template 2 - Orange Premium */
            .from-orange-500 { --tw-gradient-from: #f97316 !important; background-color: #f97316 !important; }
            .to-orange-600 { --tw-gradient-to: #ea580c !important; background-color: #ea580c !important; }
            .bg-orange-500 { background-color: #f97316 !important; }
            .bg-orange-600 { background-color: #ea580c !important; }

            /* Template 3 - Blue Professional */
            .from-blue-600 { --tw-gradient-from: #2563eb !important; background-color: #2563eb !important; }
            .to-blue-700 { --tw-gradient-to: #1d4ed8 !important; background-color: #1d4ed8 !important; }
            .to-blue-900 { --tw-gradient-to: #1e3a8a !important; background-color: #1e3a8a !important; }
            .from-blue-500 { --tw-gradient-from: #3b82f6 !important; background-color: #3b82f6 !important; }
            .to-blue-600 { --tw-gradient-to: #2563eb !important; background-color: #2563eb !important; }
            .bg-blue-500 { background-color: #3b82f6 !important; }
            .bg-blue-600 { background-color: #2563eb !important; }
            .bg-blue-700 { background-color: #1d4ed8 !important; }
            .bg-blue-900 { background-color: #1e3a8a !important; }

            /* Template 4 - Green Modern */
            .from-green-600 { --tw-gradient-from: #16a34a !important; background-color: #16a34a !important; }
            .to-green-700 { --tw-gradient-to: #15803d !important; background-color: #15803d !important; }
            .to-green-900 { --tw-gradient-to: #14532d !important; background-color: #14532d !important; }
            .from-green-500 { --tw-gradient-from: #22c55e !important; background-color: #22c55e !important; }
            .to-green-600 { --tw-gradient-to: #16a34a !important; background-color: #16a34a !important; }
            .bg-green-500 { background-color: #22c55e !important; }
            .bg-green-600 { background-color: #16a34a !important; }
            .bg-green-700 { background-color: #15803d !important; }
            .bg-green-900 { background-color: #14532d !important; }

            /* Template 5 - Purple Luxury */
            .from-purple-600 { --tw-gradient-from: #9333ea !important; background-color: #9333ea !important; }
            .to-purple-700 { --tw-gradient-to: #7c3aed !important; background-color: #7c3aed !important; }
            .to-purple-900 { --tw-gradient-to: #581c87 !important; background-color: #581c87 !important; }
            .from-purple-500 { --tw-gradient-from: #a855f7 !important; background-color: #a855f7 !important; }
            .to-purple-600 { --tw-gradient-to: #9333ea !important; background-color: #9333ea !important; }
            .bg-purple-500 { background-color: #a855f7 !important; }
            .bg-purple-600 { background-color: #9333ea !important; }
            .bg-purple-700 { background-color: #7c3aed !important; }
            .bg-purple-900 { background-color: #581c87 !important; }

            /* Template 6 - Indigo Tech */
            .from-indigo-600 { --tw-gradient-from: #4f46e5 !important; background-color: #4f46e5 !important; }
            .to-indigo-700 { --tw-gradient-to: #4338ca !important; background-color: #4338ca !important; }
            .to-indigo-900 { --tw-gradient-to: #312e81 !important; background-color: #312e81 !important; }
            .from-indigo-500 { --tw-gradient-from: #6366f1 !important; background-color: #6366f1 !important; }
            .to-indigo-600 { --tw-gradient-to: #4f46e5 !important; background-color: #4f46e5 !important; }
            .bg-indigo-500 { background-color: #6366f1 !important; }
            .bg-indigo-600 { background-color: #4f46e5 !important; }
            .bg-indigo-700 { background-color: #4338ca !important; }
            .bg-indigo-900 { background-color: #312e81 !important; }

            /* Template 7 - Pink Creative */
            .from-pink-600 { --tw-gradient-from: #db2777 !important; background-color: #db2777 !important; }
            .to-pink-700 { --tw-gradient-to: #be185d !important; background-color: #be185d !important; }
            .to-pink-900 { --tw-gradient-to: #831843 !important; background-color: #831843 !important; }
            .from-pink-500 { --tw-gradient-from: #ec4899 !important; background-color: #ec4899 !important; }
            .to-pink-600 { --tw-gradient-to: #db2777 !important; background-color: #db2777 !important; }
            .bg-pink-500 { background-color: #ec4899 !important; }
            .bg-pink-600 { background-color: #db2777 !important; }
            .bg-pink-700 { background-color: #be185d !important; }
            .bg-pink-900 { background-color: #831843 !important; }

            /* Template 8 - Teal Fresh */
            .from-teal-600 { --tw-gradient-from: #0d9488 !important; background-color: #0d9488 !important; }
            .to-teal-700 { --tw-gradient-to: #0f766e !important; background-color: #0f766e !important; }
            .to-teal-900 { --tw-gradient-to: #134e4a !important; background-color: #134e4a !important; }
            .from-teal-500 { --tw-gradient-from: #14b8a6 !important; background-color: #14b8a6 !important; }
            .to-teal-600 { --tw-gradient-to: #0d9488 !important; background-color: #0d9488 !important; }
            .bg-teal-500 { background-color: #14b8a6 !important; }
            .bg-teal-600 { background-color: #0d9488 !important; }
            .bg-teal-700 { background-color: #0f766e !important; }
            .bg-teal-900 { background-color: #134e4a !important; }

            /* Template 9 - Cyan Digital */
            .from-cyan-600 { --tw-gradient-from: #0891b2 !important; background-color: #0891b2 !important; }
            .to-cyan-700 { --tw-gradient-to: #0e7490 !important; background-color: #0e7490 !important; }
            .to-cyan-900 { --tw-gradient-to: #164e63 !important; background-color: #164e63 !important; }
            .from-cyan-500 { --tw-gradient-from: #06b6d4 !important; background-color: #06b6d4 !important; }
            .to-cyan-600 { --tw-gradient-to: #0891b2 !important; background-color: #0891b2 !important; }
            .bg-cyan-500 { background-color: #06b6d4 !important; }
            .bg-cyan-600 { background-color: #0891b2 !important; }
            .bg-cyan-700 { background-color: #0e7490 !important; }
            .bg-cyan-900 { background-color: #164e63 !important; }

            /* Template 10 - Emerald Nature */
            .from-emerald-600 { --tw-gradient-from: #059669 !important; background-color: #059669 !important; }
            .to-emerald-700 { --tw-gradient-to: #047857 !important; background-color: #047857 !important; }
            .to-emerald-900 { --tw-gradient-to: #064e3b !important; background-color: #064e3b !important; }
            .from-emerald-500 { --tw-gradient-from: #10b981 !important; background-color: #10b981 !important; }
            .to-emerald-600 { --tw-gradient-to: #059669 !important; background-color: #059669 !important; }
            .bg-emerald-500 { background-color: #10b981 !important; }
            .bg-emerald-600 { background-color: #059669 !important; }
            .bg-emerald-700 { background-color: #047857 !important; }
            .bg-emerald-900 { background-color: #064e3b !important; }
            .to-red-800 { --tw-gradient-to: #991b1b !important; }
            .text-red-600 { color: #dc2626 !important; }
            .border-red-600 { border-color: #dc2626 !important; }

            /* Blue theme colors */
            .from-blue-600 { --tw-gradient-from: #2563eb !important; }
            .to-blue-800 { --tw-gradient-to: #1e40af !important; }
            .text-blue-600 { color: #2563eb !important; }
            .border-blue-600 { border-color: #2563eb !important; }

            /* Yellow theme colors */
            .from-yellow-600 { --tw-gradient-from: #ca8a04 !important; }
            .to-yellow-800 { --tw-gradient-to: #92400e !important; }
            .text-yellow-600 { color: #ca8a04 !important; }
            .border-yellow-600 { border-color: #ca8a04 !important; }

            /* Purple theme colors */
            .from-purple-600 { --tw-gradient-from: #9333ea !important; }
            .to-purple-800 { --tw-gradient-to: #6b21a8 !important; }
            .text-purple-600 { color: #9333ea !important; }
            .border-purple-600 { border-color: #9333ea !important; }

            /* Green theme colors */
            .from-green-600 { --tw-gradient-from: #16a34a !important; }
            .to-green-800 { --tw-gradient-to: #166534 !important; }
            .text-green-600 { color: #16a34a !important; }
            .border-green-600 { border-color: #16a34a !important; }

            /* Gray theme colors */
            .from-gray-800 { --tw-gradient-from: #1f2937 !important; }
            .to-black { --tw-gradient-to: #000000 !important; }
            .text-gray-800 { color: #1f2937 !important; }
            .border-gray-800 { border-color: #1f2937 !important; }

            /* Additional color support for new templates */
            .bg-red-600 { background-color: #dc2626 !important; }
            .bg-green-600 { background-color: #16a34a !important; }
            .bg-blue-600 { background-color: #2563eb !important; }
            .bg-orange-500 { background-color: #f97316 !important; }
            .bg-orange-600 { background-color: #ea580c !important; }
            .border-red-600 { border-color: #dc2626 !important; }
            .border-green-600 { border-color: #16a34a !important; }
            .border-blue-200 { border-color: #bfdbfe !important; }
            .border-orange-600 { border-color: #ea580c !important; }
            .text-red-600 { color: #dc2626 !important; }
            .text-green-600 { color: #16a34a !important; }
            .text-blue-600 { color: #2563eb !important; }
            .text-orange-500 { color: #f97316 !important; }

            /* Table styling */
            table {
              border-collapse: collapse !important;
              width: 100% !important;
              margin: 10px 0 !important;
            }
            th, td {
              border: 1px solid #333 !important;
              padding: 6px !important;
              text-align: left !important;
            }
            th {
              background-color: var(--tw-gradient-from, #333) !important;
              color: white !important;
              font-weight: bold !important;
            }

            /* Dark mode overrides */
            .dark\\:bg-gray-900,
            .dark\\:bg-gray-800,
            .dark\\:text-white,
            .dark\\:bg-gray-700 {
              background: white !important;
              color: black !important;
            }

            /* Compact text size adjustments for A4 */
            .text-6xl { font-size: 1.8rem !important; }
            .text-5xl { font-size: 1.5rem !important; }
            .text-4xl { font-size: 1.25rem !important; }
            .text-3xl { font-size: 1.1rem !important; }
            .text-2xl { font-size: 1rem !important; }
            .text-xl { font-size: 0.9rem !important; }
            .text-lg { font-size: 0.85rem !important; }
            .text-sm { font-size: 0.75rem !important; }
            .text-xs { font-size: 0.65rem !important; }

            /* Enhanced layout preservation */
            .invoice-container {
              width: 100% !important;
              max-width: none !important;
              margin: 0 !important;
              padding: 0 !important;
            }

            /* Preserve spacing and layout */
            .space-y-1 > * + * { margin-top: 0.25rem !important; }
            .space-y-2 > * + * { margin-top: 0.5rem !important; }
            .space-y-3 > * + * { margin-top: 0.75rem !important; }
            .space-y-4 > * + * { margin-top: 1rem !important; }
            .space-x-2 > * + * { margin-left: 0.5rem !important; }
            .space-x-4 > * + * { margin-left: 1rem !important; }

            /* Grid layouts */
            .grid { display: grid !important; }
            .grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)) !important; }
            .grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)) !important; }
            .grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)) !important; }
            .grid-cols-5 { grid-template-columns: repeat(5, minmax(0, 1fr)) !important; }
            .gap-2 { gap: 0.5rem !important; }
            .gap-4 { gap: 1rem !important; }

            /* Flexbox layouts */
            .flex { display: flex !important; }
            .flex-col { flex-direction: column !important; }
            .items-center { align-items: center !important; }
            .items-start { align-items: flex-start !important; }
            .items-end { align-items: flex-end !important; }
            .justify-between { justify-content: space-between !important; }
            .justify-center { justify-content: center !important; }
            .justify-end { justify-content: flex-end !important; }

            /* Text alignment */
            .text-left { text-align: left !important; }
            .text-center { text-align: center !important; }
            .text-right { text-align: right !important; }

            /* Borders and shadows */
            .border { border-width: 1px !important; border-color: #e5e7eb !important; }
            .border-2 { border-width: 2px !important; }
            .border-t { border-top-width: 1px !important; border-top-color: #e5e7eb !important; }
            .border-b { border-bottom-width: 1px !important; border-bottom-color: #e5e7eb !important; }
            .border-gray-200 { border-color: #e5e7eb !important; }
            .border-gray-300 { border-color: #d1d5db !important; }
            .shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1) !important; }
            .shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1) !important; }

            /* Rounded corners */
            .rounded { border-radius: 0.25rem !important; }
            .rounded-lg { border-radius: 0.5rem !important; }
            .rounded-xl { border-radius: 0.75rem !important; }
            .rounded-full { border-radius: 9999px !important; }

            /* Width and height */
            .w-full { width: 100% !important; }
            .w-auto { width: auto !important; }
            .h-auto { height: auto !important; }
            .min-h-0 { min-height: 0 !important; }

            /* Overflow */
            .overflow-hidden { overflow: hidden !important; }
            .overflow-visible { overflow: visible !important; }

            /* Position */
            .relative { position: relative !important; }
            .absolute { position: absolute !important; }
            .inset-0 { top: 0; right: 0; bottom: 0; left: 0; }

            /* Transform */
            .transform { transform: translateX(var(--tw-translate-x)) translateY(var(--tw-translate-y)) rotate(var(--tw-rotate)) skewX(var(--tw-skew-x)) skewY(var(--tw-skew-y)) scaleX(var(--tw-scale-x)) scaleY(var(--tw-scale-y)) !important; }
            .rotate-45 { --tw-rotate: 45deg !important; }
            .rotate-12 { --tw-rotate: 12deg !important; }

            /* Opacity */
            .opacity-10 { opacity: 0.1 !important; }
            .opacity-15 { opacity: 0.15 !important; }
            .opacity-20 { opacity: 0.2 !important; }
            .opacity-30 { opacity: 0.3 !important; }

            /* Background colors for better contrast */
            .bg-white { background-color: #ffffff !important; }
            .bg-gray-50 { background-color: #f9fafb !important; }
            .bg-gray-100 { background-color: #f3f4f6 !important; }
            .bg-gray-200 { background-color: #e5e7eb !important; }
            .bg-gray-800 { background-color: #1f2937 !important; }
            .bg-gray-900 { background-color: #111827 !important; }

            /* Text colors */
            .text-white { color: #ffffff !important; }
            .text-black { color: #000000 !important; }
            .text-gray-600 { color: #4b5563 !important; }
            .text-gray-700 { color: #374151 !important; }
            .text-gray-800 { color: #1f2937 !important; }
            .text-gray-900 { color: #111827 !important; }

            /* Font weights */
            .font-normal { font-weight: 400 !important; }
            .font-medium { font-weight: 500 !important; }
            .font-semibold { font-weight: 600 !important; }
            .font-bold { font-weight: 700 !important; }
            .font-extrabold { font-weight: 800 !important; }

            /* Line height */
            .leading-tight { line-height: 1.25 !important; }
            .leading-snug { line-height: 1.375 !important; }
            .leading-normal { line-height: 1.5 !important; }
            .leading-relaxed { line-height: 1.625 !important; }

            /* Letter spacing */
            .tracking-wide { letter-spacing: 0.025em !important; }
            .tracking-wider { letter-spacing: 0.05em !important; }
            .tracking-widest { letter-spacing: 0.1em !important; }

            /* Table styles */
            table {
              border-collapse: collapse !important;
              width: 100% !important;
              margin: 0 !important;
            }
            th, td {
              border: 1px solid #e5e7eb !important;
              padding: 8px !important;
              vertical-align: top !important;
            }
            thead th {
              background-color: var(--header-bg-color, #f3f4f6) !important;
              font-weight: bold !important;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
            }
            tbody tr:nth-child(even) {
              background-color: #f9fafb !important;
            }

            /* QR Code preservation and sizing */
            img {
              max-width: 100% !important;
              height: auto !important;
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
            }

            /* QR Code specific sizing */
            img[src*="qrserver.com"] {
              width: 80px !important;
              height: 80px !important;
              max-width: 80px !important;
              max-height: 80px !important;
            }

            /* Myanmar font support */
            .font-myanmar {
              font-family: 'Myanmar Text', 'Pyidaungsu', 'Padauk', sans-serif !important;
            }
          }

          /* Screen styles for better preview */
          @media screen {
            .invoice-container {
              background: white;
              color: black;
            }
          }
        </style>
      `

      // Create print window with exact A4 dimensions (210mm x 297mm = 794px x 1123px at 96 DPI)
      const printWindow = window.open('', '_blank', 'width=794,height=1123,scrollbars=no,resizable=no,menubar=no,toolbar=no,location=no,status=no')
      if (!printWindow) {
        alert('Please allow popups for PDF download.')
        document.body.removeChild(loadingDiv)
        return
      }

      const invoiceElement = document.querySelector('.invoice-container')
      if (!invoiceElement) {
        alert('Invoice not found. Please try again.')
        document.body.removeChild(loadingDiv)
        printWindow.close()
        return
      }

      // Clone and prepare content
      const clonedContent = invoiceElement.cloneNode(true) as HTMLElement

      // Remove any print:hidden elements
      const hiddenElements = clonedContent.querySelectorAll('.print\\:hidden')
      hiddenElements.forEach(el => el.remove())

      // Ensure all images are loaded
      const images = clonedContent.querySelectorAll('img')
      const imagePromises = Array.from(images).map(img => {
        return new Promise((resolve) => {
          if (img.complete) {
            resolve(true)
          } else {
            img.onload = () => resolve(true)
            img.onerror = () => resolve(true)
            // Fallback timeout
            setTimeout(() => resolve(true), 2000)
          }
        })
      })

      Promise.all(imagePromises).then(() => {
        const printHTML = `
          <!DOCTYPE html>
          <html lang="en">
            <head>
              <meta charset="UTF-8">
              <title>BitsTech Invoice - ${invoiceData.saleNumber}</title>
              <meta name="viewport" content="width=794, initial-scale=1.0">
              <meta name="color-scheme" content="light only">
              ${printStyles}
              <style>
                /* Additional A4 sizing and color preservation */
                @page {
                  size: A4 portrait;
                  margin: 10mm;
                  -webkit-print-color-adjust: exact;
                  color-adjust: exact;
                  print-color-adjust: exact;
                }

                html {
                  width: 794px !important;
                  height: 1123px !important;
                  margin: 0 !important;
                  padding: 0 !important;
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  overflow: hidden !important;
                }

                body {
                  width: 794px !important;
                  height: 1123px !important;
                  margin: 0 !important;
                  padding: 0 !important;
                  background: white !important;
                  color: black !important;
                  font-size: 11px !important;
                  line-height: 1.3 !important;
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  print-color-adjust: exact !important;
                  box-sizing: border-box !important;
                  overflow: hidden !important;
                }

                .invoice-container {
                  width: 794px !important;
                  height: 1123px !important;
                  margin: 0 !important;
                  padding: 40px !important;
                  box-sizing: border-box !important;
                  background: white !important;
                  color: black !important;
                  overflow: hidden !important;
                  display: flex !important;
                  flex-direction: column !important;
                  justify-content: flex-start !important;
                }

                /* Force all elements to preserve colors */
                * {
                  -webkit-print-color-adjust: exact !important;
                  color-adjust: exact !important;
                  print-color-adjust: exact !important;
                }

                /* Override any dark mode classes */
                .dark, .dark *, [class*="dark:"] {
                  background-color: inherit !important;
                  color: inherit !important;
                }
              </style>
              <script>
                window.onload = function() {
                  // Force color preservation on all elements
                  const allElements = document.querySelectorAll('*');
                  allElements.forEach(function(el) {
                    el.style.webkitPrintColorAdjust = 'exact';
                    el.style.colorAdjust = 'exact';
                    el.style.printColorAdjust = 'exact';
                  });

                  // Remove any dark mode classes
                  document.body.classList.remove('dark');
                  document.documentElement.classList.remove('dark');

                  // Auto-print after ensuring everything is loaded
                  setTimeout(function() {
                    window.print();
                    setTimeout(function() {
                      window.close();
                    }, 1500);
                  }, 1000);
                };
              </script>
            </head>
            <body style="width: 794px; height: 1123px; margin: 0; padding: 0; background: white; color: black; -webkit-print-color-adjust: exact; color-adjust: exact; print-color-adjust: exact; overflow: hidden; box-sizing: border-box;">
              <div style="width: 794px; height: 1123px; margin: 0; padding: 40px; box-sizing: border-box; background: white; color: black; -webkit-print-color-adjust: exact; color-adjust: exact; print-color-adjust: exact; overflow: hidden; display: flex; flex-direction: column; justify-content: flex-start;">
                ${clonedContent.outerHTML}
              </div>
            </body>
          </html>
        `

        printWindow.document.open()
        printWindow.document.write(printHTML)
        printWindow.document.close()
      })

      // Remove loading
      document.body.removeChild(loadingDiv)

      printWindow.onload = () => {
        setTimeout(() => {
          printWindow.print()
          printWindow.close()
        }, 1000)
      }

      // Show success message
      const successDiv = document.createElement('div')
      successDiv.style.cssText = `
        position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%);
        background: #10b981; color: white; padding: 15px 25px;
        border-radius: 10px; z-index: 10000; text-align: center;
        font-family: Arial, sans-serif;
      `
      successDiv.innerHTML = '✅ Print dialog opened! Choose "Save as PDF" to download.'
      document.body.appendChild(successDiv)

      setTimeout(() => {
        if (document.body.contains(successDiv)) {
          document.body.removeChild(successDiv)
        }
      }, 5000)

    } catch (error) {
      console.error('PDF generation error:', error)
      alert('PDF generation failed. Please try again.')
    }
  }

  // Template rendering function
  const renderTemplate = () => {
    console.log('Rendering template:', selectedTemplate, 'with data:', invoiceData)

    if (!invoiceData) {
      console.error('No invoice data available for template rendering')
      return (
        <div className="p-8 text-center">
          <p className="text-red-500">No invoice data available</p>
        </div>
      )
    }

    switch (selectedTemplate) {
      case 1:
        return renderTemplate1() // Premium Red Professional
      case 2:
        return renderTemplate2() // Elegant Blue Corporate
      case 3:
        return renderTemplate3() // Luxury Gold Executive
      case 4:
        return renderTemplate4() // Modern Purple Creative
      case 5:
        return renderTemplate5() // Classic Green Business
      case 6:
        return renderTemplate6() // Sophisticated Black Elite
      case 7:
        return renderTemplate7() // Modern Blue Corporate
      case 8:
        return renderTemplate8() // Orange Professional
      case 9:
        return renderTemplate9() // Clean Modern
      case 10:
        return renderTemplate10() // Professional Orange
      default:
        return renderTemplate1()
    }
  }

  // Template 1 - Dark Orange Corporate (Based on provided image)
  const renderTemplate1 = () => {
    const templateCustom = getCurrentTemplateCustomization()

    console.log('Template 1 rendering with data:', {
      invoiceData,
      saleNumber: invoiceData?.saleNumber,
      customer: invoiceData?.customer,
      items: invoiceData?.items,
      total: invoiceData?.total
    })

    if (!invoiceData) {
      return (
        <div className="p-8 text-center">
          <p className="text-red-500">Invoice data not loaded</p>
        </div>
      )
    }

    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Modern Red Corporate Header */}
      <div
        className="relative mb-2 bg-gradient-to-br from-red-600 via-red-700 to-red-900 text-white p-2 rounded-lg shadow-xl overflow-hidden"
        style={{
          colorAdjust: 'exact',
          WebkitPrintColorAdjust: 'exact',
          printColorAdjust: 'exact'
        } as React.CSSProperties}
      >
        {/* Red Geometric Elements */}
        <div className="absolute top-0 right-0 w-24 h-24 bg-red-500 opacity-20 rounded-full -mr-12 -mt-12"></div>
        <div className="absolute bottom-0 left-0 w-16 h-16 bg-red-400 opacity-15 rounded-full -ml-8 -mb-8"></div>
        <div className="absolute top-1/2 right-1/4 w-12 h-12 bg-white opacity-10 transform rotate-45"></div>

        {/* Decorative Lines */}
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-3 left-1/3 w-16 h-0.5 bg-white opacity-30 transform rotate-12"></div>
          <div className="absolute bottom-4 right-1/3 w-12 h-0.5 bg-white opacity-20 transform -rotate-12"></div>
        </div>

        {/* Red Accent Triangles */}
        <div
          className="absolute top-0 right-0 w-0 h-0 border-l-[50px] border-l-transparent border-b-[50px] border-b-red-500 opacity-60"
        ></div>
        <div
          className="absolute bottom-0 right-0 w-0 h-0 border-l-[30px] border-l-transparent border-t-[15px] border-t-red-400 opacity-80"
        ></div>

        <div className="relative flex justify-between items-center">
          <div className="flex-1">
            <div className="flex items-center space-x-3">
              {templateCustom.companyLogo || companySettings.logo ? (
                <div className="relative">
                  <div className="absolute inset-0 bg-white rounded-lg opacity-20 blur-sm"></div>
                  <img
                    src={templateCustom.companyLogo || companySettings.logo}
                    alt="Company Logo"
                    className="relative w-10 h-10 object-contain rounded-lg bg-white p-2 shadow-lg"
                  />
                </div>
              ) : (
                <div className="relative">
                  <div className="absolute inset-0 bg-white rounded-lg opacity-20 blur-sm"></div>
                  <div className="relative w-10 h-10 rounded-lg flex items-center justify-center bg-white bg-opacity-95 shadow-lg">
                    <span className="text-red-600 font-bold text-lg">{(companySettings.name || 'C').charAt(0)}</span>
                  </div>
                </div>
              )}
              <div>
                <h1
                  className="text-2xl font-bold text-white drop-shadow-lg"
                  style={{ fontFamily: templateCustom.customFonts?.heading || 'Arial' }}
                >
                  {templateCustom.companyName || companySettings.name || (language === 'en' ? 'COMPANY' : 'ကုမ္ပဏီ')}
                </h1>
              </div>
            </div>
          </div>

          <div className="text-right">
            <div className="relative">
              <div className="absolute inset-0 bg-orange-500 rounded-lg opacity-20 blur-sm"></div>
              <div
                className="relative bg-gradient-to-r from-orange-500 to-orange-600 text-white px-3 py-1 rounded-lg shadow-lg mb-2"
                style={{
                  colorAdjust: 'exact',
                  WebkitPrintColorAdjust: 'exact',
                  printColorAdjust: 'exact'
                } as React.CSSProperties}
              >
                <h2 className="text-lg font-bold tracking-wide">{t.invoice.toUpperCase()}</h2>
              </div>
            </div>
            <div className="text-xs space-y-1">
              <div className="flex justify-between items-center">
                <span className="text-red-200">{language === 'en' ? 'Invoice No:' : 'ငွေတောင်းခံလွှာနံပါတ်:'}</span>
                <span className="font-bold text-white"># {invoiceData?.saleNumber}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-red-200">{language === 'en' ? 'Due Date:' : 'ပေးရမည့်ရက်:'}</span>
                <span className="font-bold text-white">{new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-red-200">{language === 'en' ? 'Invoice Date:' : 'ရက်စွဲ:'}</span>
                <span className="font-bold text-white">{invoiceData?.timestamp.toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Company Info and Customer Info Section */}
      <div className="grid grid-cols-2 gap-4 mb-2">
        {/* Company Info (INVOICE FROM) */}
        <div>
          <h3 className="text-xs font-bold text-white bg-gray-800 px-2 py-1 mb-1">{language === 'en' ? 'INVOICE FROM:' : 'ငွေတောင်းခံသူ:'}</h3>
          <div className="text-xs space-y-1">
            <p className="font-bold text-gray-900 dark:text-white">{companySettings.name}</p>
            <div className="mt-2 space-y-1">
              <div className="flex items-center space-x-1">
                <MapPin className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.address}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Phone className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.phone}</span>
              </div>
              <div className="flex items-center space-x-1">
                <Mail className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.email}</span>
              </div>
              {companySettings.website && (
                <div className="flex items-center space-x-1">
                  <Globe className="h-3 w-3 text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-400">{companySettings.website}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Customer Info (BILL TO) */}
        <div>
          <h3 className="text-xs font-bold text-white bg-gray-800 px-2 py-1 mb-1">{language === 'en' ? 'BILL TO:' : 'ငွေတောင်းခံရန်:'}</h3>
          <div className="text-xs space-y-1">
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.customer.name}</p>
            <div className="mt-2 space-y-1">
              <div className="flex items-center space-x-1">
                <Phone className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.phone}</span>
              </div>
              <div className="flex items-center space-x-1">
                <MapPin className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.address}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Method Section */}
      <div className="mb-2">
        <h3 className="text-xs font-bold text-gray-900 dark:text-white mb-1">{language === 'en' ? 'Payment Information' : 'ငွေပေးချေမှုအချက်အလက်'}</h3>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.paymentMethod || 'Cash'}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Transaction Date:' : 'ငွေပေးချေသည့်ရက်:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Currency:' : 'ငွေကြေး:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.currency || currentCurrency}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Status:' : 'အခြေအနေ:'}</span>
            <p className="font-bold text-green-600">{language === 'en' ? 'PAID' : 'ပေးချေပြီး'}</p>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-2">
        <div className="overflow-hidden rounded-lg shadow-lg border border-gray-200">
          <table className="w-full">
            <thead>
              <tr
                className="text-white bg-gradient-to-r from-red-500 to-red-600"
                style={{
                  colorAdjust: 'exact',
                  WebkitPrintColorAdjust: 'exact',
                  printColorAdjust: 'exact'
                } as React.CSSProperties}
              >
                <th className="px-2 py-2 text-left font-bold text-xs">{language === 'en' ? 'NO.' : 'စဉ်'}</th>
                <th className="px-2 py-2 text-left font-bold text-xs">{language === 'en' ? 'DESCRIPTION' : 'ဖော်ပြချက်'}</th>
                <th className="px-2 py-2 text-center font-bold text-xs">{language === 'en' ? 'PRICE' : 'စျေးနှုန်း'}</th>
                <th className="px-2 py-2 text-center font-bold text-xs">{language === 'en' ? 'QTY' : 'အရေအတွက်'}</th>
                <th className="px-2 py-2 text-right font-bold text-xs">{language === 'en' ? 'TOTAL' : 'စုစုပေါင်း'}</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800">
              {invoiceData?.items && invoiceData.items.length > 0 ? (
                invoiceData.items.map((item, index) => (
                  <tr key={index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-gray-50 dark:bg-gray-900/10' : 'bg-white dark:bg-gray-800'}`}>
                    <td className="px-2 py-2 text-gray-900 dark:text-white text-xs font-bold">{String(index + 1).padStart(2, '0')}</td>
                    <td className="px-2 py-2 text-gray-900 dark:text-white text-xs">{item.product.name}</td>
                    <td className="px-2 py-2 text-center text-gray-600 dark:text-gray-400 text-xs">{formatCurrency(item.product.price)}</td>
                    <td className="px-2 py-2 text-center text-gray-600 dark:text-gray-400 text-xs">{item.quantity}</td>
                    <td className="px-2 py-2 text-right font-bold text-gray-900 dark:text-white text-xs">{formatCurrency(item.totalPrice)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center space-y-2">
                      <ShoppingCart className="w-8 h-8 text-gray-400" />
                      <p className="text-sm font-medium">
                        {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                      </p>
                      <p className="text-xs text-gray-400">
                        {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Terms & Conditions and Summary Section */}
      <div className="grid grid-cols-2 gap-4 mb-2">
        <div>
          <h3 className="text-xs font-bold text-gray-900 dark:text-white mb-2">{language === 'en' ? 'Terms & Conditions:' : 'စည်းမျဉ်းစည်းကမ်းများ:'}</h3>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p>{language === 'en' ? 'Payment is due within 30 days of invoice date. Late payments may incur additional charges.' : 'ငွေပေးချေမှုသည် ၃၀ ရက်အတွင်း ပြုလုပ်ရမည်။'}</p>
            <p>{language === 'en' ? 'All sales are final. Returns and exchanges are not accepted unless items are defective.' : 'ပစ္စည်းများ ပြန်လည်မပေးအပ်နိုင်ပါ။'}</p>
            <p>{language === 'en' ? 'Prices are subject to change without notice. Please verify all details before payment.' : 'စျေးနှုန်းများ ကြိုတင်အသိပေးခြင်းမရှိဘဲ ပြောင်းလဲနိုင်ပါသည်။'}</p>
          </div>

          <div className="mt-4">
            <p className="text-xs font-bold text-gray-900 dark:text-white">{language === 'en' ? 'Thank you for your business with us.' : 'သင့်လုပ်ငန်းအတွက် ကျေးဇူးတင်ပါသည်။'}</p>

            {/* QR Code Section */}
            {templateCustom.showQRCode && (
              <div className="mt-3 text-left">
                <div className="inline-block bg-white p-2 rounded border">
                  <img
                    src={generateQRCode(customization.qrCodeData || `Invoice: ${invoiceData?.saleNumber}\nCompany: ${customization.companyName}\nTotal: ${formatCurrency(invoiceData?.total || 0)}`)}
                    alt="QR Code"
                    className="w-16 h-16"
                  />
                </div>
                <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {language === 'en' ? 'Scan for details' : 'အသေးစိတ်ကြည့်ရန်'}
                </p>
              </div>
            )}
          </div>

        </div>

        <div className="text-right">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'Subtotal:' : 'စုစုပေါင်း:'}</span>
              <span className="text-xs font-bold text-gray-900 dark:text-white">{formatCurrency(invoiceData?.subtotal || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'Discount:' : 'လျှော့စျေး:'}</span>
              <span className="text-xs font-bold text-gray-900 dark:text-white">{formatCurrency(invoiceData?.discount || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'Tax:' : 'အခွန်:'}</span>
              <span className="text-xs font-bold text-gray-900 dark:text-white">{formatCurrency(invoiceData?.tax || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'Shipping:' : 'ပို့ဆောင်ခ:'}</span>
              <span className="text-xs font-bold text-gray-900 dark:text-white">{formatCurrency(invoiceData?.shipping || 0)}</span>
            </div>
            <div className="relative mt-2">
              <div className="absolute inset-0 bg-red-500 rounded opacity-20 blur-sm"></div>
              <div
                className="relative bg-gradient-to-r from-red-500 to-red-600 text-white rounded px-3 py-2 shadow-lg"
                style={{
                  colorAdjust: 'exact',
                  WebkitPrintColorAdjust: 'exact',
                  printColorAdjust: 'exact'
                } as React.CSSProperties}
              >
              <div className="flex justify-between items-center">
                <span className="text-xs font-bold">{language === 'en' ? 'Total:' : 'စုစုပေါင်း:'}</span>
                <span className="text-sm font-bold">{formatCurrency(invoiceData?.total || 0)}</span>
              </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Signature Section */}
      <div className="mt-2">
        <div className="text-right">
          <div className="border-b-2 border-gray-400 mb-1 h-8 w-24 ml-auto"></div>
          <p className="text-xs font-bold text-gray-800 dark:text-white">{language === 'en' ? 'Authorised Sign' : 'လက်မှတ်'}</p>
        </div>
      </div>




    </div>
    )
  }

  // Template 2 - Modern Blue Corporate (Based on first image)
  const renderTemplate2 = () => {
    const templateCustom = getCurrentTemplateCustomization()
    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Dark Blue Header with Logo */}
      <div
        className="text-white p-2 mb-2 rounded-t-lg"
        style={{ backgroundColor: templateCustom.customColors?.secondary || '#1f2937' }}
      >
        <div className="flex justify-between items-center">
          <div className="flex items-center space-x-4">
            <div className="p-2 rounded bg-white">
              {templateCustom.companyLogo ? (
                <img
                  src={templateCustom.companyLogo}
                  alt="Company Logo"
                  className="h-8 w-8 object-contain"
                />
              ) : (
                <div
                  className="h-8 w-8 rounded flex items-center justify-center"
                  style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
                >
                  <Briefcase className="h-5 w-5 text-white" />
                </div>
              )}
            </div>
            <div>
              <h1
                className="text-2xl font-bold"
                style={{ fontFamily: templateCustom.customFonts?.heading || 'Arial' }}
              >
                {templateCustom.companyName || t.companyName}
              </h1>
            </div>
          </div>
          <div className="text-right">
            <h2
              className="text-3xl font-bold"
              style={{
                color: templateCustom.customColors?.primary || '#f97316',
                fontFamily: templateCustom.customFonts?.heading || 'Arial'
              }}
            >
              {t.invoice.toUpperCase()}
            </h2>
            <p className="text-sm text-gray-300">#{invoiceData?.saleNumber}</p>
          </div>
        </div>
      </div>

      {/* Company and Customer Info */}
      <div className="grid grid-cols-2 gap-4 mb-2">
        <div>
          <h3 className="text-xs font-bold text-gray-800 dark:text-white mb-1">{language === 'en' ? 'INVOICE FROM:' : 'ငွေတောင်းခံသူ:'}</h3>
          <div className="text-xs space-y-1">
            <p className="font-semibold text-gray-900 dark:text-white">{templateCustom.companyName || companySettings.name || 'BitsTech'}</p>
            <div className="flex items-center space-x-2">
              <MapPin className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-400">{companySettings.address || 'No. 456, Technology Street, Yangon, Myanmar'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-400">{companySettings.phone || '+95 9 ***********'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Mail className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-400">{companySettings.email || '<EMAIL>'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Globe className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-400">{companySettings.website || 'www.bitstech.com'}</span>
            </div>
          </div>
        </div>
        <div>
          <h3 className="text-xs font-bold text-gray-800 dark:text-white mb-1">{t.billTo}:</h3>
          <div className="text-xs space-y-1">
            <p className="font-semibold text-gray-900 dark:text-white">{invoiceData?.customer.name}</p>
            <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.address}</p>
            <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.phone}</p>
          </div>
          <div className="mt-2 text-xs">
            <div className="grid grid-cols-2 gap-2">
              <div>
                <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Invoice Date:' : 'ရက်စွဲ:'}</span>
                <p className="font-semibold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Due Date:' : 'ပေးရမည့်ရက်:'}</span>
                <p className="font-semibold text-gray-900 dark:text-white">{new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Information Section */}
      <div className="mb-2">
        <h3 className="text-xs font-bold text-gray-900 dark:text-white mb-1">{language === 'en' ? 'Payment Information' : 'ငွေပေးချေမှုအချက်အလက်'}</h3>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.paymentMethod || 'No Payment Method'}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Transaction Date:' : 'ငွေပေးချေသည့်ရက်:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Currency:' : 'ငွေကြေး:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.currency || currentCurrency}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Status:' : 'အခြေအနေ:'}</span>
            <p className="font-bold text-green-600">{language === 'en' ? 'PAID' : 'ပေးချေပြီး'}</p>
          </div>
        </div>
      </div>

      {/* Blue Items Table - First Image Style */}
      <div className="mb-2">
        <div className="overflow-hidden rounded-lg shadow-lg border border-blue-200">
          <table className="w-full">
            <thead>
              <tr
                className="text-white"
                style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
              >
                <th className="px-3 py-2 text-left font-bold text-xs">{language === 'en' ? 'DESCRIPTION' : 'ဖော်ပြချက်'}</th>
                <th className="px-3 py-2 text-center font-bold text-xs">{language === 'en' ? 'QTY' : 'အရေအတွက်'}</th>
                <th className="px-3 py-2 text-right font-bold text-xs">{language === 'en' ? 'UNIT PRICE' : 'တစ်ခုစျေး'}</th>
                <th className="px-3 py-2 text-right font-bold text-xs">{language === 'en' ? 'TOTAL' : 'စুစုပေါင်း'}</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800">
              {invoiceData?.items && invoiceData.items.length > 0 ? (
                invoiceData.items.map((item, index) => (
                  <tr key={index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-gray-50 dark:bg-gray-900/10' : 'bg-white dark:bg-gray-800'}`}>
                    <td className="px-2 py-2 text-gray-900 dark:text-white text-xs">{item.product.name}</td>
                    <td className="px-2 py-2 text-center text-gray-600 dark:text-gray-400 text-xs">{item.quantity}</td>
                    <td className="px-2 py-2 text-right text-gray-600 dark:text-gray-400 text-xs">{formatCurrency(item.product.price)}</td>
                    <td className="px-2 py-2 text-right font-bold text-gray-900 dark:text-white text-xs">{formatCurrency(item.totalPrice)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center space-y-2">
                      <ShoppingCart className="w-8 h-8 text-gray-400" />
                      <p className="text-sm font-medium">
                        {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                      </p>
                      <p className="text-xs text-gray-400">
                        {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment Method and Summary - First Image Style */}
      <div className="grid grid-cols-2 gap-4 mb-2">
        <div>
          <h3 className="text-xs font-bold text-gray-800 dark:text-white mb-1">{language === 'en' ? 'PAYMENT METHOD:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</h3>
          <div className="text-xs space-y-1">
            <p className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Type:' : 'ငွေပေးချေမှုအမျိုးအစား:'} {invoiceData?.paymentMethod}</p>
            <p className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Account Name:' : 'အကောင့်အမည်:'} {templateCustom.companyName || t.companyName}</p>
            <p className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Bank Detail:' : 'ဘဏ်အသေးစိတ်:'} {companySettings.taxId}</p>
          </div>

          <div className="mt-2">
            <h3 className="text-xs font-bold text-gray-800 dark:text-white mb-1">{language === 'en' ? 'TERMS AND CONDITIONS' : 'စည်းမျဉ်းစည်းကမ်းများ'}</h3>
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <p>{language === 'en' ? 'Payment is due within 30 days of invoice date. Late payments may incur additional charges.' : 'ငွေပေးချေမှုသည် ၃၀ ရက်အတွင်း ပြုလုပ်ရမည်။'}</p>
              <p>{language === 'en' ? 'All sales are final. Returns and exchanges are not accepted unless items are defective.' : 'ပစ္စည်းများ ပြန်လည်မပေးအပ်နိုင်ပါ။'}</p>
            </div>
          </div>
        </div>

        <div className="text-right">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'SUB TOTAL' : 'စုစုပေါင်း'}</span>
              <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.subtotal || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'TAX' : 'အခွန်'}</span>
              <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.tax || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'DISCOUNT' : 'လျှော့စျေး'}</span>
              <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.discount || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'SHIPPING' : 'ပို့ဆောင်ခ'}</span>
              <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.shipping || 0)}</span>
            </div>
            <div className="flex justify-between items-center mt-2">
              <span className="text-sm font-bold text-gray-900 dark:text-white">{language === 'en' ? 'TOTAL' : 'စုစုပေါင်း'}</span>
              <span className="bg-blue-600 text-white px-3 py-2 rounded text-sm font-bold">{formatCurrency(invoiceData?.total || 0)}</span>
            </div>
          </div>

          <div className="mt-2 text-right">
            <div className="border-b-2 border-gray-400 mb-1 h-8"></div>
            <p className="text-xs font-bold text-gray-800 dark:text-white">{language === 'en' ? 'SIGNATURE' : 'လက်မှတ်'}</p>
          </div>
        </div>
      </div>

      {/* QR Code and Thank You Section */}
      <div className="flex justify-between items-center mt-2">
        <div className="text-center bg-blue-500 text-white rounded-lg p-2 flex-1 mr-2">
          <p className="text-xs font-bold">{language === 'en' ? 'Thank You for Your Business!' : 'သင့်လုပ်ငန်းအတွက် ကျေးဇူးတင်ပါသည်!'}</p>
        </div>
        {templateCustom.showQRCode && (
          <div className="text-center">
            <div className="bg-white p-2 rounded border">
              <img
                src={generateQRCode(customization.qrCodeData || `Invoice: ${invoiceData?.saleNumber}\nCompany: ${customization.companyName}\nTotal: ${formatCurrency(invoiceData?.total || 0)}`)}
                alt="QR Code"
                className="w-16 h-16"
              />
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{language === 'en' ? 'Scan for details' : 'အသေးစိတ်ကြည့်ရန်'}</p>
          </div>
        )}
      </div>
    </div>
    )
  }

  // Template 3 - Blue Vertical Design (Based on second image)
  const renderTemplate3 = () => {
    const templateCustom = getCurrentTemplateCustomization()
    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Blue Vertical Header */}
      <div className="flex mb-2">
        {/* Left Blue Vertical Bar */}
        <div className="w-16 bg-gradient-to-b from-blue-600 to-blue-800 text-white p-2 rounded-l-lg flex flex-col justify-center items-center">
          <div className="transform -rotate-90 whitespace-nowrap">
            <h1 className="text-sm font-bold tracking-widest">{t.invoice.toUpperCase()}</h1>
          </div>
        </div>

        {/* Main Content Area */}
        <div className="flex-1 bg-white dark:bg-gray-800 p-2 rounded-r-lg border border-gray-200 dark:border-gray-700">
          <div className="flex justify-between items-start mb-2">
            <div>
              <div className="flex items-center space-x-3 mb-2">
                {templateCustom.companyLogo ? (
                  <img
                    src={templateCustom.companyLogo}
                    alt="Company Logo"
                    className="w-8 h-8 object-contain rounded"
                  />
                ) : (
                  <div
                    className="w-8 h-8 rounded flex items-center justify-center"
                    style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
                  >
                    <span className="text-white font-bold text-sm">C</span>
                  </div>
                )}
                <h2
                  className="text-2xl font-bold text-gray-900 dark:text-white"
                  style={{ fontFamily: templateCustom.customFonts?.heading || 'Arial' }}
                >
                  {templateCustom.companyName || companySettings.name || 'BitsTech'}
                </h2>
              </div>
              <div className="mt-2 text-xs space-y-1">
                <div className="flex items-center space-x-2">
                  <Phone className="h-3 w-3 text-blue-500" />
                  <span className="text-gray-600 dark:text-gray-400">{companySettings.phone || '+95 9 ***********'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-3 w-3 text-blue-500" />
                  <span className="text-gray-600 dark:text-gray-400">{companySettings.email || '<EMAIL>'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <MapPin className="h-3 w-3 text-blue-500" />
                  <span className="text-gray-600 dark:text-gray-400">{companySettings.address || 'No. 456, Technology Street, Yangon, Myanmar'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Globe className="h-3 w-3 text-blue-500" />
                  <span className="text-gray-600 dark:text-gray-400">{companySettings.website || 'www.bitstech.com'}</span>
                </div>
              </div>
            </div>

            <div className="text-right">
              <div className="bg-blue-500 text-white px-4 py-2 rounded">
                <h3 className="text-lg font-bold">{t.invoice.toUpperCase()}</h3>
              </div>
              <div className="mt-2 text-xs space-y-1">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'ID NO.' : 'အမှတ်စဉ်'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.saleNumber}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Account no.' : 'အကောင့်နံပါတ်'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">ACC-001</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Invoice no.' : 'ငွေတောင်းခံလွှာနံပါတ်'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.saleNumber}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'DATE:' : 'ရက်စွဲ:'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
                </div>
              </div>
            </div>
          </div>

          {/* Bill To and Payment Information Section - Side by Side */}
          <div className="grid grid-cols-2 gap-6 mb-4">
            {/* Bill To Section */}
            <div>
              <h3 className="text-sm font-bold text-gray-900 dark:text-white mb-2">{language === 'en' ? 'BILL TO:' : 'ငွေတောင်းခံရန်:'}</h3>
              <div className="text-xs space-y-1">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'NAME' : 'အမည်'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.customer.name || 'No Customer Selected'}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Address' : 'လိပ်စာ'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.customer.address || 'Please complete a POS checkout to see real data'}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Phone' : 'ဖုန်းနံပါတ်'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.customer.phone || 'N/A'}</p>
                </div>
              </div>
            </div>

            {/* Payment Information Section */}
            <div>
              <h3 className="text-sm font-bold text-gray-900 dark:text-white mb-2">{language === 'en' ? 'Payment Information' : 'ငွေပေးချေမှုအချက်အလက်'}</h3>
              <div className="text-xs space-y-1">
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.paymentMethod || 'No Payment Method'}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Transaction Date:' : 'ငွေပေးချေသည့်ရက်:'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Currency:' : 'ငွေကြေး:'}</span>
                  <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.currency || currentCurrency}</p>
                </div>
                <div>
                  <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Status:' : 'အခြေအနေ:'}</span>
                  <p className="font-bold text-green-600">{language === 'en' ? 'PAID' : 'ပေးချေပြီး'}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Blue Items Table - Second Image Style */}
      <div className="mb-2">
        <div className="overflow-hidden rounded-lg shadow-lg border border-blue-200">
          <table className="w-full">
            <thead>
              <tr className="bg-blue-500 text-white">
                <th className="px-2 py-2 text-left font-bold text-xs">{language === 'en' ? 'DESCRIPTION' : 'ဖော်ပြချက်'}</th>
                <th className="px-2 py-2 text-center font-bold text-xs">{language === 'en' ? 'QTY' : 'အရေအတွက်'}</th>
                <th className="px-2 py-2 text-right font-bold text-xs">{language === 'en' ? 'UNIT PRICE' : 'တစ်ခုစျေး'}</th>
                <th className="px-2 py-2 text-right font-bold text-xs">{language === 'en' ? 'TOTAL' : 'စုစုပေါင်း'}</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800">
              {invoiceData?.items && invoiceData.items.length > 0 ? (
                invoiceData.items.map((item, index) => (
                  <tr key={index} className="border-b border-gray-200">
                    <td className="px-2 py-2 text-gray-900 dark:text-white text-xs">{item.product.name}</td>
                    <td className="px-2 py-2 text-center text-gray-600 dark:text-gray-400 text-xs">{item.quantity}</td>
                    <td className="px-2 py-2 text-right text-gray-600 dark:text-gray-400 text-xs">{formatCurrency(item.product.price)}</td>
                    <td className="px-2 py-2 text-right font-bold text-gray-900 dark:text-white text-xs">{formatCurrency(item.totalPrice)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center space-y-2">
                      <ShoppingCart className="w-8 h-8 text-gray-400" />
                      <p className="text-sm font-medium">
                        {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                      </p>
                      <p className="text-xs text-gray-400">
                        {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Payment Method and Summary - Second Image Style */}
      <div className="grid grid-cols-2 gap-4 mb-2">
        <div>
          <h3 className="text-xs font-bold text-gray-800 dark:text-white mb-1">{language === 'en' ? 'PAYMENT METHOD:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</h3>
          <div className="text-xs space-y-1">
            <p className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Type:' : 'ငွေပေးချေမှုအမျိုးအစား:'} {invoiceData?.paymentMethod}</p>
            <p className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Account Name:' : 'အကောင့်အမည်:'} {companySettings.name}</p>
            <p className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Bank Detail:' : 'ဘဏ်အသေးစိတ်:'} {companySettings.taxId}</p>
          </div>

          <div className="mt-4">
            <h3 className="text-sm font-bold text-gray-800 dark:text-white mb-2">{language === 'en' ? 'TERMS AND CONDITIONS' : 'စည်းမျဉ်းစည်းကမ်းများ'}</h3>
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <p>{language === 'en' ? 'Payment is due within 30 days of invoice date. Late payments may incur additional charges.' : 'ငွေပေးချေမှုသည် ၃၀ ရက်အတွင်း ပြုလုပ်ရမည်။'}</p>
              <p>{language === 'en' ? 'All sales are final. Returns and exchanges are not accepted unless items are defective.' : 'ပစ္စည်းများ ပြန်လည်မပေးအပ်နိုင်ပါ။'}</p>
            </div>
          </div>
        </div>

        <div className="text-right">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'SUB TOTAL' : 'စုစုပေါင်း'}</span>
              <span
                className="text-white px-2 py-1 rounded text-xs font-bold"
                style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
              >
                {formatCurrency(invoiceData?.subtotal || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'TAX' : 'အခွန်'}</span>
              <span
                className="text-white px-2 py-1 rounded text-xs font-bold"
                style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
              >
                {formatCurrency(invoiceData?.tax || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'DISCOUNT' : 'လျှော့စျေး'}</span>
              <span
                className="text-white px-2 py-1 rounded text-xs font-bold"
                style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
              >
                {formatCurrency(invoiceData?.discount || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'SHIPPING' : 'ပို့ဆောင်ခ'}</span>
              <span
                className="text-white px-2 py-1 rounded text-xs font-bold"
                style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
              >
                {formatCurrency(invoiceData?.shipping || 0)}
              </span>
            </div>
            <div className="flex justify-between items-center mt-4 pt-2 border-t-2 border-gray-300">
              <span className="text-sm font-bold text-gray-900 dark:text-white">{language === 'en' ? 'TOTAL' : 'စုစုပေါင်း'}</span>
              <span
                className="text-white px-3 py-2 rounded text-sm font-bold"
                style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
              >
                {formatCurrency(invoiceData?.total || 0)}
              </span>
            </div>
          </div>

          <div className="mt-6 text-right">
            <div className="border-b-2 border-gray-400 mb-2 h-12"></div>
            <p className="text-xs font-bold text-gray-800 dark:text-white">{language === 'en' ? 'SIGNATURE' : 'လက်မှတ်'}</p>
          </div>
        </div>
      </div>

      {/* QR Code and Thank You Section */}
      <div className="flex justify-between items-center mt-6">
        <div className="text-center bg-blue-500 text-white rounded-lg p-4 flex-1 mr-4">
          <p className="text-sm font-bold">{language === 'en' ? 'Thank You for Your Business!' : 'သင့်လုပ်ငန်းအတွက် ကျေးဇူးတင်ပါသည်!'}</p>
        </div>
        {templateCustom.showQRCode && (
          <div className="text-center">
            <div className="bg-white p-2 rounded border">
              <img
                src={generateQRCode(`${templateCustom.qrCodeData || 'Invoice: '}${invoiceData?.saleNumber}`)}
                alt="QR Code"
                className="w-16 h-16"
              />
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{language === 'en' ? 'Scan for details' : 'အသေးစিတ်ကြည့်ရန်'}</p>
          </div>
        )}
      </div>
    </div>
    )
  }

  // Template 4 - Colorful Geometric Design (Based on third image)
  const renderTemplate4 = () => {
    const templateCustom = getCurrentTemplateCustomization()
    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Colorful Geometric Header */}
      <div className="relative mb-2 bg-white dark:bg-gray-800 p-2 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden">
        {/* Geometric Decorative Elements */}
        <div className="absolute top-0 right-0 w-0 h-0 border-l-[60px] border-l-transparent border-b-[60px] border-b-blue-500"></div>
        <div className="absolute top-0 right-0 w-0 h-0 border-l-[40px] border-l-transparent border-b-[40px] border-b-green-500 mr-5 mt-5"></div>
        <div className="absolute top-0 right-0 w-0 h-0 border-l-[20px] border-l-transparent border-b-[20px] border-b-red-500 mr-10 mt-10"></div>

        <div className="absolute bottom-0 left-0 w-0 h-0 border-r-[50px] border-r-transparent border-t-[50px] border-t-orange-500"></div>
        <div className="absolute bottom-0 left-0 w-0 h-0 border-r-[30px] border-r-transparent border-t-[30px] border-t-purple-500 ml-5 mb-5"></div>

        <div className="relative flex justify-between items-start">
          <div className="flex-1">
            <h3 className="text-xs font-bold text-gray-900 dark:text-white mb-2">{language === 'en' ? 'INVOICE FROM:' : 'ငွေတောင်းခံသူ:'}</h3>
            <div className="flex items-center space-x-3 mb-4">
              {templateCustom.companyLogo ? (
                <img
                  src={templateCustom.companyLogo}
                  alt="Company Logo"
                  className="w-12 h-12 object-contain rounded-lg"
                />
              ) : (
                <div
                  className="w-12 h-12 rounded-lg flex items-center justify-center"
                  style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
                >
                  <span className="text-white font-bold text-lg">C</span>
                </div>
              )}
              <div>
                <h1
                  className="text-2xl font-bold text-gray-900 dark:text-white"
                  style={{ fontFamily: templateCustom.customFonts?.heading || 'Arial' }}
                >
                  {templateCustom.companyName || companySettings.name || 'BitsTech'}
                </h1>
              </div>
            </div>

            <div className="text-xs space-y-1">
              <div className="flex items-center space-x-2">
                <MapPin className="h-3 w-3 text-blue-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.address || 'No. 456, Technology Street, Yangon, Myanmar'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Phone className="h-3 w-3 text-red-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.phone || '+95 9 ***********'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Mail className="h-3 w-3 text-orange-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.email || '<EMAIL>'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <Globe className="h-3 w-3 text-green-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.website || 'www.bitstech.com'}</span>
              </div>
            </div>
          </div>

          <div className="text-right">
            <div className="bg-gradient-to-r from-blue-500 to-purple-500 text-white px-4 py-2 rounded-lg mb-4">
              <h2 className="text-2xl font-bold">{t.invoice.toUpperCase()}</h2>
            </div>
            <div className="text-xs space-y-1">
              <div>
                <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Invoice #:' : 'ငွေတောင်းခံလွှာနံပါတ်:'}</span>
                <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.saleNumber}</p>
              </div>
              <div>
                <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Date:' : 'ရက်စွဲ:'}</span>
                <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Customer Information */}
      <div className="mb-2">
        <h3 className="text-xs font-bold text-gray-900 dark:text-white mb-1">{language === 'en' ? 'BILL TO:' : 'ငွေတောင်းခံရန်:'}</h3>
        <div className="bg-gray-50 dark:bg-gray-900 p-2 rounded-lg">
          <div className="text-xs space-y-1">
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.customer.name}</p>
            <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.address}</p>
            <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.phone}</p>
          </div>
        </div>
      </div>

      {/* Colorful Items Table - Third Image Style */}
      <div className="mb-2">
        <div className="overflow-hidden rounded-lg shadow-lg border border-gray-200 dark:border-gray-700">
          <table className="w-full">
            <thead>
              <tr className="bg-gradient-to-r from-blue-500 via-green-500 to-red-500 text-white">
                <th className="px-2 py-2 text-left font-bold text-xs">{language === 'en' ? 'DESCRIPTION' : 'ဖော်ပြချက်'}</th>
                <th className="px-2 py-2 text-center font-bold text-xs">{language === 'en' ? 'QTY' : 'အရေအတွက်'}</th>
                <th className="px-2 py-2 text-right font-bold text-xs">{language === 'en' ? 'UNIT PRICE' : 'တစ်ခုစျေး'}</th>
                <th className="px-2 py-2 text-right font-bold text-xs">{language === 'en' ? 'TOTAL' : 'စုစုပေါင်း'}</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800">
              {invoiceData?.items && invoiceData.items.length > 0 ? (
                invoiceData.items.map((item, index) => (
                  <tr key={index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-gray-50 dark:bg-gray-900/10' : 'bg-white dark:bg-gray-800'}`}>
                    <td className="px-2 py-2 text-gray-900 dark:text-white text-xs">{item.product.name}</td>
                    <td className="px-2 py-2 text-center text-gray-600 dark:text-gray-400 text-xs">{item.quantity}</td>
                    <td className="px-2 py-2 text-right text-gray-600 dark:text-gray-400 text-xs">{formatCurrency(item.product.price)}</td>
                    <td className="px-2 py-2 text-right font-bold text-gray-900 dark:text-white text-xs">{formatCurrency(item.totalPrice)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center space-y-2">
                      <ShoppingCart className="w-8 h-8 text-gray-400" />
                      <p className="text-sm font-medium">
                        {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                      </p>
                      <p className="text-xs text-gray-400">
                        {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Colorful Summary Section - Third Image Style */}
      <div className="grid grid-cols-2 gap-4 mb-2">
        <div>
          <h3 className="text-xs font-bold text-gray-900 dark:text-white mb-1">{language === 'en' ? 'PAYMENT INFORMATION:' : 'ငွေပေးချေမှုအချက်အလက်:'}</h3>
          <div className="grid grid-cols-2 gap-4 text-xs">
            <div>
              <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</span>
              <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.paymentMethod || 'No Payment Method'}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Transaction Date:' : 'ငွေပေးချေသည့်ရက်:'}</span>
              <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Currency:' : 'ငွေကြေး:'}</span>
              <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.currency || currentCurrency}</p>
            </div>
            <div>
              <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Status:' : 'အခြေအနေ:'}</span>
              <p className="font-bold text-green-600">{language === 'en' ? 'PAID' : 'ပေးချေပြီး'}</p>
            </div>
          </div>

          <div className="mt-4">
            <h3 className="text-sm font-bold text-gray-900 dark:text-white mb-2">{language === 'en' ? 'TERMS AND CONDITIONS' : 'စည်းမျဉ်းစည်းကမ်းများ'}</h3>
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <p>{language === 'en' ? 'Payment is due within 30 days of invoice date. Late payments may incur additional charges.' : 'ငွေပေးချေမှုသည် ၃၀ ရက်အတွင်း ပြုလုပ်ရမည်။'}</p>
              <p>{language === 'en' ? 'All sales are final. Returns and exchanges are not accepted unless items are defective.' : 'ပစ္စည်းများ ပြန်လည်မပေးအပ်နိုင်ပါ။'}</p>
            </div>
          </div>
        </div>

        <div className="text-right">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'SUB TOTAL' : 'စုစုပေါင်း'}</span>
              <span className="bg-gradient-to-r from-blue-500 to-green-500 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.subtotal || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'TAX' : 'အခွန်'}</span>
              <span className="bg-gradient-to-r from-green-500 to-red-500 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.tax || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'DISCOUNT' : 'လျှော့စျေး'}</span>
              <span className="bg-gradient-to-r from-red-500 to-purple-500 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.discount || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'SHIPPING' : 'ပို့ဆောင်ခ'}</span>
              <span className="bg-gradient-to-r from-orange-500 to-red-500 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.shipping || 0)}</span>
            </div>
            <div className="flex justify-between items-center mt-4">
              <span className="text-sm font-bold text-gray-900 dark:text-white">{language === 'en' ? 'GRAND TOTAL' : 'စုစုပေါင်း'}</span>
              <span className="bg-gradient-to-r from-purple-500 to-pink-500 text-white px-3 py-2 rounded text-sm font-bold">{formatCurrency(invoiceData?.total || 0)}</span>
            </div>
          </div>

          <div className="mt-6 text-right">
            <div className="border-b-2 border-gray-400 mb-2 h-12"></div>
            <p className="text-xs font-bold text-gray-900 dark:text-white">{language === 'en' ? 'SIGNATURE' : 'လက်မှတ်'}</p>
          </div>
        </div>
      </div>

      {/* Colorful QR Code and Thank You Section */}
      <div className="flex justify-between items-center mt-6">
        <div className="text-center bg-gradient-to-r from-blue-500 via-green-500 to-red-500 text-white rounded-lg p-4 flex-1 mr-4">
          <p className="text-sm font-bold">{language === 'en' ? 'Thank You for Your Business!' : 'သင့်လုပ်ငန်းအတွက် ကျေးဇူးတင်ပါသည်!'}</p>
        </div>
        {templateCustom.showQRCode && (
          <div className="text-center">
            <div className="bg-white p-2 rounded border-2 border-blue-500">
              <img
                src={generateQRCode(`${templateCustom.qrCodeData || 'Invoice: '}${invoiceData?.saleNumber}`)}
                alt="QR Code"
                className="w-16 h-16"
              />
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{language === 'en' ? 'Scan for details' : 'အသေးစိတ်ကြည့်ရန်'}</p>
          </div>
        )}
      </div>
    </div>
    )
  }

  // Template 5 - Modern Minimalist Design
  const renderTemplate5 = () => {
    const templateCustom = getCurrentTemplateCustomization()
    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Minimalist Header */}
      <div className="mb-2 border-b-2 border-gray-900 dark:border-gray-100 pb-2">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-3">
            {templateCustom.companyLogo ? (
              <img
                src={templateCustom.companyLogo}
                alt="Company Logo"
                className="w-10 h-10 object-contain rounded"
              />
            ) : (
              <div
                className="w-10 h-10 rounded flex items-center justify-center"
                style={{ backgroundColor: templateCustom.customColors?.primary || '#1f2937' }}
              >
                <span className="text-white font-bold text-lg">C</span>
              </div>
            )}
            <div>
              <h1
                className="text-3xl font-bold text-gray-900 dark:text-white mb-2"
                style={{ fontFamily: templateCustom.customFonts?.heading || 'Arial' }}
              >
                {templateCustom.companyName || companySettings.name || 'BitsTech'}
              </h1>
              <div className="text-xs space-y-1 text-gray-600 dark:text-gray-400">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-3 w-3 text-gray-500" />
                  <span>{companySettings.address || 'No. 456, Technology Street, Yangon, Myanmar'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-3 w-3 text-gray-500" />
                  <span>{companySettings.phone || '+95 9 ***********'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-3 w-3 text-gray-500" />
                  <span>{companySettings.email || '<EMAIL>'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Globe className="h-3 w-3 text-gray-500" />
                  <span>{companySettings.website || 'www.bitstech.com'}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">
            <h2 className="text-4xl font-bold text-gray-900 dark:text-white mb-2">{t.invoice.toUpperCase()}</h2>
            <div className="text-xs space-y-1">
              <p><span className="font-semibold">{language === 'en' ? 'Invoice #:' : 'ငွေတောင်းခံလွှာနံပါတ်:'}</span> {invoiceData?.saleNumber}</p>
              <p><span className="font-semibold">{language === 'en' ? 'Date:' : 'ရက်စွဲ:'}</span> {invoiceData?.timestamp.toLocaleDateString()}</p>
              <p><span className="font-semibold">{language === 'en' ? 'Due Date:' : 'ပေးရမည့်ရက်:'}</span> {new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bill To Section */}
      <div className="mb-2">
        <h3 className="text-xs font-bold text-gray-900 dark:text-white mb-1 border-b border-gray-300 dark:border-gray-600 pb-1">{t.billTo.toUpperCase()}:</h3>
        <div className="text-xs space-y-1">
          <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.customer.name}</p>
          <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.address}</p>
          <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.phone}</p>
        </div>
      </div>

      {/* Payment Information Section */}
      <div className="mb-2">
        <h3 className="text-xs font-bold text-gray-900 dark:text-white mb-1 border-b border-gray-300 dark:border-gray-600 pb-1">{language === 'en' ? 'PAYMENT INFORMATION' : 'ငွေပေးချေမှုအချက်အလက်'}:</h3>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.paymentMethod || 'No Payment Method'}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Transaction Date:' : 'ငွေပေးချေသည့်ရက်:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Currency:' : 'ငွေကြေး:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.currency || currentCurrency}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Status:' : 'အခြေအနေ:'}</span>
            <p className="font-bold text-green-600">{language === 'en' ? 'PAID' : 'ပေးချေပြီး'}</p>
          </div>
        </div>
      </div>

      {/* Minimalist Items Table */}
      <div className="mb-2">
        <div className="overflow-hidden border border-gray-300 dark:border-gray-600">
          <table className="w-full">
            <thead>
              <tr className="bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900">
                <th className="px-2 py-2 text-left font-bold text-xs">{language === 'en' ? 'DESCRIPTION' : 'ဖော်ပြချက်'}</th>
                <th className="px-2 py-2 text-center font-bold text-xs">{language === 'en' ? 'QTY' : 'အရေအတွက်'}</th>
                <th className="px-2 py-2 text-right font-bold text-xs">{language === 'en' ? 'UNIT PRICE' : 'တစ်ခုစျေး'}</th>
                <th className="px-2 py-2 text-right font-bold text-xs">{language === 'en' ? 'TOTAL' : 'စုစုပေါင်း'}</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800">
              {invoiceData?.items && invoiceData.items.length > 0 ? (
                invoiceData.items.map((item, index) => (
                  <tr key={index} className="border-b border-gray-200 dark:border-gray-600">
                    <td className="px-2 py-2 text-gray-900 dark:text-white text-xs">{item.product.name}</td>
                    <td className="px-2 py-2 text-center text-gray-600 dark:text-gray-400 text-xs">{item.quantity}</td>
                    <td className="px-2 py-2 text-right text-gray-600 dark:text-gray-400 text-xs">{formatCurrency(item.product.price)}</td>
                    <td className="px-2 py-2 text-right font-bold text-gray-900 dark:text-white text-xs">{formatCurrency(item.totalPrice)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center space-y-2">
                      <ShoppingCart className="w-8 h-8 text-gray-400" />
                      <p className="text-sm font-medium">
                        {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                      </p>
                      <p className="text-xs text-gray-400">
                        {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Minimalist Summary Section */}
      <div className="flex justify-end mb-2">
        <div className="w-48 space-y-1">
          <div className="flex justify-between items-center py-1 border-b border-gray-300 dark:border-gray-600">
            <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{t.subtotal}:</span>
            <span className="text-xs font-bold text-gray-900 dark:text-white">{formatCurrency(invoiceData?.subtotal || 0)}</span>
          </div>
          <div className="flex justify-between items-center py-1 border-b border-gray-300 dark:border-gray-600">
            <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{t.discount}:</span>
            <span className="text-xs font-bold text-red-600">-{formatCurrency(invoiceData?.discount || 0)}</span>
          </div>
          <div className="flex justify-between items-center py-1 border-b border-gray-300 dark:border-gray-600">
            <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{t.tax}:</span>
            <span className="text-xs font-bold text-gray-900 dark:text-white">{formatCurrency(invoiceData?.tax || 0)}</span>
          </div>
          <div className="flex justify-between items-center py-1 border-b border-gray-300 dark:border-gray-600">
            <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{t.shipping}:</span>
            <span className="text-xs font-bold text-gray-900 dark:text-white">{formatCurrency(invoiceData?.shipping || 0)}</span>
          </div>
          <div className="bg-gray-900 dark:bg-gray-100 text-white dark:text-gray-900 p-2 mt-2">
            <div className="flex justify-between items-center">
              <span className="text-sm font-bold">{t.grandTotal}:</span>
              <span className="text-sm font-bold">{formatCurrency(invoiceData?.total || 0)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Minimalist Signature Section */}
      <div className="border-t-2 border-gray-300 dark:border-gray-600 pt-2">
        <div className="grid grid-cols-2 gap-8 mb-2">
          <div className="text-center">
            <div className="border-b-2 border-gray-400 mb-1 h-8"></div>
            <p className="text-xs font-bold text-gray-700 dark:text-gray-300">{language === 'en' ? 'Customer Signature' : 'ဖောက်သည် လက်မှတ်'}</p>
          </div>
          <div className="text-center">
            <div className="border-b-2 border-gray-400 mb-1 h-8"></div>
            <p className="text-xs font-bold text-gray-700 dark:text-gray-300">{t.signature}</p>
            <p className="text-xs text-gray-500 mt-1">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
        </div>

        <div className="flex justify-between items-center border-t border-gray-300 dark:border-gray-600 pt-2">
          <div className="text-center flex-1">
            <p className="text-xs font-bold text-gray-900 dark:text-white">{language === 'en' ? 'Thank You for Your Business!' : 'သင့်လုပ်ငန်းအတွက် ကျေးဇူးတင်ပါသည်!'}</p>
          </div>
          {templateCustom.showQRCode && (
            <div className="text-center ml-4">
              <div className="bg-white p-2 rounded border border-gray-400">
                <img
                  src={generateQRCode(`${templateCustom.qrCodeData || 'Invoice: '}${invoiceData?.saleNumber}`)}
                  alt="QR Code"
                  className="w-16 h-16"
                />
              </div>
              <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{language === 'en' ? 'Scan for details' : 'အသေးစိတ်ကြည့်ရန်'}</p>
            </div>
          )}
        </div>
      </div>
    </div>
    )
  }

  // Template 6 - Professional Green Design
  const renderTemplate6 = () => {
    const templateCustom = getCurrentTemplateCustomization()
    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Green Professional Header */}
      <div className="mb-2 bg-green-600 text-white p-2 rounded-lg">
        <div className="flex justify-between items-start">
          <div className="flex items-center space-x-3">
            {templateCustom.companyLogo ? (
              <img
                src={templateCustom.companyLogo}
                alt="Company Logo"
                className="w-8 h-8 object-contain rounded"
              />
            ) : (
              <div
                className="w-8 h-8 rounded flex items-center justify-center"
                style={{ backgroundColor: templateCustom.customColors?.primary || '#16a34a' }}
              >
                <span className="text-white font-bold text-sm">C</span>
              </div>
            )}
            <div>
              <h1
                className="text-2xl font-bold mb-2"
                style={{ fontFamily: templateCustom.customFonts?.heading || 'Arial' }}
              >
                {templateCustom.companyName || companySettings.name || 'BitsTech'}
              </h1>
              <div className="text-xs space-y-1">
                <div className="flex items-center space-x-2">
                  <MapPin className="h-3 w-3 text-white/80" />
                  <span>{companySettings.address || 'No. 456, Technology Street, Yangon, Myanmar'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Phone className="h-3 w-3 text-white/80" />
                  <span>{companySettings.phone || '+95 9 ***********'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Mail className="h-3 w-3 text-white/80" />
                  <span>{companySettings.email || '<EMAIL>'}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Globe className="h-3 w-3 text-white/80" />
                  <span>{companySettings.website || 'www.bitstech.com'}</span>
                </div>
              </div>
            </div>
          </div>
          <div className="text-right">
            <div className="bg-white text-green-600 px-4 py-2 rounded">
              <h2 className="text-xl font-bold">{t.invoice.toUpperCase()}</h2>
            </div>
            <div className="mt-2 text-xs space-y-1">
              <p><span className="font-semibold">{language === 'en' ? 'Invoice #:' : 'ငွေတောင်းခံလွှာနံပါတ်:'}</span> {invoiceData?.saleNumber}</p>
              <p><span className="font-semibold">{language === 'en' ? 'Date:' : 'ရက်စွဲ:'}</span> {invoiceData?.timestamp.toLocaleDateString()}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Bill To Section */}
      <div className="mb-2">
        <h3 className="text-xs font-bold text-green-600 mb-1 border-b-2 border-green-600 pb-1">{t.billTo.toUpperCase()}:</h3>
        <div className="text-xs space-y-1">
          <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.customer.name}</p>
          <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.address}</p>
          <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.phone}</p>
        </div>
      </div>

      {/* Payment Information Section */}
      <div className="mb-2">
        <h3 className="text-xs font-bold text-green-600 mb-1 border-b-2 border-green-600 pb-1">{language === 'en' ? 'PAYMENT INFORMATION' : 'ငွေပေးချေမှုအချက်အလက်'}:</h3>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.paymentMethod || 'No Payment Method'}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Transaction Date:' : 'ငွေပေးချေသည့်ရက်:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Currency:' : 'ငွေကြေး:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.currency || currentCurrency}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Status:' : 'အခြေအနေ:'}</span>
            <p className="font-bold text-green-600">{language === 'en' ? 'PAID' : 'ပေးချေပြီး'}</p>
          </div>
        </div>
      </div>

      {/* Green Professional Items Table */}
      <div className="mb-2">
        <div className="overflow-hidden rounded-lg shadow-lg border border-green-200">
          <table className="w-full">
            <thead>
              <tr className="bg-green-600 text-white">
                <th className="px-2 py-2 text-left font-bold text-xs">{language === 'en' ? 'DESCRIPTION' : 'ဖော်ပြချက်'}</th>
                <th className="px-2 py-2 text-center font-bold text-xs">{language === 'en' ? 'QTY' : 'အရေအတွက်'}</th>
                <th className="px-2 py-2 text-right font-bold text-xs">{language === 'en' ? 'UNIT PRICE' : 'တစ်ခုစျေး'}</th>
                <th className="px-2 py-2 text-right font-bold text-xs">{language === 'en' ? 'TOTAL' : 'စုစုပေါင်း'}</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800">
              {invoiceData?.items && invoiceData.items.length > 0 ? (
                invoiceData.items.map((item, index) => (
                  <tr key={index} className={`border-b border-gray-200 ${index % 2 === 0 ? 'bg-green-50 dark:bg-gray-900/10' : 'bg-white dark:bg-gray-800'}`}>
                    <td className="px-2 py-2 text-gray-900 dark:text-white text-xs">{item.product.name}</td>
                    <td className="px-2 py-2 text-center text-gray-600 dark:text-gray-400 text-xs">{item.quantity}</td>
                    <td className="px-2 py-2 text-right text-gray-600 dark:text-gray-400 text-xs">{formatCurrency(item.product.price)}</td>
                    <td className="px-2 py-2 text-right font-bold text-gray-900 dark:text-white text-xs">{formatCurrency(item.totalPrice)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={4} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center space-y-2">
                      <ShoppingCart className="w-8 h-8 text-gray-400" />
                      <p className="text-sm font-medium">
                        {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                      </p>
                      <p className="text-xs text-gray-400">
                        {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Green Professional Summary Section */}
      <div className="grid grid-cols-2 gap-4 mb-2">
        <div>
          <h3 className="text-xs font-bold text-green-600 mb-1">{language === 'en' ? 'PAYMENT INFORMATION:' : 'ငွေပေးချေမှုအချက်အလက်:'}</h3>
          <div className="text-xs space-y-1">
            <p className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'} {invoiceData?.paymentMethod}</p>
            <p className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Account Name:' : 'အကောင့်အမည်:'} {companySettings.name}</p>
            <p className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Bank Detail:' : 'ဘဏ်အသေးစိတ်:'} {companySettings.taxId}</p>
          </div>

          <div className="mt-4">
            <h3 className="text-sm font-bold text-green-600 mb-2">{language === 'en' ? 'TERMS AND CONDITIONS' : 'စည်းမျဉ်းစည်းကမ်းများ'}</h3>
            <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
              <p>{language === 'en' ? 'Payment is due within 30 days of invoice date.' : 'ငွေပေးချေမှုသည် ၃၀ ရက်အတွင်း ပြုလုပ်ရမည်။'}</p>
              <p>{language === 'en' ? 'Late payments may incur additional charges.' : 'နောက်ကျသော ငွေပေးချေမှုများတွင် အပိုကြေးကောက်ခံနိုင်ပါသည်။'}</p>
            </div>
          </div>
        </div>

        <div className="text-right">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'SUB TOTAL' : 'စုစုပေါင်း'}</span>
              <span className="bg-green-600 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.subtotal || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'TAX' : 'အခွန်'}</span>
              <span className="bg-green-600 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.tax || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'DISCOUNT' : 'လျှော့စျေး'}</span>
              <span className="bg-green-600 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.discount || 0)}</span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-xs font-semibold text-gray-600 dark:text-gray-400">{language === 'en' ? 'SHIPPING' : 'ပို့ဆောင်ခ'}</span>
              <span className="bg-green-600 text-white px-2 py-1 rounded text-xs font-bold">{formatCurrency(invoiceData?.shipping || 0)}</span>
            </div>
            <div className="flex justify-between items-center mt-4">
              <span className="text-sm font-bold text-gray-900 dark:text-white">{language === 'en' ? 'GRAND TOTAL' : 'စုစုပေါင်း'}</span>
              <span className="bg-green-700 text-white px-3 py-2 rounded text-sm font-bold">{formatCurrency(invoiceData?.total || 0)}</span>
            </div>
          </div>

          <div className="mt-6 text-right">
            <div className="border-b-2 border-gray-400 mb-2 h-12"></div>
            <p className="text-xs font-bold text-gray-900 dark:text-white">{language === 'en' ? 'SIGNATURE' : 'လက်မှတ်'}</p>
          </div>
        </div>
      </div>

      {/* Green QR Code and Thank You Section */}
      <div className="flex justify-between items-center mt-6">
        <div className="text-center bg-green-600 text-white rounded-lg p-4 flex-1 mr-4">
          <p className="text-sm font-bold">{language === 'en' ? 'Thank You for Your Business!' : 'သင့်လုပ်ငန်းအတွက် ကျေးဇူးတင်ပါသည်!'}</p>
        </div>
        {templateCustom.showQRCode && (
          <div className="text-center">
            <div className="bg-white p-2 rounded border-2 border-green-600">
              <img
                src={generateQRCode(`${templateCustom.qrCodeData || 'Invoice: '}${invoiceData?.saleNumber}`)}
                alt="QR Code"
                className="w-16 h-16"
              />
            </div>
            <p className="text-xs text-gray-600 dark:text-gray-400 mt-1">{language === 'en' ? 'Scan for details' : 'အသေးစိတ်ကြည့်ရန်'}</p>
          </div>
        )}
      </div>
    </div>
    )
  }

  // Template 7 - Modern Blue Corporate (Based on first image)
  const renderTemplate7 = () => {
    const templateCustom = getCurrentTemplateCustomization()
    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Modern Blue Header with Geometric Design */}
      <div className="relative mb-2 bg-gradient-to-r from-blue-600 to-blue-700 text-white overflow-hidden">
        {/* Geometric Elements */}
        <div className="absolute top-0 left-0 w-full h-1 bg-white"></div>
        <div className="absolute top-1 left-0 w-full h-0.5 bg-blue-800"></div>
        <div className="absolute top-0 right-0 w-24 h-full bg-blue-800 transform skew-x-12"></div>

        <div className="relative p-2">
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="flex items-center space-x-1 mb-1">
                {templateCustom.companyLogo ? (
                  <img
                    src={templateCustom.companyLogo}
                    alt="Company Logo"
                    className="w-8 h-8 object-contain rounded bg-white p-1"
                  />
                ) : (
                  <div
                    className="w-8 h-8 rounded flex items-center justify-center"
                    style={{ backgroundColor: templateCustom.customColors?.primary || '#2563eb' }}
                  >
                    <span className="text-white text-sm font-bold">»</span>
                  </div>
                )}
                <div>
                  <h1
                    className="text-sm font-bold"
                    style={{ fontFamily: templateCustom.customFonts?.heading || 'Arial' }}
                  >
                    {templateCustom.companyName || companySettings.name || 'BitsTech'}
                  </h1>
                </div>
              </div>

              <div className="space-y-0.5 text-xs">
                <div className="flex items-center space-x-1">
                  <span className="w-2 h-2 bg-white/20 rounded-sm flex items-center justify-center">
                    <Phone className="h-1.5 w-1.5" />
                  </span>
                  <span>{companySettings.phone || '+95 9 ***********'}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="w-2 h-2 bg-white/20 rounded-sm flex items-center justify-center">
                    <Mail className="h-1.5 w-1.5" />
                  </span>
                  <span>{companySettings.email || '<EMAIL>'}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="w-2 h-2 bg-white/20 rounded-sm flex items-center justify-center">
                    <Globe className="h-1.5 w-1.5" />
                  </span>
                  <span>{companySettings.website || 'www.bitstech.com'}</span>
                </div>
                <div className="flex items-center space-x-1">
                  <span className="w-2 h-2 bg-white/20 rounded-sm flex items-center justify-center">
                    <MapPin className="h-1.5 w-1.5" />
                  </span>
                  <span>{companySettings.address || 'No. 456, Technology Street, Yangon, Myanmar'}</span>
                </div>
              </div>
            </div>

            <div className="text-right">
              <h2 className="text-sm font-bold mb-1">INVOICE</h2>
              <div className="space-y-0.5 text-xs">
                <div className="flex justify-between">
                  <span>Invoice No :</span>
                  <span className="font-semibold">{invoiceData?.saleNumber}</span>
                </div>
                <div className="flex justify-between">
                  <span>Date :</span>
                  <span className="font-semibold">{invoiceData?.timestamp.toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Due Date :</span>
                  <span className="font-semibold">{new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Account No :</span>
                  <span className="font-semibold">{companySettings.taxId || 'ACC-001'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bill To and Payment Info */}
      <div className="grid grid-cols-2 gap-2 mb-2">
        <div>
          <h3 className="text-xs font-bold text-blue-600 mb-1">{language === 'en' ? 'BILL TO:' : 'ငွေတောင်းခံရန်:'}</h3>
          <div className="space-y-0.5">
            <p className="font-semibold text-xs text-gray-900 dark:text-white">{invoiceData?.customer.name || 'No Customer Selected'}</p>
            <p className="text-xs text-gray-600 dark:text-gray-300">{invoiceData?.customer.address || 'Please complete a POS checkout to see real data'}</p>
            <p className="text-xs text-gray-600 dark:text-gray-300">{invoiceData?.customer.phone || 'N/A'}</p>
          </div>
        </div>
        <div>
          <h3 className="text-xs font-bold text-blue-600 mb-1">{language === 'en' ? 'PAYMENT INFO:' : 'ငွေပေးချေမှုအချက်အလက်:'}</h3>
          <div className="space-y-0.5 text-xs text-gray-900 dark:text-white">
            <div className="flex justify-between">
              <span>{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</span>
              <span>{invoiceData?.paymentMethod || 'No Payment Method'}</span>
            </div>
            <div className="flex justify-between">
              <span>{language === 'en' ? 'Transaction Date:' : 'ငွေပေးချေသည့်ရက်:'}</span>
              <span>{invoiceData?.timestamp.toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between">
              <span>{language === 'en' ? 'Currency:' : 'ငွေကြေး:'}</span>
              <span>{invoiceData?.currency || currentCurrency}</span>
            </div>
            <div className="flex justify-between">
              <span>{language === 'en' ? 'Status:' : 'အခြေအနေ:'}</span>
              <span className="text-green-600 font-bold">{language === 'en' ? 'PAID' : 'ပေးချေပြီး'}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-2">
        <div className="overflow-hidden rounded border border-blue-200">
          <table className="w-full text-xs">
            <thead>
              <tr className="bg-blue-600 text-white">
                <th className="px-2 py-1.5 text-left font-semibold">NO</th>
                <th className="px-2 py-1.5 text-left font-semibold">PRODUCT DESCRIPTION</th>
                <th className="px-2 py-1.5 text-center font-semibold">UNIT PRICE</th>
                <th className="px-2 py-1.5 text-center font-semibold">QTY</th>
                <th className="px-2 py-1.5 text-right font-semibold">TOTAL</th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-gray-800">
              {invoiceData?.items && invoiceData.items.length > 0 ? (
                invoiceData.items.map((item, index) => (
                  <tr key={index} className={`border-b ${index % 2 === 0 ? 'bg-blue-50 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'}`}>
                    <td className="px-2 py-1.5 text-center font-semibold text-gray-900 dark:text-white">{String(index + 1).padStart(2, '0')}</td>
                    <td className="px-2 py-1.5 font-semibold text-gray-900 dark:text-white">{item.product.name}</td>
                    <td className="px-2 py-1.5 text-center text-gray-900 dark:text-white">{formatCurrency(item.product.price)}</td>
                    <td className="px-2 py-1.5 text-center font-semibold text-gray-900 dark:text-white">{item.quantity}</td>
                    <td className="px-2 py-1.5 text-right font-semibold text-gray-900 dark:text-white">{formatCurrency(item.totalPrice)}</td>
                  </tr>
                ))
              ) : (
                <tr>
                  <td colSpan={5} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center space-y-2">
                      <ShoppingCart className="w-8 h-8 text-gray-400" />
                      <p className="text-sm font-medium">
                        {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                      </p>
                      <p className="text-xs text-gray-400">
                        {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                      </p>
                    </div>
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Summary */}
      <div className="flex justify-end mb-2">
        <div className="w-40 space-y-1 text-xs text-gray-900 dark:text-white">
          <div className="flex justify-between py-0.5">
            <span>Sub Total</span>
            <span className="font-semibold">{formatCurrency(invoiceData?.subtotal || 0)}</span>
          </div>
          <div className="flex justify-between py-0.5">
            <span>{language === 'en' ? 'Discount' : 'လျှော့စျေး'}</span>
            <span className="font-semibold">{formatCurrency(invoiceData?.discount || 0)}</span>
          </div>
          <div className="flex justify-between py-0.5">
            <span>{language === 'en' ? 'Tax' : 'အခွန်'}</span>
            <span className="font-semibold">{formatCurrency(invoiceData?.tax || 0)}</span>
          </div>
          <div className="flex justify-between py-0.5">
            <span>{language === 'en' ? 'Shipping' : 'ပို့ဆောင်ခ'}</span>
            <span className="font-semibold">{formatCurrency(invoiceData?.shipping || 0)}</span>
          </div>
          <div className="bg-blue-600 text-white p-1.5 rounded">
            <div className="flex justify-between items-center">
              <span className="font-bold">{language === 'en' ? 'Grand Total' : 'စုစုပေါင်း'}</span>
              <span className="text-sm font-bold">{formatCurrency(invoiceData?.total || 0)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Terms and Thank You */}
      <div className="grid grid-cols-2 gap-2 mb-2">
        <div>
          <h4 className="font-bold text-blue-600 mb-1 text-xs">TERMS & CONDITION</h4>
          <p className="text-xs text-gray-600 dark:text-gray-300 leading-tight">
            {language === 'en' ? 'Payment is due within 30 days of invoice date. Late payments may incur additional charges. All sales are final unless items are defective.' : 'ငွေပေးချေမှုသည် ၃၀ ရက်အတွင်း ပြုလုပ်ရမည်။ နောက်ကျပါက အပိုအခကြေးငွေ ကောက်ခံနိုင်ပါသည်။'}
          </p>
        </div>
        <div className="text-right">
          <h4 className="font-bold text-blue-600 mb-1 text-xs">THANK YOU FOR YOUR BUSINESS</h4>
          <div className="mt-6">
            <div className="border-b border-gray-300 mb-1 h-6"></div>
            <p className="text-xs text-gray-900 dark:text-white">Signature</p>
          </div>
        </div>
      </div>
    </div>
    )
  }

  // Template 8 - Orange Professional (Based on second image)
  const renderTemplate8 = () => {
    const templateCustom = getCurrentTemplateCustomization()
    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Header with Contact Info */}
      <div className="mb-2">
        <div className="grid grid-cols-3 gap-1 text-xs text-gray-600 dark:text-gray-300 mb-2">
          <div className="flex items-center space-x-1">
            <Phone className="h-3 w-3" />
            <span>{companySettings.phone || '+95 9 ***********'}</span>
          </div>
          <div className="flex items-center space-x-1 justify-center">
            <Mail className="h-3 w-3" />
            <span>{companySettings.email || '<EMAIL>'}</span>
          </div>
          <div className="flex items-center space-x-1 justify-end">
            <Globe className="h-3 w-3" />
            <span>{companySettings.website || 'www.bitstech.com'}</span>
          </div>
        </div>

        {/* Orange Header Bar */}
        <div className="bg-orange-500 text-white p-1 rounded-t">
          <h1 className="text-sm font-bold">{t.invoice.toUpperCase()}</h1>
        </div>
      </div>

      {/* Company and Invoice Info */}
      <div className="grid grid-cols-2 gap-2 mb-2">
        <div>
          <h2 className="text-sm font-bold text-gray-800 dark:text-white mb-1">{language === 'en' ? 'Invoice From' : 'ငွေတောင်းခံသူ'}</h2>
          <div className="flex items-center space-x-2 mb-2">
            {templateCustom.companyLogo ? (
              <img
                src={templateCustom.companyLogo}
                alt="Company Logo"
                className="w-6 h-6 object-contain rounded bg-white p-1"
              />
            ) : (
              <div
                className="w-6 h-6 rounded flex items-center justify-center"
                style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
              >
                <span className="text-white font-bold text-xs">B</span>
              </div>
            )}
            <p
              className="text-xs font-semibold text-gray-900 dark:text-white"
              style={{ fontFamily: templateCustom.customFonts?.heading || 'Arial' }}
            >
              {templateCustom.companyName || companySettings.name || 'BitsTech'}
            </p>
          </div>
          <div className="text-xs space-y-0.5">
            <div className="flex items-center space-x-2">
              <MapPin className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-300">{companySettings.address || 'No. 456, Technology Street, Yangon, Myanmar'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Phone className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-300">{companySettings.phone || '+95 9 ***********'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Mail className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-300">{companySettings.email || '<EMAIL>'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <Globe className="h-3 w-3 text-gray-500" />
              <span className="text-gray-600 dark:text-gray-300">{companySettings.website || 'www.bitstech.com'}</span>
            </div>
          </div>
        </div>
        <div className="text-right">
          <div className="bg-orange-500 text-white p-2 rounded mb-2">
            <h3 className="text-sm font-bold">{language === 'en' ? 'Total Due' : 'စုစုပေါင်း'}</h3>
            <p className="text-lg font-bold">{formatCurrency(invoiceData?.total || 0)}</p>
          </div>
          <div className="text-xs space-y-0.5 text-gray-900 dark:text-white">
            <div className="flex justify-between">
              <span>{language === 'en' ? 'Invoice #:' : 'ငွေတောင်းခံလွှာနံပါတ်:'}</span>
              <span className="font-semibold">{invoiceData?.saleNumber}</span>
            </div>
            <div className="flex justify-between">
              <span>{language === 'en' ? 'Date:' : 'ရက်စွဲ:'}</span>
              <span className="font-semibold">{invoiceData?.timestamp.toLocaleDateString()}</span>
            </div>
            <div className="flex justify-between">
              <span>{language === 'en' ? 'Due Date:' : 'ပေးရမည့်ရက်:'}</span>
              <span className="font-semibold">{new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Bill To */}
      <div className="mb-2">
        <h3 className="text-sm font-bold text-gray-800 dark:text-white mb-1">{language === 'en' ? 'Bill To:' : 'ငွေတောင်းခံရန်:'}</h3>
        <div className="text-xs">
          <p className="font-semibold text-gray-900 dark:text-white">{invoiceData?.customer.name}</p>
          <p className="text-gray-600 dark:text-gray-300">{invoiceData?.customer.address}</p>
          <p className="text-gray-600 dark:text-gray-300">{invoiceData?.customer.phone}</p>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-2">
        <table className="w-full text-xs border-collapse">
          <thead>
            <tr className="bg-orange-500 text-white">
              <th className="border border-orange-600 px-2 py-1.5 text-left font-semibold">#</th>
              <th className="border border-orange-600 px-2 py-1.5 text-left font-semibold">{language === 'en' ? 'ITEM DESCRIPTION' : 'ပစ္စည်းဖော်ပြချက်'}</th>
              <th className="border border-orange-600 px-2 py-1.5 text-center font-semibold">{language === 'en' ? 'UNIT PRICE' : 'တစ်ခုစျေး'}</th>
              <th className="border border-orange-600 px-2 py-1.5 text-center font-semibold">{language === 'en' ? 'QTY' : 'အရေအတွက်'}</th>
              <th className="border border-orange-600 px-2 py-1.5 text-right font-semibold">{language === 'en' ? 'TOTAL' : 'စုစုပေါင်း'}</th>
            </tr>
          </thead>
          <tbody>
            {invoiceData?.items && invoiceData.items.length > 0 ? (
              invoiceData.items.map((item, index) => (
                <tr key={index} className={index % 2 === 0 ? 'bg-gray-50 dark:bg-gray-700' : 'bg-white dark:bg-gray-800'}>
                  <td className="border border-gray-300 dark:border-gray-600 px-2 py-1.5 text-center font-semibold text-gray-900 dark:text-white">{index + 1}</td>
                  <td className="border border-gray-300 dark:border-gray-600 px-2 py-1.5">
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">{item.product.name}</p>
                    </div>
                  </td>
                  <td className="border border-gray-300 dark:border-gray-600 px-2 py-1.5 text-center text-gray-900 dark:text-white">{formatCurrency(item.product.price)}</td>
                  <td className="border border-gray-300 dark:border-gray-600 px-2 py-1.5 text-center font-semibold text-gray-900 dark:text-white">{item.quantity}</td>
                  <td className="border border-gray-300 dark:border-gray-600 px-2 py-1.5 text-right font-semibold text-gray-900 dark:text-white">{formatCurrency(item.totalPrice)}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="border border-gray-300 dark:border-gray-600 px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                  <div className="flex flex-col items-center space-y-2">
                    <ShoppingCart className="w-8 h-8 text-gray-400" />
                    <p className="text-sm font-medium">
                      {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                    </p>
                    <p className="text-xs text-gray-400">
                      {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Summary */}
      <div className="flex justify-end mb-2">
        <div className="w-48 space-y-1 text-xs text-gray-900 dark:text-white">
          <div className="flex justify-between py-0.5">
            <span>{language === 'en' ? 'SUB TOTAL' : 'စုစုပေါင်း'}</span>
            <span className="font-semibold">{formatCurrency(invoiceData?.subtotal || 0)}</span>
          </div>
          <div className="flex justify-between py-0.5">
            <span>{language === 'en' ? 'Discount' : 'လျှော့စျေး'}</span>
            <span className="font-semibold">{formatCurrency(invoiceData?.discount || 0)}</span>
          </div>
          <div className="flex justify-between py-0.5">
            <span>{language === 'en' ? 'Tax' : 'အခွန်'}</span>
            <span className="font-semibold">{formatCurrency(invoiceData?.tax || 0)}</span>
          </div>
          <div className="flex justify-between py-0.5">
            <span>{language === 'en' ? 'Shipping' : 'ပို့ဆောင်ခ'}</span>
            <span className="font-semibold">{formatCurrency(invoiceData?.shipping || 0)}</span>
          </div>
          <div className="bg-orange-500 text-white p-2 rounded">
            <div className="flex justify-between items-center">
              <span className="font-bold">{language === 'en' ? 'GRAND TOTAL' : 'စုစုပေါင်း'}</span>
              <span className="text-sm font-bold">{formatCurrency(invoiceData?.total || 0)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Method and Signature */}
      <div className="grid grid-cols-2 gap-4 mb-2">
        <div>
          <h4 className="font-bold text-gray-800 dark:text-white mb-1 text-xs">{language === 'en' ? 'Payment Method' : 'ငွေပေးချေမှုနည်းလမ်း'}</h4>
          <div className="text-xs text-gray-600 dark:text-gray-300 space-y-0.5">
            <p>{language === 'en' ? 'Bank Name:' : 'ဘဏ်အမည်:'} {language === 'en' ? 'KBZ Bank Ltd' : 'KBZ ဘဏ် လီမိတက်'}</p>
            <p>{language === 'en' ? 'Account Name:' : 'အကောင့်အမည်:'} {language === 'en' ? 'BitsTech Company' : 'BitsTech ကုမ္ပဏီ'}</p>
            <p>{language === 'en' ? 'Account Number:' : 'အကောင့်နံပါတ်:'} {companySettings.taxId}</p>
            <p>{language === 'en' ? 'Branch:' : 'ဘဏ်ခွဲ:'} {language === 'en' ? 'Yangon Main Branch' : 'ရန်ကုန် ပင်မဘဏ်ခွဲ'}</p>
          </div>

          <div className="mt-3">
            <h4 className="font-bold text-gray-800 dark:text-white mb-1 text-xs">{language === 'en' ? 'Terms & Conditions' : 'စည်းမျဉ်းစည်းကမ်းများ'}</h4>
            <p className="text-xs text-gray-600 dark:text-gray-300">
              {language === 'en'
                ? 'Payment is due within 30 days of invoice date. Late payments may incur additional charges.'
                : 'ငွေပေးချေမှုသည် ၃၀ ရက်အတွင်း ပြုလုပ်ရမည်။ နောက်ကျပါက အပိုအခကြေးငွေ ကောက်ခံနိုင်ပါသည်။'
              }
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="mt-8">
            <div className="border-b border-gray-300 mb-1 h-8"></div>
            <p className="text-xs font-semibold text-gray-900 dark:text-white">{language === 'en' ? 'Authorized Signature' : 'လက်မှတ်'}</p>
            <p className="text-xs text-gray-600 dark:text-gray-300">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* Footer */}
      <div className="text-center mt-6">
        <div className="flex items-center justify-center space-x-2 mb-2">
          <div className="w-6 h-6 bg-orange-500 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">B</span>
          </div>
          <span className="text-sm font-bold text-gray-800 dark:text-white">{language === 'en' ? 'BitsTech' : 'BitsTech'}</span>
        </div>
        <p className="text-xs text-gray-600 dark:text-gray-300">{language === 'en' ? 'Thanks for your business' : 'သင့်လုပ်ငန်းအတွက် ကျေးဇူးတင်ပါသည်'}</p>
      </div>
    </div>
    )
  }

  // Template 9 - Clean Modern (Based on third image)
  const renderTemplate9 = () => {
    const templateCustom = getCurrentTemplateCustomization()
    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Header with Decorative Elements */}
      <div className="relative mb-2">
        {/* Decorative circles */}
        <div className="absolute top-0 right-0 flex space-x-2">
          <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full"></div>
          <div className="w-6 h-6 bg-gray-300 dark:bg-gray-500 rounded-full"></div>
          <div className="w-4 h-4 bg-gray-400 dark:bg-gray-400 rounded-full"></div>
          <div className="w-3 h-3 bg-gray-500 dark:bg-gray-300 rounded-full"></div>
        </div>

        <div className="pt-8">
          <h1 className="text-lg font-bold text-gray-800 dark:text-white mb-1">{t.invoice.toUpperCase()}</h1>
          <div className="grid grid-cols-2 gap-2">
            <div className="text-xs space-y-0.5">
              <p><span className="font-semibold">{language === 'en' ? 'Invoice #:' : 'ငွေတောင်းခံလွှာနံပါတ်:'}</span> {invoiceData?.saleNumber}</p>
              <p><span className="font-semibold">{language === 'en' ? 'Date:' : 'ရက်စွဲ:'}</span> {invoiceData?.timestamp.toLocaleDateString()}</p>
              <p><span className="font-semibold">{language === 'en' ? 'Due Date:' : 'ပေးရမည့်ရက်:'}</span> {new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}</p>
            </div>
            <div className="text-right text-xs space-y-0.5">
              <p><span className="font-semibold">{language === 'en' ? 'Invoice From:' : 'ငွေတောင်းခံသူ:'}</span></p>
              <div className="flex items-center justify-end space-x-2 mb-1">
                {templateCustom.companyLogo ? (
                  <img
                    src={templateCustom.companyLogo}
                    alt="Company Logo"
                    className="w-5 h-5 object-contain rounded bg-white p-1"
                  />
                ) : (
                  <div
                    className="w-5 h-5 rounded flex items-center justify-center"
                    style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
                  >
                    <span className="text-white font-bold text-xs">B</span>
                  </div>
                )}
                <p
                  className="text-xs text-gray-900 dark:text-white font-semibold"
                  style={{ fontFamily: templateCustom.customFonts?.heading || 'Arial' }}
                >
                  {templateCustom.companyName || companySettings.name || 'BitsTech'}
                </p>
              </div>
              <div className="text-right text-xs space-y-0.5 mt-2">
                <p className="text-gray-600 dark:text-gray-400">{companySettings.address || 'No. 456, Technology Street, Yangon, Myanmar'}</p>
                <div className="flex items-center justify-end space-x-1">
                  <Phone className="h-3 w-3 text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-400">{companySettings.phone || '+95 9 ***********'}</span>
                </div>
                <div className="flex items-center justify-end space-x-1">
                  <Mail className="h-3 w-3 text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-400">{companySettings.email || '<EMAIL>'}</span>
                </div>
                <div className="flex items-center justify-end space-x-1">
                  <Globe className="h-3 w-3 text-gray-500" />
                  <span className="text-gray-600 dark:text-gray-400">{companySettings.website || 'www.bitstech.com'}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bill To Section */}
      <div className="mb-2">
        <div className="bg-orange-500 text-white px-3 py-1 inline-block rounded text-xs font-semibold mb-2">
          {language === 'en' ? 'BILL TO' : 'ငွေတောင်းခံရန်'}
        </div>
        <div className="text-xs space-y-0.5">
          <p className="font-semibold text-gray-900 dark:text-white">{invoiceData?.customer.name}</p>
          <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.address}</p>
          <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.phone}</p>
        </div>
      </div>

      {/* Payment Information Section */}
      <div className="mb-2">
        <div className="bg-gray-800 dark:bg-gray-200 text-white dark:text-gray-900 px-3 py-1 inline-block rounded text-xs font-semibold mb-2">
          {language === 'en' ? 'PAYMENT INFORMATION' : 'ငွေပေးချေမှုအချက်အလက်'}
        </div>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.paymentMethod || 'No Payment Method'}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Transaction Date:' : 'ငွေပေးချေသည့်ရက်:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Currency:' : 'ငွေကြေး:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.currency || currentCurrency}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Status:' : 'အခြေအနေ:'}</span>
            <p className="font-bold text-green-600">{language === 'en' ? 'PAID' : 'ပေးချေပြီး'}</p>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-4">
        <table className="w-full text-xs">
          <thead>
            <tr className="border-b-2 border-gray-300 dark:border-gray-600">
              <th className="px-2 py-2 text-left font-semibold text-gray-700 dark:text-gray-300">#</th>
              <th className="px-2 py-2 text-left font-semibold text-gray-700 dark:text-gray-300">{language === 'en' ? 'DESCRIPTION' : 'ဖော်ပြချက်'}</th>
              <th className="px-2 py-2 text-center font-semibold text-gray-700 dark:text-gray-300">{language === 'en' ? 'RATE' : 'စျေးနှုန်း'}</th>
              <th className="px-2 py-2 text-center font-semibold text-gray-700 dark:text-gray-300">{language === 'en' ? 'QTY' : 'အရေအတွက်'}</th>
              <th className="px-2 py-2 text-right font-semibold text-gray-700 dark:text-gray-300">{language === 'en' ? 'AMOUNT' : 'စုစုပေါင်း'}</th>
            </tr>
          </thead>
          <tbody>
            {invoiceData?.items && invoiceData.items.length > 0 ? (
              invoiceData.items.map((item, index) => (
                <tr key={index} className="border-b border-gray-200 dark:border-gray-600">
                  <td className="px-2 py-2 text-center text-gray-900 dark:text-white">{index + 1}</td>
                  <td className="px-2 py-2">
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">{item.product.name}</p>
                    </div>
                  </td>
                  <td className="px-2 py-2 text-center text-gray-900 dark:text-white">{formatCurrency(item.product.price)}</td>
                  <td className="px-2 py-2 text-center text-gray-900 dark:text-white">{item.quantity}</td>
                  <td className="px-2 py-2 text-right font-semibold text-gray-900 dark:text-white">{formatCurrency(item.totalPrice)}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                  <div className="flex flex-col items-center space-y-2">
                    <ShoppingCart className="w-8 h-8 text-gray-400" />
                    <p className="text-sm font-medium">
                      {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                    </p>
                    <p className="text-xs text-gray-400">
                      {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Summary Section */}
      <div className="flex justify-end mb-6">
        <div className="w-64 space-y-1 text-xs">
          <div className="flex justify-between py-1">
            <span className="text-gray-700 dark:text-gray-300">{language === 'en' ? 'Subtotal' : 'စုစုပေါင်း'}</span>
            <span className="text-gray-900 dark:text-white">{formatCurrency(invoiceData?.subtotal || 0)}</span>
          </div>
          <div className="flex justify-between py-1">
            <span className="text-gray-700 dark:text-gray-300">{language === 'en' ? 'Discount' : 'လျှော့စျေး'}</span>
            <span className="text-gray-900 dark:text-white">{formatCurrency(invoiceData?.discount || 0)}</span>
          </div>
          <div className="flex justify-between py-1">
            <span className="text-gray-700 dark:text-gray-300">{language === 'en' ? 'Tax' : 'အခွန်'}</span>
            <span className="text-gray-900 dark:text-white">{formatCurrency(invoiceData?.tax || 0)}</span>
          </div>
          <div className="flex justify-between py-1">
            <span className="text-gray-700 dark:text-gray-300">{language === 'en' ? 'Shipping' : 'ပို့ဆောင်ခ'}</span>
            <span className="text-gray-900 dark:text-white">{formatCurrency(invoiceData?.shipping || 0)}</span>
          </div>
          <div className="border-t border-gray-300 dark:border-gray-600 pt-2">
            <div className="flex justify-between items-center">
              <span className="font-bold text-gray-900 dark:text-white">{language === 'en' ? 'Total' : 'စုစုပေါင်း'}</span>
              <span className="text-lg font-bold text-gray-900 dark:text-white">{formatCurrency(invoiceData?.total || 0)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Terms and Signature */}
      <div className="grid grid-cols-2 gap-6 mb-6">
        <div>
          <h4 className="font-bold text-gray-800 dark:text-white mb-2 text-xs">{language === 'en' ? 'Payment Terms' : 'ငွေပေးချေမှု စည်းကမ်းများ'}</h4>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-1">
            <p>• {language === 'en' ? 'Payment is due within 30 days' : 'ငွေပေးချေမှုသည် ၃၀ ရက်အတွင်း ပြုလုပ်ရမည်'}</p>
            <p>• {language === 'en' ? 'Late payments may incur additional fees' : 'နောက်ကျပါက အပိုအခကြေးငွေ ကောက်ခံနိုင်ပါသည်'}</p>
            <p>• {language === 'en' ? 'Please include invoice number with payment' : 'ငွေပေးချေရာတွင် ငွေတောင်းခံလွှာနံပါတ် ပါဝင်အောင် လုပ်ပါ'}</p>
          </div>

          <div className="mt-4">
            <h4 className="font-bold text-gray-800 dark:text-white mb-1 text-xs">{language === 'en' ? 'Notes' : 'မှတ်ချက်များ'}</h4>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {language === 'en'
                ? 'Thank you for your business. We appreciate your prompt payment.'
                : 'သင့်လုပ်ငန်းအတွက် ကျေးဇူးတင်ပါသည်။ အချိန်မီ ငွေပေးချေမှုအတွက် ကျေးဇူးတင်ပါသည်။'
              }
            </p>
          </div>
        </div>
        <div className="text-right">
          <div className="mt-8">
            <div className="border-b border-gray-300 dark:border-gray-600 mb-1 h-8"></div>
            <p className="text-xs font-semibold text-gray-900 dark:text-white">{language === 'en' ? 'Authorized Signature' : 'လက်မှတ်'}</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* Footer with Logo */}
      <div className="text-center mt-8">
        <div className="flex items-center justify-center space-x-2 mb-2">
          <div className="w-0 h-0 border-l-4 border-r-4 border-b-6 border-l-transparent border-r-transparent border-b-orange-500"></div>
          <span className="text-sm font-bold text-gray-800 dark:text-white">{companySettings.name}</span>
        </div>

      </div>
    </div>
    )
  }

  // Template 10 - Professional Orange (Based on provided image)
  const renderTemplate10 = () => {
    const templateCustom = getCurrentTemplateCustomization()
    return (
    <div style={{
      fontFamily: templateCustom.customFonts?.body || 'Arial',
      width: '100%',
      maxWidth: '850px',
      margin: '0 auto',
      padding: '25px',
      boxSizing: 'border-box'
    }}>
      {/* Header with decorative circles */}
      <div className="relative mb-2">
        {/* Decorative circles in top right */}
        <div className="absolute top-0 right-0 flex space-x-1">
          <div className="w-8 h-8 bg-gray-200 dark:bg-gray-600 rounded-full opacity-60"></div>
          <div className="w-6 h-6 bg-gray-300 dark:bg-gray-500 rounded-full opacity-70"></div>
          <div className="w-4 h-4 bg-gray-400 dark:bg-gray-400 rounded-full opacity-80"></div>
          <div className="w-3 h-3 bg-gray-500 dark:bg-gray-300 rounded-full opacity-90"></div>
          <div className="w-2 h-2 bg-gray-600 dark:bg-gray-200 rounded-full"></div>
        </div>

        <div className="pt-6">
          <h1
            className="text-lg font-bold mb-1"
            style={{
              color: templateCustom.customColors?.primary || '#f97316',
              fontFamily: templateCustom.customFonts?.heading || 'inherit'
            }}
          >
            {t.invoice.toUpperCase()}
          </h1>

          <div className="grid grid-cols-2 gap-2">
            <div className="space-y-1 text-xs">
              <div className="flex">
                <span className="font-semibold text-gray-700 dark:text-gray-300 w-20">{language === 'en' ? 'Invoice #:' : 'ငွေတောင်းခံလွှာနံပါတ်:'}</span>
                <span className="text-gray-900 dark:text-white">{invoiceData?.saleNumber}</span>
              </div>
              <div className="flex">
                <span className="font-semibold text-gray-700 dark:text-gray-300 w-20">{language === 'en' ? 'Date:' : 'ရက်စွဲ:'}</span>
                <span className="text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</span>
              </div>
              <div className="flex">
                <span className="font-semibold text-gray-700 dark:text-gray-300 w-20">{language === 'en' ? 'Due Date:' : 'ပေးရမည့်ရက်:'}</span>
                <span className="text-gray-900 dark:text-white">{new Date(Date.now() + 30*24*60*60*1000).toLocaleDateString()}</span>
              </div>
            </div>

            <div className="text-right space-y-0.5 text-xs">
              <p className="font-semibold text-gray-700 dark:text-gray-300">{language === 'en' ? 'From:' : 'မှ:'}</p>
              <div className="flex items-center justify-end space-x-2 mb-1">
                {templateCustom.companyLogo ? (
                  <img
                    src={templateCustom.companyLogo}
                    alt="Company Logo"
                    className="w-5 h-5 object-contain rounded bg-white p-1"
                  />
                ) : (
                  <div
                    className="w-5 h-5 rounded flex items-center justify-center"
                    style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
                  >
                    <span className="text-white font-bold text-xs">B</span>
                  </div>
                )}
                <p
                  className="text-xs font-semibold"
                  style={{
                    color: templateCustom.customColors?.primary || '#f97316',
                    fontFamily: templateCustom.customFonts?.heading || 'Arial'
                  }}
                >
                  {templateCustom.companyName || companySettings.name || 'BitsTech'}
                </p>
              </div>
              <div className="flex items-center justify-end space-x-1">
                <MapPin className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.address || 'No. 456, Technology Street, Yangon, Myanmar'}</span>
              </div>
              <div className="flex items-center justify-end space-x-1">
                <Phone className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.phone || '+95 9 ***********'}</span>
              </div>
              <div className="flex items-center justify-end space-x-1">
                <Mail className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.email || '<EMAIL>'}</span>
              </div>
              <div className="flex items-center justify-end space-x-1">
                <Globe className="h-3 w-3 text-gray-500" />
                <span className="text-gray-600 dark:text-gray-400">{companySettings.website || 'www.bitstech.com'}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Bill To Section with Orange Badge */}
      <div className="mb-2">
        <div
          className="text-white px-2 py-1 inline-block rounded text-xs font-bold mb-2"
          style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
        >
          {language === 'en' ? 'BILL TO' : 'ငွေတောင်းခံရန်'}
        </div>
        <div className="space-y-0.5 text-xs">
          <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.customer.name}</p>
          <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.address}</p>
          <p className="text-gray-600 dark:text-gray-400">{invoiceData?.customer.phone}</p>
        </div>
      </div>

      {/* Payment Information Section */}
      <div className="mb-2">
        <div
          className="text-white px-2 py-1 inline-block rounded text-xs font-bold mb-2"
          style={{ backgroundColor: templateCustom.customColors?.primary || '#f97316' }}
        >
          {language === 'en' ? 'PAYMENT INFORMATION' : 'ငွေပေးချေမှုအချက်အလက်'}
        </div>
        <div className="grid grid-cols-2 gap-4 text-xs">
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Payment Method:' : 'ငွေပေးချေမှုနည်းလမ်း:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.paymentMethod || 'No Payment Method'}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Transaction Date:' : 'ငွေပေးချေသည့်ရက်:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Currency:' : 'ငွေကြေး:'}</span>
            <p className="font-bold text-gray-900 dark:text-white">{invoiceData?.currency || currentCurrency}</p>
          </div>
          <div>
            <span className="text-gray-600 dark:text-gray-400">{language === 'en' ? 'Status:' : 'အခြေအနေ:'}</span>
            <p className="font-bold text-green-600">{language === 'en' ? 'PAID' : 'ပေးချေပြီး'}</p>
          </div>
        </div>
      </div>

      {/* Items Table */}
      <div className="mb-2">
        <table className="w-full text-xs">
          <thead>
            <tr className="border-b border-gray-400 dark:border-gray-500">
              <th className="px-2 py-1.5 text-left font-bold text-gray-800 dark:text-white">#</th>
              <th className="px-2 py-1.5 text-left font-bold text-gray-800 dark:text-white">{language === 'en' ? 'DESCRIPTION' : 'ဖော်ပြချက်'}</th>
              <th className="px-2 py-1.5 text-center font-bold text-gray-800 dark:text-white">{language === 'en' ? 'RATE' : 'စျေးနှုန်း'}</th>
              <th className="px-2 py-1.5 text-center font-bold text-gray-800 dark:text-white">{language === 'en' ? 'QTY' : 'အရေအတွက်'}</th>
              <th className="px-2 py-1.5 text-right font-bold text-gray-800 dark:text-white">{language === 'en' ? 'AMOUNT' : 'စုစုပေါင်း'}</th>
            </tr>
          </thead>
          <tbody>
            {invoiceData?.items && invoiceData.items.length > 0 ? (
              invoiceData.items.map((item, index) => (
                <tr key={index} className="border-b border-gray-200 dark:border-gray-600">
                  <td className="px-2 py-1.5 text-center text-gray-900 dark:text-white">{index + 1}</td>
                  <td className="px-2 py-1.5">
                    <div>
                      <p className="font-semibold text-gray-900 dark:text-white">{item.product.name}</p>
                    </div>
                  </td>
                  <td className="px-2 py-1.5 text-center text-gray-900 dark:text-white">{formatCurrency(item.product.price)}</td>
                  <td className="px-2 py-1.5 text-center text-gray-900 dark:text-white">{item.quantity}</td>
                  <td className="px-2 py-1.5 text-right font-semibold text-gray-900 dark:text-white">{formatCurrency(item.totalPrice)}</td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={5} className="px-4 py-8 text-center text-gray-500 dark:text-gray-400">
                  <div className="flex flex-col items-center space-y-2">
                    <ShoppingCart className="w-8 h-8 text-gray-400" />
                    <p className="text-sm font-medium">
                      {language === 'en' ? 'No items in this invoice' : 'ဤငွေတောင်းခံလွှာတွင် ပစ္စည်းများမရှိပါ'}
                    </p>
                    <p className="text-xs text-gray-400">
                      {language === 'en' ? 'Complete a POS checkout to see real invoice data' : 'POS checkout ပြီးမှ အစစ်အမှန် invoice data ကို မြင်ရမည်'}
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

      {/* Summary Section */}
      <div className="flex justify-end mb-2">
        <div className="w-48 space-y-1 text-xs">
          <div className="flex justify-between py-0.5">
            <span className="text-gray-700 dark:text-gray-300">{language === 'en' ? 'Subtotal' : 'စုစုပေါင်း'}</span>
            <span className="text-gray-900 dark:text-white">{formatCurrency(invoiceData?.subtotal || 0)}</span>
          </div>
          <div className="flex justify-between py-0.5">
            <span className="text-gray-700 dark:text-gray-300">{language === 'en' ? 'Discount' : 'လျှော့စျေး'}</span>
            <span className="text-gray-900 dark:text-white">{formatCurrency(invoiceData?.discount || 0)}</span>
          </div>
          <div className="flex justify-between py-0.5">
            <span className="text-gray-700 dark:text-gray-300">{language === 'en' ? 'Tax' : 'အခွန်'}</span>
            <span className="text-gray-900 dark:text-white">{formatCurrency(invoiceData?.tax || 0)}</span>
          </div>
          <div className="flex justify-between py-0.5">
            <span className="text-gray-700 dark:text-gray-300">{language === 'en' ? 'Shipping' : 'ပို့ဆောင်ခ'}</span>
            <span className="text-gray-900 dark:text-white">{formatCurrency(invoiceData?.shipping || 0)}</span>
          </div>
          <div className="border-t border-gray-400 dark:border-gray-500 pt-1 mt-1">
            <div className="flex justify-between items-center">
              <span className="font-bold text-sm text-gray-900 dark:text-white">{language === 'en' ? 'Total' : 'စုစုပေါင်း'}</span>
              <span className="text-sm font-bold text-gray-900 dark:text-white">{formatCurrency(invoiceData?.total || 0)}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Payment Terms and Signature */}
      <div className="grid grid-cols-2 gap-2 mb-2">
        <div>
          <h4 className="font-bold text-gray-800 dark:text-white mb-2 text-xs">{language === 'en' ? 'Terms & Conditions' : 'စည်းမျဉ်းစည်းကမ်းများ'}</h4>
          <div className="text-xs text-gray-600 dark:text-gray-400 space-y-0.5">
            <p>• {language === 'en' ? 'Payment is due within 30 days of invoice date' : 'ငွေပေးချေမှုသည် ၃၀ ရက်အတွင်း ပြုလုပ်ရမည်'}</p>
            <p>• {language === 'en' ? 'Late payments may incur additional fees' : 'နောက်ကျပါက အပိုအခကြေးငွေ ကောက်ခံနိုင်ပါသည်'}</p>
            <p>• {language === 'en' ? 'All sales are final unless items are defective' : 'ပစ္စည်းများ ပြန်လည်မပေးအပ်နိုင်ပါ'}</p>
            <p>• {language === 'en' ? 'Bank transfer preferred for large amounts' : 'ငွေပမာဏ များအတွက် ဘဏ်လွှဲပေးမှု ပိုကောင်းပါသည်'}</p>
          </div>

          <div className="mt-3">
            <h4 className="font-bold text-gray-800 dark:text-white mb-1 text-xs">{language === 'en' ? 'Notes' : 'မှတ်ချက်များ'}</h4>
            <p className="text-xs text-gray-600 dark:text-gray-400">
              {language === 'en'
                ? 'Thank you for choosing BitsTech. We appreciate your business and look forward to serving you again.'
                : 'BitsTech ကို ရွေးချယ်ပေးတဲ့အတွက် ကျေးဇူးတင်ပါတယ်။ သင့်လုပ်ငန်းအတွက် ကျေးဇူးတင်ပြီး နောက်တစ်ကြိမ် ဝန်ဆောင်မှုပေးရန် မျှော်လင့်ပါသည်။'
              }
            </p>
          </div>
        </div>

        <div className="text-right">
          <div className="mt-6">
            <div className="border-b border-gray-400 dark:border-gray-500 mb-1 h-8"></div>
            <p className="text-xs font-bold text-gray-900 dark:text-white">{language === 'en' ? 'Authorized Signature' : 'လက်မှတ်'}</p>
            <p className="text-xs text-gray-600 dark:text-gray-400">{invoiceData?.timestamp.toLocaleDateString()}</p>
          </div>
        </div>
      </div>

      {/* Footer with Company Logo */}
      <div className="text-center mt-2">
        <div className="flex items-center justify-center space-x-2 mb-2">
          <div
            className="w-0 h-0 border-l-4 border-r-4 border-b-6 border-l-transparent border-r-transparent"
            style={{ borderBottomColor: templateCustom.customColors?.primary || '#f97316' }}
          ></div>
          <span
            className="text-sm font-bold"
            style={{
              color: templateCustom.customColors?.primary || '#f97316',
              fontFamily: templateCustom.customFonts?.heading || 'inherit'
            }}
          >
            {templateCustom.companyName || companySettings.name}
          </span>
        </div>

      </div>
    </div>
    )
  }



  if (!invoiceData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 dark:from-gray-900 dark:via-slate-900 dark:to-gray-800 flex items-center justify-center">
        <div className="text-center space-y-6">
          <div className="text-red-500 text-6xl">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            {language === 'en' ? 'Invoice Not Found' : 'ငွေတောင်းခံလွှာ မတွေ့ပါ'}
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            {language === 'en' ? 'The requested invoice could not be loaded' : 'တောင်းဆိုထားသော ငွေတောင်းခံလွှာကို ဖွင့်၍မရပါ'}
          </p>
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
          >
            {language === 'en' ? 'Reload Page' : 'စာမျက်နှာ ပြန်ဖွင့်ရန်'}
          </button>
        </div>
      </div>
    )
  }

  if (!invoiceData) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Receipt className="h-16 w-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
            No Invoice Found
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            No invoice data available
          </p>
          <Button onClick={() => router.push('/pos')} className="bg-blue-600 hover:bg-blue-700">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to POS
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 print:bg-white">
      {/* Premium Navigation Header */}
      <header className="bg-white/95 dark:bg-gray-900/95 backdrop-blur-sm shadow-lg border-b border-gray-200 dark:border-gray-700 print:hidden sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            {/* Left Section */}
            <div className="flex items-center space-x-6">
              {/* Dashboard Button */}
              <Button
                variant="outline"
                onClick={() => router.push('/dashboard')}
                className="flex items-center space-x-2 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300 shadow-md hover:shadow-lg"
              >
                <ArrowLeft className="h-4 w-4" />
                <span className="font-medium">Dashboard</span>
              </Button>

              {/* POS Button */}
              <Button
                variant="outline"
                onClick={() => router.push('/pos')}
                className="flex items-center space-x-2 bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 text-white border-2 border-green-500 hover:border-green-600 transition-all duration-300 shadow-md hover:shadow-lg"
              >
                <ShoppingCart className="h-4 w-4" />
                <span className="font-medium">{language === 'en' ? 'POS System' : 'POS စနစ်'}</span>
              </Button>

              <div className="flex items-center space-x-3">
                <div className="p-2 bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl shadow-lg">
                  <Receipt className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="flex items-center space-x-2">
                    <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                      Premium Invoice
                    </h1>
                    {isRealTimeData && (
                      <div className="flex items-center space-x-1 px-2 py-1 bg-green-100 dark:bg-green-900/20 rounded-full border border-green-300 dark:border-green-700">
                        <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                        <span className="text-xs font-medium text-green-700 dark:text-green-300">
                          {language === 'en' ? 'Real Data' : 'အစစ်အမှန် ဒေတာ'}
                        </span>
                      </div>
                    )}
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {isRealTimeData
                      ? (language === 'en' ? 'Live POS Transaction Data' : 'POS မှ တিုက်ရိုက် ဒေတာ')
                      : 'Professional Invoice Generator'
                    }
                  </p>

                </div>
              </div>
            </div>

            {/* Right Section - Controls */}
            <div className="flex items-center space-x-4">
              {/* Theme Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={toggleTheme}
                className="p-2 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 hover:border-blue-500 dark:hover:border-blue-400 transition-all duration-300"
                title="Toggle Theme"
              >
                {actualTheme === 'dark' ? (
                  <Sun className="h-4 w-4 text-yellow-500" />
                ) : (
                  <Moon className="h-4 w-4 text-blue-600" />
                )}
              </Button>

              {/* Language Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={toggleLanguage}
                className="p-2 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 hover:border-green-500 dark:hover:border-green-400 transition-all duration-300"
                title="Toggle Language"
              >
                <Globe className="h-4 w-4 text-green-600 mr-1" />
                <span className="text-xs font-medium text-green-600">
                  {language === 'en' ? 'EN' : 'မြန်မာ'}
                </span>
              </Button>

              {/* Currency Toggle */}
              <Button
                variant="outline"
                size="sm"
                onClick={() => {
                  const currencies = ['USD', 'MMK', 'THB']
                  const currentIndex = currencies.indexOf(currentCurrency)
                  const nextIndex = (currentIndex + 1) % currencies.length
                  setCurrency(currencies[nextIndex] as any)
                  console.log('💰 Currency changed to:', currencies[nextIndex])
                }}
                className="p-2 bg-white dark:bg-gray-800 border-2 border-gray-300 dark:border-gray-600 hover:border-purple-500 dark:hover:border-purple-400 transition-all duration-300"
                title="Toggle Currency"
              >
                <DollarSign className="h-4 w-4 text-purple-600" />
              </Button>

              {/* Current Currency Display */}
              <div className="px-3 py-2 bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-900/20 dark:to-purple-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
                <span className="text-sm font-medium text-blue-700 dark:text-blue-300 flex items-center gap-1">
                  {currentCurrencyInfo?.flag && <span>{currentCurrencyInfo.flag}</span>}
                  {currentCurrency}
                </span>
              </div>

              {/* Print Button */}
              <Button
                onClick={() => {
                  // Add comprehensive print styles to current document
                  const printStyleSheet = document.createElement('style')
                  printStyleSheet.id = 'enhanced-print-styles'
                  printStyleSheet.textContent = `
                    @media print {
                      @page {
                        size: A4 portrait;
                        margin: 10mm;
                        -webkit-print-color-adjust: exact;
                        color-adjust: exact;
                        print-color-adjust: exact;
                      }

                      * {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                      }

                      html, body {
                        width: 210mm !important;
                        background: white !important;
                        color: black !important;
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                      }

                      /* Force light mode for all elements */
                      .dark, .dark *,
                      .dark\\:bg-gray-800, .dark\\:bg-gray-900,
                      .dark\\:text-white, .dark\\:text-gray-300, .dark\\:text-gray-400,
                      .dark\\:border-gray-700 {
                        background-color: white !important;
                        color: black !important;
                        border-color: #e5e7eb !important;
                      }

                      /* Preserve template colors */
                      .bg-gradient-to-r, .bg-gradient-to-br, .bg-gradient-to-l {
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                      }

                      /* Hide print controls */
                      .print\\:hidden { display: none !important; }

                      /* Invoice container sizing */
                      .invoice-container {
                        width: 190mm !important;
                        max-width: 190mm !important;
                        margin: 0 auto !important;
                        padding: 0 !important;
                        background: white !important;
                      }
                    }
                  `
                  document.head.appendChild(printStyleSheet)

                  // Store current theme state
                  const isDarkMode = document.documentElement.classList.contains('dark')
                  const bodyHasDark = document.body.classList.contains('dark')

                  // Temporarily remove dark mode class for printing
                  if (isDarkMode) {
                    document.documentElement.classList.remove('dark')
                  }
                  if (bodyHasDark) {
                    document.body.classList.remove('dark')
                  }

                  // Print with delay to ensure styles are applied
                  setTimeout(() => {
                    window.print()
                  }, 200)

                  // Listen for print dialog events to restore theme
                  const restoreTheme = () => {
                    setTimeout(() => {
                      if (isDarkMode) {
                        document.documentElement.classList.add('dark')
                      }
                      if (bodyHasDark) {
                        document.body.classList.add('dark')
                      }
                      const styleElement = document.getElementById('enhanced-print-styles')
                      if (styleElement && document.head.contains(styleElement)) {
                        document.head.removeChild(styleElement)
                      }
                    }, 500)
                  }

                  // Multiple event listeners to catch print dialog close
                  window.addEventListener('afterprint', restoreTheme, { once: true })

                  // Fallback timeout in case events don't fire
                  setTimeout(restoreTheme, 3000)
                }}
                className="p-2 bg-blue-600 hover:bg-blue-700 text-white border-2 border-blue-600 hover:border-blue-700 transition-all duration-300"
                title={t.print}
              >
                <Printer className="h-4 w-4" />
              </Button>

              {/* Download PDF Button */}
              <Button
                onClick={handleDownload}
                className="p-2 bg-green-600 hover:bg-green-700 text-white border-2 border-green-600 hover:border-green-700 transition-all duration-300"
                title={t.download}
              >
                <Download className="h-4 w-4" />
              </Button>

              {/* Email Button */}
              <Button
                onClick={() => {
                  const subject = `${t.invoice} ${invoiceData?.saleNumber}`
                  const body = `${language === 'en' ? 'Please find attached invoice' : 'ငွေတောင်းခံလွှာ ပူးတွဲ ပါရှိပါသည်'} ${invoiceData?.saleNumber}`
                  const mailtoLink = `mailto:?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`
                  window.open(mailtoLink, '_blank')
                }}
                className="p-2 bg-purple-600 hover:bg-purple-700 text-white border-2 border-purple-600 hover:border-purple-700 transition-all duration-300"
                title={language === 'en' ? 'Send Email' : 'အီးမေးလ် ပို့မည်'}
              >
                <Send className="h-4 w-4" />
              </Button>

              {/* Save Button */}
              <Button
                onClick={() => {
                  try {
                    if (!invoiceData) {
                      alert(language === 'en' ? 'No invoice data to save' : 'သိမ်းဆည်းရန် ငွေတောင်းခံလွှာ အချက်အလက် မရှိပါ')
                      return
                    }

                    const invoiceHistory = JSON.parse(localStorage.getItem('invoiceHistory') || '[]')
                    const newInvoice = {
                      ...invoiceData,
                      template: selectedTemplate,
                      savedAt: new Date().toISOString(),
                      isRealTime: checkIsRealTimeData(invoiceData)
                    }

                    // Check if invoice already exists
                    const existingIndex = invoiceHistory.findIndex((item: any) =>
                      item.saleNumber === newInvoice.saleNumber
                    )

                    if (existingIndex >= 0) {
                      // Update existing invoice
                      invoiceHistory[existingIndex] = newInvoice
                      console.log('📝 Invoice updated in history:', newInvoice.saleNumber)
                    } else {
                      // Add new invoice
                      invoiceHistory.push(newInvoice)
                      console.log('💾 Invoice saved to history:', newInvoice.saleNumber)
                    }

                    localStorage.setItem('invoiceHistory', JSON.stringify(invoiceHistory))
                    setHistoryRefresh(prev => prev + 1) // Trigger history refresh

                    alert(language === 'en' ? 'Invoice saved successfully!' : 'ငွေတောင်းခံလွှာ သိမ်းဆည်းပြီးပါပြီ!')
                  } catch (error) {
                    console.warn('Failed to save invoice:', error)
                    alert(language === 'en' ? 'Failed to save invoice. Storage may be full.' : 'ငွေတောင်းခံလွှာ သိမ်းဆည်း၍မရပါ။ Storage ပြည့်နေနိုင်ပါသည်။')
                  }
                }}
                className="p-2 bg-orange-600 hover:bg-orange-700 text-white border-2 border-orange-600 hover:border-orange-700 transition-all duration-300"
                title={language === 'en' ? 'Save Invoice' : 'ငွေတောင်းခံလွှာ သိမ်းဆည်းမည်'}
              >
                <Save className="h-4 w-4" />
              </Button>

              {/* Excel Export Button */}
              <Button
                onClick={exportToExcel}
                className="p-2 bg-emerald-600 hover:bg-emerald-700 text-white border-2 border-emerald-600 hover:border-emerald-700 transition-all duration-300"
                title={language === 'en' ? 'Export to Excel' : 'Excel သို့ ထုတ်ယူမည်'}
              >
                <FileSpreadsheet className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content with Sidebar Layout */}
      <div className="flex max-w-7xl mx-auto p-6 gap-6">
        {/* Left Sidebar - Template Selector */}
        <div className="w-80 print:hidden">
          <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 sticky top-24">
            <div className="flex items-center space-x-2 mb-4">
              <div className="p-2 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg">
                <Palette className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-bold text-gray-900 dark:text-white">
                  {t.templates}
                </h3>
                <p className="text-xs text-gray-500 dark:text-gray-400">
                  {t.chooseDesigns}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-2">
              {[
                { id: 1, name: 'Premium Orange', color: 'orange', desc: 'Professional', icon: Crown },
                { id: 2, name: 'Corporate Blue', color: 'blue', desc: 'Corporate', icon: Briefcase },
                { id: 3, name: 'Luxury Blue', color: 'blue', desc: 'Executive', icon: Award },
                { id: 4, name: 'Creative Colorful', color: 'purple', desc: 'Creative', icon: Zap },
                { id: 5, name: 'Business Gray', color: 'gray', desc: 'Minimal', icon: Shield },
                { id: 6, name: 'Professional Green', color: 'green', desc: 'Business', icon: Diamond },
                { id: 7, name: 'Modern Blue', color: 'blue', desc: 'Corporate', icon: Building },
                { id: 8, name: 'Orange Pro', color: 'orange', desc: 'Professional', icon: FileText },
                { id: 9, name: 'Clean Modern', color: 'gray', desc: 'Minimal', icon: Layers },
                { id: 10, name: 'Professional Orange', color: 'orange', desc: 'Premium', icon: Star }
              ].map((template) => {
                const IconComponent = template.icon
                return (
                  <button
                    key={template.id}
                    type="button"
                    onClick={() => setSelectedTemplate(template.id)}
                    className={`group relative p-1.5 rounded-md border-2 text-left transition-all duration-200 hover:scale-105 h-20 w-full ${
                      selectedTemplate === template.id
                        ? `border-${template.color === 'yellow' ? 'yellow' : template.color === 'orange' ? 'orange' : template.color}-500 bg-gradient-to-br from-${template.color === 'yellow' ? 'yellow' : template.color === 'orange' ? 'orange' : template.color}-50 to-${template.color === 'yellow' ? 'yellow' : template.color === 'orange' ? 'orange' : template.color}-100 dark:from-${template.color === 'yellow' ? 'yellow' : template.color === 'orange' ? 'orange' : template.color}-900/20 dark:to-${template.color === 'yellow' ? 'yellow' : template.color === 'orange' ? 'orange' : template.color}-800/20 shadow-md`
                        : 'border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 hover:border-gray-400 dark:hover:border-gray-500 shadow-sm hover:shadow-md'
                    }`}
                  >
                    <div className={`w-full h-6 rounded mb-1 bg-gradient-to-r ${
                      template.color === 'red' ? 'from-red-400 to-red-600' :
                      template.color === 'blue' ? 'from-blue-400 to-blue-600' :
                      template.color === 'yellow' ? 'from-yellow-400 to-yellow-600' :
                      template.color === 'purple' ? 'from-purple-400 to-purple-600' :
                      template.color === 'green' ? 'from-green-400 to-green-600' :
                      template.color === 'orange' ? 'from-orange-400 to-orange-600' :
                      'from-gray-400 to-gray-600'
                    } flex items-center justify-center`}>
                      <IconComponent className="h-3 w-3 text-white" />
                    </div>

                    <h4 className={`font-medium text-xs mb-0.5 leading-tight ${
                      selectedTemplate === template.id
                        ? `text-${template.color === 'yellow' ? 'yellow' : template.color === 'orange' ? 'orange' : template.color}-700 dark:text-${template.color === 'yellow' ? 'yellow' : template.color === 'orange' ? 'orange' : template.color}-300`
                        : 'text-gray-900 dark:text-white'
                    }`}>
                      {template.name}
                    </h4>

                    <p className="text-xs text-gray-500 dark:text-gray-400 leading-tight">
                      {template.desc}
                    </p>

                    {selectedTemplate === template.id && (
                      <div className={`absolute -top-1 -right-1 w-4 h-4 bg-${template.color === 'yellow' ? 'yellow' : template.color === 'orange' ? 'orange' : template.color}-500 rounded-full flex items-center justify-center shadow-md`}>
                        <span className="text-white text-xs">✓</span>
                      </div>
                    )}
                  </button>
                )
              })}
            </div>

            {/* Customization Panel */}
            <div className="mt-6 p-4 bg-gradient-to-br from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg border border-purple-200 dark:border-purple-700">
              <h4 className="text-sm font-bold text-purple-700 dark:text-purple-300 mb-3 flex items-center">
                <PaletteIcon className="h-4 w-4 mr-1" />
                {language === 'en' ? 'Customization' : 'စိတ်ကြိုက်ပြင်ဆင်မှု'}
              </h4>

              <div className="space-y-3">
                {/* QR Code Toggle */}
                <div className="flex items-center justify-between">
                  <span className="text-xs text-gray-700 dark:text-gray-300">
                    {language === 'en' ? 'Show QR Code' : 'QR Code ပြသမည်'}
                  </span>
                  <button
                    type="button"
                    onClick={() => setCustomization(prev => ({ ...prev, showQRCode: !prev.showQRCode }))}
                    className={`w-8 h-4 rounded-full transition-colors ${
                      customization.showQRCode ? 'bg-purple-600' : 'bg-gray-300'
                    }`}
                    title={language === 'en' ? 'Toggle QR Code' : 'QR Code ပြသမှု ပြောင်းလဲမည်'}
                    aria-label={language === 'en' ? 'Toggle QR Code display' : 'QR Code ပြသမှု ပြောင်းလဲမည်'}
                  >
                    <div className={`w-3 h-3 bg-white rounded-full transition-transform ${
                      customization.showQRCode ? 'translate-x-4' : 'translate-x-0.5'
                    }`} />
                  </button>
                </div>

                {/* QR Code Data Input */}
                {customization.showQRCode && (
                  <div>
                    <label className="text-xs text-gray-700 dark:text-gray-300 block mb-1">
                      {language === 'en' ? 'QR Code Content' : 'QR Code အကြောင်းအရာ'}
                    </label>
                    <div className="space-y-2">
                      <input
                        type="text"
                        value={customization.qrCodeData || ''}
                        onChange={(e) => setCustomization(prev => ({ ...prev, qrCodeData: e.target.value }))}
                        className="w-full text-xs p-2 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
                        placeholder={language === 'en' ? 'Enter QR code content (e.g., website URL, contact info, etc.)' : 'QR code အကြောင်းအရာ ရိုက်ထည့်ပါ (ဥပမာ: website URL, ဆက်သွယ်ရန်အချက်အလက်)'}
                      />
                      <div className="text-xs text-gray-500 dark:text-gray-400">
                        <p className="mb-1">{language === 'en' ? 'Examples:' : 'ဥပမာများ:'}</p>
                        <div className="space-y-1 text-xs">
                          <button
                            type="button"
                            onClick={() => setCustomization(prev => ({ ...prev, qrCodeData: `https://${companySettings.website}` }))}
                            className="block w-full text-left p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            • Website: https://{companySettings.website}
                          </button>
                          <button
                            type="button"
                            onClick={() => setCustomization(prev => ({ ...prev, qrCodeData: `mailto:${companySettings.email}` }))}
                            className="block w-full text-left p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            • Email: mailto:{companySettings.email}
                          </button>
                          <button
                            type="button"
                            onClick={() => setCustomization(prev => ({ ...prev, qrCodeData: `tel:${companySettings.phone.replace(/\s/g, '')}` }))}
                            className="block w-full text-left p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            • Phone: tel:{companySettings.phone}
                          </button>
                          <button
                            type="button"
                            onClick={() => setCustomization(prev => ({ ...prev, qrCodeData: 'https://www.facebook.com/bitstech.myanmar' }))}
                            className="block w-full text-left p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            • Facebook: https://www.facebook.com/bitstech.myanmar
                          </button>
                          <button
                            type="button"
                            onClick={() => setCustomization(prev => ({ ...prev, qrCodeData: 'https://m.me/bitstech.myanmar' }))}
                            className="block w-full text-left p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            • Messenger: https://m.me/bitstech.myanmar
                          </button>
                          <button
                            type="button"
                            onClick={() => setCustomization(prev => ({ ...prev, qrCodeData: 'https://www.instagram.com/bitstech.myanmar' }))}
                            className="block w-full text-left p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            • Instagram: https://www.instagram.com/bitstech.myanmar
                          </button>
                          <button
                            type="button"
                            onClick={() => setCustomization(prev => ({
                              ...prev,
                              qrCodeData: `Invoice: ${invoiceData?.saleNumber}\nCompany: ${customization.companyName}\nTotal: ${formatCurrency(invoiceData?.total || 0)}\nDate: ${invoiceData?.timestamp.toLocaleDateString()}\nPayment: ${invoiceData?.paymentMethod}\nCustomer: ${invoiceData?.customer.name}${isRealTimeData ? '\n✓ Real-time Data' : '\n⚠ Sample Data'}`
                            }))}
                            className="block w-full text-left p-1 rounded hover:bg-gray-100 dark:hover:bg-gray-700"
                          >
                            • {language === 'en' ? 'Invoice Details (Enhanced)' : 'ငွေတောင်းခံလွှာ အသေးစိတ် (အပြည့်အစုံ)'}
                          </button>
                        </div>
                      </div>

                      {/* QR Code Preview */}
                      {customization.qrCodeData && (
                        <div className="mt-3 p-3 bg-gray-50 dark:bg-gray-700 rounded border">
                          <div className="flex items-center space-x-3">
                            <img
                              src={generateQRCode(customization.qrCodeData)}
                              alt="QR Code Preview"
                              className="w-16 h-16 object-contain rounded border bg-white"
                            />
                            <div className="flex-1">
                              <p className="text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {language === 'en' ? 'QR Code Preview' : 'QR Code နမူနာ'}
                              </p>
                              <p className="text-xs text-gray-500 dark:text-gray-400 break-all">
                                {customization.qrCodeData.length > 50
                                  ? `${customization.qrCodeData.substring(0, 50)}...`
                                  : customization.qrCodeData
                                }
                              </p>
                              <p className="text-xs text-green-600 dark:text-green-400 mt-1">
                                ✓ {language === 'en' ? 'Scannable QR Code' : 'ဖတ်ရှုနိုင်သော QR Code'}
                              </p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {/* Color Picker */}
                <div>
                  <label className="text-xs text-gray-700 dark:text-gray-300 block mb-1">
                    {language === 'en' ? 'Primary Color' : 'အဓိက အရောင်'}
                  </label>
                  <input
                    type="color"
                    value={getCurrentTemplateCustomization().customColors?.primary || '#f97316'}
                    onChange={(e) => updateTemplateCustomization({
                      customColors: {
                        ...getCurrentTemplateCustomization().customColors,
                        primary: e.target.value
                      }
                    })}
                    className="w-full h-6 rounded border border-gray-300 dark:border-gray-600"
                    title={language === 'en' ? `Template ${selectedTemplate} Primary Color` : `Template ${selectedTemplate} အဓိက အရောင်`}
                    aria-label={language === 'en' ? 'Primary color picker' : 'အဓိက အရောင် ရွေးချယ်ရန်'}
                  />
                </div>

                {/* Company Name */}
                <div>
                  <label className="text-xs text-gray-700 dark:text-gray-300 block mb-1">
                    {language === 'en' ? 'Company Name' : 'ကုမ္ပဏီအမည်'}
                  </label>
                  <input
                    type="text"
                    value={getCurrentTemplateCustomization().companyName || customization.companyName || ''}
                    onChange={(e) => {
                      const companyName = e.target.value
                      // Update all templates with the same company name
                      const updatedCustomizations: {[key: number]: any} = {}
                      for (let i = 1; i <= 10; i++) {
                        updatedCustomizations[i] = {
                          ...templateCustomizations[i],
                          companyName: companyName
                        }
                      }
                      setTemplateCustomizations(updatedCustomizations)
                      // Also update the main customization
                      setCustomization(prev => ({ ...prev, companyName: companyName }))

                      // Save to localStorage with error handling
                      try {
                        localStorage.setItem('template-customizations', JSON.stringify(updatedCustomizations))
                      } catch (error) {
                        console.warn('Failed to save company name to localStorage:', error)
                      }
                    }}
                    className="w-full text-xs p-1 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
                    placeholder={language === 'en' ? 'Company name (applies to all templates)' : 'ကုမ္ပဏီအမည် (template အားလုံးတွင် အသုံးပြုမည်)'}
                  />
                </div>

                {/* Font Family */}
                <div>
                  <label className="text-xs text-gray-700 dark:text-gray-300 block mb-1">
                    {language === 'en' ? 'Font Family' : 'စာလုံးမျိုး'}
                  </label>
                  <select
                    value={getCurrentTemplateCustomization().customFonts?.heading || customization.customFonts?.heading || 'Arial'}
                    onChange={(e) => {
                      const fontFamily = e.target.value
                      const fontConfig = {
                        heading: fontFamily,
                        body: fontFamily
                      }
                      // Update all templates with the same font
                      const updatedCustomizations: {[key: number]: any} = {}
                      for (let i = 1; i <= 10; i++) {
                        updatedCustomizations[i] = {
                          ...templateCustomizations[i],
                          customFonts: fontConfig
                        }
                      }
                      setTemplateCustomizations(updatedCustomizations)
                      // Also update the main customization
                      setCustomization(prev => ({ ...prev, customFonts: fontConfig }))

                      // Save to localStorage with error handling
                      try {
                        localStorage.setItem('template-customizations', JSON.stringify(updatedCustomizations))
                      } catch (error) {
                        console.warn('Failed to save font family to localStorage:', error)
                      }
                    }}
                    className="w-full text-xs p-1 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
                    title={language === 'en' ? 'Font Family (applies to all templates)' : 'စာလုံးမျိုး (template အားလုံးတွင် အသုံးပြုမည်)'}
                  >
                    <option value="Arial">Arial</option>
                    <option value="Times New Roman">Times New Roman</option>
                    <option value="Helvetica">Helvetica</option>
                    <option value="Georgia">Georgia</option>
                  </select>
                </div>

                {/* Company Logo Upload */}
                <div>
                  <label className="text-xs text-gray-700 dark:text-gray-300 block mb-1">
                    {language === 'en' ? 'Company Logo' : 'ကုမ္ပဏီ လိုဂို'}
                  </label>
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => {
                      const file = e.target.files?.[0]
                      if (file) {
                        const reader = new FileReader()
                        reader.onload = (event) => {
                          const logoData = event.target?.result as string
                          // Update all templates with the same logo
                          const updatedCustomizations: {[key: number]: any} = {}
                          for (let i = 1; i <= 10; i++) {
                            updatedCustomizations[i] = {
                              ...templateCustomizations[i],
                              companyLogo: logoData
                            }
                          }
                          setTemplateCustomizations(updatedCustomizations)
                          // Also update the main customization
                          setCustomization(prev => ({ ...prev, companyLogo: logoData }))

                          // Save to localStorage with error handling
                          try {
                            localStorage.setItem('template-customizations', JSON.stringify(updatedCustomizations))
                            localStorage.setItem('invoice-customization', JSON.stringify({ ...customization, companyLogo: logoData }))
                          } catch (error) {
                            console.warn('Failed to save to localStorage:', error)
                            // Clear some old data if storage is full
                            localStorage.removeItem('invoiceHistory')
                            try {
                              localStorage.setItem('template-customizations', JSON.stringify(updatedCustomizations))
                            } catch (e) {
                              console.error('Storage quota exceeded even after cleanup')
                            }
                          }
                        }
                        reader.readAsDataURL(file)
                      }
                    }}
                    className="w-full text-xs p-1 rounded border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
                    title={language === 'en' ? 'Upload Company Logo (applies to all templates)' : 'ကုမ္ပဏီ လိုဂို တင်ပါ (template အားလုံးတွင် အသုံးပြုမည်)'}
                  />
                  {(getCurrentTemplateCustomization().companyLogo || customization.companyLogo) && (
                    <div className="mt-2 p-2 bg-gray-50 dark:bg-gray-700 rounded border">
                      <img
                        src={getCurrentTemplateCustomization().companyLogo || customization.companyLogo}
                        alt="Company Logo"
                        className="w-12 h-12 object-contain mx-auto rounded border bg-white"
                      />
                      <p className="text-xs text-center text-gray-600 dark:text-gray-400 mt-1">
                        {language === 'en' ? 'Logo Preview' : 'လိုဂို နမူနာ'}
                      </p>
                    </div>
                  )}
                </div>

                {/* Clear Customization Button */}
                <div className="mt-4">
                  <Button
                    onClick={() => {
                      // Clear all template customizations
                      setTemplateCustomizations({})
                      // Reset main customization to defaults
                      setCustomization({
                        companyLogo: null,
                        companyName: 'BitsTech',
                        customColors: {
                          primary: '#f97316',
                          secondary: '#1f2937',
                          accent: '#059669'
                        },
                        customFonts: {
                          heading: 'Arial',
                          body: 'Arial'
                        },
                        invoiceNumberFormat: 'INV-{number}',
                        showQRCode: false,
                        qrCodeData: 'Invoice: ',
                        customNotes: ''
                      })
                      // Clear localStorage
                      localStorage.removeItem('template-customizations')
                      localStorage.removeItem('invoice-customization')
                    }}
                    variant="outline"
                    size="sm"
                    className="w-full text-xs"
                  >
                    <History className="h-3 w-3 mr-1" />
                    {language === 'en' ? 'Clear All Customizations' : 'စိတ်ကြိုက်ပြင်ဆင်မှုများ ရှင်းလင်းမည်'}
                  </Button>
                </div>
              </div>
            </div>

            {/* Data Source Indicator */}
            <div className={`mt-6 p-3 rounded-lg border ${isRealTimeData
              ? 'bg-gradient-to-br from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-green-200 dark:border-green-700'
              : 'bg-gradient-to-br from-yellow-50 to-orange-50 dark:from-yellow-900/20 dark:to-orange-900/20 border-yellow-200 dark:border-yellow-700'
            }`}>
              <div className="flex items-center space-x-2">
                <div className={`w-2 h-2 rounded-full ${isRealTimeData ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                <span className={`text-xs font-medium ${isRealTimeData ? 'text-green-700 dark:text-green-300' : 'text-yellow-700 dark:text-yellow-300'}`}>
                  {isRealTimeData
                    ? (language === 'en' ? 'Real-Time Data' : 'တကယ့် အချက်အလက်')
                    : (language === 'en' ? 'Sample Data' : 'နမူနာ အချက်အလက်')
                  }
                </span>
              </div>
              <p className={`text-xs mt-1 ${isRealTimeData ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}`}>
                {isRealTimeData
                  ? (language === 'en' ? 'Connected to POS system' : 'POS စနစ်နဲ့ ချိတ်ဆက်ထားသည်')
                  : (language === 'en' ? 'Using demo data' : 'နမူနာ အချက်အလက် သုံးနေသည်')
                }
              </p>
            </div>

            {/* Invoice History Panel */}
            <div className="mt-6 p-4 bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700">
              <h4 className="text-sm font-bold text-blue-700 dark:text-blue-300 mb-3 flex items-center">
                <History className="h-4 w-4 mr-1" />
                {language === 'en' ? 'Invoice History' : 'ငွေတောင်းခံလွှာ မှတ်တမ်း'}
              </h4>

              <div className="space-y-2 max-h-32 overflow-y-auto">
                {invoiceHistory.slice(-5).map((invoice: any, index: number) => (
                    <div
                      key={index}
                      className="flex justify-between items-center p-2 bg-white dark:bg-gray-800 rounded border text-xs hover:bg-blue-50 dark:hover:bg-blue-900/20"
                    >
                      <div
                        className="flex-1 cursor-pointer"
                        onClick={() => {
                          setInvoiceData(invoice)
                          setSelectedTemplate(invoice.template || 1)
                          setIsRealTimeData(checkIsRealTimeData(invoice))
                          console.log('📋 Invoice loaded from history:', {
                            saleNumber: invoice.saleNumber,
                            isRealTime: checkIsRealTimeData(invoice),
                            source: invoice.isRealTime ? 'Database' : 'LocalStorage'
                          })
                        }}
                      >
                        <div className="flex justify-between items-center">
                          <div className="flex items-center space-x-2">
                            <span className="font-medium text-gray-900 dark:text-white">
                              {invoice.saleNumber}
                            </span>
                            {invoice.isRealTime && (
                              <span className="px-1 py-0.5 text-xs bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400 rounded">
                                {language === 'en' ? 'Live' : 'တိုက်ရိုက်'}
                              </span>
                            )}
                          </div>
                          <span className="text-gray-500 dark:text-gray-400">
                            {new Date(invoice.savedAt || invoice.timestamp).toLocaleDateString()}
                          </span>
                        </div>
                      </div>

                      {/* Delete Button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation()
                          if (confirm(language === 'en' ? 'Are you sure you want to delete this invoice?' : 'ဤငွေတောင်းခံလွှာကို ဖျက်မှာ သေချာပါသလား?')) {
                            try {
                              // Handle real-time data differently
                              if (invoice.isRealTime) {
                                alert(language === 'en'
                                  ? 'Cannot delete real-time database records. This will only remove from local history.'
                                  : 'Database မှ အချက်အလက်များကို မဖျက်နိုင်ပါ။ Local history မှသာ ဖယ်ရှားမည်။'
                                )
                              }

                              // Remove from local storage
                              const localHistory = JSON.parse(localStorage.getItem('invoiceHistory') || '[]')
                              const updatedLocalHistory = localHistory.filter((item: any) =>
                                item.saleNumber !== invoice.saleNumber
                              )
                              localStorage.setItem('invoiceHistory', JSON.stringify(updatedLocalHistory))

                              // Update state to trigger re-render
                              setHistoryRefresh(prev => prev + 1)

                              console.log('🗑️ Invoice deleted from history:', invoice.saleNumber)
                            } catch (error) {
                              console.error('Error deleting invoice:', error)
                              alert(language === 'en' ? 'Failed to delete invoice' : 'ငွေတောင်းခံလွှာ ဖျက်၍မရပါ')
                            }
                          }
                        }}
                        className="ml-2 p-1 text-red-500 hover:text-red-700 hover:bg-red-50 dark:hover:bg-red-900/20 rounded transition-colors"
                        title={language === 'en' ? 'Delete Invoice' : 'ငွေတောင်းခံလွှာ ဖျက်ရန်'}
                      >
                        <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      </button>
                    </div>
                  ))
                }
                {invoiceHistory.length === 0 && (
                  <div className="text-center py-4 text-gray-500 dark:text-gray-400 text-xs">
                    <History className="h-6 w-6 mx-auto mb-2 opacity-50" />
                    <p>{language === 'en' ? 'No saved invoices yet' : 'သိမ်းဆည်းထားသော ငွေတောင်းခံလွှာများ မရှိသေးပါ'}</p>
                    <p className="text-xs opacity-75 mt-1">
                      {language === 'en' ? 'Complete a POS sale or save invoices to see them here' : 'POS ရောင်းချမှု ပြုလုပ်ပါ သို့မဟုတ် ငွေတောင်းခံလွှာများ သိမ်းဆည်းပါ'}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Right Content - Invoice */}
        <div className="flex-1 min-w-0">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden print:shadow-none print:rounded-none print:bg-white">
            <div className="invoice-container px-6 py-4 print:px-0 print:py-0 print:w-full print:max-w-none" style={{
              printColorAdjust: 'exact',
              WebkitPrintColorAdjust: 'exact',
              colorAdjust: 'exact',
              width: '100%',
              maxWidth: '100%',
              height: 'auto',
              minHeight: '800px',
              margin: '0',
              padding: '20px',
              boxSizing: 'border-box',
              overflow: 'visible'
            } as React.CSSProperties}>
              <div className="print:w-full print:min-h-screen print:bg-white print:text-black">
                {renderTemplate()}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}




