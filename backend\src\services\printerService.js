// Thermal Printer Service for ESC/POS printers
const fs = require('fs');
const path = require('path');

class ThermalPrinterService {
    constructor() {
        this.printers = new Map();
        this.defaultPrinter = null;
        this.isInitialized = false;
    }

    // Initialize printer service
    async initialize() {
        try {
            console.log('🖨️ Initializing Thermal Printer Service...');
            
            // Detect available printers
            await this.detectPrinters();
            
            this.isInitialized = true;
            console.log('✅ Thermal Printer Service initialized successfully');
            
            return {
                success: true,
                message: 'Printer service initialized',
                printers: Array.from(this.printers.keys())
            };
        } catch (error) {
            console.error('❌ Failed to initialize printer service:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Detect available thermal printers
    async detectPrinters() {
        try {
            // For Windows - detect USB and network printers
            if (process.platform === 'win32') {
                await this.detectWindowsPrinters();
            } else if (process.platform === 'linux') {
                await this.detectLinuxPrinters();
            } else if (process.platform === 'darwin') {
                await this.detectMacPrinters();
            }

            console.log(`🔍 Detected ${this.printers.size} thermal printers`);
        } catch (error) {
            console.warn('⚠️ Printer detection failed:', error.message);
        }
    }

    // Windows printer detection
    async detectWindowsPrinters() {
        // Common thermal printer names
        const thermalPrinterNames = [
            'EPSON TM-T20',
            'EPSON TM-T82',
            'EPSON TM-T88',
            'Star TSP100',
            'Star TSP650',
            'Citizen CT-S310',
            'Bixolon SRP-350',
            'POS-80',
            'POS-58'
        ];

        // Simulate printer detection (in real implementation, use Windows API)
        thermalPrinterNames.forEach(name => {
            this.printers.set(name, {
                name: name,
                type: 'thermal',
                connection: 'USB',
                status: 'ready',
                paperWidth: 80, // mm
                characterWidth: 48
            });
        });

        // Set first printer as default
        if (this.printers.size > 0) {
            this.defaultPrinter = this.printers.keys().next().value;
        }
    }

    // Linux printer detection
    async detectLinuxPrinters() {
        // Use CUPS or direct USB detection
        console.log('🐧 Detecting Linux thermal printers...');
        
        // Simulate detection
        this.printers.set('USB Thermal Printer', {
            name: 'USB Thermal Printer',
            type: 'thermal',
            connection: 'USB',
            status: 'ready',
            paperWidth: 80,
            characterWidth: 48
        });
    }

    // macOS printer detection
    async detectMacPrinters() {
        console.log('🍎 Detecting macOS thermal printers...');
        
        // Simulate detection
        this.printers.set('Thermal Printer', {
            name: 'Thermal Printer',
            type: 'thermal',
            connection: 'USB',
            status: 'ready',
            paperWidth: 80,
            characterWidth: 48
        });
    }

    // Print receipt
    async printReceipt(receiptData, printerName = null) {
        try {
            if (!this.isInitialized) {
                await this.initialize();
            }

            const printer = printerName || this.defaultPrinter;
            if (!printer || !this.printers.has(printer)) {
                throw new Error('No thermal printer available');
            }

            console.log(`🖨️ Printing receipt on ${printer}...`);

            // Generate ESC/POS commands
            const escPosCommands = this.generateESCPOSCommands(receiptData);

            // Send to printer (simulation)
            await this.sendToPrinter(printer, escPosCommands);

            console.log('✅ Receipt printed successfully');
            
            return {
                success: true,
                message: 'Receipt printed successfully',
                printer: printer,
                timestamp: new Date().toISOString()
            };
        } catch (error) {
            console.error('❌ Print failed:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Generate ESC/POS commands for thermal printer
    generateESCPOSCommands(receiptData) {
        const ESC = '\x1B';
        const GS = '\x1D';
        
        let commands = '';
        
        // Initialize printer
        commands += ESC + '@'; // Initialize
        commands += ESC + 'a' + '\x01'; // Center alignment
        
        // Header
        commands += ESC + '!' + '\x18'; // Double height and width
        commands += receiptData.companyName + '\n';
        commands += ESC + '!' + '\x00'; // Normal size
        
        if (receiptData.companyAddress) {
            commands += receiptData.companyAddress + '\n';
        }
        
        if (receiptData.companyPhone) {
            commands += 'Tel: ' + receiptData.companyPhone + '\n';
        }
        
        commands += '\n';
        commands += '================================\n';
        commands += ESC + 'a' + '\x00'; // Left alignment
        
        // Receipt details
        commands += 'Receipt No: ' + receiptData.receiptNumber + '\n';
        commands += 'Date: ' + new Date(receiptData.date).toLocaleDateString() + '\n';
        commands += 'Time: ' + new Date(receiptData.date).toLocaleTimeString() + '\n';
        commands += 'Cashier: ' + receiptData.cashier + '\n';
        commands += '================================\n';
        
        // Items
        receiptData.items.forEach(item => {
            const itemLine = this.formatItemLine(item);
            commands += itemLine + '\n';
        });
        
        commands += '================================\n';
        
        // Totals
        commands += this.formatTotalLine('Subtotal:', receiptData.subtotal) + '\n';
        
        if (receiptData.tax > 0) {
            commands += this.formatTotalLine('Tax:', receiptData.tax) + '\n';
        }
        
        if (receiptData.discount > 0) {
            commands += this.formatTotalLine('Discount:', receiptData.discount) + '\n';
        }
        
        commands += '--------------------------------\n';
        commands += ESC + '!' + '\x18'; // Double height and width
        commands += this.formatTotalLine('TOTAL:', receiptData.total) + '\n';
        commands += ESC + '!' + '\x00'; // Normal size
        
        // Payment info
        commands += '================================\n';
        commands += this.formatTotalLine('Cash:', receiptData.cashReceived) + '\n';
        commands += this.formatTotalLine('Change:', receiptData.change) + '\n';
        
        // Footer
        commands += '\n';
        commands += ESC + 'a' + '\x01'; // Center alignment
        commands += 'Thank you for your business!\n';
        commands += 'Please come again\n';
        commands += '\n\n\n';
        
        // Cut paper
        commands += GS + 'V' + '\x42' + '\x00'; // Partial cut
        
        return commands;
    }

    // Format item line for receipt
    formatItemLine(item) {
        const maxWidth = 48;
        const name = item.name.substring(0, 20);
        const qty = `${item.quantity}x`;
        const price = this.formatCurrency(item.price);
        const total = this.formatCurrency(item.total);
        
        const line1 = name;
        const line2 = `  ${qty} @ ${price}`.padEnd(maxWidth - total.length) + total;
        
        return line1 + '\n' + line2;
    }

    // Format total line
    formatTotalLine(label, amount) {
        const maxWidth = 48;
        const formattedAmount = this.formatCurrency(amount);
        return label.padEnd(maxWidth - formattedAmount.length) + formattedAmount;
    }

    // Format currency
    formatCurrency(amount) {
        return new Intl.NumberFormat('en-US', {
            style: 'currency',
            currency: 'MMK',
            minimumFractionDigits: 0
        }).format(amount).replace('MMK', 'K');
    }

    // Send commands to printer (simulation)
    async sendToPrinter(printerName, commands) {
        // In real implementation, this would send to actual printer
        // For now, save to file for testing
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const filename = `receipt_${timestamp}.txt`;
        const filepath = path.join(__dirname, '../../temp', filename);
        
        // Ensure temp directory exists
        const tempDir = path.dirname(filepath);
        if (!fs.existsSync(tempDir)) {
            fs.mkdirSync(tempDir, { recursive: true });
        }
        
        fs.writeFileSync(filepath, commands);
        console.log(`📄 Receipt saved to: ${filepath}`);
        
        return true;
    }

    // Get printer status
    getPrinterStatus(printerName = null) {
        const printer = printerName || this.defaultPrinter;
        
        if (!printer || !this.printers.has(printer)) {
            return {
                success: false,
                error: 'Printer not found'
            };
        }
        
        return {
            success: true,
            printer: this.printers.get(printer)
        };
    }

    // Get all available printers
    getAvailablePrinters() {
        return {
            success: true,
            printers: Array.from(this.printers.entries()).map(([name, info]) => ({
                name,
                ...info
            })),
            defaultPrinter: this.defaultPrinter
        };
    }

    // Set default printer
    setDefaultPrinter(printerName) {
        if (!this.printers.has(printerName)) {
            return {
                success: false,
                error: 'Printer not found'
            };
        }
        
        this.defaultPrinter = printerName;
        
        return {
            success: true,
            message: `Default printer set to ${printerName}`
        };
    }
}

// Export singleton instance
module.exports = new ThermalPrinterService();
