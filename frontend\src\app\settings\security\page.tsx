'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useSettings } from '@/contexts/settings-context'
import apiClient from '@/lib/api'
import {
  Shield,
  ArrowLeft,
  Save,
  Check,
  Lock,
  Key,
  Eye,
  EyeOff,
  Activity,
  Download,
  Upload,
  RefreshCw,
  Fingerprint,
  QrCode,
  Copy,
  RotateCcw,
  AlertTriangle,
  CheckCircle,
  Clock,
  User,
  Globe,
  Smartphone,
  Search,
  Filter,
  Calendar,
  MapPin
} from 'lucide-react'

interface SecurityLog {
  id: string
  timestamp: string
  event: string
  user: string
  ip: string
  status: 'success' | 'failed' | 'warning'
}

export default function SecuritySettingsPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [showCurrentPassword, setShowCurrentPassword] = useState(false)
  const [showNewPassword, setShowNewPassword] = useState(false)

  const [passwordSettings, setPasswordSettings] = useState({
    currentPassword: '',
    newPassword: '',
    confirmPassword: '',
    requireComplexPassword: true,
    passwordExpiry: 90,
    maxLoginAttempts: 3,
    lockoutDuration: 30
  })

  const [securitySettings, setSecuritySettings] = useState({
    twoFactorAuth: false,
    sessionTimeout: 60,
    ipWhitelist: '',
    auditLogging: true,
    autoBackup: true,
    backupFrequency: 'daily',
    encryptData: true
  })

  const [twoFactorEnabled, setTwoFactorEnabled] = useState(false)
  const [showQRCode, setShowQRCode] = useState(false)
  const [verificationCode, setVerificationCode] = useState('')
  const [backupCodes, setBackupCodes] = useState<string[]>([])
  const [qrCodeSecret, setQrCodeSecret] = useState('')

  const [securityLogs, setSecurityLogs] = useState<SecurityLog[]>([])
  const [filteredLogs, setFilteredLogs] = useState<SecurityLog[]>([])
  const [logFilter, setLogFilter] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [loading, setLoading] = useState(false)

  // Load security settings and logs
  useEffect(() => {
    if (isAuthenticated) {
      loadSecuritySettings()
      loadSecurityLogs()
    }
  }, [isAuthenticated])

  // Filter logs when filter changes
  useEffect(() => {
    let filtered = securityLogs

    if (logFilter) {
      filtered = filtered.filter(log =>
        log.event.toLowerCase().includes(logFilter.toLowerCase()) ||
        log.user.toLowerCase().includes(logFilter.toLowerCase()) ||
        log.ip.includes(logFilter)
      )
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(log => log.status === statusFilter)
    }

    setFilteredLogs(filtered)
  }, [securityLogs, logFilter, statusFilter])

  const loadSecuritySettings = async () => {
    try {
      const response = await apiClient.getSecuritySettings()

      if (response.success) {
        setSecuritySettings(response.data)
        setTwoFactorEnabled(response.data.twoFactorAuth)
        setPasswordSettings(prev => ({
          ...prev,
          requireComplexPassword: response.data.passwordPolicy?.requireComplexPassword ?? true,
          passwordExpiry: response.data.passwordPolicy?.passwordExpiry ?? 90,
          maxLoginAttempts: response.data.passwordPolicy?.maxLoginAttempts ?? 3,
          lockoutDuration: response.data.passwordPolicy?.lockoutDuration ?? 30
        }))
      }
    } catch (error) {
      console.error('Failed to load security settings:', error)
    }
  }

  const loadSecurityLogs = async () => {
    setLoading(true)
    try {
      const response = await apiClient.getSecurityLogs({ limit: 50 })

      if (response.success) {
        // Transform API data to match UI interface
        const transformedLogs = response.data.map((log: any) => ({
          id: log.id,
          timestamp: new Date(log.timestamp).toLocaleString(),
          event: log.event.replace(/_/g, ' ').replace(/\b\w/g, (l: string) => l.toUpperCase()),
          user: log.user,
          ip: log.ip,
          status: log.status
        }))
        setSecurityLogs(transformedLogs)
      }
    } catch (error) {
      console.error('Failed to load security logs:', error)
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  const updatePasswordSetting = (field: string, value: any) => {
    setPasswordSettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const updateSecuritySetting = (field: string, value: any) => {
    setSecuritySettings(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handlePasswordChange = async () => {
    if (!passwordSettings.currentPassword || !passwordSettings.newPassword) {
      alert(language === 'mm'
        ? 'စကားဝှက် အချက်အလက်များ အားလုံး ဖြည့်စွက်ပါ'
        : 'Please fill in all password fields'
      )
      return
    }

    if (passwordSettings.newPassword !== passwordSettings.confirmPassword) {
      alert(language === 'mm'
        ? 'စကားဝှက် အသစ်များ မတူညီပါ'
        : 'New passwords do not match'
      )
      return
    }

    if (passwordSettings.newPassword.length < 8) {
      alert(language === 'mm'
        ? 'စကားဝှက် အနည်းဆုံး ၈ လုံး ရှိရမည်'
        : 'Password must be at least 8 characters long'
      )
      return
    }

    try {
      setSaving(true)
      const response = await apiClient.updatePassword(
        passwordSettings.currentPassword,
        passwordSettings.newPassword
      )

      if (response.success) {
        alert(language === 'mm'
          ? 'စကားဝှက် အောင်မြင်စွာ ပြောင်းလဲပြီးပါပြီ!'
          : 'Password changed successfully!'
        )
        setPasswordSettings(prev => ({
          ...prev,
          currentPassword: '',
          newPassword: '',
          confirmPassword: ''
        }))
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)

        // Reload security logs to show password change event
        loadSecurityLogs()
      } else {
        throw new Error(response.message || 'Failed to change password')
      }
    } catch (error) {
      console.error('Error changing password:', error)
      alert(language === 'mm'
        ? 'စကားဝှက် ပြောင်းလဲမှု မအောင်မြင်ပါ'
        : 'Failed to change password'
      )
    } finally {
      setSaving(false)
    }
  }

  const generateQRCode = () => {
    // Generate mock QR code secret
    const secret = 'JBSWY3DPEHPK3PXP'
    setQrCodeSecret(secret)
    setShowQRCode(true)

    // Generate backup codes
    const codes = Array.from({ length: 8 }, () =>
      Math.random().toString(36).substring(2, 8).toUpperCase()
    )
    setBackupCodes(codes)
  }

  const enable2FA = async () => {
    if (!verificationCode || verificationCode.length !== 6) {
      alert(language === 'mm'
        ? 'စစ်ဆေးရေး ကုဒ် ၆ လုံး ထည့်ပါ'
        : 'Please enter 6-digit verification code'
      )
      return
    }

    // Mock verification
    if (verificationCode === '123456') {
      setTwoFactorEnabled(true)
      setSecuritySettings(prev => ({ ...prev, twoFactorAuth: true }))
      setShowQRCode(false)
      setVerificationCode('')
      alert(language === 'mm'
        ? '2FA အောင်မြင်စွာ ဖွင့်ပြီးပါပြီ'
        : '2FA enabled successfully'
      )
    } else {
      alert(language === 'mm'
        ? 'စစ်ဆေးရေး ကုဒ် မမှန်ပါ'
        : 'Invalid verification code'
      )
    }
  }

  const disable2FA = () => {
    if (confirm(language === 'mm'
      ? '2FA ပိတ်မည်လား? လုံခြုံရေး လျော့နည်းသွားပါမည်'
      : 'Disable 2FA? This will reduce your account security'
    )) {
      setTwoFactorEnabled(false)
      setSecuritySettings(prev => ({ ...prev, twoFactorAuth: false }))
      setBackupCodes([])
      setQrCodeSecret('')
    }
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text)
    alert(language === 'mm' ? 'ကူးယူပြီးပါပြီ' : 'Copied to clipboard')
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Combine all security settings
      const allSettings = {
        ...securitySettings,
        passwordPolicy: {
          requireComplexPassword: passwordSettings.requireComplexPassword,
          passwordExpiry: passwordSettings.passwordExpiry,
          maxLoginAttempts: passwordSettings.maxLoginAttempts,
          lockoutDuration: passwordSettings.lockoutDuration
        }
      }

      const response = await apiClient.updateSecuritySettings(allSettings)

      if (response.success) {
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)

        // Reload security logs to show settings change event
        loadSecurityLogs()
      } else {
        throw new Error(response.message || 'Failed to save security settings')
      }
    } catch (error) {
      console.error('Error saving security settings:', error)
      alert(language === 'mm'
        ? 'လုံခြုံရေး ဆက်တင်များ သိမ်းဆည်းမှု မအောင်မြင်ပါ'
        : 'Failed to save security settings'
      )
    } finally {
      setSaving(false)
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'success': return 'text-green-600 bg-green-100 dark:bg-green-900/20'
      case 'failed': return 'text-red-600 bg-red-100 dark:bg-red-900/20'
      case 'warning': return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20'
      default: return 'text-gray-600 bg-gray-100 dark:bg-gray-900/20'
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-red-50 dark:hover:bg-red-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-red-600 to-pink-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Shield className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {language === 'mm' ? 'လုံခြုံရေး နှင့် ကိုယ်ရေးကိုယ်တာ' : 'Security & Privacy'}
                </h1>
                <p className="text-white/80 mt-1">
                  {language === 'mm'
                    ? 'လုံခြုံရေး ဆက်တင်များ၊ အရန်သိမ်းမှု ရွေးချယ်မှုများ နှင့် ဒေတာ ကိုယ်ရေးကိုယ်တာ စီမံခန့်ခွဲပါ'
                    : 'Manage security settings, backup options, and data privacy'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {/* Password & Authentication */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lock className="h-5 w-5 text-red-600" />
                {language === 'mm' ? 'စကားဝှက် နှင့် အထောက်အထား' : 'Password & Authentication'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'စကားဝှက် ပြောင်းလဲခြင်း နှင့် အထောက်အထား ဆက်တင်များ'
                  : 'Change password and authentication settings'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Change Password */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'စကားဝှက် ပြောင်းလဲခြင်း' : 'Change Password'}
                </Label>

                <div className="space-y-3">
                  <div className="space-y-2">
                    <Label className="text-sm">
                      {language === 'mm' ? 'လက်ရှိ စကားဝှက်' : 'Current Password'}
                    </Label>
                    <div className="relative">
                      <Input
                        type={showCurrentPassword ? 'text' : 'password'}
                        value={passwordSettings.currentPassword}
                        onChange={(e) => updatePasswordSetting('currentPassword', e.target.value)}
                        placeholder="Enter current password"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-auto p-1"
                        onClick={() => setShowCurrentPassword(!showCurrentPassword)}
                      >
                        {showCurrentPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">
                      {language === 'mm' ? 'စကားဝှက် အသစ်' : 'New Password'}
                    </Label>
                    <div className="relative">
                      <Input
                        type={showNewPassword ? 'text' : 'password'}
                        value={passwordSettings.newPassword}
                        onChange={(e) => updatePasswordSetting('newPassword', e.target.value)}
                        placeholder="Enter new password"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-auto p-1"
                        onClick={() => setShowNewPassword(!showNewPassword)}
                      >
                        {showNewPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                      </Button>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">
                      {language === 'mm' ? 'စကားဝှက် အတည်ပြုခြင်း' : 'Confirm Password'}
                    </Label>
                    <Input
                      type="password"
                      value={passwordSettings.confirmPassword}
                      onChange={(e) => updatePasswordSetting('confirmPassword', e.target.value)}
                      placeholder="Confirm new password"
                    />
                  </div>

                  <Button
                    onClick={handlePasswordChange}
                    className="w-full bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700"
                  >
                    <Key className="h-4 w-4 mr-2" />
                    {language === 'mm' ? 'စကားဝှက် ပြောင်းလဲရန်' : 'Change Password'}
                  </Button>
                </div>
              </div>

              {/* Password Policy */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'စကားဝှက် မူဝါဒ' : 'Password Policy'}
                </Label>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {language === 'mm' ? 'ရှုပ်ထွေးသော စကားဝှက် လိုအပ်' : 'Require Complex Password'}
                    </span>
                    <input
                      type="checkbox"
                      checked={passwordSettings.requireComplexPassword}
                      onChange={(e) => updatePasswordSetting('requireComplexPassword', e.target.checked)}
                      className="rounded"
                      aria-label={language === 'mm' ? 'ရှုပ်ထွေးသော စကားဝှက် လိုအပ်' : 'Require Complex Password'}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {language === 'mm' ? 'စကားဝှက် သက်တမ်း (ရက်)' : 'Password Expiry (days)'}
                    </span>
                    <Input
                      type="number"
                      value={passwordSettings.passwordExpiry}
                      onChange={(e) => updatePasswordSetting('passwordExpiry', parseInt(e.target.value) || 90)}
                      className="w-20 text-sm"
                      min="30"
                      max="365"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {language === 'mm' ? 'အများဆုံး လော့ဂ်အင် ကြိုးစားမှု' : 'Max Login Attempts'}
                    </span>
                    <Input
                      type="number"
                      value={passwordSettings.maxLoginAttempts}
                      onChange={(e) => updatePasswordSetting('maxLoginAttempts', parseInt(e.target.value) || 3)}
                      className="w-20 text-sm"
                      min="3"
                      max="10"
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {language === 'mm' ? 'လော့ခ်ထုတ် ကြာချိန် (မိနစ်)' : 'Lockout Duration (minutes)'}
                    </span>
                    <Input
                      type="number"
                      value={passwordSettings.lockoutDuration}
                      onChange={(e) => updatePasswordSetting('lockoutDuration', parseInt(e.target.value) || 30)}
                      className="w-20 text-sm"
                      min="15"
                      max="120"
                    />
                  </div>
                </div>
              </div>

              {/* Two-Factor Authentication */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'နှစ်ဆင့် အထောက်အထား' : 'Two-Factor Authentication'}
                </Label>

                {!twoFactorEnabled ? (
                  <div className="space-y-4">
                    <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                      <div className="flex items-center gap-3 mb-3">
                        <Fingerprint className="h-5 w-5 text-blue-600" />
                        <div>
                          <span className="text-sm font-medium">
                            {language === 'mm' ? '2FA ဖွင့်ရန်' : 'Enable 2FA'}
                          </span>
                          <p className="text-xs text-gray-600 dark:text-gray-400">
                            {language === 'mm'
                              ? 'အပိုင်း လုံခြုံရေး အတွက် နှစ်ဆင့် အထောက်အထား'
                              : 'Extra security with two-factor authentication'
                            }
                          </p>
                        </div>
                      </div>

                      {!showQRCode ? (
                        <Button
                          onClick={generateQRCode}
                          className="w-full bg-blue-600 hover:bg-blue-700"
                        >
                          <QrCode className="h-4 w-4 mr-2" />
                          {language === 'mm' ? '2FA စတင်ရန်' : 'Setup 2FA'}
                        </Button>
                      ) : (
                        <div className="space-y-4">
                          {/* QR Code */}
                          <div className="text-center">
                            <div className="w-48 h-48 bg-gray-100 border border-gray-300 rounded-lg mx-auto flex items-center justify-center">
                              <div className="text-center">
                                <QrCode className="h-16 w-16 mx-auto mb-2 text-gray-400" />
                                <p className="text-xs text-gray-500">QR Code</p>
                                <p className="text-xs text-gray-400 mt-1">BitsTech POS</p>
                              </div>
                            </div>
                            <p className="text-xs text-gray-600 mt-2">
                              {language === 'mm'
                                ? 'Google Authenticator သို့မဟုတ် Authy နဲ့ စကင်န်ပါ'
                                : 'Scan with Google Authenticator or Authy'
                              }
                            </p>
                          </div>

                          {/* Manual Entry */}
                          <div className="space-y-2">
                            <Label className="text-xs">
                              {language === 'mm' ? 'လက်ဖြင့် ထည့်ရန်' : 'Manual Entry'}
                            </Label>
                            <div className="flex items-center gap-2">
                              <Input
                                value={qrCodeSecret}
                                readOnly
                                className="text-xs font-mono"
                              />
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => copyToClipboard(qrCodeSecret)}
                              >
                                <Copy className="h-3 w-3" />
                              </Button>
                            </div>
                          </div>

                          {/* Verification */}
                          <div className="space-y-2">
                            <Label className="text-xs">
                              {language === 'mm' ? 'စစ်ဆေးရေး ကုဒ်' : 'Verification Code'}
                            </Label>
                            <div className="flex gap-2">
                              <Input
                                value={verificationCode}
                                onChange={(e) => setVerificationCode(e.target.value)}
                                placeholder="123456"
                                maxLength={6}
                                className="text-center font-mono"
                              />
                              <Button
                                onClick={enable2FA}
                                disabled={verificationCode.length !== 6}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <CheckCircle className="h-4 w-4 mr-1" />
                                {language === 'mm' ? 'စစ်ဆေး' : 'Verify'}
                              </Button>
                            </div>
                            <p className="text-xs text-gray-500">
                              {language === 'mm'
                                ? 'စမ်းသပ်ရန် 123456 ကို အသုံးပြုပါ'
                                : 'Use 123456 for testing'
                              }
                            </p>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {/* 2FA Enabled Status */}
                    <div className="p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-700 rounded-lg">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-3">
                          <CheckCircle className="h-5 w-5 text-green-600" />
                          <div>
                            <span className="text-sm font-medium text-green-800 dark:text-green-200">
                              {language === 'mm' ? '2FA ဖွင့်ထားပါသည်' : '2FA Enabled'}
                            </span>
                            <p className="text-xs text-green-600 dark:text-green-300">
                              {language === 'mm'
                                ? 'သင့်အကောင့် လုံခြုံစွာ ကာကွယ်ထားပါသည်'
                                : 'Your account is securely protected'
                              }
                            </p>
                          </div>
                        </div>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={disable2FA}
                          className="text-red-600 hover:bg-red-50"
                        >
                          {language === 'mm' ? 'ပိတ်ရန်' : 'Disable'}
                        </Button>
                      </div>
                    </div>

                    {/* Backup Codes */}
                    {backupCodes.length > 0 && (
                      <div className="p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <Label className="text-sm font-medium">
                            {language === 'mm' ? 'အရန် ကုဒ်များ' : 'Backup Codes'}
                          </Label>
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => copyToClipboard(backupCodes.join('\n'))}
                          >
                            <Copy className="h-3 w-3 mr-1" />
                            {language === 'mm' ? 'ကူးယူ' : 'Copy All'}
                          </Button>
                        </div>
                        <div className="grid grid-cols-2 gap-2">
                          {backupCodes.map((code, index) => (
                            <div
                              key={index}
                              className="p-2 bg-gray-50 dark:bg-gray-800 rounded text-xs font-mono text-center"
                            >
                              {code}
                            </div>
                          ))}
                        </div>
                        <p className="text-xs text-gray-600 dark:text-gray-400 mt-2">
                          {language === 'mm'
                            ? 'ဤ ကုဒ်များကို လုံခြုံသော နေရာတွင် သိမ်းဆည်းပါ'
                            : 'Store these codes in a safe place'
                          }
                        </p>
                      </div>
                    )}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* System Security */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'စနစ် လုံခြုံရေး' : 'System Security'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'စနစ် လုံခြုံရေး နှင့် ဒေတာ ကာကွယ်မှု ဆက်တင်များ'
                  : 'System security and data protection settings'
                }
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Session & Access */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'ဆက်ရှင် နှင့် ဝင်ရောက်ခွင့်' : 'Session & Access'}
                </Label>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {language === 'mm' ? 'ဆက်ရှင် သက်တမ်း (မိနစ်)' : 'Session Timeout (minutes)'}
                    </span>
                    <Input
                      type="number"
                      value={securitySettings.sessionTimeout}
                      onChange={(e) => updateSecuritySetting('sessionTimeout', parseInt(e.target.value) || 60)}
                      className="w-20 text-sm"
                      min="15"
                      max="480"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label className="text-sm">
                      {language === 'mm' ? 'IP လိပ်စာ ခွင့်ပြုစာရင်း' : 'IP Whitelist'}
                    </Label>
                    <Input
                      value={securitySettings.ipWhitelist}
                      onChange={(e) => updateSecuritySetting('ipWhitelist', e.target.value)}
                      placeholder="***********/24, 10.0.0.0/8"
                      className="text-sm"
                    />
                    <p className="text-xs text-gray-600 dark:text-gray-400">
                      {language === 'mm'
                        ? 'ကော်မာ ခြားထားသော IP လိပ်စာများ သို့မဟုတ် CIDR ranges'
                        : 'Comma-separated IP addresses or CIDR ranges'
                      }
                    </p>
                  </div>
                </div>
              </div>

              {/* Data Protection */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'ဒေတာ ကာကွယ်မှု' : 'Data Protection'}
                </Label>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {language === 'mm' ? 'ဒေတာ ကုဒ်ဝှက်ခြင်း' : 'Data Encryption'}
                    </span>
                    <input
                      type="checkbox"
                      checked={securitySettings.encryptData}
                      onChange={(e) => updateSecuritySetting('encryptData', e.target.checked)}
                      className="rounded"
                      aria-label={language === 'mm' ? 'ဒေတာ ကုဒ်ဝှက်ခြင်း' : 'Data Encryption'}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {language === 'mm' ? 'စနစ် မှတ်တမ်း' : 'Audit Logging'}
                    </span>
                    <input
                      type="checkbox"
                      checked={securitySettings.auditLogging}
                      onChange={(e) => updateSecuritySetting('auditLogging', e.target.checked)}
                      className="rounded"
                      aria-label={language === 'mm' ? 'စနစ် မှတ်တမ်း' : 'Audit Logging'}
                    />
                  </div>
                </div>
              </div>

              {/* Backup Settings */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'အရန်သိမ်းမှု ဆက်တင်များ' : 'Backup Settings'}
                </Label>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {language === 'mm' ? 'အလိုအလျောက် အရန်သိမ်းမှု' : 'Auto Backup'}
                    </span>
                    <input
                      type="checkbox"
                      checked={securitySettings.autoBackup}
                      onChange={(e) => updateSecuritySetting('autoBackup', e.target.checked)}
                      className="rounded"
                      aria-label={language === 'mm' ? 'အလိုအလျောက် အရန်သိမ်းမှု' : 'Auto Backup'}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm">
                      {language === 'mm' ? 'အရန်သိမ်းမှု ကြိမ်နှုန်း' : 'Backup Frequency'}
                    </span>
                    <select
                      className="p-1 border border-gray-300 rounded dark:border-gray-600 dark:bg-gray-800 text-sm"
                      value={securitySettings.backupFrequency}
                      onChange={(e) => updateSecuritySetting('backupFrequency', e.target.value)}
                      aria-label={language === 'mm' ? 'အရန်သိမ်းမှု ကြိမ်နှုန်း' : 'Backup Frequency'}
                    >
                      <option value="hourly">{language === 'mm' ? 'နာရီတိုင်း' : 'Hourly'}</option>
                      <option value="daily">{language === 'mm' ? 'နေ့စဉ်' : 'Daily'}</option>
                      <option value="weekly">{language === 'mm' ? 'အပတ်စဉ်' : 'Weekly'}</option>
                    </select>
                  </div>

                  <div className="flex gap-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      <Download className="h-4 w-4 mr-2" />
                      {language === 'mm' ? 'ဒေါင်းလုဒ်' : 'Download'}
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      <Upload className="h-4 w-4 mr-2" />
                      {language === 'mm' ? 'ပြန်လည်ရယူ' : 'Restore'}
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Security Logs */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Activity className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'လုံခြုံရေး မှတ်တမ်းများ' : 'Security Logs'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'နောက်ဆုံး လုံခြုံရေး ဖြစ်ရပ်များ နှင့် စနစ် လုပ်ဆောင်မှုများ'
                  : 'Recent security events and system activities'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Filters */}
              <div className="flex flex-col sm:flex-row gap-4 mb-6">
                <div className="flex-1">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder={language === 'mm' ? 'ရှာဖွေရန်...' : 'Search logs...'}
                      value={logFilter}
                      onChange={(e) => setLogFilter(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="flex gap-2">
                  <select
                    className="px-3 py-2 border border-gray-300 rounded-md dark:border-gray-600 dark:bg-gray-800 text-sm"
                    value={statusFilter}
                    onChange={(e) => setStatusFilter(e.target.value)}
                    aria-label={language === 'mm' ? 'အခြေအနေ စစ်ထုတ်ရန်' : 'Filter by status'}
                  >
                    <option value="all">{language === 'mm' ? 'အားလုံး' : 'All Status'}</option>
                    <option value="success">{language === 'mm' ? 'အောင်မြင်' : 'Success'}</option>
                    <option value="failed">{language === 'mm' ? 'မအောင်မြင်' : 'Failed'}</option>
                    <option value="warning">{language === 'mm' ? 'သတိပေး' : 'Warning'}</option>
                  </select>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={loadSecurityLogs}
                    disabled={loading}
                  >
                    <RefreshCw className={`h-4 w-4 mr-2 ${loading ? 'animate-spin' : ''}`} />
                    {language === 'mm' ? 'ပြန်လည်ဖွင့်' : 'Refresh'}
                  </Button>
                </div>
              </div>

              {/* Logs List */}
              <div className="space-y-3">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600"></div>
                  </div>
                ) : filteredLogs.length === 0 ? (
                  <div className="text-center py-8 text-gray-500">
                    {language === 'mm' ? 'မှတ်တမ်း မတွေ့ပါ' : 'No logs found'}
                  </div>
                ) : (
                  filteredLogs.map((log) => (
                    <div key={log.id} className="flex items-center justify-between p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-colors">
                      <div className="flex items-center gap-4">
                        <Badge className={`text-xs ${getStatusColor(log.status)}`}>
                          {log.status === 'success' ? (language === 'mm' ? 'အောင်မြင်' : 'Success') :
                           log.status === 'failed' ? (language === 'mm' ? 'မအောင်မြင်' : 'Failed') :
                           log.status === 'warning' ? (language === 'mm' ? 'သတိပေး' : 'Warning') : log.status}
                        </Badge>
                        <div>
                          <div className="font-medium text-sm">{log.event}</div>
                          <div className="text-xs text-gray-600 dark:text-gray-400 flex items-center gap-2">
                            <span className="flex items-center gap-1">
                              <User className="h-3 w-3" />
                              {log.user}
                            </span>
                            <span className="flex items-center gap-1">
                              <MapPin className="h-3 w-3" />
                              {log.ip}
                            </span>
                          </div>
                        </div>
                      </div>
                      <div className="text-xs text-gray-500 flex items-center gap-1">
                        <Calendar className="h-3 w-3" />
                        {log.timestamp}
                      </div>
                    </div>
                  ))
                )}
              </div>

              {/* Actions */}
              <div className="flex justify-between items-center mt-6 pt-4 border-t border-gray-200 dark:border-gray-700">
                <div className="text-sm text-gray-600 dark:text-gray-400">
                  {language === 'mm'
                    ? `စုစုပေါင်း ${filteredLogs.length} ခု`
                    : `Total: ${filteredLogs.length} logs`
                  }
                </div>
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'မှတ်တမ်း ထုတ်ယူ' : 'Export Logs'}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-gradient-to-r from-red-600 to-pink-600 hover:from-red-700 hover:to-pink-700 text-white px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
              </div>
            ) : saved ? (
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းပြီး' : 'Saved'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းရန်' : 'Save Security Settings'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
