// Settings Synchronization Across Tabs
// This module handles real-time settings sync between multiple browser tabs

import { useState, useEffect, useCallback } from 'react'

interface SettingsChangeEvent {
  type: 'settings-change'
  key: string
  value: any
  timestamp: number
  tabId: string
}

interface SyncedSetting {
  key: string
  value: any
  lastModified: number
  source: 'local' | 'remote'
}

class SettingsSyncManager {
  private channel: BroadcastChannel | null = null
  private tabId: string
  private listeners: Map<string, Set<(value: any) => void>> = new Map()
  private settings: Map<string, SyncedSetting> = new Map()
  private storagePrefix = 'bitstech_settings_'
  private syncEnabled = true

  constructor() {
    this.tabId = this.generateTabId()
    this.initializeBroadcastChannel()
    this.loadSettingsFromStorage()
    this.setupStorageListener()
  }

  private generateTabId(): string {
    return `tab_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  private initializeBroadcastChannel() {
    if (typeof window !== 'undefined' && 'BroadcastChannel' in window) {
      this.channel = new BroadcastChannel('bitstech-settings-sync')
      this.channel.addEventListener('message', this.handleBroadcastMessage.bind(this))
    }
  }

  private loadSettingsFromStorage() {
    if (typeof window === 'undefined') return

    try {
      // Load all settings from localStorage
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i)
        if (key?.startsWith(this.storagePrefix)) {
          const settingKey = key.replace(this.storagePrefix, '')
          const value = this.getFromStorage(settingKey)
          if (value !== null) {
            this.settings.set(settingKey, {
              key: settingKey,
              value,
              lastModified: Date.now(),
              source: 'local'
            })
          }
        }
      }
    } catch (error) {
      console.error('Failed to load settings from storage:', error)
    }
  }

  private setupStorageListener() {
    if (typeof window === 'undefined') return

    window.addEventListener('storage', (event) => {
      if (!event.key?.startsWith(this.storagePrefix)) return

      const settingKey = event.key.replace(this.storagePrefix, '')
      const newValue = event.newValue ? JSON.parse(event.newValue) : null

      if (newValue !== null) {
        this.settings.set(settingKey, {
          key: settingKey,
          value: newValue,
          lastModified: Date.now(),
          source: 'remote'
        })

        // Notify listeners
        this.notifyListeners(settingKey, newValue)
      }
    })
  }

  private handleBroadcastMessage(event: MessageEvent<SettingsChangeEvent>) {
    if (!this.syncEnabled || event.data.tabId === this.tabId) return

    const { key, value, timestamp } = event.data
    const currentSetting = this.settings.get(key)

    // Only update if the remote change is newer
    if (!currentSetting || timestamp > currentSetting.lastModified) {
      this.settings.set(key, {
        key,
        value,
        lastModified: timestamp,
        source: 'remote'
      })

      // Update localStorage without triggering storage event
      this.setToStorage(key, value, false)

      // Notify listeners
      this.notifyListeners(key, value)
    }
  }

  private notifyListeners(key: string, value: any) {
    const keyListeners = this.listeners.get(key)
    if (keyListeners) {
      keyListeners.forEach(listener => {
        try {
          listener(value)
        } catch (error) {
          console.error('Error in settings listener:', error)
        }
      })
    }

    // Also notify wildcard listeners
    const wildcardListeners = this.listeners.get('*')
    if (wildcardListeners) {
      wildcardListeners.forEach(listener => {
        try {
          listener({ key, value })
        } catch (error) {
          console.error('Error in wildcard settings listener:', error)
        }
      })
    }
  }

  private getFromStorage(key: string): any {
    try {
      const item = localStorage.getItem(this.storagePrefix + key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error(`Failed to get setting ${key} from storage:`, error)
      return null
    }
  }

  private setToStorage(key: string, value: any, broadcast = true) {
    try {
      localStorage.setItem(this.storagePrefix + key, JSON.stringify(value))
      
      if (broadcast && this.channel) {
        const event: SettingsChangeEvent = {
          type: 'settings-change',
          key,
          value,
          timestamp: Date.now(),
          tabId: this.tabId
        }
        this.channel.postMessage(event)
      }
    } catch (error) {
      console.error(`Failed to set setting ${key} to storage:`, error)
    }
  }

  // Public API
  public setSetting(key: string, value: any): void {
    const timestamp = Date.now()
    
    this.settings.set(key, {
      key,
      value,
      lastModified: timestamp,
      source: 'local'
    })

    this.setToStorage(key, value, true)
    this.notifyListeners(key, value)
  }

  public getSetting(key: string, defaultValue: any = null): any {
    const setting = this.settings.get(key)
    return setting ? setting.value : defaultValue
  }

  public removeSetting(key: string): void {
    this.settings.delete(key)
    
    try {
      localStorage.removeItem(this.storagePrefix + key)
      
      if (this.channel) {
        const event: SettingsChangeEvent = {
          type: 'settings-change',
          key,
          value: null,
          timestamp: Date.now(),
          tabId: this.tabId
        }
        this.channel.postMessage(event)
      }
    } catch (error) {
      console.error(`Failed to remove setting ${key}:`, error)
    }

    this.notifyListeners(key, null)
  }

  public subscribe(key: string, listener: (value: any) => void): () => void {
    if (!this.listeners.has(key)) {
      this.listeners.set(key, new Set())
    }
    
    this.listeners.get(key)!.add(listener)

    // Return unsubscribe function
    return () => {
      const keyListeners = this.listeners.get(key)
      if (keyListeners) {
        keyListeners.delete(listener)
        if (keyListeners.size === 0) {
          this.listeners.delete(key)
        }
      }
    }
  }

  public getAllSettings(): Record<string, any> {
    const result: Record<string, any> = {}
    this.settings.forEach((setting, key) => {
      result[key] = setting.value
    })
    return result
  }

  public clearAllSettings(): void {
    const keys = Array.from(this.settings.keys())
    
    keys.forEach(key => {
      this.removeSetting(key)
    })
  }

  public enableSync(): void {
    this.syncEnabled = true
  }

  public disableSync(): void {
    this.syncEnabled = false
  }

  public isSyncEnabled(): boolean {
    return this.syncEnabled
  }

  public getTabId(): string {
    return this.tabId
  }

  public getConnectionStatus(): {
    broadcastSupported: boolean
    channelOpen: boolean
    tabId: string
    settingsCount: number
  } {
    return {
      broadcastSupported: typeof window !== 'undefined' && 'BroadcastChannel' in window,
      channelOpen: this.channel !== null,
      tabId: this.tabId,
      settingsCount: this.settings.size
    }
  }

  public destroy(): void {
    if (this.channel) {
      this.channel.close()
      this.channel = null
    }
    this.listeners.clear()
    this.settings.clear()
  }
}

// Create singleton instance
let settingsSyncManager: SettingsSyncManager | null = null

export function getSettingsSyncManager(): SettingsSyncManager {
  if (!settingsSyncManager) {
    settingsSyncManager = new SettingsSyncManager()
  }
  return settingsSyncManager
}

// React hook for using synced settings
export function useSyncedSetting<T>(key: string, defaultValue: T): [T, (value: T) => void] {
  const [value, setValue] = useState<T>(() => {
    const manager = getSettingsSyncManager()
    return manager.getSetting(key, defaultValue)
  })

  useEffect(() => {
    const manager = getSettingsSyncManager()
    
    // Subscribe to changes
    const unsubscribe = manager.subscribe(key, (newValue) => {
      setValue(newValue ?? defaultValue)
    })

    return unsubscribe
  }, [key, defaultValue])

  const setSyncedValue = useCallback((newValue: T) => {
    const manager = getSettingsSyncManager()
    manager.setSetting(key, newValue)
  }, [key])

  return [value, setSyncedValue]
}

// Utility functions
export function syncSetting(key: string, value: any): void {
  const manager = getSettingsSyncManager()
  manager.setSetting(key, value)
}

export function getSyncedSetting(key: string, defaultValue: any = null): any {
  const manager = getSettingsSyncManager()
  return manager.getSetting(key, defaultValue)
}

export function removeSyncedSetting(key: string): void {
  const manager = getSettingsSyncManager()
  manager.removeSetting(key)
}

export function subscribeToSetting(key: string, listener: (value: any) => void): () => void {
  const manager = getSettingsSyncManager()
  return manager.subscribe(key, listener)
}

// Export types
export type { SettingsChangeEvent, SyncedSetting }
