'use client'

import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, usePara<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { useAuth } from '@/contexts/auth-context'
import apiClient from '@/lib/api'
import {
  Package,
  ArrowLeft,
  CheckCircle,
  XCircle,
  Clock,
  User,
  Calendar,
  FileText,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  RefreshCw,
  Edit,
  Trash2,
  Send
} from 'lucide-react'

interface StockAdjustment {
  _id: string
  adjustmentNumber: string
  type: string
  reason: string
  notes?: string
  status: 'draft' | 'pending' | 'approved' | 'applied' | 'rejected'
  totalItems: number
  totalCostImpact: number
  currency: string
  adjustmentDate: string
  createdBy: {
    _id: string
    firstName: string
    lastName: string
    email: string
  }
  approvedBy?: {
    _id: string
    firstName: string
    lastName: string
    email: string
  }
  approvalDate?: string
  appliedDate?: string
  rejectionReason?: string
  items: Array<{
    _id: string
    product: {
      _id: string
      name: string
      sku: string
      category: {
        name: string
        color: string
      }
    }
    productName: string
    sku: string
    currentQuantity: number
    adjustmentQuantity: number
    newQuantity: number
    unitCost: number
    totalCost: number
    notes?: string
  }>
}

export default function AdjustmentDetailPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const [adjustment, setAdjustment] = useState<StockAdjustment | null>(null)
  const [loading, setLoading] = useState(true)
  const [processing, setProcessing] = useState(false)
  const [showApprovalForm, setShowApprovalForm] = useState(false)
  const [approvalComments, setApprovalComments] = useState('')

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated && params.id) {
      fetchAdjustment()
    }
  }, [isAuthenticated, params.id])

  const fetchAdjustment = async () => {
    try {
      setLoading(true)
      
      // Mock data for now - will implement real API later
      const mockAdjustment: StockAdjustment = {
        _id: params.id as string,
        adjustmentNumber: 'ADJ-240101-001',
        type: 'damage',
        reason: 'Laptop damaged during shipping from supplier',
        notes: 'Screen cracked, keyboard not working. Need to claim insurance.',
        status: 'pending',
        totalItems: 1,
        totalCostImpact: -720000,
        currency: 'MMK',
        adjustmentDate: '2024-01-01T10:00:00Z',
        createdBy: {
          _id: '1',
          firstName: 'John',
          lastName: 'Doe',
          email: '<EMAIL>'
        },
        items: [
          {
            _id: '1',
            product: {
              _id: '1',
              name: 'ASUS VivoBook 15',
              sku: 'LAP001',
              category: {
                name: 'Laptops',
                color: '#3B82F6'
              }
            },
            productName: 'ASUS VivoBook 15',
            sku: 'LAP001',
            currentQuantity: 15,
            adjustmentQuantity: -1,
            newQuantity: 14,
            unitCost: 720000,
            totalCost: -720000,
            notes: 'Damaged unit - screen cracked'
          }
        ]
      }

      setAdjustment(mockAdjustment)
    } catch (error) {
      console.error('Error fetching adjustment:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleApproval = async (approved: boolean) => {
    if (!adjustment) return

    try {
      setProcessing(true)

      // Mock API call
      console.log('Approval decision:', { approved, comments: approvalComments })
      
      // Update local state
      setAdjustment(prev => prev ? {
        ...prev,
        status: approved ? 'approved' : 'rejected',
        approvedBy: approved ? {
          _id: user?.id || '',
          firstName: user?.firstName || '',
          lastName: user?.lastName || '',
          email: user?.email || ''
        } : undefined,
        approvalDate: approved ? new Date().toISOString() : undefined,
        rejectionReason: !approved ? approvalComments : undefined
      } : null)

      setShowApprovalForm(false)
      setApprovalComments('')
    } catch (error) {
      console.error('Error processing approval:', error)
    } finally {
      setProcessing(false)
    }
  }

  const handleApplyAdjustment = async () => {
    if (!adjustment) return

    try {
      setProcessing(true)

      // Mock API call
      console.log('Applying adjustment:', adjustment._id)
      
      // Update local state
      setAdjustment(prev => prev ? {
        ...prev,
        status: 'applied',
        appliedDate: new Date().toISOString()
      } : null)
    } catch (error) {
      console.error('Error applying adjustment:', error)
    } finally {
      setProcessing(false)
    }
  }

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'increase': return <TrendingUp className="h-5 w-5 text-green-600" />
      case 'decrease': return <TrendingDown className="h-5 w-5 text-red-600" />
      case 'damage': return <AlertTriangle className="h-5 w-5 text-orange-600" />
      case 'theft': return <XCircle className="h-5 w-5 text-red-600" />
      case 'expired': return <Clock className="h-5 w-5 text-gray-600" />
      case 'return': return <RefreshCw className="h-5 w-5 text-blue-600" />
      case 'transfer': return <Package className="h-5 w-5 text-purple-600" />
      default: return <Edit className="h-5 w-5 text-gray-600" />
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'draft':
        return <Badge variant="outline" className="bg-gray-100 text-gray-800">Draft</Badge>
      case 'pending':
        return <Badge variant="outline" className="bg-yellow-100 text-yellow-800">Pending Approval</Badge>
      case 'approved':
        return <Badge variant="outline" className="bg-blue-100 text-blue-800">Approved</Badge>
      case 'applied':
        return <Badge variant="outline" className="bg-green-100 text-green-800">Applied</Badge>
      case 'rejected':
        return <Badge variant="outline" className="bg-red-100 text-red-800">Rejected</Badge>
      default:
        return <Badge variant="outline">Unknown</Badge>
    }
  }

  const formatPrice = (price: number, currency: string = 'MMK') => {
    return `${price.toLocaleString()} ${currency}`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const canApprove = user?.role === 'admin' || user?.role === 'manager'
  const canEdit = adjustment?.status === 'draft' && adjustment?.createdBy._id === user?.id
  const canDelete = adjustment?.status === 'draft' && adjustment?.createdBy._id === user?.id
  const canApply = (user?.role === 'admin' || user?.role === 'manager') && adjustment?.status === 'approved'

  const language = user?.preferences?.language as 'en' | 'mm' || 'en'

  const text = {
    en: {
      adjustmentDetails: 'Adjustment Details',
      backToList: 'Back to Adjustments',
      approve: 'Approve',
      reject: 'Reject',
      apply: 'Apply to Inventory',
      edit: 'Edit',
      delete: 'Delete',
      submitForApproval: 'Submit for Approval',
      approvalComments: 'Approval Comments',
      commentsPlaceholder: 'Enter comments for approval/rejection...',
      cancel: 'Cancel',
      processing: 'Processing...',
      adjustmentNumber: 'Adjustment Number',
      type: 'Type',
      status: 'Status',
      reason: 'Reason',
      notes: 'Notes',
      createdBy: 'Created By',
      createdDate: 'Created Date',
      approvedBy: 'Approved By',
      approvalDate: 'Approval Date',
      appliedDate: 'Applied Date',
      rejectionReason: 'Rejection Reason',
      totalItems: 'Total Items',
      totalImpact: 'Total Cost Impact',
      adjustedProducts: 'Adjusted Products',
      product: 'Product',
      currentStock: 'Current Stock',
      adjustment: 'Adjustment',
      newStock: 'New Stock',
      unitCost: 'Unit Cost',
      totalCost: 'Total Cost',
      itemNotes: 'Notes',
      noNotes: 'No additional notes',
      confirmApproval: 'Are you sure you want to approve this adjustment?',
      confirmRejection: 'Are you sure you want to reject this adjustment?',
      confirmApply: 'Are you sure you want to apply this adjustment to inventory? This action cannot be undone.',
      types: {
        increase: 'Stock Increase',
        decrease: 'Stock Decrease',
        correction: 'Stock Correction',
        damage: 'Damage',
        theft: 'Theft',
        expired: 'Expired',
        return: 'Return',
        transfer: 'Transfer'
      }
    },
    mm: {
      adjustmentDetails: 'ပြင်ဆင်မှု အသေးစိတ်',
      backToList: 'ပြင်ဆင်မှုများ စာရင်းသို့ ပြန်သွားရန်',
      approve: 'အတည်ပြုရန်',
      reject: 'ငြင်းပယ်ရန်',
      apply: 'စတော့သို့ အသုံးချရန်',
      edit: 'ပြင်ရန်',
      delete: 'ဖျက်ရန်',
      submitForApproval: 'အတည်ပြုချက်အတွက် တင်သွင်းရန်',
      approvalComments: 'အတည်ပြုချက် မှတ်ချက်များ',
      commentsPlaceholder: 'အတည်ပြုခြင်း/ငြင်းပယ်ခြင်းအတွက် မှတ်ချက်များ ရေးပါ...',
      cancel: 'မလုပ်တော့ပါ',
      processing: 'လုပ်ဆောင်နေသည်...',
      adjustmentNumber: 'ပြင်ဆင်မှု နံပါတ်',
      type: 'အမျိုးအစား',
      status: 'အခြေအနေ',
      reason: 'အကြောင်းပြချက်',
      notes: 'မှတ်ချက်များ',
      createdBy: 'ဖန်တီးသူ',
      createdDate: 'ဖန်တီးသည့် ရက်စွဲ',
      approvedBy: 'အတည်ပြုသူ',
      approvalDate: 'အတည်ပြုသည့် ရက်စွဲ',
      appliedDate: 'အသုံးချသည့် ရက်စွဲ',
      rejectionReason: 'ငြင်းပယ်ရခြင်း အကြောင်းပြချက်',
      totalItems: 'စုစုပေါင်း ပစ္စည်းများ',
      totalImpact: 'စုစုပေါင်း ကုန်ကျစရိတ် သက်ရောက်မှု',
      adjustedProducts: 'ပြင်ဆင်ထားသော ကုန်ပစ္စည်းများ',
      product: 'ကုန်ပစ္စည်း',
      currentStock: 'လက်ရှိ စတော့',
      adjustment: 'ပြင်ဆင်မှု',
      newStock: 'စတော့ အသစ်',
      unitCost: 'ယူနစ် ကုန်ကျစရိတ်',
      totalCost: 'စုစုပေါင်း ကုန်ကျစရိတ်',
      itemNotes: 'မှတ်ချက်များ',
      noNotes: 'နောက်ထပ် မှတ်ချက် မရှိပါ',
      confirmApproval: 'ဤ ပြင်ဆင်မှုကို အတည်ပြုလိုသည်မှာ သေချာပါသလား?',
      confirmRejection: 'ဤ ပြင်ဆင်မှုကို ငြင်းပယ်လိုသည်မှာ သေချာပါသလား?',
      confirmApply: 'ဤ ပြင်ဆင်မှုကို စတော့သို့ အသုံးချလိုသည်မှာ သေချာပါသလား? ဤ လုပ်ဆောင်ချက်ကို ပြန်ပြင်၍ မရပါ။',
      types: {
        increase: 'စတော့ တိုးခြင်း',
        decrease: 'စတော့ လျှော့ခြင်း',
        correction: 'စတော့ ပြင်ဆင်ခြင်း',
        damage: 'ပျက်စီးမှု',
        theft: 'ခိုးယူမှု',
        expired: 'သက်တမ်းကုန်',
        return: 'ပြန်အမ်း',
        transfer: 'လွှဲပြောင်း'
      }
    }
  }

  const t = text[language]

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated || !adjustment) {
    return null
  }

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.back()}>
              <ArrowLeft className="h-4 w-4 mr-2" />
              {t.backToList}
            </Button>
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                {t.adjustmentDetails}
              </h1>
              <p className="text-gray-600 dark:text-gray-400 mt-1">
                {adjustment.adjustmentNumber}
              </p>
            </div>
          </div>
          
          <div className="flex gap-2">
            {/* Action buttons based on status and permissions */}
            {adjustment.status === 'draft' && canEdit && (
              <>
                <Button variant="outline" onClick={() => router.push(`/inventory/adjustments/${adjustment._id}/edit`)}>
                  <Edit className="h-4 w-4 mr-2" />
                  {t.edit}
                </Button>
                <Button variant="outline" className="text-red-600 hover:text-red-700">
                  <Trash2 className="h-4 w-4 mr-2" />
                  {t.delete}
                </Button>
                <Button>
                  <Send className="h-4 w-4 mr-2" />
                  {t.submitForApproval}
                </Button>
              </>
            )}
            
            {adjustment.status === 'pending' && canApprove && (
              <>
                <Button variant="outline" onClick={() => setShowApprovalForm(true)}>
                  <XCircle className="h-4 w-4 mr-2" />
                  {t.reject}
                </Button>
                <Button onClick={() => setShowApprovalForm(true)}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  {t.approve}
                </Button>
              </>
            )}
            
            {adjustment.status === 'approved' && canApply && (
              <Button onClick={handleApplyAdjustment} disabled={processing}>
                <Package className="h-4 w-4 mr-2" />
                {processing ? t.processing : t.apply}
              </Button>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Adjustment Info */}
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center gap-2">
                    {getTypeIcon(adjustment.type)}
                    {t.types[adjustment.type as keyof typeof t.types] || adjustment.type}
                  </CardTitle>
                  {getStatusBadge(adjustment.status)}
                </div>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t.adjustmentNumber}</Label>
                    <div className="text-sm font-mono">{adjustment.adjustmentNumber}</div>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t.createdDate}</Label>
                    <div className="text-sm">{formatDate(adjustment.adjustmentDate)}</div>
                  </div>
                  
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t.createdBy}</Label>
                    <div className="text-sm">{adjustment.createdBy.firstName} {adjustment.createdBy.lastName}</div>
                  </div>
                  
                  {adjustment.approvedBy && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">{t.approvedBy}</Label>
                      <div className="text-sm">{adjustment.approvedBy.firstName} {adjustment.approvedBy.lastName}</div>
                    </div>
                  )}
                  
                  {adjustment.approvalDate && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">{t.approvalDate}</Label>
                      <div className="text-sm">{formatDate(adjustment.approvalDate)}</div>
                    </div>
                  )}
                  
                  {adjustment.appliedDate && (
                    <div>
                      <Label className="text-sm font-medium text-gray-500">{t.appliedDate}</Label>
                      <div className="text-sm">{formatDate(adjustment.appliedDate)}</div>
                    </div>
                  )}
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-500">{t.reason}</Label>
                  <div className="text-sm mt-1">{adjustment.reason}</div>
                </div>

                {adjustment.notes && (
                  <div>
                    <Label className="text-sm font-medium text-gray-500">{t.notes}</Label>
                    <div className="text-sm mt-1">{adjustment.notes}</div>
                  </div>
                )}

                {adjustment.rejectionReason && (
                  <div>
                    <Label className="text-sm font-medium text-red-500">{t.rejectionReason}</Label>
                    <div className="text-sm mt-1 text-red-600">{adjustment.rejectionReason}</div>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Products */}
            <Card>
              <CardHeader>
                <CardTitle>{t.adjustedProducts}</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead className="bg-gray-50 dark:bg-gray-800">
                      <tr>
                        <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t.product}
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t.currentStock}
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t.adjustment}
                        </th>
                        <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t.newStock}
                        </th>
                        <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                          {t.totalCost}
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                      {adjustment.items.map((item) => (
                        <tr key={item._id}>
                          <td className="px-4 py-4">
                            <div>
                              <div className="text-sm font-medium text-gray-900 dark:text-white">
                                {item.product.name}
                              </div>
                              <div className="text-sm text-gray-500">
                                SKU: {item.product.sku}
                              </div>
                              {item.notes && (
                                <div className="text-xs text-gray-400 mt-1">
                                  {item.notes}
                                </div>
                              )}
                            </div>
                          </td>
                          <td className="px-4 py-4 text-center">
                            <div className="text-sm">{item.currentQuantity}</div>
                          </td>
                          <td className="px-4 py-4 text-center">
                            <div className={`text-sm font-medium ${
                              item.adjustmentQuantity >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {item.adjustmentQuantity >= 0 ? '+' : ''}{item.adjustmentQuantity}
                            </div>
                          </td>
                          <td className="px-4 py-4 text-center">
                            <div className="text-sm">{item.newQuantity}</div>
                          </td>
                          <td className="px-4 py-4 text-right">
                            <div className={`text-sm font-medium ${
                              item.totalCost >= 0 ? 'text-green-600' : 'text-red-600'
                            }`}>
                              {item.totalCost >= 0 ? '+' : ''}{formatPrice(item.totalCost, adjustment.currency)}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Summary Sidebar */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t.totalItems}:</span>
                  <span className="font-medium">{adjustment.totalItems}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-sm text-gray-500">{t.totalImpact}:</span>
                  <span className={`font-medium ${
                    adjustment.totalCostImpact >= 0 ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {adjustment.totalCostImpact >= 0 ? '+' : ''}{formatPrice(adjustment.totalCostImpact, adjustment.currency)}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Approval Form Modal */}
        {showApprovalForm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 w-full max-w-md">
              <h3 className="text-lg font-medium mb-4">
                {t.approvalComments}
              </h3>
              
              <div className="space-y-4">
                <div>
                  <Label htmlFor="comments">{t.approvalComments}</Label>
                  <Textarea
                    id="comments"
                    value={approvalComments}
                    onChange={(e) => setApprovalComments(e.target.value)}
                    placeholder={t.commentsPlaceholder}
                    rows={4}
                  />
                </div>
                
                <div className="flex gap-2 justify-end">
                  <Button variant="outline" onClick={() => setShowApprovalForm(false)}>
                    {t.cancel}
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => handleApproval(false)}
                    disabled={processing}
                    className="text-red-600 hover:text-red-700"
                  >
                    {processing ? t.processing : t.reject}
                  </Button>
                  <Button 
                    onClick={() => handleApproval(true)}
                    disabled={processing}
                  >
                    {processing ? t.processing : t.approve}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </MainLayout>
  )
}
