# BitesTech POS System Environment Variables

# ===========================================
# APPLICATION SETTINGS
# ===========================================
NODE_ENV=development
APP_NAME=BitesTech POS System
APP_VERSION=1.0.0
APP_URL=http://localhost:3000

# ===========================================
# FRONTEND SETTINGS
# ===========================================
NEXT_PUBLIC_API_URL=http://localhost:5000/api
NEXT_PUBLIC_APP_NAME=BitesTech POS
NEXT_PUBLIC_DEFAULT_LANGUAGE=en
NEXT_PUBLIC_DEFAULT_CURRENCY=MMK
NEXT_PUBLIC_DEFAULT_THEME=light

# ===========================================
# BACKEND SETTINGS
# ===========================================
PORT=5000
API_PREFIX=/api
CORS_ORIGIN=http://localhost:3000

# ===========================================
# DATABASE SETTINGS
# ===========================================
# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/bitestech_pos
MONGODB_DB_NAME=bitestech_pos

# PostgreSQL Configuration (Alternative)
# DATABASE_URL=postgresql://username:password@localhost:5432/bitestech_pos
# DB_HOST=localhost
# DB_PORT=5432
# DB_NAME=bitestech_pos
# DB_USER=postgres
# DB_PASSWORD=password

# ===========================================
# AUTHENTICATION & SECURITY
# ===========================================
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d
BCRYPT_SALT_ROUNDS=12

# Session Settings
SESSION_SECRET=your-session-secret-key
SESSION_TIMEOUT=3600000

# ===========================================
# EMAIL SETTINGS
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
EMAIL_FROM=<EMAIL>

# ===========================================
# FILE UPLOAD SETTINGS
# ===========================================
UPLOAD_DIR=uploads
MAX_FILE_SIZE=5242880
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf

# ===========================================
# PAYMENT GATEWAY SETTINGS
# ===========================================
# Stripe Configuration
STRIPE_PUBLIC_KEY=pk_test_your_stripe_public_key
STRIPE_SECRET_KEY=sk_test_your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayPal Configuration
PAYPAL_CLIENT_ID=your_paypal_client_id
PAYPAL_CLIENT_SECRET=your_paypal_client_secret
PAYPAL_MODE=sandbox

# ===========================================
# EXTERNAL SERVICES
# ===========================================
# Currency Exchange API
EXCHANGE_RATE_API_KEY=your_exchange_rate_api_key
EXCHANGE_RATE_API_URL=https://api.exchangerate-api.com/v4/latest

# SMS Service (Optional)
SMS_API_KEY=your_sms_api_key
SMS_SENDER_ID=BitesTech

# ===========================================
# LOGGING & MONITORING
# ===========================================
LOG_LEVEL=info
LOG_FILE=logs/app.log
ENABLE_REQUEST_LOGGING=true

# Error Tracking (Optional)
SENTRY_DSN=your_sentry_dsn

# ===========================================
# DEVELOPMENT SETTINGS
# ===========================================
# Hot Reload
FAST_REFRESH=true
NEXT_TELEMETRY_DISABLED=1

# Debug Settings
DEBUG=false
VERBOSE_LOGGING=false

# ===========================================
# PRODUCTION SETTINGS
# ===========================================
# SSL Settings
SSL_ENABLED=false
SSL_CERT_PATH=
SSL_KEY_PATH=

# Cache Settings
REDIS_URL=redis://localhost:6379
CACHE_TTL=3600

# Rate Limiting
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX_REQUESTS=100

# ===========================================
# BACKUP SETTINGS
# ===========================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
BACKUP_STORAGE_PATH=backups/

# ===========================================
# COMPANY SETTINGS
# ===========================================
COMPANY_NAME=Your Company Name
COMPANY_ADDRESS=Your Company Address
COMPANY_PHONE=+95-9-***********
COMPANY_EMAIL=<EMAIL>
COMPANY_WEBSITE=https://yourcompany.com

# Tax Settings
DEFAULT_TAX_RATE=5
TAX_INCLUSIVE=false

# ===========================================
# FEATURE FLAGS
# ===========================================
ENABLE_BARCODE_SCANNER=true
ENABLE_RECEIPT_PRINTER=true
ENABLE_CUSTOMER_DISPLAY=false
ENABLE_LOYALTY_PROGRAM=false
ENABLE_MULTI_LOCATION=false

# ===========================================
# LOCALIZATION
# ===========================================
SUPPORTED_LANGUAGES=en,mm
SUPPORTED_CURRENCIES=MMK,THB,USD
DEFAULT_TIMEZONE=Asia/Yangon

# ===========================================
# SECURITY HEADERS
# ===========================================
HELMET_ENABLED=true
CORS_CREDENTIALS=true
TRUST_PROXY=false
