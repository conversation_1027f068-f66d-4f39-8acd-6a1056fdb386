const mongoose = require('mongoose');
require('dotenv').config();

const Settings = require('../models/Settings');
const ExchangeRate = require('../models/ExchangeRate');

const connectDB = async () => {
    try {
        await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos');
        console.log('MongoDB connected for seeding...');
    } catch (error) {
        console.error('Database connection error:', error);
        process.exit(1);
    }
};

const seedSettings = async () => {
    try {
        console.log('🌱 Seeding settings...');

        // Clear existing settings
        await Settings.deleteMany({});
        await ExchangeRate.deleteMany({});

        // Create default settings
        const defaultSettings = new Settings({
            storeName: 'BitsTech POS',
            storeEmail: '<EMAIL>',
            storePhone: '+95-9-***********',
            storeAddress: 'Yangon, Myanmar',
            defaultTaxRate: 0,
            taxName: 'Tax',
            taxNumber: 'TAX-001-2024',
            primaryCurrency: 'MMK',
            currencyPosition: 'after',
            decimalPlaces: 2,
            thousandSeparator: ',',
            decimalSeparator: '.',
            defaultLanguage: 'en',
            defaultTheme: 'light',
            defaultColorScheme: 'blue',
            receiptHeader: 'Thank you for your purchase!',
            receiptFooter: 'Please come again!',
            showLogo: true,
            logoUrl: '',
            lowStockThreshold: 10,
            enableLowStockAlerts: true,
            autoReorderEnabled: false,
            autoReorderQuantity: 50,
            enableBarcode: true,
            enableCustomerDisplay: true,
            enableReceiptPrinting: true,
            defaultPaymentMethod: 'cash',
            emailNotifications: true,
            smsNotifications: false,
            pushNotifications: true,
            sessionTimeout: 30,
            requirePasswordChange: false,
            passwordExpiryDays: 90,
            autoBackupEnabled: true,
            backupFrequency: 'daily',
            maintenanceMode: false,
            debugMode: false
        });

        await defaultSettings.save();
        console.log('✅ Default settings created');

        // Create default exchange rates
        const defaultExchangeRates = new ExchangeRate({
            baseCurrency: 'MMK',
            rates: {
                MMK: {
                    rate: 1,
                    symbol: 'K',
                    name: 'Myanmar Kyat',
                    flag: '🇲🇲'
                },
                USD: {
                    rate: 0.00048,
                    symbol: '$',
                    name: 'US Dollar',
                    flag: '🇺🇸'
                },
                THB: {
                    rate: 0.016,
                    symbol: '฿',
                    name: 'Thai Baht',
                    flag: '🇹🇭'
                }
            },
            source: 'system',
            isActive: true
        });

        await defaultExchangeRates.save();
        console.log('✅ Default exchange rates created');

        console.log('🎉 Settings seeding completed successfully!');

    } catch (error) {
        console.error('❌ Error seeding settings:', error);
        throw error;
    }
};

const runSeed = async () => {
    try {
        await connectDB();
        await seedSettings();
        console.log('✅ Seeding completed successfully!');
        process.exit(0);
    } catch (error) {
        console.error('❌ Seeding failed:', error);
        process.exit(1);
    }
};

// Run if called directly
if (require.main === module) {
    runSeed();
}

module.exports = { seedSettings };
