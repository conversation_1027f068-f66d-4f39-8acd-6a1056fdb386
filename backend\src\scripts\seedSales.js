const mongoose = require('mongoose');
const Sale = require('../models/Sale');
const Product = require('../models/Product');
const Customer = require('../models/Customer');
const User = require('../models/User');

const generateRandomSales = async () => {
    try {
        console.log('🌱 Generating sample sales data...');
        
        // Get existing data
        const products = await Product.find({ isActive: true });
        const customers = await Customer.find({ isActive: true });
        const users = await User.find({ isActive: true });
        
        if (products.length === 0 || users.length === 0) {
            throw new Error('No products or users found. Please seed products and users first.');
        }
        
        const paymentMethods = ['cash', 'card', 'kbz_pay', 'wave_money', 'nug_pay'];
        const sampleSales = [];
        
        // Generate sales for the last 30 days
        const today = new Date();
        const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
        
        for (let i = 0; i < 50; i++) {
            // Random date within last 30 days
            const saleDate = new Date(thirtyDaysAgo.getTime() + Math.random() * (today.getTime() - thirtyDaysAgo.getTime()));
            
            // Random number of items (1-5)
            const itemCount = Math.floor(Math.random() * 5) + 1;
            const items = [];
            let subtotal = 0;
            
            for (let j = 0; j < itemCount; j++) {
                const product = products[Math.floor(Math.random() * products.length)];
                const quantity = Math.floor(Math.random() * 3) + 1;
                const price = product.price;
                const total = price * quantity;
                
                items.push({
                    product: product._id,
                    productName: product.name,
                    productSku: product.sku,
                    quantity: quantity,
                    price: price,
                    total: total
                });
                
                subtotal += total;
            }
            
            // Calculate tax and total
            const taxRate = 0.05; // 5% tax
            const taxAmount = Math.round(subtotal * taxRate);
            const total = subtotal + taxAmount;
            
            // Random discount (0-20%)
            const discountPercentage = Math.random() < 0.3 ? Math.floor(Math.random() * 20) : 0;
            const discountAmount = Math.round(total * (discountPercentage / 100));
            const finalTotal = total - discountAmount;
            
            const sale = {
                saleNumber: `SALE-${Date.now()}-${i.toString().padStart(3, '0')}`,
                items: items,
                subtotal: subtotal,
                taxAmount: taxAmount,
                discountAmount: discountAmount,
                discountPercentage: discountPercentage,
                total: finalTotal,
                paymentMethod: paymentMethods[Math.floor(Math.random() * paymentMethods.length)],
                paymentStatus: 'completed',
                customer: customers.length > 0 && Math.random() > 0.3 ? customers[Math.floor(Math.random() * customers.length)]._id : null,
                cashier: users[Math.floor(Math.random() * users.length)]._id,
                notes: Math.random() > 0.7 ? 'Sample sale transaction' : '',
                createdAt: saleDate,
                updatedAt: saleDate
            };
            
            sampleSales.push(sale);
        }
        
        return sampleSales;
        
    } catch (error) {
        console.error('❌ Error generating sales data:', error);
        throw error;
    }
};

const seedSales = async () => {
    try {
        console.log('🌱 Seeding sales...');
        
        // Clear existing sales
        await Sale.deleteMany({});
        console.log('🗑️ Cleared existing sales');
        
        // Generate and create sales
        const salesData = await generateRandomSales();
        const sales = await Sale.insertMany(salesData);
        console.log(`✅ Created ${sales.length} sales transactions`);
        
        // Update customer statistics
        console.log('📊 Updating customer statistics...');
        const customers = await Customer.find({ isActive: true });
        
        for (const customer of customers) {
            const customerSales = await Sale.find({ customer: customer._id });
            const totalPurchases = customerSales.length;
            const totalSpent = customerSales.reduce((sum, sale) => sum + sale.total, 0);
            const loyaltyPoints = Math.floor(totalSpent / 1000); // 1 point per 1000 MMK
            
            await Customer.findByIdAndUpdate(customer._id, {
                totalPurchases: totalPurchases,
                totalSpent: totalSpent,
                loyaltyPoints: loyaltyPoints
            });
        }
        
        // Display sales statistics
        const totalSales = await Sale.countDocuments();
        const totalRevenue = await Sale.aggregate([
            { $group: { _id: null, total: { $sum: '$total' } } }
        ]);
        
        const salesByPaymentMethod = await Sale.aggregate([
            { $group: { _id: '$paymentMethod', count: { $sum: 1 }, total: { $sum: '$total' } } },
            { $sort: { count: -1 } }
        ]);
        
        const salesByDate = await Sale.aggregate([
            {
                $group: {
                    _id: { $dateToString: { format: '%Y-%m-%d', date: '$createdAt' } },
                    count: { $sum: 1 },
                    total: { $sum: '$total' }
                }
            },
            { $sort: { _id: -1 } },
            { $limit: 7 }
        ]);
        
        console.log('\n📊 Sales Statistics:');
        console.log(`- Total Sales: ${totalSales}`);
        console.log(`- Total Revenue: ${totalRevenue[0]?.total || 0} MMK`);
        console.log(`- Average Sale: ${Math.round((totalRevenue[0]?.total || 0) / totalSales)} MMK`);
        
        console.log('\n💳 Sales by Payment Method:');
        salesByPaymentMethod.forEach(method => {
            console.log(`- ${method._id}: ${method.count} sales (${method.total} MMK)`);
        });
        
        console.log('\n📅 Recent Sales by Date:');
        salesByDate.forEach(day => {
            console.log(`- ${day._id}: ${day.count} sales (${day.total} MMK)`);
        });
        
        console.log('🎉 Sales seeding completed successfully!');
        
    } catch (error) {
        console.error('❌ Error seeding sales:', error);
        throw error;
    }
};

module.exports = { seedSales };

// Run if called directly
if (require.main === module) {
    const connectDB = async () => {
        try {
            const mongoURI = process.env.MONGO_URI || process.env.MONGODB_URI || 'mongodb://localhost:27017/bitstech_pos';
            await mongoose.connect(mongoURI);
            console.log('✅ MongoDB connected for sales seeding');
        } catch (error) {
            console.error('❌ MongoDB connection failed:', error.message);
            process.exit(1);
        }
    };

    const runSeed = async () => {
        try {
            await connectDB();
            await seedSales();
            console.log('\n🎯 Sales seeding completed successfully!');
            process.exit(0);
        } catch (error) {
            console.error('❌ Sales seeding failed:', error);
            process.exit(1);
        }
    };

    runSeed();
}
