const express = require('express');
const router = express.Router();
const PurchaseOrder = require('../models/PurchaseOrder');
const Product = require('../models/Product');
const { auth, authorize } = require('../middleware/auth');

// @route   GET /api/purchase-orders/pending-receiving
// @desc    Get purchase orders pending for receiving
// @access  Private (Manager, Admin)
router.get('/pending-receiving', auth, authorize(['admin', 'manager']), async (req, res) => {
  try {
    const { search, priority, status } = req.query;
    
    // Build query
    let query = {
      status: { $in: ['approved', 'ordered', 'partially_received'] }
    };

    if (search) {
      query.$or = [
        { orderNumber: { $regex: search, $options: 'i' } },
        { 'supplier.name': { $regex: search, $options: 'i' } }
      ];
    }

    if (priority) {
      query.priority = priority;
    }

    if (status) {
      query.status = status;
    }

    const purchaseOrders = await PurchaseOrder.find(query)
      .populate('items.product', 'name sku')
      .sort({ expectedDeliveryDate: 1, priority: -1 })
      .lean();

    // Calculate completion percentage and pending items for each PO
    const enrichedPOs = purchaseOrders.map(po => {
      const totalItems = po.items.reduce((sum, item) => sum + item.quantity, 0);
      const receivedItems = po.items.reduce((sum, item) => sum + (item.receivedQuantity || 0), 0);
      const pendingItems = totalItems - receivedItems;
      const completionPercentage = totalItems > 0 ? Math.round((receivedItems / totalItems) * 100) : 0;

      return {
        ...po,
        totalItems,
        receivedItems,
        pendingItems,
        completionPercentage
      };
    });

    res.json({
      success: true,
      data: enrichedPOs,
      count: enrichedPOs.length
    });
  } catch (error) {
    console.error('Error fetching pending POs:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching pending purchase orders'
    });
  }
});

// @route   GET /api/purchase-orders/:id/receiving-details
// @desc    Get detailed information for receiving a specific PO
// @access  Private (Manager, Admin)
router.get('/:id/receiving-details', auth, authorize(['admin', 'manager']), async (req, res) => {
  try {
    const purchaseOrder = await PurchaseOrder.findById(req.params.id)
      .populate('items.product', 'name sku currentStock')
      .lean();

    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Calculate pending quantities for each item
    const enrichedItems = purchaseOrder.items.map(item => ({
      ...item,
      pendingQuantity: item.quantity - (item.receivedQuantity || 0),
      receivingQuantity: 0, // Default receiving quantity
      condition: 'good', // Default condition
      notes: ''
    }));

    const enrichedPO = {
      ...purchaseOrder,
      items: enrichedItems
    };

    res.json({
      success: true,
      data: enrichedPO
    });
  } catch (error) {
    console.error('Error fetching PO receiving details:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching purchase order details'
    });
  }
});

// @route   POST /api/purchase-orders/:id/receive
// @desc    Process receiving of items for a purchase order
// @access  Private (Manager, Admin)
router.post('/:id/receive', auth, authorize(['admin', 'manager']), async (req, res) => {
  try {
    const { 
      receivedItems, 
      deliveryDate, 
      receivedBy, 
      deliveryNotes,
      supplierInvoiceNumber 
    } = req.body;

    const purchaseOrder = await PurchaseOrder.findById(req.params.id);

    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    // Validate received items
    if (!receivedItems || !Array.isArray(receivedItems) || receivedItems.length === 0) {
      return res.status(400).json({
        success: false,
        message: 'No items specified for receiving'
      });
    }

    // Process each received item
    const receivingResults = [];
    
    for (const receivedItem of receivedItems) {
      const { itemId, receivingQuantity, condition, notes } = receivedItem;
      
      if (receivingQuantity <= 0) continue;

      // Find the item in the purchase order
      const poItem = purchaseOrder.items.find(item => item._id.toString() === itemId);
      
      if (!poItem) {
        return res.status(400).json({
          success: false,
          message: `Item with ID ${itemId} not found in purchase order`
        });
      }

      const currentReceived = poItem.receivedQuantity || 0;
      const pendingQuantity = poItem.quantity - currentReceived;

      if (receivingQuantity > pendingQuantity) {
        return res.status(400).json({
          success: false,
          message: `Cannot receive ${receivingQuantity} items. Only ${pendingQuantity} items are pending for ${poItem.productName}`
        });
      }

      // Update received quantity in PO
      poItem.receivedQuantity = currentReceived + receivingQuantity;
      
      // Update product inventory (only for items in good condition)
      if (condition === 'good') {
        const product = await Product.findById(poItem.product);
        if (product) {
          product.inventory.quantity += receivingQuantity;
          await product.save();
        }
      }

      // Record receiving details
      if (!poItem.receivingHistory) {
        poItem.receivingHistory = [];
      }

      poItem.receivingHistory.push({
        receivedQuantity: receivingQuantity,
        condition,
        notes,
        receivedBy,
        receivedDate: deliveryDate || new Date(),
        createdAt: new Date()
      });

      receivingResults.push({
        itemId,
        productName: poItem.productName,
        receivedQuantity: receivingQuantity,
        condition,
        totalReceived: poItem.receivedQuantity,
        totalOrdered: poItem.quantity
      });
    }

    // Update PO status based on completion
    const totalOrdered = purchaseOrder.items.reduce((sum, item) => sum + item.quantity, 0);
    const totalReceived = purchaseOrder.items.reduce((sum, item) => sum + (item.receivedQuantity || 0), 0);

    if (totalReceived >= totalOrdered) {
      purchaseOrder.status = 'completed';
    } else if (totalReceived > 0) {
      purchaseOrder.status = 'partially_received';
    }

    // Add receiving record to PO
    if (!purchaseOrder.receivingHistory) {
      purchaseOrder.receivingHistory = [];
    }

    purchaseOrder.receivingHistory.push({
      receivedBy,
      receivedDate: deliveryDate || new Date(),
      deliveryNotes,
      supplierInvoiceNumber,
      items: receivingResults,
      createdAt: new Date()
    });

    await purchaseOrder.save();

    res.json({
      success: true,
      message: `Successfully received ${receivingResults.length} item types`,
      data: {
        purchaseOrder: {
          _id: purchaseOrder._id,
          orderNumber: purchaseOrder.orderNumber,
          status: purchaseOrder.status,
          totalOrdered,
          totalReceived,
          completionPercentage: Math.round((totalReceived / totalOrdered) * 100)
        },
        receivingResults
      }
    });

  } catch (error) {
    console.error('Error processing PO receiving:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while processing receiving'
    });
  }
});

// @route   GET /api/purchase-orders/:id/receiving-history
// @desc    Get receiving history for a purchase order
// @access  Private (Manager, Admin)
router.get('/:id/receiving-history', auth, authorize(['admin', 'manager']), async (req, res) => {
  try {
    const purchaseOrder = await PurchaseOrder.findById(req.params.id)
      .select('orderNumber receivingHistory')
      .lean();

    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    res.json({
      success: true,
      data: {
        orderNumber: purchaseOrder.orderNumber,
        receivingHistory: purchaseOrder.receivingHistory || []
      }
    });
  } catch (error) {
    console.error('Error fetching receiving history:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while fetching receiving history'
    });
  }
});

// @route   POST /api/purchase-orders/:id/partial-receive
// @desc    Mark items as partially received with damage/defect reporting
// @access  Private (Manager, Admin)
router.post('/:id/partial-receive', auth, authorize(['admin', 'manager']), async (req, res) => {
  try {
    const { itemId, receivedQuantity, damagedQuantity, defectiveQuantity, notes } = req.body;

    const purchaseOrder = await PurchaseOrder.findById(req.params.id);

    if (!purchaseOrder) {
      return res.status(404).json({
        success: false,
        message: 'Purchase order not found'
      });
    }

    const poItem = purchaseOrder.items.find(item => item._id.toString() === itemId);
    
    if (!poItem) {
      return res.status(400).json({
        success: false,
        message: 'Item not found in purchase order'
      });
    }

    const totalReceiving = receivedQuantity + (damagedQuantity || 0) + (defectiveQuantity || 0);
    const currentReceived = poItem.receivedQuantity || 0;
    const pendingQuantity = poItem.quantity - currentReceived;

    if (totalReceiving > pendingQuantity) {
      return res.status(400).json({
        success: false,
        message: 'Total receiving quantity exceeds pending quantity'
      });
    }

    // Update inventory only for good items
    if (receivedQuantity > 0) {
      const product = await Product.findById(poItem.product);
      if (product) {
        product.inventory.quantity += receivedQuantity;
        await product.save();
      }
    }

    // Update PO item
    poItem.receivedQuantity = currentReceived + totalReceiving;
    
    if (!poItem.qualityControl) {
      poItem.qualityControl = {
        goodQuantity: 0,
        damagedQuantity: 0,
        defectiveQuantity: 0
      };
    }

    poItem.qualityControl.goodQuantity += receivedQuantity;
    poItem.qualityControl.damagedQuantity += (damagedQuantity || 0);
    poItem.qualityControl.defectiveQuantity += (defectiveQuantity || 0);

    await purchaseOrder.save();

    res.json({
      success: true,
      message: 'Partial receiving recorded successfully',
      data: {
        itemId,
        receivedQuantity,
        damagedQuantity: damagedQuantity || 0,
        defectiveQuantity: defectiveQuantity || 0,
        totalReceived: poItem.receivedQuantity,
        pendingQuantity: poItem.quantity - poItem.receivedQuantity
      }
    });

  } catch (error) {
    console.error('Error processing partial receiving:', error);
    res.status(500).json({
      success: false,
      message: 'Server error while processing partial receiving'
    });
  }
});

module.exports = router;
