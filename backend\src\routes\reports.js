const express = require('express');
const { protect, authorize } = require('../middleware/auth');
const Sale = require('../models/Sale');
const Product = require('../models/Product');

const router = express.Router();

// @route   GET /api/reports/sales
// @desc    Get sales reports
// @access  Private
router.get('/sales', protect, async (req, res) => {
    try {
        const { startDate, endDate, groupBy = 'day' } = req.query;

        // Default to last 30 days if no dates provided
        const end = endDate ? new Date(endDate) : new Date();
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        // Ensure end date includes the full day
        end.setHours(23, 59, 59, 999);
        start.setHours(0, 0, 0, 0);

        const sales = await Sale.find({
            createdAt: { $gte: start, $lte: end },
            status: 'completed'
        }).populate('cashier', 'firstName lastName');

        // Calculate summary
        const totalSales = sales.length;
        const totalRevenue = sales.reduce((sum, sale) => sum + sale.total, 0);
        const averageOrderValue = totalSales > 0 ? totalRevenue / totalSales : 0;
        const totalItems = sales.reduce((sum, sale) =>
            sum + sale.items.reduce((itemSum, item) => itemSum + item.quantity, 0), 0
        );

        // Group data by time period
        const groupedData = {};
        sales.forEach(sale => {
            let key;
            const date = new Date(sale.createdAt);

            switch (groupBy) {
                case 'hour':
                    key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
                    break;
                case 'week':
                    const weekStart = new Date(date);
                    weekStart.setDate(date.getDate() - date.getDay());
                    key = `${weekStart.getFullYear()}-W${Math.ceil((weekStart.getTime() - new Date(weekStart.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`;
                    break;
                case 'month':
                    key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                    break;
                default: // day
                    key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
            }

            if (!groupedData[key]) {
                groupedData[key] = {
                    period: key,
                    sales: 0,
                    revenue: 0,
                    items: 0
                };
            }

            groupedData[key].sales += 1;
            groupedData[key].revenue += sale.total;
            groupedData[key].items += sale.items.reduce((sum, item) => sum + item.quantity, 0);
        });

        const chartData = Object.values(groupedData).sort((a, b) => a.period.localeCompare(b.period));

        res.json({
            success: true,
            data: {
                summary: {
                    totalSales,
                    totalRevenue,
                    averageOrderValue,
                    totalItems,
                    period: `${start.toISOString().split('T')[0]} to ${end.toISOString().split('T')[0]}`
                },
                chartData,
                recentSales: sales.slice(-10).reverse() // Last 10 sales
            }
        });
    } catch (error) {
        console.error('Error generating sales report:', error);
        res.status(500).json({
            success: false,
            message: 'Error generating sales report'
        });
    }
});

// @route   GET /api/reports/products
// @desc    Get product performance reports
// @access  Private
router.get('/products', protect, async (req, res) => {
    try {
        const { startDate, endDate, limit = 20 } = req.query;

        const end = endDate ? new Date(endDate) : new Date();
        const start = startDate ? new Date(startDate) : new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);

        end.setHours(23, 59, 59, 999);
        start.setHours(0, 0, 0, 0);

        // Get sales data for the period
        const sales = await Sale.find({
            createdAt: { $gte: start, $lte: end },
            status: 'completed'
        });

        // Aggregate product performance
        const productStats = {};
        sales.forEach(sale => {
            sale.items.forEach(item => {
                const productId = item.product.toString();
                if (!productStats[productId]) {
                    productStats[productId] = {
                        productId,
                        productName: item.productName,
                        productSku: item.productSku,
                        totalQuantity: 0,
                        totalRevenue: 0,
                        salesCount: 0
                    };
                }

                productStats[productId].totalQuantity += item.quantity;
                productStats[productId].totalRevenue += item.totalPrice;
                productStats[productId].salesCount += 1;
            });
        });

        // Convert to array and sort by revenue
        const topProducts = Object.values(productStats)
            .sort((a, b) => b.totalRevenue - a.totalRevenue)
            .slice(0, parseInt(limit));

        // Get current inventory status
        const products = await Product.find({ isActive: true })
            .populate('category', 'name')
            .select('name sku inventory price category');

        const lowStockProducts = products.filter(p => p.inventory.quantity <= p.inventory.minStock);

        res.json({
            success: true,
            data: {
                topProducts,
                lowStockProducts: lowStockProducts.slice(0, 10),
                summary: {
                    totalProducts: products.length,
                    lowStockCount: lowStockProducts.length,
                    outOfStockCount: products.filter(p => p.inventory.quantity === 0).length,
                    totalInventoryValue: products.reduce((sum, p) => sum + (p.inventory.quantity * p.price), 0)
                }
            }
        });
    } catch (error) {
        console.error('Error generating product report:', error);
        res.status(500).json({
            success: false,
            message: 'Error generating product report'
        });
    }
});

module.exports = router;
