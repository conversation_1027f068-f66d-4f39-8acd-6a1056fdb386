'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON> } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useAuth } from '@/contexts/auth-context'
import { useTheme } from '@/contexts/theme-context'
import { useCurrency } from '@/contexts/currency-context'
import apiClient from '@/lib/api'
import {
  Users,
  Plus,
  Search,
  Filter,
  Star,
  Gift,
  ShoppingBag,
  DollarSign,
  Calendar,
  Phone,
  Mail,
  MapPin,
  Eye,
  Edit,
  Trash2,
  Download,
  Crown,
  Heart,
  TrendingUp,
  Award,
  BarChart3
} from 'lucide-react'

interface Customer {
  _id: string
  name: string
  email: string
  phone: string
  address?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  customerType: 'regular' | 'vip' | 'wholesale' | 'new'
  loyaltyPoints: number
  totalSpent: number
  totalOrders: number
  lastPurchase?: string
  joinDate: string
  status: 'active' | 'inactive'
  notes?: string
  preferences?: {
    language: 'en' | 'mm'
    currency: 'MMK' | 'USD'
    notifications: boolean
  }
}

export default function CustomersPage() {
  const { user, isAuthenticated, isLoading } = useAuth()
  const { language } = useTheme()
  const { formatCurrency } = useCurrency()
  const router = useRouter()
  const [customers, setCustomers] = useState<Customer[]>([])
  const [filteredCustomers, setFilteredCustomers] = useState<Customer[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [typeFilter, setTypeFilter] = useState('all')
  const [statusFilter, setStatusFilter] = useState('all')
  const [showAddModal, setShowAddModal] = useState(false)
  const [showAnalytics, setShowAnalytics] = useState(false)
  const [customerAnalytics, setCustomerAnalytics] = useState<any>(null)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  // Real-time currency sync
  useEffect(() => {
    const handleCurrencyChange = (event: CustomEvent) => {
      const newCurrency = event.detail.currency
      console.log('🔄 Customers currency changed to:', newCurrency)
      // Customers will automatically re-render with new currency formatting
    }

    window.addEventListener('global-currency-sync', handleCurrencyChange as EventListener)
    return () => {
      window.removeEventListener('global-currency-sync', handleCurrencyChange as EventListener)
    }
  }, [])

  useEffect(() => {
    if (isAuthenticated) {
      fetchCustomers()
    }
  }, [isAuthenticated])

  useEffect(() => {
    if (showAnalytics && customers.length > 0) {
      fetchCustomerAnalytics()
    }
  }, [showAnalytics, customers])

  useEffect(() => {
    filterCustomers()
  }, [customers, searchQuery, typeFilter, statusFilter])

  const fetchCustomers = async () => {
    try {
      setLoading(true)

      // Fetch customers from real API
      const response = await apiClient.getCustomers({
        page: 1,
        limit: 100,
        search: searchQuery,
        isActive: statusFilter === 'all' ? undefined : statusFilter === 'active'
      })

      if (response.success && response.data) {
        // Ensure response.data is an array
        const customersData = Array.isArray(response.data) ? response.data : []
        setCustomers(customersData)
        console.log('✅ Customers loaded:', customersData.length, 'customers')
      } else {
        console.error('Failed to fetch customers:', response.error)
        setCustomers([])
      }
    } catch (error) {
      console.error('Error fetching customers:', error)
      setCustomers([])
    } finally {
      setLoading(false)
    }
  }

  const fetchCustomerAnalytics = async () => {
    try {
      console.log('🔄 Loading customer analytics...')

      // Get customer purchase history and analytics
      const analyticsData = await Promise.all(
        customers.slice(0, 20).map(async (customer) => {
          try {
            const salesResponse = await apiClient.getSales({ customer: customer._id })
            const sales = salesResponse.success ? salesResponse.data : []

            const totalSpent = sales.reduce((sum: number, sale: any) => sum + (sale.total || 0), 0)
            const totalOrders = sales.length
            const avgOrderValue = totalOrders > 0 ? totalSpent / totalOrders : 0
            const lastOrderDate = sales.length > 0 ?
              new Date(Math.max(...sales.map((s: any) => new Date(s.createdAt).getTime()))) : null

            // Calculate customer loyalty score
            const daysSinceLastOrder = lastOrderDate ?
              Math.floor((new Date().getTime() - lastOrderDate.getTime()) / (1000 * 60 * 60 * 24)) : 999

            let loyaltyScore = 0
            if (totalOrders >= 20) loyaltyScore += 40
            else if (totalOrders >= 10) loyaltyScore += 30
            else if (totalOrders >= 5) loyaltyScore += 20
            else if (totalOrders >= 1) loyaltyScore += 10

            if (totalSpent >= 5000000) loyaltyScore += 30 // 5M MMK
            else if (totalSpent >= 1000000) loyaltyScore += 20
            else if (totalSpent >= 500000) loyaltyScore += 10

            if (daysSinceLastOrder <= 30) loyaltyScore += 20
            else if (daysSinceLastOrder <= 90) loyaltyScore += 10
            else if (daysSinceLastOrder > 365) loyaltyScore -= 20

            const customerTier = loyaltyScore >= 80 ? 'VIP' :
                               loyaltyScore >= 60 ? 'Gold' :
                               loyaltyScore >= 40 ? 'Silver' :
                               loyaltyScore >= 20 ? 'Bronze' : 'Regular'

            return {
              ...customer,
              totalSpent,
              totalOrders,
              avgOrderValue,
              lastOrderDate,
              loyaltyScore: Math.max(0, Math.min(100, loyaltyScore)),
              customerTier,
              daysSinceLastOrder
            }
          } catch (error) {
            console.error(`Error fetching analytics for customer ${customer._id}:`, error)
            return {
              ...customer,
              totalSpent: 0,
              totalOrders: 0,
              avgOrderValue: 0,
              lastOrderDate: null,
              loyaltyScore: 0,
              customerTier: 'Regular',
              daysSinceLastOrder: 999
            }
          }
        })
      )

      setCustomerAnalytics(analyticsData.sort((a, b) => b.loyaltyScore - a.loyaltyScore))
      console.log('✅ Customer analytics loaded:', analyticsData.length)
    } catch (error) {
      console.error('Error fetching customer analytics:', error)
      setCustomerAnalytics([])
    }
  }

  const filterCustomers = () => {
    // Ensure customers is an array before filtering
    if (!Array.isArray(customers)) {
      setFilteredCustomers([])
      return
    }

    let filtered = customers

    if (searchQuery) {
      filtered = filtered.filter(customer =>
        customer.name?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.email?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        customer.phone?.includes(searchQuery)
      )
    }

    if (typeFilter !== 'all') {
      filtered = filtered.filter(customer => customer.customerType === typeFilter)
    }

    if (statusFilter !== 'all') {
      filtered = filtered.filter(customer => customer.status === statusFilter)
    }

    setFilteredCustomers(filtered)
  }

  const getCustomerTypeIcon = (type: string) => {
    switch (type) {
      case 'vip': return <Crown className="h-4 w-4 text-yellow-600" />
      case 'wholesale': return <ShoppingBag className="h-4 w-4 text-purple-600" />
      case 'regular': return <Users className="h-4 w-4 text-blue-600" />
      case 'new': return <Star className="h-4 w-4 text-green-600" />
      default: return <Users className="h-4 w-4 text-gray-600" />
    }
  }

  const getCustomerTypeBadge = (type: string) => {
    switch (type) {
      case 'vip':
        return <Badge className="bg-yellow-100 text-yellow-800">VIP</Badge>
      case 'wholesale':
        return <Badge className="bg-purple-100 text-purple-800">Wholesale</Badge>
      case 'regular':
        return <Badge className="bg-blue-100 text-blue-800">Regular</Badge>
      case 'new':
        return <Badge className="bg-green-100 text-green-800">New</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Active</Badge>
      case 'inactive':
        return <Badge className="bg-gray-100 text-gray-800">Inactive</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getLoyaltyLevel = (points: number) => {
    if (points >= 3000) return { level: 'Platinum', color: 'text-purple-600', icon: Crown }
    if (points >= 2000) return { level: 'Gold', color: 'text-yellow-600', icon: Award }
    if (points >= 1000) return { level: 'Silver', color: 'text-gray-600', icon: Star }
    return { level: 'Bronze', color: 'text-orange-600', icon: Heart }
  }



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatLastPurchase = (dateString?: string) => {
    if (!dateString) return 'Never'
    
    const now = new Date()
    const purchaseDate = new Date(dateString)
    const diffInDays = Math.floor((now.getTime() - purchaseDate.getTime()) / (1000 * 60 * 60 * 24))
    
    if (diffInDays === 0) return 'Today'
    if (diffInDays === 1) return 'Yesterday'
    if (diffInDays < 7) return `${diffInDays} days ago`
    if (diffInDays < 30) return `${Math.floor(diffInDays / 7)} weeks ago`
    return formatDate(dateString)
  }

  const getCustomerStats = () => {
    // Ensure customers is an array before processing
    if (!Array.isArray(customers) || customers.length === 0) {
      return { total: 0, active: 0, vip: 0, totalSpent: 0, totalPoints: 0 }
    }

    const total = customers.length
    const active = customers.filter(c => c.status === 'active').length
    const vip = customers.filter(c => c.customerType === 'vip').length
    const totalSpent = customers.reduce((sum, c) => sum + (c.totalSpent || 0), 0)
    const totalPoints = customers.reduce((sum, c) => sum + (c.loyaltyPoints || 0), 0)

    return { total, active, vip, totalSpent, totalPoints }
  }

  const handleViewCustomer = (customerId: string) => {
    // For now, show customer details in alert
    const customer = customers.find(c => c._id === customerId)
    if (customer) {
      const details = `
Customer Details:
Name: ${customer.name}
Email: ${customer.email}
Phone: ${customer.phone}
Type: ${customer.customerType}
Total Spent: ${formatCurrency(customer.totalSpent)}
Loyalty Points: ${customer.loyaltyPoints}
Status: ${customer.status}
      `
      alert(details)
    }
  }

  const handleEditCustomer = async (customerId: string) => {
    // For now, show edit form in prompt
    const customer = customers.find(c => c._id === customerId)
    if (customer) {
      const newName = prompt('Edit Customer Name:', customer.name)
      const newEmail = prompt('Edit Customer Email:', customer.email)
      const newPhone = prompt('Edit Customer Phone:', customer.phone)

      if (newName && newEmail && newPhone) {
        try {
          const response = await apiClient.updateCustomer(customerId, {
            name: newName,
            email: newEmail,
            phone: newPhone
          })

          if (response.success) {
            // Refresh customers list
            await fetchCustomers()
            alert(language === 'mm' ? 'ဖောက်သည် အချက်အလက် ပြင်ဆင်ပြီးပါပြီ' : 'Customer updated successfully!')
          } else {
            alert('Failed to update customer: ' + (response.error || 'Unknown error'))
          }
        } catch (error) {
          console.error('Error updating customer:', error)
          alert('Error updating customer. Please try again.')
        }
      }
    }
  }

  const handleDeleteCustomer = async (customerId: string) => {
    const customer = customers.find(c => c._id === customerId)
    if (customer) {
      const confirmMessage = language === 'mm'
        ? `${customer.name} ကို ဖျက်မည်လား? ဤလုပ်ဆောင်ချက်ကို ပြန်ပြင်၍မရပါ။`
        : `Delete ${customer.name}? This action cannot be undone.`

      if (window.confirm(confirmMessage)) {
        try {
          const response = await apiClient.deleteCustomer(customerId)

          if (response.success) {
            // Refresh customers list
            await fetchCustomers()
            alert(language === 'mm' ? 'ဖောက်သည် ဖျက်ပြီးပါပြီ' : 'Customer deleted successfully!')
          } else {
            alert('Failed to delete customer: ' + (response.error || 'Unknown error'))
          }
        } catch (error) {
          console.error('Error deleting customer:', error)
          alert('Error deleting customer. Please try again.')
        }
      }
    }
  }



  if (!isAuthenticated) {
    return null
  }

  const stats = getCustomerStats()

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-pink-600 to-rose-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                  <Users className="h-8 w-8" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold">
                    {language === 'mm' ? 'ဖောက်သည် စီမံခန့်ခွဲမှု' : 'Customer Management'}
                  </h1>
                  <p className="text-white/80 mt-1">
                    {language === 'mm' 
                      ? 'ဖောက်သည်များ၊ သစ္စာရှိမှု အမှတ်များ နှင့် ဝယ်ယူမှု မှတ်တမ်းများ'
                      : 'Customer profiles, loyalty points, and purchase history'
                    }
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <Button
                  onClick={() => setShowAddModal(true)}
                  className="bg-white/20 hover:bg-white/30 text-white border-white/30"
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ဖောက်သည် အသစ်' : 'Add Customer'}
                </Button>
                <Button
                  onClick={() => setShowAnalytics(!showAnalytics)}
                  className={`${showAnalytics ? 'bg-white/30' : 'bg-white/20'} hover:bg-white/30 text-white border-white/30`}
                >
                  <BarChart3 className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ခွဲခြမ်းစိတ်ဖြာမှု' : 'Analytics'}
                </Button>
                <Button className="bg-white/20 hover:bg-white/30 text-white border-white/30">
                  <Download className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ထုတ်ယူရန်' : 'Export'}
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Customer Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'စုစုပေါင်း ဖောက်သည်များ' : 'Total Customers'}
                  </p>
                  <p className="text-2xl font-bold">{stats.total}</p>
                </div>
                <div className="p-3 bg-blue-100 dark:bg-blue-900/20 rounded-xl">
                  <Users className="h-6 w-6 text-blue-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'လက်ရှိ ဖောက်သည်များ' : 'Active Customers'}
                  </p>
                  <p className="text-2xl font-bold text-green-600">{stats.active}</p>
                </div>
                <div className="p-3 bg-green-100 dark:bg-green-900/20 rounded-xl">
                  <TrendingUp className="h-6 w-6 text-green-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {language === 'mm' ? 'VIP ဖောက်သည်များ' : 'VIP Customers'}
                  </p>
                  <p className="text-2xl font-bold text-yellow-600">{stats.vip}</p>
                </div>
                <div className="p-3 bg-yellow-100 dark:bg-yellow-900/20 rounded-xl">
                  <Crown className="h-6 w-6 text-yellow-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {language === 'mm' ? 'စုစုပေါင်း ရောင်းအား' : 'Total Revenue'}
                  </p>
                  <div className="flex items-center gap-1">
                    <p className="text-lg font-bold text-purple-600 truncate">
                      {formatCurrency(stats.totalSpent)}
                    </p>
                  </div>
                </div>
                <div className="p-2 bg-purple-100 dark:bg-purple-900/20 rounded-lg flex-shrink-0">
                  <DollarSign className="h-5 w-5 text-purple-600" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="min-w-0 flex-1">
                  <p className="text-xs text-gray-600 dark:text-gray-400 truncate">
                    {language === 'mm' ? 'စုစုပေါင်း အမှတ်များ' : 'Total Points'}
                  </p>
                  <div className="flex items-center gap-1">
                    <p className="text-lg font-bold text-orange-600 truncate">
                      {(stats.totalPoints / 1000).toFixed(1)}K
                    </p>
                    <Gift className="h-4 w-4 text-orange-600 flex-shrink-0" />
                  </div>
                </div>
                <div className="p-2 bg-orange-100 dark:bg-orange-900/20 rounded-lg flex-shrink-0">
                  <Gift className="h-5 w-5 text-orange-600" />
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder={language === 'mm' ? 'အမည်၊ အီးမေးလ် သို့မဟုတ် ဖုန်းနံပါတ် ရှာရန်...' : 'Search by name, email, or phone...'}
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-4">
                <Select value={typeFilter} onValueChange={setTypeFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder={language === 'mm' ? 'အမျိုးအစား' : 'Type'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Types'}</SelectItem>
                    <SelectItem value="vip">{language === 'mm' ? 'VIP' : 'VIP'}</SelectItem>
                    <SelectItem value="wholesale">{language === 'mm' ? 'လက်ကားရောင်း' : 'Wholesale'}</SelectItem>
                    <SelectItem value="regular">{language === 'mm' ? 'ပုံမှန်' : 'Regular'}</SelectItem>
                    <SelectItem value="new">{language === 'mm' ? 'အသစ်' : 'New'}</SelectItem>
                  </SelectContent>
                </Select>

                <Select value={statusFilter} onValueChange={setStatusFilter}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder={language === 'mm' ? 'အခြေအနေ' : 'Status'} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">{language === 'mm' ? 'အားလုံး' : 'All Status'}</SelectItem>
                    <SelectItem value="active">{language === 'mm' ? 'လက်ရှိ' : 'Active'}</SelectItem>
                    <SelectItem value="inactive">{language === 'mm' ? 'မလှုပ်ရှား' : 'Inactive'}</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Customer Analytics */}
        {showAnalytics && customerAnalytics && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'ဖောက်သည် ခွဲခြမ်းစိတ်ဖြာမှု' : 'Customer Analytics'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ဖောက်သည်များ၏ ဝယ်ယူမှု အပြုအမူ၊ သစ္စာရှိမှု အမှတ်များ နှင့် စွမ်းဆောင်ရည် ခွဲခြမ်းစိတ်ဖြာမှု'
                  : 'Customer purchase behavior, loyalty scores, and performance analysis'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="overflow-x-auto">
                <table className="w-full text-sm">
                  <thead className="bg-gray-50 dark:bg-gray-800">
                    <tr>
                      <th className="px-4 py-2 text-left font-medium text-gray-500">
                        {language === 'mm' ? 'အဆင့်' : 'Rank'}
                      </th>
                      <th className="px-4 py-2 text-left font-medium text-gray-500">
                        {language === 'mm' ? 'ဖောက်သည်' : 'Customer'}
                      </th>
                      <th className="px-4 py-2 text-center font-medium text-gray-500">
                        {language === 'mm' ? 'အဆင့်' : 'Tier'}
                      </th>
                      <th className="px-4 py-2 text-center font-medium text-gray-500">
                        {language === 'mm' ? 'သစ္စာရှိမှု' : 'Loyalty'}
                      </th>
                      <th className="px-4 py-2 text-center font-medium text-gray-500">
                        {language === 'mm' ? 'အမှာစာများ' : 'Orders'}
                      </th>
                      <th className="px-4 py-2 text-right font-medium text-gray-500">
                        {language === 'mm' ? 'စုစုပေါင်း' : 'Total Spent'}
                      </th>
                      <th className="px-4 py-2 text-right font-medium text-gray-500">
                        {language === 'mm' ? 'ပျမ်းမျှ' : 'Avg Order'}
                      </th>
                      <th className="px-4 py-2 text-center font-medium text-gray-500">
                        {language === 'mm' ? 'နောက်ဆုံး' : 'Last Order'}
                      </th>
                    </tr>
                  </thead>
                  <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                    {customerAnalytics.slice(0, 15).map((customer: any, index: number) => (
                      <tr key={customer._id} className="hover:bg-gray-50 dark:hover:bg-gray-800">
                        <td className="px-4 py-3">
                          <div className="flex items-center gap-2">
                            <span className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                              index === 0 ? 'bg-yellow-100 text-yellow-800' :
                              index === 1 ? 'bg-gray-100 text-gray-800' :
                              index === 2 ? 'bg-orange-100 text-orange-800' :
                              'bg-blue-100 text-blue-800'
                            }`}>
                              {index + 1}
                            </span>
                            {index < 3 && (
                              <Award className={`h-4 w-4 ${
                                index === 0 ? 'text-yellow-500' :
                                index === 1 ? 'text-gray-500' :
                                'text-orange-500'
                              }`} />
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center gap-3">
                            <div className="w-8 h-8 bg-gradient-to-br from-pink-500 to-rose-600 rounded-full flex items-center justify-center text-white font-bold text-sm">
                              {customer.name.charAt(0).toUpperCase()}
                            </div>
                            <div>
                              <div className="font-medium text-gray-900 dark:text-white">
                                {customer.name}
                              </div>
                              <div className="text-xs text-gray-500">{customer.email}</div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <Badge className={`${
                            customer.customerTier === 'VIP' ? 'bg-purple-100 text-purple-800' :
                            customer.customerTier === 'Gold' ? 'bg-yellow-100 text-yellow-800' :
                            customer.customerTier === 'Silver' ? 'bg-gray-100 text-gray-800' :
                            customer.customerTier === 'Bronze' ? 'bg-orange-100 text-orange-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {customer.customerTier}
                          </Badge>
                        </td>
                        <td className="px-4 py-3 text-center">
                          <div className="flex flex-col items-center gap-1">
                            <span className="font-bold text-blue-600">{customer.loyaltyScore}</span>
                            <div className="w-16 bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-600 h-2 rounded-full"
                                style={{ width: `${customer.loyaltyScore}%` }}
                              ></div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-center font-medium">
                          {customer.totalOrders}
                        </td>
                        <td className="px-4 py-3 text-right font-medium">
                          {formatCurrency(customer.totalSpent)}
                        </td>
                        <td className="px-4 py-3 text-right font-medium">
                          {formatCurrency(customer.avgOrderValue)}
                        </td>
                        <td className="px-4 py-3 text-center">
                          <span className={`text-xs ${
                            customer.daysSinceLastOrder <= 30 ? 'text-green-600' :
                            customer.daysSinceLastOrder <= 90 ? 'text-yellow-600' :
                            customer.daysSinceLastOrder <= 365 ? 'text-orange-600' :
                            'text-red-600'
                          }`}>
                            {customer.daysSinceLastOrder === 999 ? 'Never' :
                             customer.daysSinceLastOrder === 0 ? 'Today' :
                             customer.daysSinceLastOrder === 1 ? 'Yesterday' :
                             `${customer.daysSinceLastOrder}d ago`}
                          </span>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Customers List */}
        <div className="space-y-4">
          {filteredCustomers.length === 0 ? (
            <Card>
              <CardContent className="p-12 text-center">
                <Users className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  {language === 'mm' ? 'ဖောက်သည် မတွေ့ရပါ' : 'No Customers Found'}
                </h3>
                <p className="text-gray-600 dark:text-gray-400">
                  {language === 'mm' 
                    ? 'လက်ရှိ စစ်ထုတ်မှု အတိုင်း ဖောက်သည် မတွေ့ရပါ'
                    : 'No customers match your current filters'
                  }
                </p>
              </CardContent>
            </Card>
          ) : (
            filteredCustomers.map((customer) => {
              const loyaltyLevel = getLoyaltyLevel(customer.loyaltyPoints)
              const LoyaltyIcon = loyaltyLevel.icon
              
              return (
                <Card key={customer._id} className="hover:shadow-lg transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div className="w-12 h-12 bg-gradient-to-br from-pink-500 to-rose-600 rounded-full flex items-center justify-center text-white font-bold text-lg">
                          {customer.name.charAt(0).toUpperCase()}
                        </div>
                        
                        <div>
                          <div className="flex items-center gap-2 mb-1">
                            <h3 className="font-semibold text-lg">{customer.name}</h3>
                            {getCustomerTypeIcon(customer.customerType)}
                            {getCustomerTypeBadge(customer.customerType)}
                            {getStatusBadge(customer.status)}
                          </div>
                          
                          <div className="flex items-center gap-4 text-sm text-gray-600 mb-2">
                            <div className="flex items-center gap-1">
                              <Mail className="h-4 w-4" />
                              <span>{customer.email}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Phone className="h-4 w-4" />
                              <span>{customer.phone}</span>
                            </div>
                            {customer.address && (
                              <div className="flex items-center gap-1">
                                <MapPin className="h-4 w-4" />
                                <span className="truncate max-w-48">
                                  {typeof customer.address === 'string'
                                    ? customer.address
                                    : (customer.address && typeof customer.address === 'object' && 'street' in customer.address
                                        ? (customer.address as any).street
                                        : ''
                                      )
                                  }
                                </span>
                              </div>
                            )}
                          </div>
                          
                          <div className="flex items-center gap-6 text-sm">
                            <div className="flex items-center gap-1">
                              <LoyaltyIcon className={`h-4 w-4 ${loyaltyLevel.color}`} />
                              <span className={loyaltyLevel.color}>
                                {loyaltyLevel.level}: {customer.loyaltyPoints} {language === 'mm' ? 'အမှတ်' : 'points'}
                              </span>
                            </div>
                            <div className="flex items-center gap-1">
                              <DollarSign className="h-4 w-4 text-green-600" />
                              <span>{language === 'mm' ? 'စုစုပေါင်း' : 'Total'}: {formatCurrency(customer.totalSpent)}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <ShoppingBag className="h-4 w-4 text-blue-600" />
                              <span>{customer.totalOrders} {language === 'mm' ? 'အမှာစာများ' : 'orders'}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <Calendar className="h-4 w-4 text-purple-600" />
                              <span>{language === 'mm' ? 'နောက်ဆုံး' : 'Last'}: {formatLastPurchase(customer.lastPurchase)}</span>
                            </div>
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewCustomer(customer._id)}
                        >
                          <Eye className="h-4 w-4 mr-2" />
                          {language === 'mm' ? 'ကြည့်ရန်' : 'View'}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEditCustomer(customer._id)}
                        >
                          <Edit className="h-4 w-4 mr-2" />
                          {language === 'mm' ? 'ပြင်ဆင်' : 'Edit'}
                        </Button>

                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          onClick={() => handleDeleteCustomer(customer._id)}
                        >
                          <Trash2 className="h-4 w-4 mr-2" />
                          {language === 'mm' ? 'ဖျက်ရန်' : 'Delete'}
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )
            })
          )}
        </div>
      </div>
    </MainLayout>
  )
}
