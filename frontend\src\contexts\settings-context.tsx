'use client'

import React, { createContext, useContext, useEffect, useState } from 'react'

export type Language = 'en' | 'mm'
export type Currency = 'MMK' | 'USD' | 'THB'
export type Theme = 'light' | 'dark' | 'system'


interface CurrencyInfo {
  code: Currency
  symbol: string
  name: string
  nameLocal: string
  rate: number // Exchange rate to MMK
}

interface SettingsContextType {
  language: Language
  currency: Currency
  theme: Theme
  setLanguage: (language: Language) => void
  setCurrency: (currency: Currency) => void
  setTheme: (theme: Theme) => void
  formatCurrency: (amount: number, showSymbol?: boolean) => string
  getCurrencyInfo: (code: Currency) => CurrencyInfo
  convertCurrency: (amount: number, from: Currency, to: Currency) => number
  availableCurrencies: CurrencyInfo[]
}

const SettingsContext = createContext<SettingsContextType | undefined>(undefined)

export function SettingsProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('en')
  const [currency, setCurrency] = useState<Currency>('MMK')
  const [theme, setTheme] = useState<Theme>('light')

  const availableCurrencies: CurrencyInfo[] = [
    {
      code: 'MMK',
      symbol: 'K',
      name: 'Myanmar Kyat',
      nameLocal: 'မြန်မာ ကျပ်',
      rate: 1
    },
    {
      code: 'USD',
      symbol: '$',
      name: 'US Dollar',
      nameLocal: 'အမေရိကန် ဒေါ်လာ',
      rate: 0.00048 // 1 USD = ~2100 MMK
    },
    {
      code: 'THB',
      symbol: '฿',
      name: 'Thai Baht',
      nameLocal: 'ထိုင်း ဘတ်',
      rate: 0.017 // 1 THB = ~60 MMK
    }
  ]

  useEffect(() => {
    // Load saved settings only on client side
    if (typeof window !== 'undefined') {
      const savedLanguage = localStorage.getItem('language') as Language
      const savedCurrency = localStorage.getItem('bitstech_current_currency') as Currency

      if (savedLanguage && ['en', 'mm'].includes(savedLanguage)) {
        setLanguage(savedLanguage)
      }

      if (savedCurrency && availableCurrencies.find(c => c.code === savedCurrency)) {
        setCurrency(savedCurrency)
      }
    }
  }, [])

  useEffect(() => {
    // Save language preference only on client side
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', language)

      // Update document language
      document.documentElement.lang = language === 'mm' ? 'my' : 'en'

      // Only dispatch event if language actually changed (not initial load)
      const storedLanguage = localStorage.getItem('language')
      if (storedLanguage && storedLanguage !== language) {
        window.dispatchEvent(new CustomEvent('language-changed', {
          detail: { language }
        }))
        console.log('🌐 Settings language changed:', language)
      }
    }
  }, [language])

  useEffect(() => {
    // Save currency preference only on client side and sync with Currency Context
    if (typeof window !== 'undefined') {
      localStorage.setItem('bitstech_current_currency', currency)
      // Dispatch event to sync with Currency Context (only if different)
      const currentStoredCurrency = localStorage.getItem('bitstech_current_currency')
      if (currentStoredCurrency !== currency) {
        window.dispatchEvent(new CustomEvent('settings-sync', {
          detail: { type: 'currency', data: { currency } }
        }))
      }
    }
  }, [currency])

  // Listen for currency changes from Currency Context
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleCurrencySync = (event: any) => {
        if (event.detail.type === 'currency' && event.detail.data.currency !== currency) {
          setCurrency(event.detail.data.currency)
        }
      }

      window.addEventListener('settings-sync', handleCurrencySync)
      return () => window.removeEventListener('settings-sync', handleCurrencySync)
    }
  }, [currency])

  // Listen for language changes from Theme Context
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const handleLanguageChange = (event: any) => {
        const newLanguage = event.detail.language
        if (newLanguage && newLanguage !== language) {
          // Use setTimeout to avoid setState during render
          setTimeout(() => {
            setLanguage(newLanguage)
            console.log('🌐 Settings language synced:', newLanguage)
          }, 0)
        }
      }

      window.addEventListener('language-changed', handleLanguageChange)
      return () => window.removeEventListener('language-changed', handleLanguageChange)
    }
  }, [language])



  const getCurrencyInfo = (code: Currency): CurrencyInfo => {
    return availableCurrencies.find(c => c.code === code) || availableCurrencies[0]
  }

  const convertCurrency = (amount: number, from: Currency, to: Currency): number => {
    if (from === to) return amount

    const fromInfo = getCurrencyInfo(from)
    const toInfo = getCurrencyInfo(to)

    // Convert to MMK first, then to target currency
    const mmkAmount = from === 'MMK' ? amount : amount / fromInfo.rate
    const convertedAmount = to === 'MMK' ? mmkAmount : mmkAmount * toInfo.rate

    return Math.round(convertedAmount * 100) / 100
  }

  const formatCurrency = (amount: number, showSymbol: boolean = true): string => {
    const currencyInfo = getCurrencyInfo(currency)
    const convertedAmount = convertCurrency(amount, 'MMK', currency)

    let formattedAmount: string

    if (currency === 'MMK') {
      // Format MMK without decimals
      formattedAmount = Math.round(convertedAmount).toLocaleString()
    } else {
      // Format other currencies with 2 decimals
      formattedAmount = convertedAmount.toLocaleString(undefined, {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      })
    }

    if (showSymbol) {
      if (currency === 'MMK') {
        return `${formattedAmount} ${currencyInfo.symbol}`
      } else {
        return `${currencyInfo.symbol}${formattedAmount}`
      }
    }

    return formattedAmount
  }

  const value = {
    language,
    currency,
    theme,
    setLanguage,
    setCurrency,
    setTheme,
    formatCurrency,
    getCurrencyInfo,
    convertCurrency,
    availableCurrencies
  }

  return (
    <SettingsContext.Provider value={value}>
      {children}
    </SettingsContext.Provider>
  )
}

export function useSettings() {
  const context = useContext(SettingsContext)
  if (context === undefined) {
    throw new Error('useSettings must be used within a SettingsProvider')
  }
  return context
}

// Translation helper
export const translations = {
  en: {
    // Navigation
    dashboard: 'Dashboard',
    analytics: 'Analytics',
    kpis: 'KPIs',
    forecasting: 'Forecasting',
    pos: 'POS Terminal',
    mobilePOS: 'Mobile POS',
    products: 'Products',
    inventory: 'Inventory',
    customers: 'Customers',
    reports: 'Reports',
    settings: 'Settings',

    // Common
    save: 'Save',
    cancel: 'Cancel',
    delete: 'Delete',
    edit: 'Edit',
    add: 'Add',
    search: 'Search',
    filter: 'Filter',
    export: 'Export',
    import: 'Import',
    refresh: 'Refresh',
    loading: 'Loading...',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Information',

    // POS
    addToCart: 'Add to Cart',
    removeFromCart: 'Remove from Cart',
    checkout: 'Checkout',
    payment: 'Payment',
    cash: 'Cash',
    card: 'Card',
    mobile: 'Mobile Payment',
    qr: 'QR Code',
    total: 'Total',
    subtotal: 'Subtotal',
    tax: 'Tax',
    discount: 'Discount',
    change: 'Change',
    receipt: 'Receipt',

    // Products
    productName: 'Product Name',
    category: 'Category',
    price: 'Price',
    stock: 'Stock',
    barcode: 'Barcode',
    description: 'Description',

    // Customers
    customerName: 'Customer Name',
    phone: 'Phone',
    email: 'Email',
    address: 'Address',
    loyaltyPoints: 'Loyalty Points',

    // Settings
    language: 'Language',
    currency: 'Currency',
    theme: 'Theme',
    notifications: 'Notifications',
    security: 'Security',
    backup: 'Backup',

    // Time
    today: 'Today',
    yesterday: 'Yesterday',
    thisWeek: 'This Week',
    thisMonth: 'This Month',
    thisYear: 'This Year',

    // Status
    active: 'Active',
    inactive: 'Inactive',
    pending: 'Pending',
    completed: 'Completed',
    cancelled: 'Cancelled',

    // Units
    pieces: 'pieces',
    items: 'items',
    orders: 'orders',
    sales: 'sales'
  },
  mm: {
    // Navigation
    dashboard: 'ဒက်ရှ်ဘုတ်',
    analytics: 'ခွဲခြမ်းစိတ်ဖြာမှု',
    kpis: 'KPI များ',
    forecasting: 'ခန့်မှန်းချက်',
    pos: 'ရောင်းချရေး',
    mobilePOS: 'မိုဘိုင်း POS',
    products: 'ကုန်ပစ္စည်းများ',
    inventory: 'စတော့',
    customers: 'ဖောက်သည်များ',
    reports: 'အစီရင်ခံစာများ',
    settings: 'ဆက်တင်များ',

    // Common
    save: 'သိမ်းရန်',
    cancel: 'မလုပ်တော့',
    delete: 'ဖျက်ရန်',
    edit: 'ပြင်ဆင်ရန်',
    add: 'ထည့်ရန်',
    search: 'ရှာရန်',
    filter: 'စစ်ထုတ်ရန်',
    export: 'ထုတ်ယူရန်',
    import: 'တင်သွင်းရန်',
    refresh: 'ပြန်လည်ရယူ',
    loading: 'ရယူနေသည်...',
    success: 'အောင်မြင်ပါပြီ',
    error: 'အမှားအယွင်း',
    warning: 'သတိပေးချက်',
    info: 'အချက်အလက်',

    // POS
    addToCart: 'စျေးခြင်းတောင်းထဲ ထည့်ရန်',
    removeFromCart: 'စျေးခြင်းတောင်းမှ ဖယ်ရှားရန်',
    checkout: 'ငွေရှင်းရန်',
    payment: 'ငွေပေးချေမှု',
    cash: 'ငွေသား',
    card: 'ကတ်',
    mobile: 'မိုဘိုင်း ငွေပေးချေမှု',
    qr: 'QR ကုဒ်',
    total: 'စုစုပေါင်း',
    subtotal: 'ခွဲစုစုပေါင်း',
    tax: 'အခွန်',
    discount: 'လျှော့စျေး',
    change: 'ပြန်အမ်း',
    receipt: 'ဘောက်ချာ',

    // Products
    productName: 'ကုန်ပစ္စည်း အမည်',
    category: 'အမျိုးအစား',
    price: 'စျေးနှုန်း',
    stock: 'စတော့',
    barcode: 'ဘားကုဒ်',
    description: 'ဖော်ပြချက်',

    // Customers
    customerName: 'ဖောက်သည် အမည်',
    phone: 'ဖုန်းနံပါတ်',
    email: 'အီးမေးလ်',
    address: 'လိပ်စာ',
    loyaltyPoints: 'သစ္စာရှိမှု အမှတ်များ',

    // Settings
    language: 'ဘာသာစကား',
    currency: 'ငွေကြေး',
    theme: 'အပြင်အဆင်',
    notifications: 'အကြောင်းကြားချက်များ',
    security: 'လုံခြုံရေး',
    backup: 'အရန်သိမ်းဆည်းမှု',

    // Time
    today: 'ယနေ့',
    yesterday: 'မနေ့က',
    thisWeek: 'ဒီအပတ်',
    thisMonth: 'ဒီလ',
    thisYear: 'ဒီနှစ်',

    // Status
    active: 'အသုံးပြုနေသော',
    inactive: 'အသုံးမပြုသော',
    pending: 'စောင့်ဆိုင်းနေသော',
    completed: 'ပြီးစီးသော',
    cancelled: 'ပယ်ဖျက်သော',

    // Units
    pieces: 'ခု',
    items: 'ပစ္စည်းများ',
    orders: 'အမှာစာများ',
    sales: 'ရောင်းအားများ'
  },
  th: {
    // Navigation
    dashboard: 'แดชบอร์ด',
    analytics: 'การวิเคราะห์',
    kpis: 'ตัวชี้วัด',
    forecasting: 'การพยากรณ์',
    pos: 'จุดขาย',
    mobilePOS: 'จุดขายมือถือ',
    products: 'สินค้า',
    inventory: 'คลังสินค้า',
    customers: 'ลูกค้า',
    reports: 'รายงาน',
    settings: 'การตั้งค่า',

    // Common
    save: 'บันทึก',
    cancel: 'ยกเลิก',
    delete: 'ลบ',
    edit: 'แก้ไข',
    add: 'เพิ่ม',
    search: 'ค้นหา',
    filter: 'กรอง',
    export: 'ส่งออก',
    import: 'นำเข้า',
    refresh: 'รีเฟรช',
    loading: 'กำลังโหลด...',
    success: 'สำเร็จ',
    error: 'ข้อผิดพลาด',
    warning: 'คำเตือน',
    info: 'ข้อมูล',

    // POS
    addToCart: 'เพิ่มลงตะกร้า',
    removeFromCart: 'ลบออกจากตะกร้า',
    checkout: 'ชำระเงิน',
    payment: 'การชำระเงิน',
    cash: 'เงินสด',
    card: 'บัตร',
    mobile: 'ชำระผ่านมือถือ',
    qr: 'QR Code',
    total: 'รวมทั้งหมด',
    subtotal: 'ยอดรวม',
    tax: 'ภาษี',
    discount: 'ส่วนลด',
    change: 'เงินทอน',
    receipt: 'ใบเสร็จ',

    // Products
    productName: 'ชื่อสินค้า',
    category: 'หมวดหมู่',
    price: 'ราคา',
    stock: 'สต็อก',
    barcode: 'บาร์โค้ด',
    description: 'รายละเอียด',

    // Customers
    customerName: 'ชื่อลูกค้า',
    phone: 'เบอร์โทร',
    email: 'อีเมล',
    address: 'ที่อยู่',
    loyaltyPoints: 'คะแนนสะสม',

    // Settings
    language: 'ภาษา',
    currency: 'สกุลเงิน',
    theme: 'ธีม',
    notifications: 'การแจ้งเตือน',
    security: 'ความปลอดภัย',
    backup: 'สำรองข้อมูล',

    // Time
    today: 'วันนี้',
    yesterday: 'เมื่อวาน',
    thisWeek: 'สัปดาห์นี้',
    thisMonth: 'เดือนนี้',
    thisYear: 'ปีนี้',

    // Status
    active: 'ใช้งาน',
    inactive: 'ไม่ใช้งาน',
    pending: 'รอดำเนินการ',
    completed: 'เสร็จสิ้น',
    cancelled: 'ยกเลิก',

    // Units
    pieces: 'ชิ้น',
    items: 'รายการ',
    orders: 'คำสั่งซื้อ',
    sales: 'ยอดขาย'
  }
}

export function useTranslation() {
  const { language } = useSettings()

  const t = (key: keyof typeof translations.en): string => {
    return translations[language][key] || translations.en[key] || key
  }

  return { t, language }
}
