'use client'

import React, { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Search,
  Plus,
  Minus,
  Trash2,
  ShoppingCart,
  CreditCard,
  Smartphone,
  Building,
  Banknote,
  QrCode,
  Wallet,
  DollarSign
} from 'lucide-react'

interface Product {
  _id: string
  name: string
  price: number
  currency: string
  category: {
    _id: string
    name: string
    color: string
    icon: string
  }
  sku: string
  inventory: {
    quantity: number
    unit: string
  }
  image?: string
  isActive: boolean
}

interface CartItem {
  product: Product
  quantity: number
  totalPrice: number
}

interface EnhancedCartProps {
  cart: CartItem[]
  language: 'en' | 'mm'
  currency: string
  updateQuantity: (productId: string, quantity: number) => void
  removeFromCart: (productId: string) => void
  clearCart: () => void
  processPayment: () => Promise<void>
  isProcessing: boolean
  getTotalAmount: () => number
  getTotalItems: () => number
}

export function EnhancedCart({
  cart,
  language,
  currency,
  updateQuantity,
  removeFromCart,
  clearCart,
  processPayment,
  isProcessing,
  getTotalAmount,
  getTotalItems
}: EnhancedCartProps) {
  const [customerName, setCustomerName] = useState('')
  const [customerPhone, setCustomerPhone] = useState('')
  const [customerAddress, setCustomerAddress] = useState('')
  const [customerInfo, setCustomerInfo] = useState<any>(null)
  const [taxRate, setTaxRate] = useState(5)
  const [discountPercent, setDiscountPercent] = useState(0)
  const [shippingCost, setShippingCost] = useState(0)
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState('')
  const [showPaymentMethods, setShowPaymentMethods] = useState(false)
  const [showPaymentVerification, setShowPaymentVerification] = useState(false)
  const [paymentDetails, setPaymentDetails] = useState({
    phoneNumber: '',
    accountName: '',
    transactionId: ''
  })
  const [verificationPassword, setVerificationPassword] = useState('')

  const updateCustomerInfo = () => {
    if (customerName && customerPhone) {
      setCustomerInfo({
        name: customerName,
        phone: customerPhone,
        address: customerAddress
      })
      setShowPaymentMethods(true)
    }
  }

  const clearCustomerInfo = () => {
    setCustomerName('')
    setCustomerPhone('')
    setCustomerAddress('')
    setCustomerInfo(null)
    setShowPaymentMethods(false)
  }

  // Currency options
  const currencies = [
    { code: 'MMK', symbol: 'K', name: language === 'mm' ? 'ကျပ်' : 'Myanmar Kyat', flag: '🇲🇲' },
    { code: 'THB', symbol: '฿', name: language === 'mm' ? 'ဘတ်' : 'Thai Baht', flag: '🇹🇭' },
    { code: 'USD', symbol: '$', name: language === 'mm' ? 'ဒေါ်လာ' : 'US Dollar', flag: '🇺🇸' }
  ]

  const currencySymbol = currencies.find(c => c.code === currency)?.symbol || '$'

  const subtotal = getTotalAmount()
  const taxAmount = Math.round(subtotal * (taxRate / 100))
  const discountAmount = Math.round(subtotal * (discountPercent / 100))
  const totalAmount = subtotal + taxAmount - discountAmount + shippingCost

  const paymentMethods = [
    { id: 'kbz', name: 'KBZ Pay', icon: Smartphone, color: 'bg-blue-600' },
    { id: 'wave', name: 'Wave Money', icon: Wallet, color: 'bg-purple-600' },
    { id: 'aya', name: 'AYA Pay', icon: CreditCard, color: 'bg-red-600' },
    { id: 'cb', name: 'CB Bank', icon: Building, color: 'bg-green-600' },
    { id: 'nug', name: 'NUG Pay', icon: DollarSign, color: 'bg-yellow-600' },
    { id: 'cash', name: 'Cash', icon: Banknote, color: 'bg-gray-600' }
  ]

  const handlePaymentMethodSelect = (methodId: string) => {
    setSelectedPaymentMethod(methodId)
    // Don't show verification immediately, wait for checkout button
  }

  const handleCheckout = () => {
    if (selectedPaymentMethod && cart.length > 0 && customerInfo) {
      setShowPaymentVerification(true)
    }
  }

  const handleVerifyPayment = () => {
    if (verificationPassword === 'admin123') { // Simple verification
      processPayment()
      setShowPaymentVerification(false)
      // Reset form
      setVerificationPassword('')
    } else {
      alert(language === 'mm' ? 'စကားဝှက် မှားနေပါသည်' : 'Incorrect password')
    }
  }

  return (
    <div className="w-full bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-slate-800 dark:to-slate-900 text-slate-800 dark:text-white shadow-xl rounded-lg border border-blue-200 dark:border-slate-600 flex flex-col h-full overflow-hidden">
      {/* Header */}
      <div className="p-3 lg:p-4 bg-gradient-to-r from-blue-600 to-indigo-600 text-white flex-shrink-0">
        <h2 className="text-sm lg:text-lg font-bold flex items-center">
          <ShoppingCart className="h-4 w-4 mr-2" />
          {language === 'mm' ? 'အော်ဒါ အသေးစိတ်များ' : 'Order Details'}
        </h2>
      </div>

      <div className="flex-1 overflow-y-auto">
        <div className="p-3 lg:p-4 space-y-3 lg:space-y-4">
        {/* Customer Details Section */}
        <div className="bg-white dark:bg-slate-700 rounded-lg p-3 shadow-sm">
          <h3 className="text-sm lg:text-base font-semibold mb-3 text-blue-700 dark:text-blue-300">
            {language === 'mm' ? 'ဖောက်သည် အချက်အလက်များ' : 'Customer Details'}
          </h3>

          <div className="space-y-2">
            <div>
              <label className="block text-xs font-medium text-slate-600 dark:text-slate-300 mb-1">
                {language === 'mm' ? 'ဖောက်သည် အမည်' : 'Customer Name'} *
              </label>
              <Input
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                placeholder={language === 'mm' ? 'မောင်မောင်' : 'Maung Maung'}
                className="h-8 text-xs bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-slate-600 dark:text-slate-300 mb-1">
                {language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone No'} *
              </label>
              <Input
                value={customerPhone}
                onChange={(e) => setCustomerPhone(e.target.value)}
                placeholder="***********"
                className="h-8 text-xs bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-slate-600 dark:text-slate-300 mb-1">
                {language === 'mm' ? 'လိပ်စာ' : 'Address'}
              </label>
              <Input
                value={customerAddress}
                onChange={(e) => setCustomerAddress(e.target.value)}
                placeholder={language === 'mm' ? 'ရန်ကုန်မြို့၊ ကမာရွတ်မြို့နယ်' : 'Yangon City, Kamayut Township'}
                className="h-8 text-xs bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-slate-600 dark:text-slate-300 mb-1">
                {language === 'mm' ? 'အီးမေးလ်' : 'Email'}
              </label>
              <Input
                type="email"
                placeholder={language === 'mm' ? '<EMAIL>' : '<EMAIL>'}
                className="h-8 text-xs bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600"
              />
            </div>

            <div>
              <label className="block text-xs font-medium text-slate-600 dark:text-slate-300 mb-1">
                {language === 'mm' ? 'မှတ်ချက်' : 'Notes'}
              </label>
              <Input
                placeholder={language === 'mm' ? 'အပိုမှတ်ချက်များ...' : 'Additional notes...'}
                className="h-8 text-xs bg-white dark:bg-slate-800 border-slate-300 dark:border-slate-600"
              />
            </div>

            <div className="flex gap-2">
              <Button
                onClick={updateCustomerInfo}
                className="flex-1 h-8 text-xs bg-blue-600 hover:bg-blue-700"
                disabled={!customerName || !customerPhone}
              >
                {language === 'mm' ? 'အပ်ဒိတ်' : 'Update'}
              </Button>
              <Button
                onClick={clearCustomerInfo}
                variant="outline"
                className="flex-1 h-8 text-xs border-slate-300 dark:border-slate-600"
              >
                {language === 'mm' ? 'ရှင်း' : 'Clear'}
              </Button>
            </div>
          </div>
        </div>

        {/* Customer Information Display */}
        {customerInfo && (
          <div className="p-4 bg-slate-100 dark:bg-slate-800 rounded-lg border border-slate-300 dark:border-slate-600">
            <h4 className="font-semibold mb-3">
              {language === 'mm' ? 'ဖောက်သည် အချက်အလက်များ' : 'Customer Information'}
            </h4>
            <div className="space-y-2 text-sm">
              <div className="flex items-center gap-2">
                <span className="font-medium text-slate-600 dark:text-slate-300">{language === 'mm' ? 'အမည်:' : 'Name:'}</span>
                <span>{customerInfo.name}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-slate-600 dark:text-slate-300">{language === 'mm' ? 'လိပ်စာ:' : 'Address:'}</span>
                <span>{customerInfo.address || '-'}</span>
              </div>
              <div className="flex items-center gap-2">
                <span className="font-medium text-slate-600 dark:text-slate-300">{language === 'mm' ? 'ဖုန်း:' : 'Phone:'}</span>
                <span>{customerInfo.phone}</span>
              </div>
            </div>
          </div>
        )}

        {/* Current Order Table */}
        <div>
          <h4 className="font-semibold mb-3">
            {language === 'mm' ? 'လက်ရှိ အော်ဒါ' : 'Current Order'}
          </h4>

          {cart.length === 0 ? (
            <div className="text-center py-8 text-slate-500 dark:text-slate-400 bg-slate-100 dark:bg-slate-800 rounded-lg border border-slate-300 dark:border-slate-600">
              <ShoppingCart className="h-12 w-12 mx-auto mb-3 opacity-50" />
              <p>{language === 'mm' ? 'ခြင်းတောင်းထဲ ပစ္စည်းမရှိပါ' : 'No items in cart'}</p>
            </div>
          ) : (
            <div className="space-y-2">
              <div className="grid grid-cols-12 gap-1 text-xs font-medium text-slate-600 dark:text-slate-300 pb-2 border-b border-slate-300 dark:border-slate-600">
                <div className="col-span-4">{language === 'mm' ? 'ပစ္စည်း' : 'Product'}</div>
                <div className="col-span-2 text-right">{language === 'mm' ? 'ဈေး' : 'Price'}</div>
                <div className="col-span-1 text-center">{language === 'mm' ? 'လက်ကျန်' : 'Stock'}</div>
                <div className="col-span-2 text-center">{language === 'mm' ? 'အရေအတွက်' : 'Qty'}</div>
                <div className="col-span-1 text-center">{language === 'mm' ? 'လျှော့စျေး' : 'Disc'}</div>
                <div className="col-span-2 text-right">{language === 'mm' ? 'စုစုပေါင်း' : 'Total'}</div>
                <div className="col-span-1 text-center">{language === 'mm' ? 'လုပ်ဆောင်ချက်' : 'Action'}</div>
              </div>

              {cart.map(item => (
                <div key={item.product._id} className="grid grid-cols-12 gap-1 py-2 text-sm border-b border-slate-300 dark:border-slate-700 hover:bg-slate-100 dark:hover:bg-slate-800/50 transition-colors">
                  <div className="col-span-4">
                    <div className="font-medium text-xs truncate">{item.product.name}</div>
                    <div className="text-xs text-slate-500 dark:text-slate-400">{item.product.sku}</div>
                  </div>
                  <div className="col-span-2 text-right font-medium text-xs">
                    {currencySymbol}{item.product.price.toLocaleString()}
                  </div>
                  <div className="col-span-1 text-center">
                    <span className={`px-1 py-0.5 rounded text-xs ${
                      item.product.inventory.quantity > 10
                        ? 'bg-green-900 text-green-300'
                        : item.product.inventory.quantity > 0
                          ? 'bg-yellow-900 text-yellow-300'
                          : 'bg-red-900 text-red-300'
                    }`}>
                      {item.product.inventory.quantity}
                    </span>
                  </div>
                  <div className="col-span-2">
                    <div className="flex items-center justify-center gap-1">
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateQuantity(item.product._id, item.quantity - 1)}
                        className="h-5 w-5 p-0 border-slate-600 hover:bg-slate-700 transition-all hover:scale-110"
                      >
                        <Minus className="h-2 w-2" />
                      </Button>
                      <span className="w-6 text-center text-xs font-medium bg-slate-700 rounded px-1">
                        {item.quantity}
                      </span>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateQuantity(item.product._id, item.quantity + 1)}
                        className="h-5 w-5 p-0 border-slate-600 hover:bg-slate-700 transition-all hover:scale-110"
                        disabled={item.quantity >= item.product.inventory.quantity}
                      >
                        <Plus className="h-2 w-2" />
                      </Button>
                    </div>
                  </div>
                  <div className="col-span-1 text-center text-xs">0%</div>
                  <div className="col-span-2 text-right font-bold text-blue-400 text-xs">
                    {currencySymbol}{item.totalPrice.toLocaleString()}
                  </div>
                  <div className="col-span-1 text-center">
                    <Button
                      size="sm"
                      variant="ghost"
                      onClick={() => removeFromCart(item.product._id)}
                      className="h-5 w-5 p-0 text-red-400 hover:text-red-300 hover:bg-red-900/20 transition-all hover:scale-110"
                    >
                      <Trash2 className="h-2 w-2" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Order Summary */}
        {cart.length > 0 && (
          <div className="space-y-4">
            {/* Subtotal */}
            <div className="flex justify-between items-center text-lg">
              <span>{language === 'mm' ? 'စုစုပေါင်း' : 'Subtotal'}</span>
              <span className="font-bold">{currencySymbol} {subtotal.toLocaleString()}</span>
            </div>

            {/* Tax */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <span>{language === 'mm' ? 'အခွန်' : 'Tax'}</span>
                <Input
                  type="number"
                  value={taxRate}
                  onChange={(e) => setTaxRate(Number(e.target.value))}
                  className="w-16 h-8 text-center bg-slate-800 border-slate-600"
                  min="0"
                  max="100"
                />
                <span>%</span>
              </div>
              <span>{currencySymbol}{taxAmount.toLocaleString()}</span>
            </div>

            {/* Discount */}
            <div className="flex justify-between items-center">
              <span>{language === 'mm' ? 'လျှော့စျေး (%)' : 'Discount (%)'}</span>
              <span>{currencySymbol} {discountAmount.toLocaleString()}</span>
            </div>

            {/* Shipping */}
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-2">
                <span>{language === 'mm' ? 'ပို့ဆောင်ခ' : 'Shipping'}</span>
                <Input
                  type="number"
                  value={shippingCost}
                  onChange={(e) => setShippingCost(Number(e.target.value))}
                  className="w-20 h-8 text-center bg-slate-800 border-slate-600"
                  min="0"
                />
              </div>
              <span>{currencySymbol}{shippingCost.toLocaleString()}</span>
            </div>

            {/* Total */}
            <div className="border-t border-slate-600 pt-4">
              <div className="flex justify-between items-center text-xl font-bold">
                <span>{language === 'mm' ? 'စုစုပေါင်း' : 'Totals'}</span>
                <span className="text-green-400">{currencySymbol} {totalAmount.toLocaleString()}</span>
              </div>
            </div>
          </div>
        )}

        {/* Payment Methods */}
        {showPaymentMethods && cart.length > 0 && (
          <div className="space-y-4">
            <h3 className="text-lg font-semibold flex items-center">
              <CreditCard className="h-5 w-5 mr-2" />
              {language === 'mm' ? 'ငွေပေးချေမှု နည်းလမ်း' : 'Payment Method'}
            </h3>

            <div className="grid grid-cols-3 gap-3">
              {paymentMethods.map(method => {
                const IconComponent = method.icon
                return (
                  <Button
                    key={method.id}
                    onClick={() => handlePaymentMethodSelect(method.id)}
                    className={`h-16 flex flex-col items-center justify-center gap-1 ${
                      selectedPaymentMethod === method.id
                        ? `${method.color} ring-2 ring-white`
                        : 'bg-slate-700 hover:bg-slate-600'
                    }`}
                  >
                    <IconComponent className="h-6 w-6" />
                    <span className="text-xs">{method.name}</span>
                  </Button>
                )
              })}
            </div>

            {selectedPaymentMethod && (
              <div className="p-4 bg-green-900/20 border border-green-600 rounded-lg">
                <div className="flex items-center gap-2 text-green-400">
                  <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                  <span className="text-sm font-medium">
                    {language === 'mm' ? 'ရွေးချယ်ထားသည်:' : 'Selected:'} {
                      paymentMethods.find(m => m.id === selectedPaymentMethod)?.name
                    }
                  </span>
                </div>
              </div>
            )}

            {/* Payment Details Form */}
            {selectedPaymentMethod && selectedPaymentMethod !== 'cash' && (
              <div className="space-y-3 p-4 bg-slate-800 rounded-lg border border-slate-600">
                <div className="grid grid-cols-2 gap-3">
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-1">
                      {language === 'mm' ? 'ဖုန်းနံပါတ်' : 'Phone Number'}
                    </label>
                    <Input
                      value={paymentDetails.phoneNumber}
                      onChange={(e) => setPaymentDetails(prev => ({ ...prev, phoneNumber: e.target.value }))}
                      placeholder="***********"
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-300 mb-1">
                      {language === 'mm' ? 'အကောင့်အမည်' : 'Account Name'}
                    </label>
                    <Input
                      value={paymentDetails.accountName}
                      onChange={(e) => setPaymentDetails(prev => ({ ...prev, accountName: e.target.value }))}
                      placeholder="John Doe"
                      className="bg-slate-700 border-slate-600 text-white"
                    />
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-1">
                    {language === 'mm' ? 'QR ကုဒ် / ငွေလွှဲ ID' : 'QR Code / Transaction ID'}
                  </label>
                  <Input
                    value={paymentDetails.transactionId}
                    onChange={(e) => setPaymentDetails(prev => ({ ...prev, transactionId: e.target.value }))}
                    placeholder="KBZ123456789"
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>
              </div>
            )}

            {/* Action Buttons */}
            <div className="space-y-3">
              <Button
                onClick={handleCheckout}
                className="w-full bg-green-600 hover:bg-green-700 h-12 text-lg font-semibold transition-all hover:scale-105 hover:shadow-lg"
                disabled={!selectedPaymentMethod || !customerInfo}
              >
                {language === 'mm' ? 'ငွေပေးချေရန်' : 'Checkout'}
              </Button>

              <Button
                onClick={() => {
                  clearCart()
                  clearCustomerInfo()
                  setSelectedPaymentMethod('')
                  setShowPaymentMethods(false)
                }}
                variant="outline"
                className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
              >
                {language === 'mm' ? 'အားလုံး ပြန်လည်သတ်မှတ်ရန်' : 'Reset All'}
              </Button>
            </div>
          </div>
        )}

        {/* Payment Verification Modal */}
        {showPaymentVerification && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-slate-800 rounded-lg p-6 w-96 max-w-[90vw]">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold flex items-center">
                  <CreditCard className="h-5 w-5 mr-2" />
                  {language === 'mm' ? 'ငွေပေးချေမှု အတည်ပြုခြင်း' : 'Payment Verification'}
                </h3>
                <Button
                  onClick={() => setShowPaymentVerification(false)}
                  variant="ghost"
                  className="h-8 w-8 p-0"
                >
                  ×
                </Button>
              </div>

              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-slate-400">{language === 'mm' ? 'နည်းလမ်း:' : 'Method:'}</span>
                    <div className="font-semibold text-blue-400">
                      {paymentMethods.find(m => m.id === selectedPaymentMethod)?.name}
                    </div>
                  </div>
                  <div>
                    <span className="text-slate-400">{language === 'mm' ? 'ပမာណ:' : 'Amount:'}</span>
                    <div className="font-bold text-green-400 text-lg">
                      {totalAmount.toLocaleString()} MMK
                    </div>
                  </div>
                  <div>
                    <span className="text-slate-400">{language === 'mm' ? 'အကောင့်:' : 'Account:'}</span>
                    <div className="font-medium">{paymentDetails.phoneNumber || '***********'}</div>
                  </div>
                  <div>
                    <span className="text-slate-400">{language === 'mm' ? 'အမည်:' : 'Name:'}</span>
                    <div className="font-medium">{paymentDetails.accountName || 'John Doe'}</div>
                  </div>
                </div>

                <div>
                  <span className="text-slate-400 text-sm">{language === 'mm' ? 'ငွေလွှဲ ID:' : 'Transaction ID:'}</span>
                  <div className="font-mono text-sm bg-slate-700 p-2 rounded">{paymentDetails.transactionId || 'KBZ123456789'}</div>
                </div>

                {/* QR Code */}
                <div className="flex flex-col items-center py-4">
                  <div className="bg-white p-4 rounded-lg mb-2">
                    <QrCode className="h-24 w-24 text-blue-600" />
                  </div>
                  <span className="text-sm text-slate-400">
                    {language === 'mm' ? 'အတည်ပြုရန်အတွက် စကင်န်ဖတ်ပါ' : 'Scan for verification'}
                  </span>
                </div>

                <div className="bg-green-900/20 border border-green-600 rounded p-3">
                  <div className="flex items-center gap-2 text-green-400 text-sm">
                    <ShoppingCart className="h-4 w-4" />
                    <span>{language === 'mm' ? 'စုစုပေါင်း ပစ္စည်းများ:' : 'Total Items:'}</span>
                    <span className="font-bold">$ {totalAmount.toLocaleString()}</span>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-slate-300 mb-2">
                    🔒 {language === 'mm' ? 'လုံခြုံရေး အတည်ပြုခြင်း:' : 'Security Verification:'}
                  </label>
                  <Input
                    type="password"
                    value={verificationPassword}
                    onChange={(e) => setVerificationPassword(e.target.value)}
                    placeholder={language === 'mm' ? 'စကားဝှက် ရိုက်ထည့်ပါ (admin123)' : 'Enter verification code (admin123)'}
                    className="bg-slate-700 border-slate-600 text-white"
                  />
                </div>

                <div className="space-y-2">
                  <Button
                    onClick={handleVerifyPayment}
                    className="w-full bg-green-600 hover:bg-green-700 h-12 transition-all hover:scale-105"
                    disabled={isProcessing || !verificationPassword}
                  >
                    {isProcessing
                      ? (language === 'mm' ? 'လုပ်ဆောင်နေသည်...' : 'Processing...')
                      : (language === 'mm' ? '✅ အတည်ပြုပြီး ငွေပေးချေရန်' : '✅ Verify & Process Payment')
                    }
                  </Button>

                  <Button
                    variant="outline"
                    className="w-full border-slate-600 text-slate-300 hover:bg-slate-700"
                    onClick={() => setShowPaymentVerification(false)}
                  >
                    {language === 'mm' ? '📝 အချက်အလက် ပြင်ဆင်ရန်' : '📝 Update Info'}
                  </Button>
                </div>
              </div>
            </div>
          </div>
        )}
        </div>
      </div>

      {/* Footer */}
      <div className="p-4 lg:p-6 border-t border-slate-200 dark:border-slate-700 bg-slate-50 dark:bg-slate-800 flex-shrink-0">
        <div className="text-center text-xs text-slate-500 dark:text-slate-400">
          <p>BitsTech POS v1.0</p>
          <p className="mt-1">
            {language === 'mm' ? 'မြန်မာစီးပွားရေးအတွက်' : 'Built for Myanmar businesses'}
          </p>
        </div>
      </div>
    </div>
  )
}
