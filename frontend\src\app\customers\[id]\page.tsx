'use client'

import { useState, useEffect } from 'react'
import { useRout<PERSON>, useParams } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import {
  ArrowLeft,
  Edit,
  Gift,
  ShoppingBag,
  DollarSign,
  Calendar,
  Phone,
  Mail,
  MapPin,
  Star,
  Crown,
  Award,
  Heart,
  TrendingUp,
  Package,
  Clock,
  User,
  CreditCard
} from 'lucide-react'

interface Customer {
  _id: string
  name: string
  email: string
  phone: string
  address?: string
  dateOfBirth?: string
  gender?: 'male' | 'female' | 'other'
  customerType: 'regular' | 'vip' | 'wholesale' | 'new'
  loyaltyPoints: number
  totalSpent: number
  totalOrders: number
  lastPurchase?: string
  joinDate: string
  status: 'active' | 'inactive'
  notes?: string
  preferences?: {
    language: 'en' | 'mm'
    currency: 'MMK' | 'USD'
    notifications: boolean
  }
}

interface PurchaseHistory {
  _id: string
  orderNumber: string
  date: string
  items: {
    name: string
    quantity: number
    price: number
  }[]
  total: number
  pointsEarned: number
  status: 'completed' | 'pending' | 'cancelled'
}

interface LoyaltyTransaction {
  _id: string
  type: 'earned' | 'redeemed'
  points: number
  description: string
  date: string
  orderId?: string
}

export default function CustomerDetailPage() {
  const { user: currentUser, isAuthenticated, isLoading } = useAuth()
  const router = useRouter()
  const params = useParams()
  const customerId = params.id as string
  
  const [customer, setCustomer] = useState<Customer | null>(null)
  const [purchaseHistory, setPurchaseHistory] = useState<PurchaseHistory[]>([])
  const [loyaltyTransactions, setLoyaltyTransactions] = useState<LoyaltyTransaction[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    }
  }, [isAuthenticated, isLoading, router])

  useEffect(() => {
    if (isAuthenticated && customerId) {
      fetchCustomerDetails()
      fetchPurchaseHistory()
      fetchLoyaltyTransactions()
    }
  }, [isAuthenticated, customerId])

  const fetchCustomerDetails = async () => {
    try {
      setLoading(true)
      
      // Mock customer data
      const mockCustomer: Customer = {
        _id: customerId,
        name: 'Aung Kyaw Min',
        email: '<EMAIL>',
        phone: '+95-9-123456789',
        address: 'No. 123, Yangon Street, Yangon, Myanmar',
        dateOfBirth: '1985-03-15T00:00:00Z',
        gender: 'male',
        customerType: 'vip',
        loyaltyPoints: 2850,
        totalSpent: 5680000,
        totalOrders: 45,
        lastPurchase: '2024-01-14T10:30:00Z',
        joinDate: '2023-06-15T00:00:00Z',
        status: 'active',
        notes: 'Preferred customer, always buys high-end laptops. Prefers to be contacted via email.',
        preferences: {
          language: 'mm',
          currency: 'MMK',
          notifications: true
        }
      }

      setCustomer(mockCustomer)
    } catch (error) {
      console.error('Error fetching customer details:', error)
    } finally {
      setLoading(false)
    }
  }

  const fetchPurchaseHistory = async () => {
    try {
      // Mock purchase history
      const mockHistory: PurchaseHistory[] = [
        {
          _id: '1',
          orderNumber: 'ORD-2024-001',
          date: '2024-01-14T10:30:00Z',
          items: [
            { name: 'ASUS VivoBook 15', quantity: 1, price: 1200000 },
            { name: 'Wireless Mouse', quantity: 1, price: 25000 }
          ],
          total: 1225000,
          pointsEarned: 122,
          status: 'completed'
        },
        {
          _id: '2',
          orderNumber: 'ORD-2024-002',
          date: '2024-01-10T15:45:00Z',
          items: [
            { name: 'Samsung 27" Monitor', quantity: 1, price: 450000 },
            { name: 'HDMI Cable', quantity: 2, price: 15000 }
          ],
          total: 480000,
          pointsEarned: 48,
          status: 'completed'
        },
        {
          _id: '3',
          orderNumber: 'ORD-2024-003',
          date: '2024-01-05T09:20:00Z',
          items: [
            { name: 'Kingston 1TB SSD', quantity: 1, price: 180000 },
            { name: 'USB-C Hub', quantity: 1, price: 45000 }
          ],
          total: 225000,
          pointsEarned: 22,
          status: 'completed'
        }
      ]

      setPurchaseHistory(mockHistory)
    } catch (error) {
      console.error('Error fetching purchase history:', error)
    }
  }

  const fetchLoyaltyTransactions = async () => {
    try {
      // Mock loyalty transactions
      const mockTransactions: LoyaltyTransaction[] = [
        {
          _id: '1',
          type: 'earned',
          points: 122,
          description: 'Purchase: ASUS VivoBook 15 + Accessories',
          date: '2024-01-14T10:30:00Z',
          orderId: 'ORD-2024-001'
        },
        {
          _id: '2',
          type: 'earned',
          points: 48,
          description: 'Purchase: Samsung Monitor + Cable',
          date: '2024-01-10T15:45:00Z',
          orderId: 'ORD-2024-002'
        },
        {
          _id: '3',
          type: 'redeemed',
          points: -100,
          description: 'Discount applied on SSD purchase',
          date: '2024-01-05T09:20:00Z',
          orderId: 'ORD-2024-003'
        },
        {
          _id: '4',
          type: 'earned',
          points: 22,
          description: 'Purchase: Storage + USB Hub',
          date: '2024-01-05T09:20:00Z',
          orderId: 'ORD-2024-003'
        },
        {
          _id: '5',
          type: 'earned',
          points: 50,
          description: 'Birthday bonus points',
          date: '2024-03-15T00:00:00Z'
        }
      ]

      setLoyaltyTransactions(mockTransactions)
    } catch (error) {
      console.error('Error fetching loyalty transactions:', error)
    }
  }

  const getLoyaltyLevel = (points: number) => {
    if (points >= 3000) return { level: 'Platinum', color: 'text-purple-600', icon: Crown, bg: 'bg-purple-100' }
    if (points >= 2000) return { level: 'Gold', color: 'text-yellow-600', icon: Award, bg: 'bg-yellow-100' }
    if (points >= 1000) return { level: 'Silver', color: 'text-gray-600', icon: Star, bg: 'bg-gray-100' }
    return { level: 'Bronze', color: 'text-orange-600', icon: Heart, bg: 'bg-orange-100' }
  }

  const getCustomerTypeBadge = (type: string) => {
    switch (type) {
      case 'vip':
        return <Badge className="bg-yellow-100 text-yellow-800">VIP Customer</Badge>
      case 'wholesale':
        return <Badge className="bg-purple-100 text-purple-800">Wholesale</Badge>
      case 'regular':
        return <Badge className="bg-blue-100 text-blue-800">Regular</Badge>
      case 'new':
        return <Badge className="bg-green-100 text-green-800">New Customer</Badge>
      default:
        return <Badge variant="outline">{type}</Badge>
    }
  }

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'completed':
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>
      case 'pending':
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>
      case 'cancelled':
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const formatCurrency = (amount: number) => {
    return `${amount.toLocaleString()} MMK`
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const calculateAge = (birthDate: string) => {
    const today = new Date()
    const birth = new Date(birthDate)
    let age = today.getFullYear() - birth.getFullYear()
    const monthDiff = today.getMonth() - birth.getMonth()
    
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--
    }
    
    return age
  }

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated || !customer) {
    return null
  }

  const language = currentUser?.preferences?.language as 'en' | 'mm' || 'en'
  const loyaltyLevel = getLoyaltyLevel(customer.loyaltyPoints)
  const LoyaltyIcon = loyaltyLevel.icon

  return (
    <MainLayout language={language}>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/customers')}
            className="hover:bg-pink-50 dark:hover:bg-pink-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Customers'}
          </Button>
        </div>

        {/* Customer Profile Header */}
        <Card>
          <CardContent className="p-8">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-6">
                <div className="w-20 h-20 bg-gradient-to-br from-pink-500 to-rose-600 rounded-full flex items-center justify-center text-white font-bold text-2xl">
                  {customer.name.charAt(0).toUpperCase()}
                </div>
                
                <div>
                  <div className="flex items-center gap-3 mb-2">
                    <h1 className="text-3xl font-bold">{customer.name}</h1>
                    {getCustomerTypeBadge(customer.customerType)}
                    <Badge className={`${loyaltyLevel.bg} ${loyaltyLevel.color}`}>
                      <LoyaltyIcon className="h-3 w-3 mr-1" />
                      {loyaltyLevel.level}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-6 text-gray-600 mb-3">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4" />
                      <span>{customer.email}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4" />
                      <span>{customer.phone}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      <span>{language === 'mm' ? 'အသက်' : 'Age'}: {customer.dateOfBirth ? calculateAge(customer.dateOfBirth) : 'N/A'}</span>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-6 text-sm">
                    <div className="flex items-center gap-1">
                      <Gift className="h-4 w-4 text-orange-600" />
                      <span className="font-medium">{customer.loyaltyPoints.toLocaleString()} {language === 'mm' ? 'အမှတ်များ' : 'Points'}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <DollarSign className="h-4 w-4 text-green-600" />
                      <span className="font-medium">{formatCurrency(customer.totalSpent)} {language === 'mm' ? 'စုစုပေါင်း' : 'Total Spent'}</span>
                    </div>
                    <div className="flex items-center gap-1">
                      <ShoppingBag className="h-4 w-4 text-blue-600" />
                      <span className="font-medium">{customer.totalOrders} {language === 'mm' ? 'အမှာစာများ' : 'Orders'}</span>
                    </div>
                  </div>
                </div>
              </div>
              
              <div className="flex items-center gap-3">
                <Button
                  variant="outline"
                  onClick={() => router.push(`/customers/${customer._id}/edit`)}
                >
                  <Edit className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ပြင်ဆင်ရန်' : 'Edit Customer'}
                </Button>
                
                <Button className="bg-gradient-to-r from-pink-600 to-rose-600 hover:from-pink-700 hover:to-rose-700">
                  <Gift className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'အမှတ် ပေးရန်' : 'Award Points'}
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Customer Information */}
          <div className="lg:col-span-2 space-y-6">
            {/* Personal Information */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5 text-blue-600" />
                  {language === 'mm' ? 'ကိုယ်ရေးကိုယ်တာ အချက်အလက်များ' : 'Personal Information'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'လိင်' : 'Gender'}
                    </label>
                    <p className="text-lg capitalize">{customer.gender || 'Not specified'}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'မွေးနေ့' : 'Date of Birth'}
                    </label>
                    <p className="text-lg">
                      {customer.dateOfBirth ? formatDate(customer.dateOfBirth) : 'Not provided'}
                    </p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'ဖောက်သည် ဖြစ်သည့်ရက်' : 'Customer Since'}
                    </label>
                    <p className="text-lg">{formatDate(customer.joinDate)}</p>
                  </div>
                  
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'နောက်ဆုံး ဝယ်ယူမှု' : 'Last Purchase'}
                    </label>
                    <p className="text-lg">
                      {customer.lastPurchase ? formatDate(customer.lastPurchase) : 'Never'}
                    </p>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-600">
                    {language === 'mm' ? 'လိပ်စာ' : 'Address'}
                  </label>
                  <p className="text-lg">
                    {typeof customer.address === 'string'
                      ? customer.address
                      : (customer.address && typeof customer.address === 'object' && 'street' in customer.address
                          ? (customer.address as any).street
                          : 'Not provided'
                        ) || 'Not provided'
                    }
                  </p>
                </div>
                
                {customer.notes && (
                  <div>
                    <label className="text-sm font-medium text-gray-600">
                      {language === 'mm' ? 'မှတ်ချက်များ' : 'Notes'}
                    </label>
                    <p className="text-lg">{customer.notes}</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Purchase History */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <ShoppingBag className="h-5 w-5 text-green-600" />
                  {language === 'mm' ? 'ဝယ်ယူမှု မှတ်တမ်းများ' : 'Purchase History'}
                </CardTitle>
                <CardDescription>
                  {language === 'mm' 
                    ? 'နောက်ဆုံး ဝယ်ယူမှုများ နှင့် အသေးစိတ်များ'
                    : 'Recent purchases and order details'
                  }
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {purchaseHistory.map((order) => (
                    <div key={order._id} className="p-4 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-semibold">{order.orderNumber}</h4>
                          <p className="text-sm text-gray-600">{formatDateTime(order.date)}</p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-green-600">{formatCurrency(order.total)}</p>
                          <div className="flex items-center gap-2">
                            {getStatusBadge(order.status)}
                            <Badge variant="outline" className="text-xs">
                              +{order.pointsEarned} {language === 'mm' ? 'အမှတ်' : 'pts'}
                            </Badge>
                          </div>
                        </div>
                      </div>
                      
                      <div className="space-y-1">
                        {order.items.map((item, index) => (
                          <div key={index} className="flex justify-between text-sm">
                            <span>{item.name} x{item.quantity}</span>
                            <span>{formatCurrency(item.price * item.quantity)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Loyalty & Quick Stats */}
          <div className="space-y-6">
            {/* Loyalty Points */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Gift className="h-5 w-5 text-orange-600" />
                  {language === 'mm' ? 'သစ္စာရှိမှု အမှတ်များ' : 'Loyalty Points'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="text-center">
                  <div className={`w-20 h-20 ${loyaltyLevel.bg} rounded-full flex items-center justify-center mx-auto mb-3`}>
                    <LoyaltyIcon className={`h-10 w-10 ${loyaltyLevel.color}`} />
                  </div>
                  <h3 className={`text-2xl font-bold ${loyaltyLevel.color}`}>{loyaltyLevel.level}</h3>
                  <p className="text-3xl font-bold text-gray-900 dark:text-white">
                    {customer.loyaltyPoints.toLocaleString()}
                  </p>
                  <p className="text-sm text-gray-600">
                    {language === 'mm' ? 'လက်ရှိ အမှတ်များ' : 'Available Points'}
                  </p>
                </div>
                
                <div className="space-y-2">
                  <div className="flex justify-between text-sm">
                    <span>{language === 'mm' ? 'နောက်တစ်ဆင့်သို့' : 'Next Level'}:</span>
                    <span className="font-medium">
                      {customer.loyaltyPoints >= 3000 ? 'Max Level' : `${3000 - customer.loyaltyPoints} points`}
                    </span>
                  </div>
                  
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-gradient-to-r from-orange-500 to-pink-500 h-2 rounded-full transition-all duration-500" 
                      style={{ width: `${Math.min((customer.loyaltyPoints / 3000) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-purple-600" />
                  {language === 'mm' ? 'အမြန် ကိန်းဂဏန်းများ' : 'Quick Stats'}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {language === 'mm' ? 'ပျမ်းမျှ အမှာစာ တန်ဖိုး' : 'Average Order Value'}
                  </span>
                  <span className="font-semibold">
                    {formatCurrency(Math.round(customer.totalSpent / customer.totalOrders))}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {language === 'mm' ? 'စုစုပေါင်း အမှတ် ရရှိ' : 'Total Points Earned'}
                  </span>
                  <span className="font-semibold">
                    {Math.round(customer.totalSpent / 1000).toLocaleString()}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {language === 'mm' ? 'ဖောက်သည် အဆင့်' : 'Customer Tier'}
                  </span>
                  <span className={`font-semibold ${loyaltyLevel.color}`}>
                    {loyaltyLevel.level}
                  </span>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">
                    {language === 'mm' ? 'အခြေအနေ' : 'Status'}
                  </span>
                  <Badge className={customer.status === 'active' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}>
                    {customer.status === 'active' ? (language === 'mm' ? 'လက်ရှိ' : 'Active') : (language === 'mm' ? 'မလှုပ်ရှား' : 'Inactive')}
                  </Badge>
                </div>
              </CardContent>
            </Card>

            {/* Loyalty Transactions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CreditCard className="h-5 w-5 text-blue-600" />
                  {language === 'mm' ? 'အမှတ် လုပ်ငန်းများ' : 'Points Transactions'}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {loyaltyTransactions.slice(0, 5).map((transaction) => (
                    <div key={transaction._id} className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-800 rounded">
                      <div className="flex-1">
                        <p className="text-sm font-medium">{transaction.description}</p>
                        <p className="text-xs text-gray-600">{formatDateTime(transaction.date)}</p>
                      </div>
                      <div className={`font-semibold ${transaction.type === 'earned' ? 'text-green-600' : 'text-red-600'}`}>
                        {transaction.type === 'earned' ? '+' : ''}{transaction.points}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </MainLayout>
  )
}
