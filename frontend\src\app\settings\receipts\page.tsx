'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from '@/components/layout/main-layout'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Badge } from '@/components/ui/badge'
import { useAuth } from '@/contexts/auth-context'
import { useSettings } from '@/contexts/settings-context'
import apiClient from '@/lib/api'


import {
  Receipt,
  ArrowLeft,
  Save,
  Check,
  Eye,
  Download,
  Upload,
  Printer,
  FileText,
  Type,
  Layout,
  Settings,
  Plus,
  Copy,
  Trash2
} from 'lucide-react'

interface ReceiptTemplate {
  id: string
  name: string
  description: string
  isDefault: boolean
  headerLogo: boolean
  headerText: string
  footerText: string
  showQR: boolean
  showBarcode: boolean
  paperSize: 'thermal-58' | 'thermal-80' | 'a4'
  fontSize: 'small' | 'medium' | 'large'
  alignment: 'left' | 'center' | 'right'
  showTax: boolean
  showDiscount: boolean
  showCustomerInfo: boolean
  isProfessional?: boolean
}

export default function ReceiptTemplatesPage() {
  const { isAuthenticated, isLoading } = useAuth()
  const { language } = useSettings()
  const router = useRouter()
  const [saving, setSaving] = useState(false)
  const [saved, setSaved] = useState(false)
  const [selectedTemplate, setSelectedTemplate] = useState('default')
  const [showCreateTemplate, setShowCreateTemplate] = useState(false)
  const [newTemplateName, setNewTemplateName] = useState('')
  const [previewData] = useState({
    items: [
      { name: 'Gaming Mouse', price: 45000, qty: 1 },
      { name: 'Mechanical Keyboard', price: 85000, qty: 1 },
      { name: 'USB Cable', price: 5000, qty: 2 }
    ],
    subtotal: 135000,
    tax: 6750,
    total: 141750,
    customer: 'John Doe',
    date: new Date().toLocaleDateString(),
    invoiceNo: 'INV-001'
  })

  const [templates, setTemplates] = useState<ReceiptTemplate[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  const currentTemplate = templates.find(t => t.id === selectedTemplate) || templates[0] || null

  // Load receipt templates
  const loadTemplates = async () => {
    try {
      setLoading(true)
      setError(null)
      const response = await apiClient.getReceiptTemplates()

      if (response.success && response.data) {
        setTemplates(response.data)
        // Set first template as selected if none selected
        if (!selectedTemplate && response.data.length > 0) {
          setSelectedTemplate(response.data[0].id)
        }
      } else {
        // Fallback to default templates
        const defaultTemplates: ReceiptTemplate[] = [
          {
            id: 'modern',
            name: 'Modern',
            description: 'Clean and modern design',
            fontSize: 'medium' as const,
            alignment: 'left' as const,
            paperSize: 'a4' as const,
            headerLogo: true,
            headerText: 'BitsTech Computer Store\nThank you for your purchase!',
            footerText: 'Visit us again!\nwww.bitstech.com',
            showQR: true,
            showBarcode: false,
            showTax: true,
            showDiscount: true,
            showCustomerInfo: false,
            isDefault: true
          },
          {
            id: 'classic',
            name: 'Classic',
            description: 'Traditional business style',
            fontSize: 'medium' as const,
            alignment: 'center' as const,
            paperSize: 'a4' as const,
            headerLogo: true,
            headerText: 'BitsTech Computer Store\nThank you for your purchase!',
            footerText: 'Visit us again!\nwww.bitstech.com',
            showQR: true,
            showBarcode: false,
            showTax: true,
            showDiscount: true,
            showCustomerInfo: false,
            isDefault: false
          },
          {
            id: 'minimal',
            name: 'Minimal',
            description: 'Simple and clean',
            fontSize: 'small' as const,
            alignment: 'left' as const,
            paperSize: 'thermal-80' as const,
            headerLogo: false,
            headerText: 'BitsTech Store',
            footerText: 'Thank you!',
            showQR: false,
            showBarcode: false,
            showTax: true,
            showDiscount: false,
            showCustomerInfo: false,
            isDefault: false
          }
        ]
        setTemplates(defaultTemplates)
        if (!selectedTemplate) {
          setSelectedTemplate('modern')
        }
        console.log('Using default templates')
      }
    } catch (error) {
      console.error('Error loading templates:', error)
      setError('Failed to load templates. Using default templates.')

      // Fallback to default templates on error
      const defaultTemplates: ReceiptTemplate[] = [
        {
          id: 'modern',
          name: 'Modern',
          description: 'Clean and modern design',
          fontSize: 'medium' as const,
          alignment: 'left' as const,
          paperSize: 'a4' as const,
          headerLogo: true,
          headerText: 'BitsTech Computer Store\nThank you for your purchase!',
          footerText: 'Visit us again!\nwww.bitstech.com',
          showQR: true,
          showBarcode: false,
          showTax: true,
          showDiscount: true,
          showCustomerInfo: false,
          isDefault: true
        }
      ]
      setTemplates(defaultTemplates)
      if (!selectedTemplate) {
        setSelectedTemplate('modern')
      }
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      router.push('/auth/login')
    } else if (isAuthenticated) {
      loadTemplates()
    }
  }, [isAuthenticated, isLoading, router])

  const generateReceiptHTML = () => {
    return `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Receipt Preview</title>
        <style>
          body {
            font-family: monospace;
            margin: 20px;
            line-height: 1.2;
            ${currentTemplate?.fontSize === 'small' ? 'font-size: 12px;' :
              currentTemplate?.fontSize === 'large' ? 'font-size: 16px;' : 'font-size: 14px;'}
            ${currentTemplate?.alignment === 'center' ? 'text-align: center;' :
              currentTemplate?.alignment === 'right' ? 'text-align: right;' : 'text-align: left;'}
          }
          .receipt {
            ${currentTemplate?.paperSize === 'thermal-58' ? 'max-width: 200px;' :
              currentTemplate?.paperSize === 'thermal-80' ? 'max-width: 280px;' : 'max-width: 100%;'}
            margin: 0 auto;
            border: 2px dashed #ccc;
            padding: 20px;
          }
          .divider { border-top: 1px dashed #666; margin: 10px 0; }
          .flex { display: flex; justify-content: space-between; }
          .bold { font-weight: bold; }
          .small { font-size: 0.8em; color: #666; }
          .logo { width: 64px; height: 64px; background: #eee; margin: 0 auto 10px; display: flex; align-items: center; justify-content: center; }
          .qr, .barcode { background: #eee; margin: 10px auto; display: flex; align-items: center; justify-content: center; }
          .qr { width: 64px; height: 64px; }
          .barcode { width: 80px; height: 32px; }
        </style>
      </head>
      <body>
        <div class="receipt">
          ${currentTemplate?.headerLogo ? '<div class="logo">LOGO</div>' : ''}
          <div style="margin-bottom: 15px; white-space: pre-line;">${currentTemplate?.headerText || ''}</div>
          <div class="divider"></div>
          <div style="margin-bottom: 15px;">
            <div>Invoice: ${previewData.invoiceNo}</div>
            <div>Date: ${previewData.date}</div>
            ${currentTemplate?.showCustomerInfo ? `<div>Customer: ${previewData.customer}</div>` : ''}
          </div>
          <div class="divider"></div>
          <div style="margin-bottom: 15px;">
            ${previewData.items.map(item => `
              <div class="flex" style="margin-bottom: 5px;">
                <div>
                  <div>${item.name}</div>
                  <div class="small">${item.qty} x ${item.price.toLocaleString()}</div>
                </div>
                <div>${(item.price * item.qty).toLocaleString()}</div>
              </div>
            `).join('')}
          </div>
          <div class="divider"></div>
          <div>
            <div class="flex"><span>Subtotal:</span><span>${previewData.subtotal.toLocaleString()}</span></div>
            ${currentTemplate?.showTax ? `<div class="flex"><span>Tax (5%):</span><span>${previewData.tax.toLocaleString()}</span></div>` : ''}
            ${currentTemplate?.showDiscount ? '<div class="flex" style="color: red;"><span>Discount:</span><span>-0</span></div>' : ''}
            <div class="divider"></div>
            <div class="flex bold"><span>Total:</span><span>${previewData.total.toLocaleString()} MMK</span></div>
          </div>
          <div class="divider"></div>
          <div style="margin-top: 15px; white-space: pre-line; font-size: 0.8em;">${currentTemplate?.footerText || ''}</div>
          ${(currentTemplate?.showQR || currentTemplate?.showBarcode) ? `
            <div style="margin-top: 15px; text-align: center;">
              ${currentTemplate?.showQR ? '<div class="qr">QR</div>' : ''}
              ${currentTemplate?.showBarcode ? '<div class="barcode">|||||||</div>' : ''}
            </div>
          ` : ''}
        </div>
      </body>
      </html>
    `;
  }

  const updateTemplate = async (field: keyof ReceiptTemplate, value: any) => {
    if (!selectedTemplate || !currentTemplate) return

    // Update local state immediately for better UX
    setTemplates(prev => prev.map(template =>
      template.id === selectedTemplate
        ? { ...template, [field]: value }
        : template
    ))

    // Save to API in background
    try {
      const updatedTemplate = { ...currentTemplate, [field]: value }
      await apiClient.updateReceiptTemplate(selectedTemplate, updatedTemplate)
    } catch (error) {
      console.error('Error updating template:', error)
      // Revert local changes if API fails
      loadTemplates()
    }
  }

  const setDefaultTemplate = async (templateId: string) => {
    try {
      const response = await apiClient.setDefaultReceiptTemplate(templateId)

      if (response.success) {
        setTemplates(prev => prev.map(template => ({
          ...template,
          isDefault: template.id === templateId
        })))
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)
      } else {
        throw new Error(response.message || 'Failed to set default template')
      }
    } catch (error) {
      console.error('Error setting default template:', error)
      alert(language === 'mm'
        ? 'မူလ ပုံစံ သတ်မှတ်မှု မအောင်မြင်ပါ'
        : 'Failed to set default template'
      )
    }
  }

  const createNewTemplate = async () => {
    if (!newTemplateName.trim()) {
      alert(language === 'mm'
        ? 'ပုံစံ အမည် ဖြည့်စွက်ပါ'
        : 'Please enter template name'
      )
      return
    }

    try {
      setSaving(true)
      const templateData = {
        name: newTemplateName,
        description: 'Custom receipt template',
        headerLogo: true,
        headerText: 'BitsTech Computer Store\nThank you for your purchase!',
        footerText: 'Visit us again!\nwww.bitstech.com',
        showQR: true,
        showBarcode: false,
        paperSize: 'thermal-80' as const,
        fontSize: 'medium' as const,
        alignment: 'center' as const,
        showTax: true,
        showDiscount: true,
        showCustomerInfo: false
      }

      const response = await apiClient.createReceiptTemplate(templateData)

      if (response.success) {
        setTemplates(prev => [...prev, response.data])
        setSelectedTemplate(response.data.id)
        setNewTemplateName('')
        setShowCreateTemplate(false)
        setSaved(true)
        setTimeout(() => setSaved(false), 3000)
      } else {
        throw new Error(response.message || 'Failed to create template')
      }
    } catch (error) {
      console.error('Error creating template:', error)
      alert(language === 'mm'
        ? 'ပုံစံ ဖန်တီးမှု မအောင်မြင်ပါ'
        : 'Failed to create template'
      )
    } finally {
      setSaving(false)
    }
  }

  const duplicateTemplate = (templateId: string) => {
    const template = templates.find(t => t.id === templateId)
    if (!template) return

    const duplicatedTemplate: ReceiptTemplate = {
      ...template,
      id: `copy_${Date.now()}`,
      name: `${template.name} (Copy)`,
      isDefault: false
    }

    setTemplates(prev => [...prev, duplicatedTemplate])
    setSelectedTemplate(duplicatedTemplate.id)
  }

  const deleteTemplate = async (templateId: string) => {
    if (templates.length <= 1) {
      alert(language === 'mm'
        ? 'အနည်းဆုံး ပုံစံ တစ်ခု ရှိရမည်'
        : 'At least one template must remain'
      )
      return
    }

    const template = templates.find(t => t.id === templateId)
    if (template?.isDefault) {
      alert(language === 'mm'
        ? 'မူလ ပုံစံကို ဖျက်၍ မရပါ'
        : 'Cannot delete default template'
      )
      return
    }

    const confirmMessage = language === 'mm'
      ? 'ဤ ပုံစံကို ဖျက်မည်လား?'
      : 'Are you sure you want to delete this template?'

    if (confirm(confirmMessage)) {
      try {
        const response = await apiClient.deleteReceiptTemplate(templateId)

        if (response.success) {
          setTemplates(prev => prev.filter(t => t.id !== templateId))
          if (selectedTemplate === templateId) {
            const remainingTemplates = templates.filter(t => t.id !== templateId)
            setSelectedTemplate(remainingTemplates[0]?.id || '')
          }
          setSaved(true)
          setTimeout(() => setSaved(false), 3000)
        } else {
          throw new Error(response.message || 'Failed to delete template')
        }
      } catch (error) {
        console.error('Error deleting template:', error)
        alert(language === 'mm'
          ? 'ပုံစံ ဖျက်ခြင်း မအောင်မြင်ပါ'
          : 'Failed to delete template'
        )
      }
    }
  }

  const exportTemplates = () => {
    const dataStr = JSON.stringify(templates, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    const link = document.createElement('a')
    link.href = url
    link.download = `bitstech_receipt_templates_${new Date().toISOString().slice(0, 10)}.json`
    link.click()
    URL.revokeObjectURL(url)
  }

  const importTemplates = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (!file) return

    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const importedTemplates = JSON.parse(e.target?.result as string)
        if (Array.isArray(importedTemplates)) {
          // Merge with existing templates, avoiding duplicates
          const newTemplates = importedTemplates.filter(
            (imported: ReceiptTemplate) => !templates.some(existing => existing.id === imported.id)
          )
          setTemplates(prev => [...prev, ...newTemplates])
          alert(language === 'mm'
            ? `${newTemplates.length} ပုံစံ သွင်းယူပြီးပါပြီ`
            : `${newTemplates.length} templates imported successfully`
          )
        }
      } catch (error) {
        alert(language === 'mm'
          ? 'ဖိုင် သွင်းယူမှု မအောင်မြင်ပါ'
          : 'Failed to import templates'
        )
      }
    }
    reader.readAsText(file)
    // Reset input
    event.target.value = ''
  }

  const handleSave = async () => {
    setSaving(true)
    try {
      // Save current template changes
      const currentTemplateData = templates.find(t => t.id === selectedTemplate)
      if (currentTemplateData) {
        const response = await apiClient.updateReceiptTemplate(selectedTemplate, currentTemplateData)

        if (response.success) {
          setSaved(true)
          setTimeout(() => setSaved(false), 3000)
        } else {
          throw new Error(response.message || 'Failed to save template')
        }
      }
    } catch (error) {
      console.error('Error saving template:', error)
      alert(language === 'mm'
        ? 'ပုံစံ သိမ်းဆည်းမှု မအောင်မြင်ပါ'
        : 'Failed to save template'
      )
    } finally {
      setSaving(false)
    }
  }

  if (isLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!isAuthenticated) {
    return null
  }

  // Show error if templates failed to load
  if (error) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600 mb-2">
              {language === 'mm' ? 'ဒေတာ ရယူမှု မအောင်မြင်ပါ' : 'Failed to load data'}
            </h2>
            <p className="text-gray-600 mb-4">{error}</p>
            <Button onClick={loadTemplates}>
              {language === 'mm' ? 'ပြန်လည်ကြိုးစားရန်' : 'Retry'}
            </Button>
          </div>
        </div>
      </MainLayout>
    )
  }

  // Show message if no templates available
  if (!currentTemplate && templates.length === 0) {
    return (
      <MainLayout>
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center">
            <h2 className="text-xl font-semibold mb-2">
              {language === 'mm' ? 'ပုံစံများ မရှိပါ' : 'No Templates Available'}
            </h2>
            <p className="text-gray-600 mb-4">
              {language === 'mm'
                ? 'ပုံစံ အသစ် ဖန်တီးပါ သို့မဟုတ် ပြန်လည်ရယူပါ'
                : 'Create a new template or reload the page'
              }
            </p>
            <div className="flex gap-2 justify-center">
              <Button onClick={() => setShowCreateTemplate(true)}>
                {language === 'mm' ? 'ပုံစံ အသစ် ဖန်တီးရန်' : 'Create New Template'}
              </Button>
              <Button variant="outline" onClick={loadTemplates}>
                {language === 'mm' ? 'ပြန်လည်ရယူရန်' : 'Reload'}
              </Button>
            </div>
          </div>
        </div>
      </MainLayout>
    )
  }



  return (
    <MainLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => router.push('/settings')}
            className="hover:bg-purple-50 dark:hover:bg-purple-900/20"
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            {language === 'mm' ? 'ပြန်သွားရန်' : 'Back to Settings'}
          </Button>
        </div>

        {/* Page Header */}
        <div className="relative overflow-hidden rounded-2xl bg-gradient-to-br from-purple-600 to-pink-600 p-8 text-white">
          <div className="absolute inset-0 bg-white/10 backdrop-blur-sm"></div>
          <div className="relative z-10">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <Receipt className="h-8 w-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {language === 'mm' ? 'ဘောက်ချာ ပုံစံများ' : 'Receipt Templates'}
                </h1>
                <p className="text-white/80 mt-1">
                  {language === 'mm'
                    ? 'ဘောက်ချာ ပုံစံများကို စိတ်ကြိုက် ပြုလုပ်ပြီး အမှတ်တံဆိပ် ထည့်သွင်းပါ'
                    : 'Customize receipt layouts and add your branding'
                  }
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
          {/* Template Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Layout className="h-5 w-5 text-purple-600" />
                {language === 'mm' ? 'ပုံစံများ' : 'Templates'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {/* Create New Template Button */}
              {showCreateTemplate ? (
                <div className="p-3 border-2 border-dashed border-purple-300 rounded-lg">
                  <div className="space-y-3">
                    <Input
                      placeholder={language === 'mm' ? 'ပုံစံ အမည်' : 'Template name'}
                      value={newTemplateName}
                      onChange={(e) => setNewTemplateName(e.target.value)}
                      onKeyDown={(e) => e.key === 'Enter' && createNewTemplate()}
                    />
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={createNewTemplate}
                        disabled={!newTemplateName.trim()}
                        className="bg-purple-600 hover:bg-purple-700"
                      >
                        <Plus className="h-3 w-3 mr-1" />
                        {language === 'mm' ? 'ဖန်တီးရန်' : 'Create'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setShowCreateTemplate(false)
                          setNewTemplateName('')
                        }}
                      >
                        {language === 'mm' ? 'ပယ်ဖျက်' : 'Cancel'}
                      </Button>
                    </div>
                  </div>
                </div>
              ) : (
                <Button
                  variant="outline"
                  className="w-full border-dashed border-purple-300 text-purple-600 hover:bg-purple-50"
                  onClick={() => setShowCreateTemplate(true)}
                >
                  <Plus className="h-4 w-4 mr-2" />
                  {language === 'mm' ? 'ပုံစံ အသစ် ဖန်တီးရန်' : 'Create New Template'}
                </Button>
              )}

              {/* Template List */}
              {templates.map((template) => (
                <div
                  key={template.id}
                  className={`p-3 border-2 rounded-lg cursor-pointer transition-all duration-300 ${
                    selectedTemplate === template.id
                      ? 'border-purple-500 bg-purple-50 dark:bg-purple-900/20'
                      : 'border-gray-200 dark:border-gray-700 hover:border-gray-300'
                  }`}
                  onClick={() => setSelectedTemplate(template.id)}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2">
                        <h3 className="font-medium">{template.name}</h3>
                        {template.isDefault && (
                          <Badge variant="secondary" className="bg-green-100 text-green-700 text-xs">
                            {language === 'mm' ? 'မူလ' : 'Default'}
                          </Badge>
                        )}
                      </div>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                        {template.description}
                      </p>
                    </div>
                  </div>
                  {selectedTemplate === template.id && (
                    <div className="mt-3 flex gap-2 flex-wrap">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          setDefaultTemplate(template.id)
                        }}
                        disabled={template.isDefault}
                      >
                        <Settings className="h-3 w-3 mr-1" />
                        {language === 'mm' ? 'မူလ သတ်မှတ်' : 'Set Default'}
                      </Button>
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={(e) => {
                          e.stopPropagation()
                          duplicateTemplate(template.id)
                        }}
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        {language === 'mm' ? 'ကူးယူ' : 'Duplicate'}
                      </Button>
                      {!template.isDefault && templates.length > 1 && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={(e) => {
                            e.stopPropagation()
                            deleteTemplate(template.id)
                          }}
                          className="text-red-600 hover:bg-red-50"
                        >
                          <Trash2 className="h-3 w-3 mr-1" />
                          {language === 'mm' ? 'ဖျက်' : 'Delete'}
                        </Button>
                      )}
                    </div>
                  )}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Template Configuration */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Type className="h-5 w-5 text-blue-600" />
                {language === 'mm' ? 'ပုံစံ ပြင်ဆင်ခြင်း' : 'Template Configuration'}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Header Settings */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'ခေါင်းစီး ဆက်တင်များ' : 'Header Settings'}
                </Label>

                <div className="flex items-center justify-between">
                  <Label className="text-sm">
                    {language === 'mm' ? 'လိုဂို ပြသရန်' : 'Show Logo'}
                  </Label>
                  <input
                    type="checkbox"
                    checked={currentTemplate?.headerLogo || false}
                    onChange={(e) => updateTemplate('headerLogo', e.target.checked)}
                    className="rounded"
                    aria-label={language === 'mm' ? 'လိုဂို ပြသရန်' : 'Show Logo'}
                  />
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">
                    {language === 'mm' ? 'ခေါင်းစီး စာသား' : 'Header Text'}
                  </Label>
                  <textarea
                    className="w-full p-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800 text-sm"
                    rows={3}
                    value={currentTemplate?.headerText || ''}
                    onChange={(e) => updateTemplate('headerText', e.target.value)}
                    aria-label={language === 'mm' ? 'ခေါင်းစီး စာသား' : 'Header Text'}
                  />
                </div>
              </div>

              {/* Paper & Format Settings */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'စက္ကူ နှင့် ပုံစံ' : 'Paper & Format'}
                </Label>

                <div className="space-y-2">
                  <Label className="text-sm">
                    {language === 'mm' ? 'စက္ကူ အရွယ်အစား' : 'Paper Size'}
                  </Label>
                  <select
                    className="w-full p-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800"
                    value={currentTemplate?.paperSize || 'thermal-80'}
                    onChange={(e) => updateTemplate('paperSize', e.target.value)}
                    aria-label={language === 'mm' ? 'စက္ကူ အရွယ်အစား' : 'Paper Size'}
                  >
                    <option value="thermal-58">Thermal 58mm</option>
                    <option value="thermal-80">Thermal 80mm</option>
                    <option value="a4">A4 Paper</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">
                    {language === 'mm' ? 'စာလုံး အရွယ်အစား' : 'Font Size'}
                  </Label>
                  <select
                    className="w-full p-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800"
                    value={currentTemplate?.fontSize || 'medium'}
                    onChange={(e) => updateTemplate('fontSize', e.target.value)}
                    aria-label={language === 'mm' ? 'စာလုံး အရွယ်အစား' : 'Font Size'}
                  >
                    <option value="small">Small</option>
                    <option value="medium">Medium</option>
                    <option value="large">Large</option>
                  </select>
                </div>

                <div className="space-y-2">
                  <Label className="text-sm">
                    {language === 'mm' ? 'စာသား ညှိခြင်း' : 'Text Alignment'}
                  </Label>
                  <select
                    className="w-full p-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800"
                    value={currentTemplate?.alignment || 'center'}
                    onChange={(e) => updateTemplate('alignment', e.target.value)}
                    aria-label={language === 'mm' ? 'စာသား ညှိခြင်း' : 'Text Alignment'}
                  >
                    <option value="left">Left</option>
                    <option value="center">Center</option>
                    <option value="right">Right</option>
                  </select>
                </div>
              </div>

              {/* Display Options */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'ပြသမှု ရွေးချယ်မှုများ' : 'Display Options'}
                </Label>

                <div className="space-y-3">
                  {[
                    { key: 'showTax', label: language === 'mm' ? 'အခွန် ပြသရန်' : 'Show Tax' },
                    { key: 'showDiscount', label: language === 'mm' ? 'လျှော့စျေး ပြသရန်' : 'Show Discount' },
                    { key: 'showCustomerInfo', label: language === 'mm' ? 'ဖောက်သည် အချက်အလက်' : 'Show Customer Info' },
                    { key: 'showQR', label: language === 'mm' ? 'QR ကုဒ် ပြသရန်' : 'Show QR Code' },
                    { key: 'showBarcode', label: language === 'mm' ? 'ဘားကုဒ် ပြသရန်' : 'Show Barcode' }
                  ].map((option) => (
                    <div key={option.key} className="flex items-center justify-between">
                      <Label className="text-sm">{option.label}</Label>
                      <input
                        type="checkbox"
                        checked={currentTemplate[option.key as keyof ReceiptTemplate] as boolean}
                        onChange={(e) => updateTemplate(option.key as keyof ReceiptTemplate, e.target.checked)}
                        className="rounded"
                        aria-label={option.label}
                      />
                    </div>
                  ))}
                </div>
              </div>

              {/* Footer Settings */}
              <div className="space-y-4">
                <Label className="text-sm font-medium">
                  {language === 'mm' ? 'အောက်ခြေ ဆက်တင်များ' : 'Footer Settings'}
                </Label>

                <div className="space-y-2">
                  <Label className="text-sm">
                    {language === 'mm' ? 'အောက်ခြေ စာသား' : 'Footer Text'}
                  </Label>
                  <textarea
                    className="w-full p-2 border border-gray-300 rounded-lg dark:border-gray-600 dark:bg-gray-800 text-sm"
                    rows={3}
                    value={currentTemplate?.footerText || ''}
                    onChange={(e) => updateTemplate('footerText', e.target.value)}
                    aria-label={language === 'mm' ? 'အောက်ခြေ စာသား' : 'Footer Text'}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Live Preview */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-green-600" />
                {language === 'mm' ? 'တိုက်ရိုက် နမူနာ' : 'Live Preview'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'သင့် ဘောက်ချာ ပုံစံ နမူနာ ကြည့်ရှုပါ'
                  : 'Preview how your receipt will look'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Preview Controls */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const printContent = document.querySelector('.receipt-preview');
                        if (printContent) {
                          const printWindow = window.open('', '_blank');
                          if (printWindow) {
                            printWindow.document.write(`
                              <!DOCTYPE html>
                              <html>
                                <head>
                                  <title>Receipt Print</title>
                                  <style>
                                    @media print {
                                      body { margin: 0; padding: 20px; }
                                      .receipt-preview {
                                        max-width: none !important;
                                        border: none !important;
                                        box-shadow: none !important;
                                        background: white !important;
                                      }
                                    }
                                    body {
                                      font-family: 'Courier New', monospace;
                                      margin: 0;
                                      padding: 20px;
                                      background: white;
                                    }
                                    .receipt-preview {
                                      max-width: ${currentTemplate?.paperSize === 'thermal-58' ? '200px' :
                                        currentTemplate?.paperSize === 'thermal-80' ? '280px' : '100%'};
                                      margin: 0 auto;
                                      padding: 20px;
                                      background: white;
                                    }
                                    .logo { text-align: center; font-weight: bold; margin-bottom: 10px; }
                                    .divider { border-top: 1px dashed #000; margin: 10px 0; }
                                    .flex { display: flex; justify-content: space-between; }
                                    .bold { font-weight: bold; }
                                    .small { font-size: 0.8em; color: #666; }
                                    .qr, .barcode {
                                      text-align: center;
                                      border: 1px solid #ccc;
                                      padding: 10px;
                                      margin: 10px 0;
                                      background: #f5f5f5;
                                    }
                                  </style>
                                </head>
                                <body>
                                  ${printContent.outerHTML}
                                </body>
                              </html>
                            `);
                            printWindow.document.close();
                            printWindow.focus();
                            printWindow.print();
                            printWindow.close();
                          }
                        }
                      }}
                      className="flex items-center gap-2"
                    >
                      <Printer className="h-4 w-4" />
                      {language === 'mm' ? 'စမ်းသပ် ပုံနှိပ်' : 'Test Print'}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const element = document.getElementById('receipt-preview')
                        if (element) {
                          const printWindow = window.open('', '_blank')
                          printWindow?.document.write(`
                            <html>
                              <head><title>Receipt Preview</title></head>
                              <body>${element.innerHTML}</body>
                            </html>
                          `)
                          printWindow?.print()
                        }
                      }}
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      {language === 'mm' ? 'PDF ဒေါင်းလုဒ်' : 'Download PDF'}
                    </Button>
                  </div>

                  <div className="text-xs text-gray-500">
                    {currentTemplate.paperSize} • {currentTemplate.fontSize}
                  </div>
                </div>

                {/* Live Preview */}
                <div
                  id="receipt-preview"
                  className={`bg-white text-black border border-gray-300 rounded-lg p-4 shadow-sm transition-all duration-300 ${
                    currentTemplate.paperSize === 'thermal-58' ? 'max-w-[200px]' :
                    currentTemplate.paperSize === 'thermal-80' ? 'max-w-[280px]' : 'max-w-full'
                  } mx-auto`}
                  style={{
                    fontFamily: 'monospace',
                    fontSize: currentTemplate.fontSize === 'small' ? '10px' :
                             currentTemplate.fontSize === 'large' ? '14px' : '12px',
                    color: '#000000'
                  }}
                >
                  {/* Receipt Preview */}
                  <div className={`text-${currentTemplate.alignment} space-y-2`}>
                    {/* Header */}
                    {currentTemplate.headerLogo && (
                      <div className="flex justify-center mb-3">
                        <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-white font-bold text-sm">
                          BT
                        </div>
                      </div>
                    )}

                    <div className="whitespace-pre-line font-bold text-black">
                      {currentTemplate.headerText}
                    </div>

                    <div className="border-t border-dashed border-gray-400 my-3"></div>

                    {/* Date & Invoice */}
                    <div className="flex justify-between text-xs text-black">
                      <span>Date: {new Date().toLocaleDateString()}</span>
                      <span>INV-001</span>
                    </div>

                    {/* Customer Info */}
                    {currentTemplate.showCustomerInfo && (
                      <>
                        <div className="text-xs">
                          <div>Customer: John Doe</div>
                          <div>Phone: +95 9 123 456 789</div>
                        </div>
                        <div className="border-t border-dashed border-gray-400 my-2"></div>
                      </>
                    )}

                    {/* Sample Items */}
                    <div className="space-y-1">
                      {previewData.items.map((item, index) => (
                        <div key={index} className="flex justify-between text-xs text-black">
                          <span>{item.name} x{item.qty}</span>
                          <span>{item.price.toLocaleString()} K</span>
                        </div>
                      ))}
                    </div>

                    <div className="border-t border-dashed border-gray-400 my-2"></div>

                    {/* Totals */}
                    <div className="space-y-1 text-xs text-black">
                      <div className="flex justify-between">
                        <span>Subtotal:</span>
                        <span>{previewData.subtotal.toLocaleString()} K</span>
                      </div>
                      {currentTemplate.showTax && (
                        <div className="flex justify-between">
                          <span>Tax (5%):</span>
                          <span>{previewData.tax.toLocaleString()} K</span>
                        </div>
                      )}
                      {currentTemplate.showDiscount && (
                        <div className="flex justify-between">
                          <span>Discount:</span>
                          <span>-5,000 K</span>
                        </div>
                      )}
                      <div className="flex justify-between font-bold border-t border-gray-300 pt-1">
                        <span>TOTAL:</span>
                        <span>{previewData.total.toLocaleString()} K</span>
                      </div>
                    </div>

                    <div className="border-t border-dashed border-gray-400 my-3"></div>

                    {/* Payment Method */}
                    <div className="text-xs">
                      <div>Payment: Cash</div>
                      <div>Change: 8,250 K</div>
                    </div>

                    {/* QR/Barcode */}
                    {currentTemplate.showQR && (
                      <div className="flex justify-center my-3">
                        <div className="w-16 h-16 bg-gray-200 border border-gray-300 flex items-center justify-center text-xs rounded">
                          QR Code
                        </div>
                      </div>
                    )}

                    {currentTemplate.showBarcode && (
                      <div className="flex justify-center my-2">
                        <div className="w-24 h-8 bg-gray-200 border border-gray-300 flex items-center justify-center text-xs rounded">
                          |||| |||| ||||
                        </div>
                      </div>
                    )}

                    {/* Footer */}
                    <div className="whitespace-pre-line mt-3 text-xs">
                      {currentTemplate.footerText}
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Live Preview */}
          <Card className="lg:col-span-1 xl:col-span-1">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Eye className="h-5 w-5 text-green-600" />
                {language === 'mm' ? 'တိုက်ရိုက် ကြည့်ရှုခြင်း' : 'Live Preview'}
              </CardTitle>
              <CardDescription>
                {language === 'mm'
                  ? 'ပြင်ဆင်မှုများကို တိုက်ရိုက် ကြည့်ရှုပါ'
                  : 'See your changes in real-time'
                }
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {/* Preview Controls */}
                <div className="flex gap-2 flex-wrap">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const printContent = document.querySelector('.receipt-preview');
                      if (printContent) {
                        const printWindow = window.open('', '_blank');
                        if (printWindow) {
                          printWindow.document.write(`
                            <!DOCTYPE html>
                            <html>
                              <head>
                                <title>Receipt Print</title>
                                <style>
                                  @media print {
                                    body { margin: 0; padding: 20px; }
                                    .receipt-preview {
                                      max-width: none !important;
                                      border: none !important;
                                      box-shadow: none !important;
                                      background: white !important;
                                    }
                                  }
                                  body {
                                    font-family: 'Courier New', monospace;
                                    margin: 0;
                                    padding: 20px;
                                    background: white;
                                  }
                                  .receipt-preview {
                                    max-width: ${currentTemplate?.paperSize === 'thermal-58' ? '200px' :
                                      currentTemplate?.paperSize === 'thermal-80' ? '280px' : '100%'};
                                    margin: 0 auto;
                                    padding: 20px;
                                    background: white;
                                  }
                                  .logo { text-align: center; font-weight: bold; margin-bottom: 10px; }
                                  .divider { border-top: 1px dashed #000; margin: 10px 0; }
                                  .flex { display: flex; justify-content: space-between; }
                                  .bold { font-weight: bold; }
                                  .small { font-size: 0.8em; color: #666; }
                                  .qr, .barcode {
                                    text-align: center;
                                    border: 1px solid #ccc;
                                    padding: 10px;
                                    margin: 10px 0;
                                    background: #f5f5f5;
                                  }
                                </style>
                              </head>
                              <body>
                                ${printContent.outerHTML}
                              </body>
                            </html>
                          `);
                          printWindow.document.close();
                          printWindow.focus();
                          printWindow.print();
                          printWindow.close();
                        }
                      }
                    }}
                    className="text-blue-600 hover:bg-blue-50"
                  >
                    <Printer className="h-3 w-3 mr-1" />
                    {language === 'mm' ? 'စမ်းပုံနှိပ်' : 'Test Print'}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => {
                      const printWindow = window.open('', '_blank');
                      if (printWindow) {
                        printWindow.document.write(generateReceiptHTML());
                        printWindow.document.close();
                        printWindow.print();
                      }
                    }}
                    className="text-green-600 hover:bg-green-50"
                  >
                    <FileText className="h-3 w-3 mr-1" />
                    {language === 'mm' ? 'PDF ထုတ်' : 'Export PDF'}
                  </Button>
                </div>

                {/* Receipt Preview */}
                <div
                  className={`
                    receipt-preview bg-white border-2 border-dashed border-gray-300 rounded-lg p-4
                    ${currentTemplate.paperSize === 'thermal-58' ? 'max-w-[200px]' :
                      currentTemplate.paperSize === 'thermal-80' ? 'max-w-[280px]' : 'max-w-full'}
                    ${currentTemplate.fontSize === 'small' ? 'text-xs' :
                      currentTemplate.fontSize === 'large' ? 'text-base' : 'text-sm'}
                    ${currentTemplate.alignment === 'center' ? 'text-center' :
                      currentTemplate.alignment === 'right' ? 'text-right' : 'text-left'}
                    font-mono mx-auto
                  `}
                  style={{
                    fontFamily: 'monospace',
                    lineHeight: '1.2',
                    color: '#000'
                  }}
                >
                  {/* Receipt Preview */}
                  <div>
                  {/* Header */}
                  {currentTemplate.headerLogo && (
                    <div className="mb-2 flex justify-center">
                      <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
                        LOGO
                      </div>
                    </div>
                  )}

                  <div className="mb-3 whitespace-pre-line">
                    {currentTemplate.headerText}
                  </div>

                  <div className="border-t border-dashed border-gray-400 my-2"></div>

                  {/* Invoice Info */}
                  <div className="mb-3 space-y-1">
                    <div>Invoice: {previewData.invoiceNo}</div>
                    <div>Date: {previewData.date}</div>
                    {currentTemplate.showCustomerInfo && (
                      <div>Customer: {previewData.customer}</div>
                    )}
                  </div>

                  <div className="border-t border-dashed border-gray-400 my-2"></div>

                  {/* Items */}
                  <div className="mb-3">
                    {previewData.items.map((item, index) => (
                      <div key={index} className="flex justify-between mb-1">
                        <div className="flex-1">
                          <div>{item.name}</div>
                          <div className="text-xs text-gray-600">
                            {item.qty} x {item.price.toLocaleString()}
                          </div>
                        </div>
                        <div className="ml-2">
                          {(item.price * item.qty).toLocaleString()}
                        </div>
                      </div>
                    ))}
                  </div>

                  <div className="border-t border-dashed border-gray-400 my-2"></div>

                  {/* Totals */}
                  <div className="space-y-1">
                    <div className="flex justify-between">
                      <span>Subtotal:</span>
                      <span>{previewData.subtotal.toLocaleString()}</span>
                    </div>
                    {currentTemplate.showTax && (
                      <div className="flex justify-between">
                        <span>Tax (5%):</span>
                        <span>{previewData.tax.toLocaleString()}</span>
                      </div>
                    )}
                    {currentTemplate.showDiscount && (
                      <div className="flex justify-between text-red-600">
                        <span>Discount:</span>
                        <span>-0</span>
                      </div>
                    )}
                    <div className="border-t border-dashed border-gray-400 my-1"></div>
                    <div className="flex justify-between font-bold">
                      <span>Total:</span>
                      <span>{previewData.total.toLocaleString()} MMK</span>
                    </div>
                  </div>

                  <div className="border-t border-dashed border-gray-400 my-2"></div>

                  {/* Footer */}
                  <div className="mt-3 whitespace-pre-line text-xs">
                    {currentTemplate.footerText}
                  </div>

                  {/* QR Code / Barcode */}
                  {(currentTemplate.showQR || currentTemplate.showBarcode) && (
                    <div className="mt-3 flex justify-center">
                      {currentTemplate.showQR && (
                        <div className="w-16 h-16 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500 mr-2">
                          QR
                        </div>
                      )}
                      {currentTemplate.showBarcode && (
                        <div className="w-20 h-8 bg-gray-200 rounded flex items-center justify-center text-xs text-gray-500">
                          |||||||
                        </div>
                      )}
                    </div>
                  )}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-between items-center">
          <div className="flex gap-3">
            <Button
              variant="outline"
              onClick={exportTemplates}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {language === 'mm' ? 'ပုံစံများ ထုတ်ယူ' : 'Export Templates'}
            </Button>

            <div className="relative">
              <input
                type="file"
                accept=".json"
                onChange={importTemplates}
                className="absolute inset-0 w-full h-full opacity-0 cursor-pointer"
                id="import-templates"
                aria-label={language === 'mm' ? 'ပုံစံများ သွင်းယူ' : 'Import Templates'}
              />
              <Button
                variant="outline"
                className="flex items-center gap-2"
                onClick={() => document.getElementById('import-templates')?.click()}
              >
                <Upload className="h-4 w-4" />
                {language === 'mm' ? 'ပုံစံများ သွင်းယူ' : 'Import Templates'}
              </Button>
            </div>
          </div>

          <Button
            onClick={handleSave}
            disabled={saving}
            className="bg-gradient-to-r from-purple-600 to-pink-600 hover:from-purple-700 hover:to-pink-700 text-white px-8 py-2 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300"
          >
            {saving ? (
              <div className="flex items-center gap-2">
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                {language === 'mm' ? 'သိမ်းနေသည်...' : 'Saving...'}
              </div>
            ) : saved ? (
              <div className="flex items-center gap-2">
                <Check className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းပြီး' : 'Saved'}
              </div>
            ) : (
              <div className="flex items-center gap-2">
                <Save className="h-4 w-4" />
                {language === 'mm' ? 'သိမ်းရန်' : 'Save Templates'}
              </div>
            )}
          </Button>
        </div>
      </div>
    </MainLayout>
  )
}
